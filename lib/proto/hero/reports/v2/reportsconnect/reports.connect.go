// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: hero/reports/v2/reports.proto

package reportsconnect

import (
	context "context"
	errors "errors"
	http "net/http"
	v2 "proto/hero/reports/v2"
	strings "strings"

	connect "connectrpc.com/connect"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// ReportServiceName is the fully-qualified name of the ReportService service.
	ReportServiceName = "hero.reports.v2.ReportService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// ReportServiceCreateReportSectionProcedure is the fully-qualified name of the ReportService's
	// CreateReportSection RPC.
	ReportServiceCreateReportSectionProcedure = "/hero.reports.v2.ReportService/CreateReportSection"
	// ReportServiceGetReportSectionProcedure is the fully-qualified name of the ReportService's
	// GetReportSection RPC.
	ReportServiceGetReportSectionProcedure = "/hero.reports.v2.ReportService/GetReportSection"
	// ReportServiceUpdateReportSectionProcedure is the fully-qualified name of the ReportService's
	// UpdateReportSection RPC.
	ReportServiceUpdateReportSectionProcedure = "/hero.reports.v2.ReportService/UpdateReportSection"
	// ReportServiceDeleteReportSectionProcedure is the fully-qualified name of the ReportService's
	// DeleteReportSection RPC.
	ReportServiceDeleteReportSectionProcedure = "/hero.reports.v2.ReportService/DeleteReportSection"
	// ReportServiceListReportSectionsProcedure is the fully-qualified name of the ReportService's
	// ListReportSections RPC.
	ReportServiceListReportSectionsProcedure = "/hero.reports.v2.ReportService/ListReportSections"
	// ReportServiceCreateReportProcedure is the fully-qualified name of the ReportService's
	// CreateReport RPC.
	ReportServiceCreateReportProcedure = "/hero.reports.v2.ReportService/CreateReport"
	// ReportServiceGetReportProcedure is the fully-qualified name of the ReportService's GetReport RPC.
	ReportServiceGetReportProcedure = "/hero.reports.v2.ReportService/GetReport"
	// ReportServiceUpdateReportProcedure is the fully-qualified name of the ReportService's
	// UpdateReport RPC.
	ReportServiceUpdateReportProcedure = "/hero.reports.v2.ReportService/UpdateReport"
	// ReportServiceUpdateReportStatusProcedure is the fully-qualified name of the ReportService's
	// UpdateReportStatus RPC.
	ReportServiceUpdateReportStatusProcedure = "/hero.reports.v2.ReportService/UpdateReportStatus"
	// ReportServiceListReportsProcedure is the fully-qualified name of the ReportService's ListReports
	// RPC.
	ReportServiceListReportsProcedure = "/hero.reports.v2.ReportService/ListReports"
	// ReportServiceBatchGetReportsProcedure is the fully-qualified name of the ReportService's
	// BatchGetReports RPC.
	ReportServiceBatchGetReportsProcedure = "/hero.reports.v2.ReportService/BatchGetReports"
	// ReportServiceDeleteReportProcedure is the fully-qualified name of the ReportService's
	// DeleteReport RPC.
	ReportServiceDeleteReportProcedure = "/hero.reports.v2.ReportService/DeleteReport"
	// ReportServiceListReportsBySituationIdProcedure is the fully-qualified name of the ReportService's
	// ListReportsBySituationId RPC.
	ReportServiceListReportsBySituationIdProcedure = "/hero.reports.v2.ReportService/ListReportsBySituationId"
	// ReportServiceListReportsByCaseIdProcedure is the fully-qualified name of the ReportService's
	// ListReportsByCaseId RPC.
	ReportServiceListReportsByCaseIdProcedure = "/hero.reports.v2.ReportService/ListReportsByCaseId"
	// ReportServiceAddCommentProcedure is the fully-qualified name of the ReportService's AddComment
	// RPC.
	ReportServiceAddCommentProcedure = "/hero.reports.v2.ReportService/AddComment"
	// ReportServiceGetCommentsProcedure is the fully-qualified name of the ReportService's GetComments
	// RPC.
	ReportServiceGetCommentsProcedure = "/hero.reports.v2.ReportService/GetComments"
	// ReportServiceUpdateCommentProcedure is the fully-qualified name of the ReportService's
	// UpdateComment RPC.
	ReportServiceUpdateCommentProcedure = "/hero.reports.v2.ReportService/UpdateComment"
	// ReportServiceDeleteCommentProcedure is the fully-qualified name of the ReportService's
	// DeleteComment RPC.
	ReportServiceDeleteCommentProcedure = "/hero.reports.v2.ReportService/DeleteComment"
	// ReportServiceResolveCommentProcedure is the fully-qualified name of the ReportService's
	// ResolveComment RPC.
	ReportServiceResolveCommentProcedure = "/hero.reports.v2.ReportService/ResolveComment"
	// ReportServiceSubmitForReviewProcedure is the fully-qualified name of the ReportService's
	// SubmitForReview RPC.
	ReportServiceSubmitForReviewProcedure = "/hero.reports.v2.ReportService/SubmitForReview"
	// ReportServiceAddReviewRoundProcedure is the fully-qualified name of the ReportService's
	// AddReviewRound RPC.
	ReportServiceAddReviewRoundProcedure = "/hero.reports.v2.ReportService/AddReviewRound"
	// ReportServiceGetReviewRoundProcedure is the fully-qualified name of the ReportService's
	// GetReviewRound RPC.
	ReportServiceGetReviewRoundProcedure = "/hero.reports.v2.ReportService/GetReviewRound"
	// ReportServiceUpdateReviewRoundProcedure is the fully-qualified name of the ReportService's
	// UpdateReviewRound RPC.
	ReportServiceUpdateReviewRoundProcedure = "/hero.reports.v2.ReportService/UpdateReviewRound"
	// ReportServiceDeleteReviewRoundProcedure is the fully-qualified name of the ReportService's
	// DeleteReviewRound RPC.
	ReportServiceDeleteReviewRoundProcedure = "/hero.reports.v2.ReportService/DeleteReviewRound"
	// ReportServiceApproveReviewRoundProcedure is the fully-qualified name of the ReportService's
	// ApproveReviewRound RPC.
	ReportServiceApproveReviewRoundProcedure = "/hero.reports.v2.ReportService/ApproveReviewRound"
	// ReportServiceRequestChangesProcedure is the fully-qualified name of the ReportService's
	// RequestChanges RPC.
	ReportServiceRequestChangesProcedure = "/hero.reports.v2.ReportService/RequestChanges"
	// ReportServiceListReviewRoundsForReportProcedure is the fully-qualified name of the
	// ReportService's ListReviewRoundsForReport RPC.
	ReportServiceListReviewRoundsForReportProcedure = "/hero.reports.v2.ReportService/ListReviewRoundsForReport"
	// ReportServiceGetEligibleReviewersProcedure is the fully-qualified name of the ReportService's
	// GetEligibleReviewers RPC.
	ReportServiceGetEligibleReviewersProcedure = "/hero.reports.v2.ReportService/GetEligibleReviewers"
	// ReportServiceUpdateAdditionalInfoJsonProcedure is the fully-qualified name of the ReportService's
	// UpdateAdditionalInfoJson RPC.
	ReportServiceUpdateAdditionalInfoJsonProcedure = "/hero.reports.v2.ReportService/UpdateAdditionalInfoJson"
	// ReportServiceGetAdditionalInfoProcedure is the fully-qualified name of the ReportService's
	// GetAdditionalInfo RPC.
	ReportServiceGetAdditionalInfoProcedure = "/hero.reports.v2.ReportService/GetAdditionalInfo"
	// ReportServiceGetReportVersionProcedure is the fully-qualified name of the ReportService's
	// GetReportVersion RPC.
	ReportServiceGetReportVersionProcedure = "/hero.reports.v2.ReportService/GetReportVersion"
	// ReportServiceListReportVersionsProcedure is the fully-qualified name of the ReportService's
	// ListReportVersions RPC.
	ReportServiceListReportVersionsProcedure = "/hero.reports.v2.ReportService/ListReportVersions"
	// ReportServiceSearchReportsProcedure is the fully-qualified name of the ReportService's
	// SearchReports RPC.
	ReportServiceSearchReportsProcedure = "/hero.reports.v2.ReportService/SearchReports"
	// ReportServiceCreateRelationProcedure is the fully-qualified name of the ReportService's
	// CreateRelation RPC.
	ReportServiceCreateRelationProcedure = "/hero.reports.v2.ReportService/CreateRelation"
	// ReportServiceGetRelationProcedure is the fully-qualified name of the ReportService's GetRelation
	// RPC.
	ReportServiceGetRelationProcedure = "/hero.reports.v2.ReportService/GetRelation"
	// ReportServiceUpdateRelationProcedure is the fully-qualified name of the ReportService's
	// UpdateRelation RPC.
	ReportServiceUpdateRelationProcedure = "/hero.reports.v2.ReportService/UpdateRelation"
	// ReportServiceDeleteRelationProcedure is the fully-qualified name of the ReportService's
	// DeleteRelation RPC.
	ReportServiceDeleteRelationProcedure = "/hero.reports.v2.ReportService/DeleteRelation"
	// ReportServiceListRelationsProcedure is the fully-qualified name of the ReportService's
	// ListRelations RPC.
	ReportServiceListRelationsProcedure = "/hero.reports.v2.ReportService/ListRelations"
)

// ReportServiceClient is a client for the hero.reports.v2.ReportService service.
type ReportServiceClient interface {
	// Section CRUD
	CreateReportSection(context.Context, *connect.Request[v2.CreateReportSectionRequest]) (*connect.Response[v2.ReportSection], error)
	GetReportSection(context.Context, *connect.Request[v2.GetReportSectionRequest]) (*connect.Response[v2.ReportSection], error)
	UpdateReportSection(context.Context, *connect.Request[v2.UpdateReportSectionRequest]) (*connect.Response[v2.ReportSection], error)
	DeleteReportSection(context.Context, *connect.Request[v2.DeleteReportSectionRequest]) (*connect.Response[emptypb.Empty], error)
	ListReportSections(context.Context, *connect.Request[v2.ListReportSectionsRequest]) (*connect.Response[v2.ListReportSectionsResponse], error)
	// Report CRUD & metadata
	CreateReport(context.Context, *connect.Request[v2.CreateReportRequest]) (*connect.Response[v2.CreateReportResponse], error)
	GetReport(context.Context, *connect.Request[v2.GetReportRequest]) (*connect.Response[v2.Report], error)
	UpdateReport(context.Context, *connect.Request[v2.UpdateReportRequest]) (*connect.Response[v2.Report], error)
	UpdateReportStatus(context.Context, *connect.Request[v2.UpdateReportStatusRequest]) (*connect.Response[v2.UpdateReportStatusResponse], error)
	ListReports(context.Context, *connect.Request[v2.ListReportsRequest]) (*connect.Response[v2.ListReportsResponse], error)
	BatchGetReports(context.Context, *connect.Request[v2.BatchGetReportsRequest]) (*connect.Response[v2.BatchGetReportsResponse], error)
	DeleteReport(context.Context, *connect.Request[v2.DeleteReportRequest]) (*connect.Response[emptypb.Empty], error)
	// Filtered list RPCs
	ListReportsBySituationId(context.Context, *connect.Request[v2.ListReportsBySituationIdRequest]) (*connect.Response[v2.ListReportsResponse], error)
	ListReportsByCaseId(context.Context, *connect.Request[v2.ListReportsByCaseIdRequest]) (*connect.Response[v2.ListReportsResponse], error)
	// Comments
	AddComment(context.Context, *connect.Request[v2.AddCommentRequest]) (*connect.Response[v2.Comment], error)
	GetComments(context.Context, *connect.Request[v2.GetCommentsRequest]) (*connect.Response[v2.GetCommentsResponse], error)
	UpdateComment(context.Context, *connect.Request[v2.UpdateCommentRequest]) (*connect.Response[v2.Comment], error)
	DeleteComment(context.Context, *connect.Request[v2.DeleteCommentRequest]) (*connect.Response[emptypb.Empty], error)
	ResolveComment(context.Context, *connect.Request[v2.ResolveCommentRequest]) (*connect.Response[v2.Comment], error)
	// Review workflow
	SubmitForReview(context.Context, *connect.Request[v2.SubmitForReviewRequest]) (*connect.Response[v2.Report], error)
	AddReviewRound(context.Context, *connect.Request[v2.AddReviewRoundRequest]) (*connect.Response[v2.ReviewRound], error)
	GetReviewRound(context.Context, *connect.Request[v2.GetReviewRoundRequest]) (*connect.Response[v2.ReviewRound], error)
	UpdateReviewRound(context.Context, *connect.Request[v2.UpdateReviewRoundRequest]) (*connect.Response[v2.ReviewRound], error)
	DeleteReviewRound(context.Context, *connect.Request[v2.DeleteReviewRoundRequest]) (*connect.Response[emptypb.Empty], error)
	ApproveReviewRound(context.Context, *connect.Request[v2.ApproveReviewRoundRequest]) (*connect.Response[v2.ReviewRound], error)
	RequestChanges(context.Context, *connect.Request[v2.RequestChangesRequest]) (*connect.Response[v2.ReviewRound], error)
	ListReviewRoundsForReport(context.Context, *connect.Request[v2.ListReviewRoundsForReportRequest]) (*connect.Response[v2.ListReviewRoundsForReportResponse], error)
	GetEligibleReviewers(context.Context, *connect.Request[v2.GetEligibleReviewersRequest]) (*connect.Response[v2.GetEligibleReviewersResponse], error)
	// JSON metadata
	UpdateAdditionalInfoJson(context.Context, *connect.Request[v2.UpdateAdditionalInfoJsonRequest]) (*connect.Response[v2.UpdateAdditionalInfoJsonResponse], error)
	GetAdditionalInfo(context.Context, *connect.Request[v2.GetAdditionalInfoRequest]) (*connect.Response[v2.GetAdditionalInfoResponse], error)
	// Versioning
	GetReportVersion(context.Context, *connect.Request[v2.GetReportVersionRequest]) (*connect.Response[v2.ReportSnapshot], error)
	ListReportVersions(context.Context, *connect.Request[v2.ListReportVersionsRequest]) (*connect.Response[v2.ListReportVersionsResponse], error)
	// Add the new search RPC
	SearchReports(context.Context, *connect.Request[v2.SearchReportsRequest]) (*connect.Response[v2.SearchReportsResponse], error)
	// Relation CRUD
	CreateRelation(context.Context, *connect.Request[v2.CreateRelationRequest]) (*connect.Response[v2.Relation], error)
	GetRelation(context.Context, *connect.Request[v2.GetRelationRequest]) (*connect.Response[v2.Relation], error)
	UpdateRelation(context.Context, *connect.Request[v2.UpdateRelationRequest]) (*connect.Response[v2.Relation], error)
	DeleteRelation(context.Context, *connect.Request[v2.DeleteRelationRequest]) (*connect.Response[emptypb.Empty], error)
	ListRelations(context.Context, *connect.Request[v2.ListRelationsRequest]) (*connect.Response[v2.ListRelationsResponse], error)
}

// NewReportServiceClient constructs a client for the hero.reports.v2.ReportService service. By
// default, it uses the Connect protocol with the binary Protobuf Codec, asks for gzipped responses,
// and sends uncompressed requests. To use the gRPC or gRPC-Web protocols, supply the
// connect.WithGRPC() or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewReportServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) ReportServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	reportServiceMethods := v2.File_hero_reports_v2_reports_proto.Services().ByName("ReportService").Methods()
	return &reportServiceClient{
		createReportSection: connect.NewClient[v2.CreateReportSectionRequest, v2.ReportSection](
			httpClient,
			baseURL+ReportServiceCreateReportSectionProcedure,
			connect.WithSchema(reportServiceMethods.ByName("CreateReportSection")),
			connect.WithClientOptions(opts...),
		),
		getReportSection: connect.NewClient[v2.GetReportSectionRequest, v2.ReportSection](
			httpClient,
			baseURL+ReportServiceGetReportSectionProcedure,
			connect.WithSchema(reportServiceMethods.ByName("GetReportSection")),
			connect.WithClientOptions(opts...),
		),
		updateReportSection: connect.NewClient[v2.UpdateReportSectionRequest, v2.ReportSection](
			httpClient,
			baseURL+ReportServiceUpdateReportSectionProcedure,
			connect.WithSchema(reportServiceMethods.ByName("UpdateReportSection")),
			connect.WithClientOptions(opts...),
		),
		deleteReportSection: connect.NewClient[v2.DeleteReportSectionRequest, emptypb.Empty](
			httpClient,
			baseURL+ReportServiceDeleteReportSectionProcedure,
			connect.WithSchema(reportServiceMethods.ByName("DeleteReportSection")),
			connect.WithClientOptions(opts...),
		),
		listReportSections: connect.NewClient[v2.ListReportSectionsRequest, v2.ListReportSectionsResponse](
			httpClient,
			baseURL+ReportServiceListReportSectionsProcedure,
			connect.WithSchema(reportServiceMethods.ByName("ListReportSections")),
			connect.WithClientOptions(opts...),
		),
		createReport: connect.NewClient[v2.CreateReportRequest, v2.CreateReportResponse](
			httpClient,
			baseURL+ReportServiceCreateReportProcedure,
			connect.WithSchema(reportServiceMethods.ByName("CreateReport")),
			connect.WithClientOptions(opts...),
		),
		getReport: connect.NewClient[v2.GetReportRequest, v2.Report](
			httpClient,
			baseURL+ReportServiceGetReportProcedure,
			connect.WithSchema(reportServiceMethods.ByName("GetReport")),
			connect.WithClientOptions(opts...),
		),
		updateReport: connect.NewClient[v2.UpdateReportRequest, v2.Report](
			httpClient,
			baseURL+ReportServiceUpdateReportProcedure,
			connect.WithSchema(reportServiceMethods.ByName("UpdateReport")),
			connect.WithClientOptions(opts...),
		),
		updateReportStatus: connect.NewClient[v2.UpdateReportStatusRequest, v2.UpdateReportStatusResponse](
			httpClient,
			baseURL+ReportServiceUpdateReportStatusProcedure,
			connect.WithSchema(reportServiceMethods.ByName("UpdateReportStatus")),
			connect.WithClientOptions(opts...),
		),
		listReports: connect.NewClient[v2.ListReportsRequest, v2.ListReportsResponse](
			httpClient,
			baseURL+ReportServiceListReportsProcedure,
			connect.WithSchema(reportServiceMethods.ByName("ListReports")),
			connect.WithClientOptions(opts...),
		),
		batchGetReports: connect.NewClient[v2.BatchGetReportsRequest, v2.BatchGetReportsResponse](
			httpClient,
			baseURL+ReportServiceBatchGetReportsProcedure,
			connect.WithSchema(reportServiceMethods.ByName("BatchGetReports")),
			connect.WithClientOptions(opts...),
		),
		deleteReport: connect.NewClient[v2.DeleteReportRequest, emptypb.Empty](
			httpClient,
			baseURL+ReportServiceDeleteReportProcedure,
			connect.WithSchema(reportServiceMethods.ByName("DeleteReport")),
			connect.WithClientOptions(opts...),
		),
		listReportsBySituationId: connect.NewClient[v2.ListReportsBySituationIdRequest, v2.ListReportsResponse](
			httpClient,
			baseURL+ReportServiceListReportsBySituationIdProcedure,
			connect.WithSchema(reportServiceMethods.ByName("ListReportsBySituationId")),
			connect.WithClientOptions(opts...),
		),
		listReportsByCaseId: connect.NewClient[v2.ListReportsByCaseIdRequest, v2.ListReportsResponse](
			httpClient,
			baseURL+ReportServiceListReportsByCaseIdProcedure,
			connect.WithSchema(reportServiceMethods.ByName("ListReportsByCaseId")),
			connect.WithClientOptions(opts...),
		),
		addComment: connect.NewClient[v2.AddCommentRequest, v2.Comment](
			httpClient,
			baseURL+ReportServiceAddCommentProcedure,
			connect.WithSchema(reportServiceMethods.ByName("AddComment")),
			connect.WithClientOptions(opts...),
		),
		getComments: connect.NewClient[v2.GetCommentsRequest, v2.GetCommentsResponse](
			httpClient,
			baseURL+ReportServiceGetCommentsProcedure,
			connect.WithSchema(reportServiceMethods.ByName("GetComments")),
			connect.WithClientOptions(opts...),
		),
		updateComment: connect.NewClient[v2.UpdateCommentRequest, v2.Comment](
			httpClient,
			baseURL+ReportServiceUpdateCommentProcedure,
			connect.WithSchema(reportServiceMethods.ByName("UpdateComment")),
			connect.WithClientOptions(opts...),
		),
		deleteComment: connect.NewClient[v2.DeleteCommentRequest, emptypb.Empty](
			httpClient,
			baseURL+ReportServiceDeleteCommentProcedure,
			connect.WithSchema(reportServiceMethods.ByName("DeleteComment")),
			connect.WithClientOptions(opts...),
		),
		resolveComment: connect.NewClient[v2.ResolveCommentRequest, v2.Comment](
			httpClient,
			baseURL+ReportServiceResolveCommentProcedure,
			connect.WithSchema(reportServiceMethods.ByName("ResolveComment")),
			connect.WithClientOptions(opts...),
		),
		submitForReview: connect.NewClient[v2.SubmitForReviewRequest, v2.Report](
			httpClient,
			baseURL+ReportServiceSubmitForReviewProcedure,
			connect.WithSchema(reportServiceMethods.ByName("SubmitForReview")),
			connect.WithClientOptions(opts...),
		),
		addReviewRound: connect.NewClient[v2.AddReviewRoundRequest, v2.ReviewRound](
			httpClient,
			baseURL+ReportServiceAddReviewRoundProcedure,
			connect.WithSchema(reportServiceMethods.ByName("AddReviewRound")),
			connect.WithClientOptions(opts...),
		),
		getReviewRound: connect.NewClient[v2.GetReviewRoundRequest, v2.ReviewRound](
			httpClient,
			baseURL+ReportServiceGetReviewRoundProcedure,
			connect.WithSchema(reportServiceMethods.ByName("GetReviewRound")),
			connect.WithClientOptions(opts...),
		),
		updateReviewRound: connect.NewClient[v2.UpdateReviewRoundRequest, v2.ReviewRound](
			httpClient,
			baseURL+ReportServiceUpdateReviewRoundProcedure,
			connect.WithSchema(reportServiceMethods.ByName("UpdateReviewRound")),
			connect.WithClientOptions(opts...),
		),
		deleteReviewRound: connect.NewClient[v2.DeleteReviewRoundRequest, emptypb.Empty](
			httpClient,
			baseURL+ReportServiceDeleteReviewRoundProcedure,
			connect.WithSchema(reportServiceMethods.ByName("DeleteReviewRound")),
			connect.WithClientOptions(opts...),
		),
		approveReviewRound: connect.NewClient[v2.ApproveReviewRoundRequest, v2.ReviewRound](
			httpClient,
			baseURL+ReportServiceApproveReviewRoundProcedure,
			connect.WithSchema(reportServiceMethods.ByName("ApproveReviewRound")),
			connect.WithClientOptions(opts...),
		),
		requestChanges: connect.NewClient[v2.RequestChangesRequest, v2.ReviewRound](
			httpClient,
			baseURL+ReportServiceRequestChangesProcedure,
			connect.WithSchema(reportServiceMethods.ByName("RequestChanges")),
			connect.WithClientOptions(opts...),
		),
		listReviewRoundsForReport: connect.NewClient[v2.ListReviewRoundsForReportRequest, v2.ListReviewRoundsForReportResponse](
			httpClient,
			baseURL+ReportServiceListReviewRoundsForReportProcedure,
			connect.WithSchema(reportServiceMethods.ByName("ListReviewRoundsForReport")),
			connect.WithClientOptions(opts...),
		),
		getEligibleReviewers: connect.NewClient[v2.GetEligibleReviewersRequest, v2.GetEligibleReviewersResponse](
			httpClient,
			baseURL+ReportServiceGetEligibleReviewersProcedure,
			connect.WithSchema(reportServiceMethods.ByName("GetEligibleReviewers")),
			connect.WithClientOptions(opts...),
		),
		updateAdditionalInfoJson: connect.NewClient[v2.UpdateAdditionalInfoJsonRequest, v2.UpdateAdditionalInfoJsonResponse](
			httpClient,
			baseURL+ReportServiceUpdateAdditionalInfoJsonProcedure,
			connect.WithSchema(reportServiceMethods.ByName("UpdateAdditionalInfoJson")),
			connect.WithClientOptions(opts...),
		),
		getAdditionalInfo: connect.NewClient[v2.GetAdditionalInfoRequest, v2.GetAdditionalInfoResponse](
			httpClient,
			baseURL+ReportServiceGetAdditionalInfoProcedure,
			connect.WithSchema(reportServiceMethods.ByName("GetAdditionalInfo")),
			connect.WithClientOptions(opts...),
		),
		getReportVersion: connect.NewClient[v2.GetReportVersionRequest, v2.ReportSnapshot](
			httpClient,
			baseURL+ReportServiceGetReportVersionProcedure,
			connect.WithSchema(reportServiceMethods.ByName("GetReportVersion")),
			connect.WithClientOptions(opts...),
		),
		listReportVersions: connect.NewClient[v2.ListReportVersionsRequest, v2.ListReportVersionsResponse](
			httpClient,
			baseURL+ReportServiceListReportVersionsProcedure,
			connect.WithSchema(reportServiceMethods.ByName("ListReportVersions")),
			connect.WithClientOptions(opts...),
		),
		searchReports: connect.NewClient[v2.SearchReportsRequest, v2.SearchReportsResponse](
			httpClient,
			baseURL+ReportServiceSearchReportsProcedure,
			connect.WithSchema(reportServiceMethods.ByName("SearchReports")),
			connect.WithClientOptions(opts...),
		),
		createRelation: connect.NewClient[v2.CreateRelationRequest, v2.Relation](
			httpClient,
			baseURL+ReportServiceCreateRelationProcedure,
			connect.WithSchema(reportServiceMethods.ByName("CreateRelation")),
			connect.WithClientOptions(opts...),
		),
		getRelation: connect.NewClient[v2.GetRelationRequest, v2.Relation](
			httpClient,
			baseURL+ReportServiceGetRelationProcedure,
			connect.WithSchema(reportServiceMethods.ByName("GetRelation")),
			connect.WithClientOptions(opts...),
		),
		updateRelation: connect.NewClient[v2.UpdateRelationRequest, v2.Relation](
			httpClient,
			baseURL+ReportServiceUpdateRelationProcedure,
			connect.WithSchema(reportServiceMethods.ByName("UpdateRelation")),
			connect.WithClientOptions(opts...),
		),
		deleteRelation: connect.NewClient[v2.DeleteRelationRequest, emptypb.Empty](
			httpClient,
			baseURL+ReportServiceDeleteRelationProcedure,
			connect.WithSchema(reportServiceMethods.ByName("DeleteRelation")),
			connect.WithClientOptions(opts...),
		),
		listRelations: connect.NewClient[v2.ListRelationsRequest, v2.ListRelationsResponse](
			httpClient,
			baseURL+ReportServiceListRelationsProcedure,
			connect.WithSchema(reportServiceMethods.ByName("ListRelations")),
			connect.WithClientOptions(opts...),
		),
	}
}

// reportServiceClient implements ReportServiceClient.
type reportServiceClient struct {
	createReportSection       *connect.Client[v2.CreateReportSectionRequest, v2.ReportSection]
	getReportSection          *connect.Client[v2.GetReportSectionRequest, v2.ReportSection]
	updateReportSection       *connect.Client[v2.UpdateReportSectionRequest, v2.ReportSection]
	deleteReportSection       *connect.Client[v2.DeleteReportSectionRequest, emptypb.Empty]
	listReportSections        *connect.Client[v2.ListReportSectionsRequest, v2.ListReportSectionsResponse]
	createReport              *connect.Client[v2.CreateReportRequest, v2.CreateReportResponse]
	getReport                 *connect.Client[v2.GetReportRequest, v2.Report]
	updateReport              *connect.Client[v2.UpdateReportRequest, v2.Report]
	updateReportStatus        *connect.Client[v2.UpdateReportStatusRequest, v2.UpdateReportStatusResponse]
	listReports               *connect.Client[v2.ListReportsRequest, v2.ListReportsResponse]
	batchGetReports           *connect.Client[v2.BatchGetReportsRequest, v2.BatchGetReportsResponse]
	deleteReport              *connect.Client[v2.DeleteReportRequest, emptypb.Empty]
	listReportsBySituationId  *connect.Client[v2.ListReportsBySituationIdRequest, v2.ListReportsResponse]
	listReportsByCaseId       *connect.Client[v2.ListReportsByCaseIdRequest, v2.ListReportsResponse]
	addComment                *connect.Client[v2.AddCommentRequest, v2.Comment]
	getComments               *connect.Client[v2.GetCommentsRequest, v2.GetCommentsResponse]
	updateComment             *connect.Client[v2.UpdateCommentRequest, v2.Comment]
	deleteComment             *connect.Client[v2.DeleteCommentRequest, emptypb.Empty]
	resolveComment            *connect.Client[v2.ResolveCommentRequest, v2.Comment]
	submitForReview           *connect.Client[v2.SubmitForReviewRequest, v2.Report]
	addReviewRound            *connect.Client[v2.AddReviewRoundRequest, v2.ReviewRound]
	getReviewRound            *connect.Client[v2.GetReviewRoundRequest, v2.ReviewRound]
	updateReviewRound         *connect.Client[v2.UpdateReviewRoundRequest, v2.ReviewRound]
	deleteReviewRound         *connect.Client[v2.DeleteReviewRoundRequest, emptypb.Empty]
	approveReviewRound        *connect.Client[v2.ApproveReviewRoundRequest, v2.ReviewRound]
	requestChanges            *connect.Client[v2.RequestChangesRequest, v2.ReviewRound]
	listReviewRoundsForReport *connect.Client[v2.ListReviewRoundsForReportRequest, v2.ListReviewRoundsForReportResponse]
	getEligibleReviewers      *connect.Client[v2.GetEligibleReviewersRequest, v2.GetEligibleReviewersResponse]
	updateAdditionalInfoJson  *connect.Client[v2.UpdateAdditionalInfoJsonRequest, v2.UpdateAdditionalInfoJsonResponse]
	getAdditionalInfo         *connect.Client[v2.GetAdditionalInfoRequest, v2.GetAdditionalInfoResponse]
	getReportVersion          *connect.Client[v2.GetReportVersionRequest, v2.ReportSnapshot]
	listReportVersions        *connect.Client[v2.ListReportVersionsRequest, v2.ListReportVersionsResponse]
	searchReports             *connect.Client[v2.SearchReportsRequest, v2.SearchReportsResponse]
	createRelation            *connect.Client[v2.CreateRelationRequest, v2.Relation]
	getRelation               *connect.Client[v2.GetRelationRequest, v2.Relation]
	updateRelation            *connect.Client[v2.UpdateRelationRequest, v2.Relation]
	deleteRelation            *connect.Client[v2.DeleteRelationRequest, emptypb.Empty]
	listRelations             *connect.Client[v2.ListRelationsRequest, v2.ListRelationsResponse]
}

// CreateReportSection calls hero.reports.v2.ReportService.CreateReportSection.
func (c *reportServiceClient) CreateReportSection(ctx context.Context, req *connect.Request[v2.CreateReportSectionRequest]) (*connect.Response[v2.ReportSection], error) {
	return c.createReportSection.CallUnary(ctx, req)
}

// GetReportSection calls hero.reports.v2.ReportService.GetReportSection.
func (c *reportServiceClient) GetReportSection(ctx context.Context, req *connect.Request[v2.GetReportSectionRequest]) (*connect.Response[v2.ReportSection], error) {
	return c.getReportSection.CallUnary(ctx, req)
}

// UpdateReportSection calls hero.reports.v2.ReportService.UpdateReportSection.
func (c *reportServiceClient) UpdateReportSection(ctx context.Context, req *connect.Request[v2.UpdateReportSectionRequest]) (*connect.Response[v2.ReportSection], error) {
	return c.updateReportSection.CallUnary(ctx, req)
}

// DeleteReportSection calls hero.reports.v2.ReportService.DeleteReportSection.
func (c *reportServiceClient) DeleteReportSection(ctx context.Context, req *connect.Request[v2.DeleteReportSectionRequest]) (*connect.Response[emptypb.Empty], error) {
	return c.deleteReportSection.CallUnary(ctx, req)
}

// ListReportSections calls hero.reports.v2.ReportService.ListReportSections.
func (c *reportServiceClient) ListReportSections(ctx context.Context, req *connect.Request[v2.ListReportSectionsRequest]) (*connect.Response[v2.ListReportSectionsResponse], error) {
	return c.listReportSections.CallUnary(ctx, req)
}

// CreateReport calls hero.reports.v2.ReportService.CreateReport.
func (c *reportServiceClient) CreateReport(ctx context.Context, req *connect.Request[v2.CreateReportRequest]) (*connect.Response[v2.CreateReportResponse], error) {
	return c.createReport.CallUnary(ctx, req)
}

// GetReport calls hero.reports.v2.ReportService.GetReport.
func (c *reportServiceClient) GetReport(ctx context.Context, req *connect.Request[v2.GetReportRequest]) (*connect.Response[v2.Report], error) {
	return c.getReport.CallUnary(ctx, req)
}

// UpdateReport calls hero.reports.v2.ReportService.UpdateReport.
func (c *reportServiceClient) UpdateReport(ctx context.Context, req *connect.Request[v2.UpdateReportRequest]) (*connect.Response[v2.Report], error) {
	return c.updateReport.CallUnary(ctx, req)
}

// UpdateReportStatus calls hero.reports.v2.ReportService.UpdateReportStatus.
func (c *reportServiceClient) UpdateReportStatus(ctx context.Context, req *connect.Request[v2.UpdateReportStatusRequest]) (*connect.Response[v2.UpdateReportStatusResponse], error) {
	return c.updateReportStatus.CallUnary(ctx, req)
}

// ListReports calls hero.reports.v2.ReportService.ListReports.
func (c *reportServiceClient) ListReports(ctx context.Context, req *connect.Request[v2.ListReportsRequest]) (*connect.Response[v2.ListReportsResponse], error) {
	return c.listReports.CallUnary(ctx, req)
}

// BatchGetReports calls hero.reports.v2.ReportService.BatchGetReports.
func (c *reportServiceClient) BatchGetReports(ctx context.Context, req *connect.Request[v2.BatchGetReportsRequest]) (*connect.Response[v2.BatchGetReportsResponse], error) {
	return c.batchGetReports.CallUnary(ctx, req)
}

// DeleteReport calls hero.reports.v2.ReportService.DeleteReport.
func (c *reportServiceClient) DeleteReport(ctx context.Context, req *connect.Request[v2.DeleteReportRequest]) (*connect.Response[emptypb.Empty], error) {
	return c.deleteReport.CallUnary(ctx, req)
}

// ListReportsBySituationId calls hero.reports.v2.ReportService.ListReportsBySituationId.
func (c *reportServiceClient) ListReportsBySituationId(ctx context.Context, req *connect.Request[v2.ListReportsBySituationIdRequest]) (*connect.Response[v2.ListReportsResponse], error) {
	return c.listReportsBySituationId.CallUnary(ctx, req)
}

// ListReportsByCaseId calls hero.reports.v2.ReportService.ListReportsByCaseId.
func (c *reportServiceClient) ListReportsByCaseId(ctx context.Context, req *connect.Request[v2.ListReportsByCaseIdRequest]) (*connect.Response[v2.ListReportsResponse], error) {
	return c.listReportsByCaseId.CallUnary(ctx, req)
}

// AddComment calls hero.reports.v2.ReportService.AddComment.
func (c *reportServiceClient) AddComment(ctx context.Context, req *connect.Request[v2.AddCommentRequest]) (*connect.Response[v2.Comment], error) {
	return c.addComment.CallUnary(ctx, req)
}

// GetComments calls hero.reports.v2.ReportService.GetComments.
func (c *reportServiceClient) GetComments(ctx context.Context, req *connect.Request[v2.GetCommentsRequest]) (*connect.Response[v2.GetCommentsResponse], error) {
	return c.getComments.CallUnary(ctx, req)
}

// UpdateComment calls hero.reports.v2.ReportService.UpdateComment.
func (c *reportServiceClient) UpdateComment(ctx context.Context, req *connect.Request[v2.UpdateCommentRequest]) (*connect.Response[v2.Comment], error) {
	return c.updateComment.CallUnary(ctx, req)
}

// DeleteComment calls hero.reports.v2.ReportService.DeleteComment.
func (c *reportServiceClient) DeleteComment(ctx context.Context, req *connect.Request[v2.DeleteCommentRequest]) (*connect.Response[emptypb.Empty], error) {
	return c.deleteComment.CallUnary(ctx, req)
}

// ResolveComment calls hero.reports.v2.ReportService.ResolveComment.
func (c *reportServiceClient) ResolveComment(ctx context.Context, req *connect.Request[v2.ResolveCommentRequest]) (*connect.Response[v2.Comment], error) {
	return c.resolveComment.CallUnary(ctx, req)
}

// SubmitForReview calls hero.reports.v2.ReportService.SubmitForReview.
func (c *reportServiceClient) SubmitForReview(ctx context.Context, req *connect.Request[v2.SubmitForReviewRequest]) (*connect.Response[v2.Report], error) {
	return c.submitForReview.CallUnary(ctx, req)
}

// AddReviewRound calls hero.reports.v2.ReportService.AddReviewRound.
func (c *reportServiceClient) AddReviewRound(ctx context.Context, req *connect.Request[v2.AddReviewRoundRequest]) (*connect.Response[v2.ReviewRound], error) {
	return c.addReviewRound.CallUnary(ctx, req)
}

// GetReviewRound calls hero.reports.v2.ReportService.GetReviewRound.
func (c *reportServiceClient) GetReviewRound(ctx context.Context, req *connect.Request[v2.GetReviewRoundRequest]) (*connect.Response[v2.ReviewRound], error) {
	return c.getReviewRound.CallUnary(ctx, req)
}

// UpdateReviewRound calls hero.reports.v2.ReportService.UpdateReviewRound.
func (c *reportServiceClient) UpdateReviewRound(ctx context.Context, req *connect.Request[v2.UpdateReviewRoundRequest]) (*connect.Response[v2.ReviewRound], error) {
	return c.updateReviewRound.CallUnary(ctx, req)
}

// DeleteReviewRound calls hero.reports.v2.ReportService.DeleteReviewRound.
func (c *reportServiceClient) DeleteReviewRound(ctx context.Context, req *connect.Request[v2.DeleteReviewRoundRequest]) (*connect.Response[emptypb.Empty], error) {
	return c.deleteReviewRound.CallUnary(ctx, req)
}

// ApproveReviewRound calls hero.reports.v2.ReportService.ApproveReviewRound.
func (c *reportServiceClient) ApproveReviewRound(ctx context.Context, req *connect.Request[v2.ApproveReviewRoundRequest]) (*connect.Response[v2.ReviewRound], error) {
	return c.approveReviewRound.CallUnary(ctx, req)
}

// RequestChanges calls hero.reports.v2.ReportService.RequestChanges.
func (c *reportServiceClient) RequestChanges(ctx context.Context, req *connect.Request[v2.RequestChangesRequest]) (*connect.Response[v2.ReviewRound], error) {
	return c.requestChanges.CallUnary(ctx, req)
}

// ListReviewRoundsForReport calls hero.reports.v2.ReportService.ListReviewRoundsForReport.
func (c *reportServiceClient) ListReviewRoundsForReport(ctx context.Context, req *connect.Request[v2.ListReviewRoundsForReportRequest]) (*connect.Response[v2.ListReviewRoundsForReportResponse], error) {
	return c.listReviewRoundsForReport.CallUnary(ctx, req)
}

// GetEligibleReviewers calls hero.reports.v2.ReportService.GetEligibleReviewers.
func (c *reportServiceClient) GetEligibleReviewers(ctx context.Context, req *connect.Request[v2.GetEligibleReviewersRequest]) (*connect.Response[v2.GetEligibleReviewersResponse], error) {
	return c.getEligibleReviewers.CallUnary(ctx, req)
}

// UpdateAdditionalInfoJson calls hero.reports.v2.ReportService.UpdateAdditionalInfoJson.
func (c *reportServiceClient) UpdateAdditionalInfoJson(ctx context.Context, req *connect.Request[v2.UpdateAdditionalInfoJsonRequest]) (*connect.Response[v2.UpdateAdditionalInfoJsonResponse], error) {
	return c.updateAdditionalInfoJson.CallUnary(ctx, req)
}

// GetAdditionalInfo calls hero.reports.v2.ReportService.GetAdditionalInfo.
func (c *reportServiceClient) GetAdditionalInfo(ctx context.Context, req *connect.Request[v2.GetAdditionalInfoRequest]) (*connect.Response[v2.GetAdditionalInfoResponse], error) {
	return c.getAdditionalInfo.CallUnary(ctx, req)
}

// GetReportVersion calls hero.reports.v2.ReportService.GetReportVersion.
func (c *reportServiceClient) GetReportVersion(ctx context.Context, req *connect.Request[v2.GetReportVersionRequest]) (*connect.Response[v2.ReportSnapshot], error) {
	return c.getReportVersion.CallUnary(ctx, req)
}

// ListReportVersions calls hero.reports.v2.ReportService.ListReportVersions.
func (c *reportServiceClient) ListReportVersions(ctx context.Context, req *connect.Request[v2.ListReportVersionsRequest]) (*connect.Response[v2.ListReportVersionsResponse], error) {
	return c.listReportVersions.CallUnary(ctx, req)
}

// SearchReports calls hero.reports.v2.ReportService.SearchReports.
func (c *reportServiceClient) SearchReports(ctx context.Context, req *connect.Request[v2.SearchReportsRequest]) (*connect.Response[v2.SearchReportsResponse], error) {
	return c.searchReports.CallUnary(ctx, req)
}

// CreateRelation calls hero.reports.v2.ReportService.CreateRelation.
func (c *reportServiceClient) CreateRelation(ctx context.Context, req *connect.Request[v2.CreateRelationRequest]) (*connect.Response[v2.Relation], error) {
	return c.createRelation.CallUnary(ctx, req)
}

// GetRelation calls hero.reports.v2.ReportService.GetRelation.
func (c *reportServiceClient) GetRelation(ctx context.Context, req *connect.Request[v2.GetRelationRequest]) (*connect.Response[v2.Relation], error) {
	return c.getRelation.CallUnary(ctx, req)
}

// UpdateRelation calls hero.reports.v2.ReportService.UpdateRelation.
func (c *reportServiceClient) UpdateRelation(ctx context.Context, req *connect.Request[v2.UpdateRelationRequest]) (*connect.Response[v2.Relation], error) {
	return c.updateRelation.CallUnary(ctx, req)
}

// DeleteRelation calls hero.reports.v2.ReportService.DeleteRelation.
func (c *reportServiceClient) DeleteRelation(ctx context.Context, req *connect.Request[v2.DeleteRelationRequest]) (*connect.Response[emptypb.Empty], error) {
	return c.deleteRelation.CallUnary(ctx, req)
}

// ListRelations calls hero.reports.v2.ReportService.ListRelations.
func (c *reportServiceClient) ListRelations(ctx context.Context, req *connect.Request[v2.ListRelationsRequest]) (*connect.Response[v2.ListRelationsResponse], error) {
	return c.listRelations.CallUnary(ctx, req)
}

// ReportServiceHandler is an implementation of the hero.reports.v2.ReportService service.
type ReportServiceHandler interface {
	// Section CRUD
	CreateReportSection(context.Context, *connect.Request[v2.CreateReportSectionRequest]) (*connect.Response[v2.ReportSection], error)
	GetReportSection(context.Context, *connect.Request[v2.GetReportSectionRequest]) (*connect.Response[v2.ReportSection], error)
	UpdateReportSection(context.Context, *connect.Request[v2.UpdateReportSectionRequest]) (*connect.Response[v2.ReportSection], error)
	DeleteReportSection(context.Context, *connect.Request[v2.DeleteReportSectionRequest]) (*connect.Response[emptypb.Empty], error)
	ListReportSections(context.Context, *connect.Request[v2.ListReportSectionsRequest]) (*connect.Response[v2.ListReportSectionsResponse], error)
	// Report CRUD & metadata
	CreateReport(context.Context, *connect.Request[v2.CreateReportRequest]) (*connect.Response[v2.CreateReportResponse], error)
	GetReport(context.Context, *connect.Request[v2.GetReportRequest]) (*connect.Response[v2.Report], error)
	UpdateReport(context.Context, *connect.Request[v2.UpdateReportRequest]) (*connect.Response[v2.Report], error)
	UpdateReportStatus(context.Context, *connect.Request[v2.UpdateReportStatusRequest]) (*connect.Response[v2.UpdateReportStatusResponse], error)
	ListReports(context.Context, *connect.Request[v2.ListReportsRequest]) (*connect.Response[v2.ListReportsResponse], error)
	BatchGetReports(context.Context, *connect.Request[v2.BatchGetReportsRequest]) (*connect.Response[v2.BatchGetReportsResponse], error)
	DeleteReport(context.Context, *connect.Request[v2.DeleteReportRequest]) (*connect.Response[emptypb.Empty], error)
	// Filtered list RPCs
	ListReportsBySituationId(context.Context, *connect.Request[v2.ListReportsBySituationIdRequest]) (*connect.Response[v2.ListReportsResponse], error)
	ListReportsByCaseId(context.Context, *connect.Request[v2.ListReportsByCaseIdRequest]) (*connect.Response[v2.ListReportsResponse], error)
	// Comments
	AddComment(context.Context, *connect.Request[v2.AddCommentRequest]) (*connect.Response[v2.Comment], error)
	GetComments(context.Context, *connect.Request[v2.GetCommentsRequest]) (*connect.Response[v2.GetCommentsResponse], error)
	UpdateComment(context.Context, *connect.Request[v2.UpdateCommentRequest]) (*connect.Response[v2.Comment], error)
	DeleteComment(context.Context, *connect.Request[v2.DeleteCommentRequest]) (*connect.Response[emptypb.Empty], error)
	ResolveComment(context.Context, *connect.Request[v2.ResolveCommentRequest]) (*connect.Response[v2.Comment], error)
	// Review workflow
	SubmitForReview(context.Context, *connect.Request[v2.SubmitForReviewRequest]) (*connect.Response[v2.Report], error)
	AddReviewRound(context.Context, *connect.Request[v2.AddReviewRoundRequest]) (*connect.Response[v2.ReviewRound], error)
	GetReviewRound(context.Context, *connect.Request[v2.GetReviewRoundRequest]) (*connect.Response[v2.ReviewRound], error)
	UpdateReviewRound(context.Context, *connect.Request[v2.UpdateReviewRoundRequest]) (*connect.Response[v2.ReviewRound], error)
	DeleteReviewRound(context.Context, *connect.Request[v2.DeleteReviewRoundRequest]) (*connect.Response[emptypb.Empty], error)
	ApproveReviewRound(context.Context, *connect.Request[v2.ApproveReviewRoundRequest]) (*connect.Response[v2.ReviewRound], error)
	RequestChanges(context.Context, *connect.Request[v2.RequestChangesRequest]) (*connect.Response[v2.ReviewRound], error)
	ListReviewRoundsForReport(context.Context, *connect.Request[v2.ListReviewRoundsForReportRequest]) (*connect.Response[v2.ListReviewRoundsForReportResponse], error)
	GetEligibleReviewers(context.Context, *connect.Request[v2.GetEligibleReviewersRequest]) (*connect.Response[v2.GetEligibleReviewersResponse], error)
	// JSON metadata
	UpdateAdditionalInfoJson(context.Context, *connect.Request[v2.UpdateAdditionalInfoJsonRequest]) (*connect.Response[v2.UpdateAdditionalInfoJsonResponse], error)
	GetAdditionalInfo(context.Context, *connect.Request[v2.GetAdditionalInfoRequest]) (*connect.Response[v2.GetAdditionalInfoResponse], error)
	// Versioning
	GetReportVersion(context.Context, *connect.Request[v2.GetReportVersionRequest]) (*connect.Response[v2.ReportSnapshot], error)
	ListReportVersions(context.Context, *connect.Request[v2.ListReportVersionsRequest]) (*connect.Response[v2.ListReportVersionsResponse], error)
	// Add the new search RPC
	SearchReports(context.Context, *connect.Request[v2.SearchReportsRequest]) (*connect.Response[v2.SearchReportsResponse], error)
	// Relation CRUD
	CreateRelation(context.Context, *connect.Request[v2.CreateRelationRequest]) (*connect.Response[v2.Relation], error)
	GetRelation(context.Context, *connect.Request[v2.GetRelationRequest]) (*connect.Response[v2.Relation], error)
	UpdateRelation(context.Context, *connect.Request[v2.UpdateRelationRequest]) (*connect.Response[v2.Relation], error)
	DeleteRelation(context.Context, *connect.Request[v2.DeleteRelationRequest]) (*connect.Response[emptypb.Empty], error)
	ListRelations(context.Context, *connect.Request[v2.ListRelationsRequest]) (*connect.Response[v2.ListRelationsResponse], error)
}

// NewReportServiceHandler builds an HTTP handler from the service implementation. It returns the
// path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewReportServiceHandler(svc ReportServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	reportServiceMethods := v2.File_hero_reports_v2_reports_proto.Services().ByName("ReportService").Methods()
	reportServiceCreateReportSectionHandler := connect.NewUnaryHandler(
		ReportServiceCreateReportSectionProcedure,
		svc.CreateReportSection,
		connect.WithSchema(reportServiceMethods.ByName("CreateReportSection")),
		connect.WithHandlerOptions(opts...),
	)
	reportServiceGetReportSectionHandler := connect.NewUnaryHandler(
		ReportServiceGetReportSectionProcedure,
		svc.GetReportSection,
		connect.WithSchema(reportServiceMethods.ByName("GetReportSection")),
		connect.WithHandlerOptions(opts...),
	)
	reportServiceUpdateReportSectionHandler := connect.NewUnaryHandler(
		ReportServiceUpdateReportSectionProcedure,
		svc.UpdateReportSection,
		connect.WithSchema(reportServiceMethods.ByName("UpdateReportSection")),
		connect.WithHandlerOptions(opts...),
	)
	reportServiceDeleteReportSectionHandler := connect.NewUnaryHandler(
		ReportServiceDeleteReportSectionProcedure,
		svc.DeleteReportSection,
		connect.WithSchema(reportServiceMethods.ByName("DeleteReportSection")),
		connect.WithHandlerOptions(opts...),
	)
	reportServiceListReportSectionsHandler := connect.NewUnaryHandler(
		ReportServiceListReportSectionsProcedure,
		svc.ListReportSections,
		connect.WithSchema(reportServiceMethods.ByName("ListReportSections")),
		connect.WithHandlerOptions(opts...),
	)
	reportServiceCreateReportHandler := connect.NewUnaryHandler(
		ReportServiceCreateReportProcedure,
		svc.CreateReport,
		connect.WithSchema(reportServiceMethods.ByName("CreateReport")),
		connect.WithHandlerOptions(opts...),
	)
	reportServiceGetReportHandler := connect.NewUnaryHandler(
		ReportServiceGetReportProcedure,
		svc.GetReport,
		connect.WithSchema(reportServiceMethods.ByName("GetReport")),
		connect.WithHandlerOptions(opts...),
	)
	reportServiceUpdateReportHandler := connect.NewUnaryHandler(
		ReportServiceUpdateReportProcedure,
		svc.UpdateReport,
		connect.WithSchema(reportServiceMethods.ByName("UpdateReport")),
		connect.WithHandlerOptions(opts...),
	)
	reportServiceUpdateReportStatusHandler := connect.NewUnaryHandler(
		ReportServiceUpdateReportStatusProcedure,
		svc.UpdateReportStatus,
		connect.WithSchema(reportServiceMethods.ByName("UpdateReportStatus")),
		connect.WithHandlerOptions(opts...),
	)
	reportServiceListReportsHandler := connect.NewUnaryHandler(
		ReportServiceListReportsProcedure,
		svc.ListReports,
		connect.WithSchema(reportServiceMethods.ByName("ListReports")),
		connect.WithHandlerOptions(opts...),
	)
	reportServiceBatchGetReportsHandler := connect.NewUnaryHandler(
		ReportServiceBatchGetReportsProcedure,
		svc.BatchGetReports,
		connect.WithSchema(reportServiceMethods.ByName("BatchGetReports")),
		connect.WithHandlerOptions(opts...),
	)
	reportServiceDeleteReportHandler := connect.NewUnaryHandler(
		ReportServiceDeleteReportProcedure,
		svc.DeleteReport,
		connect.WithSchema(reportServiceMethods.ByName("DeleteReport")),
		connect.WithHandlerOptions(opts...),
	)
	reportServiceListReportsBySituationIdHandler := connect.NewUnaryHandler(
		ReportServiceListReportsBySituationIdProcedure,
		svc.ListReportsBySituationId,
		connect.WithSchema(reportServiceMethods.ByName("ListReportsBySituationId")),
		connect.WithHandlerOptions(opts...),
	)
	reportServiceListReportsByCaseIdHandler := connect.NewUnaryHandler(
		ReportServiceListReportsByCaseIdProcedure,
		svc.ListReportsByCaseId,
		connect.WithSchema(reportServiceMethods.ByName("ListReportsByCaseId")),
		connect.WithHandlerOptions(opts...),
	)
	reportServiceAddCommentHandler := connect.NewUnaryHandler(
		ReportServiceAddCommentProcedure,
		svc.AddComment,
		connect.WithSchema(reportServiceMethods.ByName("AddComment")),
		connect.WithHandlerOptions(opts...),
	)
	reportServiceGetCommentsHandler := connect.NewUnaryHandler(
		ReportServiceGetCommentsProcedure,
		svc.GetComments,
		connect.WithSchema(reportServiceMethods.ByName("GetComments")),
		connect.WithHandlerOptions(opts...),
	)
	reportServiceUpdateCommentHandler := connect.NewUnaryHandler(
		ReportServiceUpdateCommentProcedure,
		svc.UpdateComment,
		connect.WithSchema(reportServiceMethods.ByName("UpdateComment")),
		connect.WithHandlerOptions(opts...),
	)
	reportServiceDeleteCommentHandler := connect.NewUnaryHandler(
		ReportServiceDeleteCommentProcedure,
		svc.DeleteComment,
		connect.WithSchema(reportServiceMethods.ByName("DeleteComment")),
		connect.WithHandlerOptions(opts...),
	)
	reportServiceResolveCommentHandler := connect.NewUnaryHandler(
		ReportServiceResolveCommentProcedure,
		svc.ResolveComment,
		connect.WithSchema(reportServiceMethods.ByName("ResolveComment")),
		connect.WithHandlerOptions(opts...),
	)
	reportServiceSubmitForReviewHandler := connect.NewUnaryHandler(
		ReportServiceSubmitForReviewProcedure,
		svc.SubmitForReview,
		connect.WithSchema(reportServiceMethods.ByName("SubmitForReview")),
		connect.WithHandlerOptions(opts...),
	)
	reportServiceAddReviewRoundHandler := connect.NewUnaryHandler(
		ReportServiceAddReviewRoundProcedure,
		svc.AddReviewRound,
		connect.WithSchema(reportServiceMethods.ByName("AddReviewRound")),
		connect.WithHandlerOptions(opts...),
	)
	reportServiceGetReviewRoundHandler := connect.NewUnaryHandler(
		ReportServiceGetReviewRoundProcedure,
		svc.GetReviewRound,
		connect.WithSchema(reportServiceMethods.ByName("GetReviewRound")),
		connect.WithHandlerOptions(opts...),
	)
	reportServiceUpdateReviewRoundHandler := connect.NewUnaryHandler(
		ReportServiceUpdateReviewRoundProcedure,
		svc.UpdateReviewRound,
		connect.WithSchema(reportServiceMethods.ByName("UpdateReviewRound")),
		connect.WithHandlerOptions(opts...),
	)
	reportServiceDeleteReviewRoundHandler := connect.NewUnaryHandler(
		ReportServiceDeleteReviewRoundProcedure,
		svc.DeleteReviewRound,
		connect.WithSchema(reportServiceMethods.ByName("DeleteReviewRound")),
		connect.WithHandlerOptions(opts...),
	)
	reportServiceApproveReviewRoundHandler := connect.NewUnaryHandler(
		ReportServiceApproveReviewRoundProcedure,
		svc.ApproveReviewRound,
		connect.WithSchema(reportServiceMethods.ByName("ApproveReviewRound")),
		connect.WithHandlerOptions(opts...),
	)
	reportServiceRequestChangesHandler := connect.NewUnaryHandler(
		ReportServiceRequestChangesProcedure,
		svc.RequestChanges,
		connect.WithSchema(reportServiceMethods.ByName("RequestChanges")),
		connect.WithHandlerOptions(opts...),
	)
	reportServiceListReviewRoundsForReportHandler := connect.NewUnaryHandler(
		ReportServiceListReviewRoundsForReportProcedure,
		svc.ListReviewRoundsForReport,
		connect.WithSchema(reportServiceMethods.ByName("ListReviewRoundsForReport")),
		connect.WithHandlerOptions(opts...),
	)
	reportServiceGetEligibleReviewersHandler := connect.NewUnaryHandler(
		ReportServiceGetEligibleReviewersProcedure,
		svc.GetEligibleReviewers,
		connect.WithSchema(reportServiceMethods.ByName("GetEligibleReviewers")),
		connect.WithHandlerOptions(opts...),
	)
	reportServiceUpdateAdditionalInfoJsonHandler := connect.NewUnaryHandler(
		ReportServiceUpdateAdditionalInfoJsonProcedure,
		svc.UpdateAdditionalInfoJson,
		connect.WithSchema(reportServiceMethods.ByName("UpdateAdditionalInfoJson")),
		connect.WithHandlerOptions(opts...),
	)
	reportServiceGetAdditionalInfoHandler := connect.NewUnaryHandler(
		ReportServiceGetAdditionalInfoProcedure,
		svc.GetAdditionalInfo,
		connect.WithSchema(reportServiceMethods.ByName("GetAdditionalInfo")),
		connect.WithHandlerOptions(opts...),
	)
	reportServiceGetReportVersionHandler := connect.NewUnaryHandler(
		ReportServiceGetReportVersionProcedure,
		svc.GetReportVersion,
		connect.WithSchema(reportServiceMethods.ByName("GetReportVersion")),
		connect.WithHandlerOptions(opts...),
	)
	reportServiceListReportVersionsHandler := connect.NewUnaryHandler(
		ReportServiceListReportVersionsProcedure,
		svc.ListReportVersions,
		connect.WithSchema(reportServiceMethods.ByName("ListReportVersions")),
		connect.WithHandlerOptions(opts...),
	)
	reportServiceSearchReportsHandler := connect.NewUnaryHandler(
		ReportServiceSearchReportsProcedure,
		svc.SearchReports,
		connect.WithSchema(reportServiceMethods.ByName("SearchReports")),
		connect.WithHandlerOptions(opts...),
	)
	reportServiceCreateRelationHandler := connect.NewUnaryHandler(
		ReportServiceCreateRelationProcedure,
		svc.CreateRelation,
		connect.WithSchema(reportServiceMethods.ByName("CreateRelation")),
		connect.WithHandlerOptions(opts...),
	)
	reportServiceGetRelationHandler := connect.NewUnaryHandler(
		ReportServiceGetRelationProcedure,
		svc.GetRelation,
		connect.WithSchema(reportServiceMethods.ByName("GetRelation")),
		connect.WithHandlerOptions(opts...),
	)
	reportServiceUpdateRelationHandler := connect.NewUnaryHandler(
		ReportServiceUpdateRelationProcedure,
		svc.UpdateRelation,
		connect.WithSchema(reportServiceMethods.ByName("UpdateRelation")),
		connect.WithHandlerOptions(opts...),
	)
	reportServiceDeleteRelationHandler := connect.NewUnaryHandler(
		ReportServiceDeleteRelationProcedure,
		svc.DeleteRelation,
		connect.WithSchema(reportServiceMethods.ByName("DeleteRelation")),
		connect.WithHandlerOptions(opts...),
	)
	reportServiceListRelationsHandler := connect.NewUnaryHandler(
		ReportServiceListRelationsProcedure,
		svc.ListRelations,
		connect.WithSchema(reportServiceMethods.ByName("ListRelations")),
		connect.WithHandlerOptions(opts...),
	)
	return "/hero.reports.v2.ReportService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case ReportServiceCreateReportSectionProcedure:
			reportServiceCreateReportSectionHandler.ServeHTTP(w, r)
		case ReportServiceGetReportSectionProcedure:
			reportServiceGetReportSectionHandler.ServeHTTP(w, r)
		case ReportServiceUpdateReportSectionProcedure:
			reportServiceUpdateReportSectionHandler.ServeHTTP(w, r)
		case ReportServiceDeleteReportSectionProcedure:
			reportServiceDeleteReportSectionHandler.ServeHTTP(w, r)
		case ReportServiceListReportSectionsProcedure:
			reportServiceListReportSectionsHandler.ServeHTTP(w, r)
		case ReportServiceCreateReportProcedure:
			reportServiceCreateReportHandler.ServeHTTP(w, r)
		case ReportServiceGetReportProcedure:
			reportServiceGetReportHandler.ServeHTTP(w, r)
		case ReportServiceUpdateReportProcedure:
			reportServiceUpdateReportHandler.ServeHTTP(w, r)
		case ReportServiceUpdateReportStatusProcedure:
			reportServiceUpdateReportStatusHandler.ServeHTTP(w, r)
		case ReportServiceListReportsProcedure:
			reportServiceListReportsHandler.ServeHTTP(w, r)
		case ReportServiceBatchGetReportsProcedure:
			reportServiceBatchGetReportsHandler.ServeHTTP(w, r)
		case ReportServiceDeleteReportProcedure:
			reportServiceDeleteReportHandler.ServeHTTP(w, r)
		case ReportServiceListReportsBySituationIdProcedure:
			reportServiceListReportsBySituationIdHandler.ServeHTTP(w, r)
		case ReportServiceListReportsByCaseIdProcedure:
			reportServiceListReportsByCaseIdHandler.ServeHTTP(w, r)
		case ReportServiceAddCommentProcedure:
			reportServiceAddCommentHandler.ServeHTTP(w, r)
		case ReportServiceGetCommentsProcedure:
			reportServiceGetCommentsHandler.ServeHTTP(w, r)
		case ReportServiceUpdateCommentProcedure:
			reportServiceUpdateCommentHandler.ServeHTTP(w, r)
		case ReportServiceDeleteCommentProcedure:
			reportServiceDeleteCommentHandler.ServeHTTP(w, r)
		case ReportServiceResolveCommentProcedure:
			reportServiceResolveCommentHandler.ServeHTTP(w, r)
		case ReportServiceSubmitForReviewProcedure:
			reportServiceSubmitForReviewHandler.ServeHTTP(w, r)
		case ReportServiceAddReviewRoundProcedure:
			reportServiceAddReviewRoundHandler.ServeHTTP(w, r)
		case ReportServiceGetReviewRoundProcedure:
			reportServiceGetReviewRoundHandler.ServeHTTP(w, r)
		case ReportServiceUpdateReviewRoundProcedure:
			reportServiceUpdateReviewRoundHandler.ServeHTTP(w, r)
		case ReportServiceDeleteReviewRoundProcedure:
			reportServiceDeleteReviewRoundHandler.ServeHTTP(w, r)
		case ReportServiceApproveReviewRoundProcedure:
			reportServiceApproveReviewRoundHandler.ServeHTTP(w, r)
		case ReportServiceRequestChangesProcedure:
			reportServiceRequestChangesHandler.ServeHTTP(w, r)
		case ReportServiceListReviewRoundsForReportProcedure:
			reportServiceListReviewRoundsForReportHandler.ServeHTTP(w, r)
		case ReportServiceGetEligibleReviewersProcedure:
			reportServiceGetEligibleReviewersHandler.ServeHTTP(w, r)
		case ReportServiceUpdateAdditionalInfoJsonProcedure:
			reportServiceUpdateAdditionalInfoJsonHandler.ServeHTTP(w, r)
		case ReportServiceGetAdditionalInfoProcedure:
			reportServiceGetAdditionalInfoHandler.ServeHTTP(w, r)
		case ReportServiceGetReportVersionProcedure:
			reportServiceGetReportVersionHandler.ServeHTTP(w, r)
		case ReportServiceListReportVersionsProcedure:
			reportServiceListReportVersionsHandler.ServeHTTP(w, r)
		case ReportServiceSearchReportsProcedure:
			reportServiceSearchReportsHandler.ServeHTTP(w, r)
		case ReportServiceCreateRelationProcedure:
			reportServiceCreateRelationHandler.ServeHTTP(w, r)
		case ReportServiceGetRelationProcedure:
			reportServiceGetRelationHandler.ServeHTTP(w, r)
		case ReportServiceUpdateRelationProcedure:
			reportServiceUpdateRelationHandler.ServeHTTP(w, r)
		case ReportServiceDeleteRelationProcedure:
			reportServiceDeleteRelationHandler.ServeHTTP(w, r)
		case ReportServiceListRelationsProcedure:
			reportServiceListRelationsHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedReportServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedReportServiceHandler struct{}

func (UnimplementedReportServiceHandler) CreateReportSection(context.Context, *connect.Request[v2.CreateReportSectionRequest]) (*connect.Response[v2.ReportSection], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.reports.v2.ReportService.CreateReportSection is not implemented"))
}

func (UnimplementedReportServiceHandler) GetReportSection(context.Context, *connect.Request[v2.GetReportSectionRequest]) (*connect.Response[v2.ReportSection], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.reports.v2.ReportService.GetReportSection is not implemented"))
}

func (UnimplementedReportServiceHandler) UpdateReportSection(context.Context, *connect.Request[v2.UpdateReportSectionRequest]) (*connect.Response[v2.ReportSection], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.reports.v2.ReportService.UpdateReportSection is not implemented"))
}

func (UnimplementedReportServiceHandler) DeleteReportSection(context.Context, *connect.Request[v2.DeleteReportSectionRequest]) (*connect.Response[emptypb.Empty], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.reports.v2.ReportService.DeleteReportSection is not implemented"))
}

func (UnimplementedReportServiceHandler) ListReportSections(context.Context, *connect.Request[v2.ListReportSectionsRequest]) (*connect.Response[v2.ListReportSectionsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.reports.v2.ReportService.ListReportSections is not implemented"))
}

func (UnimplementedReportServiceHandler) CreateReport(context.Context, *connect.Request[v2.CreateReportRequest]) (*connect.Response[v2.CreateReportResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.reports.v2.ReportService.CreateReport is not implemented"))
}

func (UnimplementedReportServiceHandler) GetReport(context.Context, *connect.Request[v2.GetReportRequest]) (*connect.Response[v2.Report], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.reports.v2.ReportService.GetReport is not implemented"))
}

func (UnimplementedReportServiceHandler) UpdateReport(context.Context, *connect.Request[v2.UpdateReportRequest]) (*connect.Response[v2.Report], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.reports.v2.ReportService.UpdateReport is not implemented"))
}

func (UnimplementedReportServiceHandler) UpdateReportStatus(context.Context, *connect.Request[v2.UpdateReportStatusRequest]) (*connect.Response[v2.UpdateReportStatusResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.reports.v2.ReportService.UpdateReportStatus is not implemented"))
}

func (UnimplementedReportServiceHandler) ListReports(context.Context, *connect.Request[v2.ListReportsRequest]) (*connect.Response[v2.ListReportsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.reports.v2.ReportService.ListReports is not implemented"))
}

func (UnimplementedReportServiceHandler) BatchGetReports(context.Context, *connect.Request[v2.BatchGetReportsRequest]) (*connect.Response[v2.BatchGetReportsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.reports.v2.ReportService.BatchGetReports is not implemented"))
}

func (UnimplementedReportServiceHandler) DeleteReport(context.Context, *connect.Request[v2.DeleteReportRequest]) (*connect.Response[emptypb.Empty], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.reports.v2.ReportService.DeleteReport is not implemented"))
}

func (UnimplementedReportServiceHandler) ListReportsBySituationId(context.Context, *connect.Request[v2.ListReportsBySituationIdRequest]) (*connect.Response[v2.ListReportsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.reports.v2.ReportService.ListReportsBySituationId is not implemented"))
}

func (UnimplementedReportServiceHandler) ListReportsByCaseId(context.Context, *connect.Request[v2.ListReportsByCaseIdRequest]) (*connect.Response[v2.ListReportsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.reports.v2.ReportService.ListReportsByCaseId is not implemented"))
}

func (UnimplementedReportServiceHandler) AddComment(context.Context, *connect.Request[v2.AddCommentRequest]) (*connect.Response[v2.Comment], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.reports.v2.ReportService.AddComment is not implemented"))
}

func (UnimplementedReportServiceHandler) GetComments(context.Context, *connect.Request[v2.GetCommentsRequest]) (*connect.Response[v2.GetCommentsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.reports.v2.ReportService.GetComments is not implemented"))
}

func (UnimplementedReportServiceHandler) UpdateComment(context.Context, *connect.Request[v2.UpdateCommentRequest]) (*connect.Response[v2.Comment], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.reports.v2.ReportService.UpdateComment is not implemented"))
}

func (UnimplementedReportServiceHandler) DeleteComment(context.Context, *connect.Request[v2.DeleteCommentRequest]) (*connect.Response[emptypb.Empty], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.reports.v2.ReportService.DeleteComment is not implemented"))
}

func (UnimplementedReportServiceHandler) ResolveComment(context.Context, *connect.Request[v2.ResolveCommentRequest]) (*connect.Response[v2.Comment], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.reports.v2.ReportService.ResolveComment is not implemented"))
}

func (UnimplementedReportServiceHandler) SubmitForReview(context.Context, *connect.Request[v2.SubmitForReviewRequest]) (*connect.Response[v2.Report], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.reports.v2.ReportService.SubmitForReview is not implemented"))
}

func (UnimplementedReportServiceHandler) AddReviewRound(context.Context, *connect.Request[v2.AddReviewRoundRequest]) (*connect.Response[v2.ReviewRound], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.reports.v2.ReportService.AddReviewRound is not implemented"))
}

func (UnimplementedReportServiceHandler) GetReviewRound(context.Context, *connect.Request[v2.GetReviewRoundRequest]) (*connect.Response[v2.ReviewRound], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.reports.v2.ReportService.GetReviewRound is not implemented"))
}

func (UnimplementedReportServiceHandler) UpdateReviewRound(context.Context, *connect.Request[v2.UpdateReviewRoundRequest]) (*connect.Response[v2.ReviewRound], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.reports.v2.ReportService.UpdateReviewRound is not implemented"))
}

func (UnimplementedReportServiceHandler) DeleteReviewRound(context.Context, *connect.Request[v2.DeleteReviewRoundRequest]) (*connect.Response[emptypb.Empty], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.reports.v2.ReportService.DeleteReviewRound is not implemented"))
}

func (UnimplementedReportServiceHandler) ApproveReviewRound(context.Context, *connect.Request[v2.ApproveReviewRoundRequest]) (*connect.Response[v2.ReviewRound], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.reports.v2.ReportService.ApproveReviewRound is not implemented"))
}

func (UnimplementedReportServiceHandler) RequestChanges(context.Context, *connect.Request[v2.RequestChangesRequest]) (*connect.Response[v2.ReviewRound], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.reports.v2.ReportService.RequestChanges is not implemented"))
}

func (UnimplementedReportServiceHandler) ListReviewRoundsForReport(context.Context, *connect.Request[v2.ListReviewRoundsForReportRequest]) (*connect.Response[v2.ListReviewRoundsForReportResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.reports.v2.ReportService.ListReviewRoundsForReport is not implemented"))
}

func (UnimplementedReportServiceHandler) GetEligibleReviewers(context.Context, *connect.Request[v2.GetEligibleReviewersRequest]) (*connect.Response[v2.GetEligibleReviewersResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.reports.v2.ReportService.GetEligibleReviewers is not implemented"))
}

func (UnimplementedReportServiceHandler) UpdateAdditionalInfoJson(context.Context, *connect.Request[v2.UpdateAdditionalInfoJsonRequest]) (*connect.Response[v2.UpdateAdditionalInfoJsonResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.reports.v2.ReportService.UpdateAdditionalInfoJson is not implemented"))
}

func (UnimplementedReportServiceHandler) GetAdditionalInfo(context.Context, *connect.Request[v2.GetAdditionalInfoRequest]) (*connect.Response[v2.GetAdditionalInfoResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.reports.v2.ReportService.GetAdditionalInfo is not implemented"))
}

func (UnimplementedReportServiceHandler) GetReportVersion(context.Context, *connect.Request[v2.GetReportVersionRequest]) (*connect.Response[v2.ReportSnapshot], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.reports.v2.ReportService.GetReportVersion is not implemented"))
}

func (UnimplementedReportServiceHandler) ListReportVersions(context.Context, *connect.Request[v2.ListReportVersionsRequest]) (*connect.Response[v2.ListReportVersionsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.reports.v2.ReportService.ListReportVersions is not implemented"))
}

func (UnimplementedReportServiceHandler) SearchReports(context.Context, *connect.Request[v2.SearchReportsRequest]) (*connect.Response[v2.SearchReportsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.reports.v2.ReportService.SearchReports is not implemented"))
}

func (UnimplementedReportServiceHandler) CreateRelation(context.Context, *connect.Request[v2.CreateRelationRequest]) (*connect.Response[v2.Relation], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.reports.v2.ReportService.CreateRelation is not implemented"))
}

func (UnimplementedReportServiceHandler) GetRelation(context.Context, *connect.Request[v2.GetRelationRequest]) (*connect.Response[v2.Relation], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.reports.v2.ReportService.GetRelation is not implemented"))
}

func (UnimplementedReportServiceHandler) UpdateRelation(context.Context, *connect.Request[v2.UpdateRelationRequest]) (*connect.Response[v2.Relation], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.reports.v2.ReportService.UpdateRelation is not implemented"))
}

func (UnimplementedReportServiceHandler) DeleteRelation(context.Context, *connect.Request[v2.DeleteRelationRequest]) (*connect.Response[emptypb.Empty], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.reports.v2.ReportService.DeleteRelation is not implemented"))
}

func (UnimplementedReportServiceHandler) ListRelations(context.Context, *connect.Request[v2.ListRelationsRequest]) (*connect.Response[v2.ListRelationsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.reports.v2.ReportService.ListRelations is not implemented"))
}
