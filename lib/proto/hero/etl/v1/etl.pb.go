// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.3
// 	protoc        (unknown)
// source: hero/etl/v1/etl.proto

package etl

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/emptypb"
	structpb "google.golang.org/protobuf/types/known/structpb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ETL Job status
type JobStatus int32

const (
	JobStatus_JOB_STATUS_UNSPECIFIED  JobStatus = 0
	JobStatus_JOB_STATUS_PENDING      JobStatus = 1 // Job queued, waiting to start
	JobStatus_JOB_STATUS_EXTRACTING   JobStatus = 2 // Extracting report data from services
	JobStatus_JOB_STATUS_TRANSFORMING JobStatus = 3 // Transforming to agency format
	JobStatus_JOB_STATUS_LOADING      JobStatus = 4 // Sending/loading data to agency
	JobStatus_JOB_STATUS_COMPLETED    JobStatus = 5 // Successfully completed
	JobStatus_JOB_STATUS_FAILED       JobStatus = 6 // Failed permanently
	JobStatus_JOB_STATUS_CANCELLED    JobStatus = 7 // Cancelled by user
)

// Enum value maps for JobStatus.
var (
	JobStatus_name = map[int32]string{
		0: "JOB_STATUS_UNSPECIFIED",
		1: "JOB_STATUS_PENDING",
		2: "JOB_STATUS_EXTRACTING",
		3: "JOB_STATUS_TRANSFORMING",
		4: "JOB_STATUS_LOADING",
		5: "JOB_STATUS_COMPLETED",
		6: "JOB_STATUS_FAILED",
		7: "JOB_STATUS_CANCELLED",
	}
	JobStatus_value = map[string]int32{
		"JOB_STATUS_UNSPECIFIED":  0,
		"JOB_STATUS_PENDING":      1,
		"JOB_STATUS_EXTRACTING":   2,
		"JOB_STATUS_TRANSFORMING": 3,
		"JOB_STATUS_LOADING":      4,
		"JOB_STATUS_COMPLETED":    5,
		"JOB_STATUS_FAILED":       6,
		"JOB_STATUS_CANCELLED":    7,
	}
)

func (x JobStatus) Enum() *JobStatus {
	p := new(JobStatus)
	*p = x
	return p
}

func (x JobStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (JobStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_hero_etl_v1_etl_proto_enumTypes[0].Descriptor()
}

func (JobStatus) Type() protoreflect.EnumType {
	return &file_hero_etl_v1_etl_proto_enumTypes[0]
}

func (x JobStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use JobStatus.Descriptor instead.
func (JobStatus) EnumDescriptor() ([]byte, []int) {
	return file_hero_etl_v1_etl_proto_rawDescGZIP(), []int{0}
}

// Data standards/formats for law enforcement reporting
type OutputFormat int32

const (
	OutputFormat_OUTPUT_FORMAT_UNSPECIFIED OutputFormat = 0
	OutputFormat_OUTPUT_FORMAT_NIBRS_XML   OutputFormat = 1 // FBI NIBRS XML format
)

// Enum value maps for OutputFormat.
var (
	OutputFormat_name = map[int32]string{
		0: "OUTPUT_FORMAT_UNSPECIFIED",
		1: "OUTPUT_FORMAT_NIBRS_XML",
	}
	OutputFormat_value = map[string]int32{
		"OUTPUT_FORMAT_UNSPECIFIED": 0,
		"OUTPUT_FORMAT_NIBRS_XML":   1,
	}
)

func (x OutputFormat) Enum() *OutputFormat {
	p := new(OutputFormat)
	*p = x
	return p
}

func (x OutputFormat) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OutputFormat) Descriptor() protoreflect.EnumDescriptor {
	return file_hero_etl_v1_etl_proto_enumTypes[1].Descriptor()
}

func (OutputFormat) Type() protoreflect.EnumType {
	return &file_hero_etl_v1_etl_proto_enumTypes[1]
}

func (x OutputFormat) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OutputFormat.Descriptor instead.
func (OutputFormat) EnumDescriptor() ([]byte, []int) {
	return file_hero_etl_v1_etl_proto_rawDescGZIP(), []int{1}
}

// ETL Job - tracks processing of reports for data standards compliance
type ETLJob struct {
	state        protoimpl.MessageState `protogen:"open.v1"`
	Id           string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                                                        // Unique job ID
	OrgId        int32                  `protobuf:"varint,2,opt,name=org_id,json=orgId,proto3" json:"org_id,omitempty"`                                                    // Organization ID for multi-tenant isolation
	AgencyId     string                 `protobuf:"bytes,3,opt,name=agency_id,json=agencyId,proto3" json:"agency_id,omitempty"`                                            // Target agency/recipient (e.g., "FBI", "STATE_POLICE", "UNIVERSITY_POLICE")
	OutputFormat OutputFormat           `protobuf:"varint,4,opt,name=output_format,json=outputFormat,proto3,enum=hero.etl.v1.OutputFormat" json:"output_format,omitempty"` // Data standard/format (NIBRS, etc.)
	Status       JobStatus              `protobuf:"varint,5,opt,name=status,proto3,enum=hero.etl.v1.JobStatus" json:"status,omitempty"`                                    // Current status
	// What's being processed
	ReportIds []string `protobuf:"bytes,6,rep,name=report_ids,json=reportIds,proto3" json:"report_ids,omitempty"` // Reports to process
	DateFrom  string   `protobuf:"bytes,7,opt,name=date_from,json=dateFrom,proto3" json:"date_from,omitempty"`    // ISO8601 date range start (optional)
	DateTo    string   `protobuf:"bytes,8,opt,name=date_to,json=dateTo,proto3" json:"date_to,omitempty"`          // ISO8601 date range end (optional)
	CaseTypes []string `protobuf:"bytes,9,rep,name=case_types,json=caseTypes,proto3" json:"case_types,omitempty"` // Filter by case types (optional)
	// Progress tracking
	TotalReports     int32 `protobuf:"varint,11,opt,name=total_reports,json=totalReports,proto3" json:"total_reports,omitempty"`             // Total reports to process
	ReportsProcessed int32 `protobuf:"varint,12,opt,name=reports_processed,json=reportsProcessed,proto3" json:"reports_processed,omitempty"` // Reports successfully processed
	ReportsFailed    int32 `protobuf:"varint,13,opt,name=reports_failed,json=reportsFailed,proto3" json:"reports_failed,omitempty"`          // Reports that failed processing
	ReportsSkipped   int32 `protobuf:"varint,14,opt,name=reports_skipped,json=reportsSkipped,proto3" json:"reports_skipped,omitempty"`       // Reports skipped (e.g., invalid data)
	// Timestamps
	CreatedAt     string `protobuf:"bytes,15,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`               // ISO8601 timestamp when job was created
	StartedAt     string `protobuf:"bytes,16,opt,name=started_at,json=startedAt,proto3" json:"started_at,omitempty"`               // ISO8601 timestamp when job started
	CompletedAt   string `protobuf:"bytes,17,opt,name=completed_at,json=completedAt,proto3" json:"completed_at,omitempty"`         // ISO8601 timestamp when job completed
	LastUpdatedAt string `protobuf:"bytes,18,opt,name=last_updated_at,json=lastUpdatedAt,proto3" json:"last_updated_at,omitempty"` // ISO8601 timestamp of last status update
	// Results and content
	ErrorMessage     string      `protobuf:"bytes,19,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`                // Primary error message if failed
	Errors           []*JobError `protobuf:"bytes,20,rep,name=errors,proto3" json:"errors,omitempty"`                                                // Detailed error list
	GeneratedContent []byte      `protobuf:"bytes,21,opt,name=generated_content,json=generatedContent,proto3" json:"generated_content,omitempty"`    // Generated content for inspection
	ContentType      string      `protobuf:"bytes,22,opt,name=content_type,json=contentType,proto3" json:"content_type,omitempty"`                   // MIME type of generated content
	ContentSizeBytes int64       `protobuf:"varint,23,opt,name=content_size_bytes,json=contentSizeBytes,proto3" json:"content_size_bytes,omitempty"` // Size of generated content
	// Submission details
	AgencySubmissionId string `protobuf:"bytes,24,opt,name=agency_submission_id,json=agencySubmissionId,proto3" json:"agency_submission_id,omitempty"` // Agency's tracking ID (if submitted)
	SubmissionResponse string `protobuf:"bytes,25,opt,name=submission_response,json=submissionResponse,proto3" json:"submission_response,omitempty"`   // Agency's response message
	SubmittedAt        string `protobuf:"bytes,26,opt,name=submitted_at,json=submittedAt,proto3" json:"submitted_at,omitempty"`                        // ISO8601 timestamp when submitted to agency
	// Retry information
	RetryCount       int32  `protobuf:"varint,27,opt,name=retry_count,json=retryCount,proto3" json:"retry_count,omitempty"`                     // Number of retry attempts made
	MaxRetries       int32  `protobuf:"varint,28,opt,name=max_retries,json=maxRetries,proto3" json:"max_retries,omitempty"`                     // Maximum retry attempts allowed
	LastRetryAt      string `protobuf:"bytes,29,opt,name=last_retry_at,json=lastRetryAt,proto3" json:"last_retry_at,omitempty"`                 // ISO8601 timestamp of last retry attempt
	AutoRetryEnabled bool   `protobuf:"varint,30,opt,name=auto_retry_enabled,json=autoRetryEnabled,proto3" json:"auto_retry_enabled,omitempty"` // Whether automatic retries are enabled
	// Metadata
	CreatedByAssetId string `protobuf:"bytes,31,opt,name=created_by_asset_id,json=createdByAssetId,proto3" json:"created_by_asset_id,omitempty"` // Who created the job
	PreviewOnly      bool   `protobuf:"varint,32,opt,name=preview_only,json=previewOnly,proto3" json:"preview_only,omitempty"`                   // Generated but not sent to agency
	JobName          string `protobuf:"bytes,33,opt,name=job_name,json=jobName,proto3" json:"job_name,omitempty"`                                // Optional human-readable job name
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *ETLJob) Reset() {
	*x = ETLJob{}
	mi := &file_hero_etl_v1_etl_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ETLJob) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ETLJob) ProtoMessage() {}

func (x *ETLJob) ProtoReflect() protoreflect.Message {
	mi := &file_hero_etl_v1_etl_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ETLJob.ProtoReflect.Descriptor instead.
func (*ETLJob) Descriptor() ([]byte, []int) {
	return file_hero_etl_v1_etl_proto_rawDescGZIP(), []int{0}
}

func (x *ETLJob) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ETLJob) GetOrgId() int32 {
	if x != nil {
		return x.OrgId
	}
	return 0
}

func (x *ETLJob) GetAgencyId() string {
	if x != nil {
		return x.AgencyId
	}
	return ""
}

func (x *ETLJob) GetOutputFormat() OutputFormat {
	if x != nil {
		return x.OutputFormat
	}
	return OutputFormat_OUTPUT_FORMAT_UNSPECIFIED
}

func (x *ETLJob) GetStatus() JobStatus {
	if x != nil {
		return x.Status
	}
	return JobStatus_JOB_STATUS_UNSPECIFIED
}

func (x *ETLJob) GetReportIds() []string {
	if x != nil {
		return x.ReportIds
	}
	return nil
}

func (x *ETLJob) GetDateFrom() string {
	if x != nil {
		return x.DateFrom
	}
	return ""
}

func (x *ETLJob) GetDateTo() string {
	if x != nil {
		return x.DateTo
	}
	return ""
}

func (x *ETLJob) GetCaseTypes() []string {
	if x != nil {
		return x.CaseTypes
	}
	return nil
}

func (x *ETLJob) GetTotalReports() int32 {
	if x != nil {
		return x.TotalReports
	}
	return 0
}

func (x *ETLJob) GetReportsProcessed() int32 {
	if x != nil {
		return x.ReportsProcessed
	}
	return 0
}

func (x *ETLJob) GetReportsFailed() int32 {
	if x != nil {
		return x.ReportsFailed
	}
	return 0
}

func (x *ETLJob) GetReportsSkipped() int32 {
	if x != nil {
		return x.ReportsSkipped
	}
	return 0
}

func (x *ETLJob) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *ETLJob) GetStartedAt() string {
	if x != nil {
		return x.StartedAt
	}
	return ""
}

func (x *ETLJob) GetCompletedAt() string {
	if x != nil {
		return x.CompletedAt
	}
	return ""
}

func (x *ETLJob) GetLastUpdatedAt() string {
	if x != nil {
		return x.LastUpdatedAt
	}
	return ""
}

func (x *ETLJob) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

func (x *ETLJob) GetErrors() []*JobError {
	if x != nil {
		return x.Errors
	}
	return nil
}

func (x *ETLJob) GetGeneratedContent() []byte {
	if x != nil {
		return x.GeneratedContent
	}
	return nil
}

func (x *ETLJob) GetContentType() string {
	if x != nil {
		return x.ContentType
	}
	return ""
}

func (x *ETLJob) GetContentSizeBytes() int64 {
	if x != nil {
		return x.ContentSizeBytes
	}
	return 0
}

func (x *ETLJob) GetAgencySubmissionId() string {
	if x != nil {
		return x.AgencySubmissionId
	}
	return ""
}

func (x *ETLJob) GetSubmissionResponse() string {
	if x != nil {
		return x.SubmissionResponse
	}
	return ""
}

func (x *ETLJob) GetSubmittedAt() string {
	if x != nil {
		return x.SubmittedAt
	}
	return ""
}

func (x *ETLJob) GetRetryCount() int32 {
	if x != nil {
		return x.RetryCount
	}
	return 0
}

func (x *ETLJob) GetMaxRetries() int32 {
	if x != nil {
		return x.MaxRetries
	}
	return 0
}

func (x *ETLJob) GetLastRetryAt() string {
	if x != nil {
		return x.LastRetryAt
	}
	return ""
}

func (x *ETLJob) GetAutoRetryEnabled() bool {
	if x != nil {
		return x.AutoRetryEnabled
	}
	return false
}

func (x *ETLJob) GetCreatedByAssetId() string {
	if x != nil {
		return x.CreatedByAssetId
	}
	return ""
}

func (x *ETLJob) GetPreviewOnly() bool {
	if x != nil {
		return x.PreviewOnly
	}
	return false
}

func (x *ETLJob) GetJobName() string {
	if x != nil {
		return x.JobName
	}
	return ""
}

// Detailed error information for jobs
type JobError struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ErrorCode     string                 `protobuf:"bytes,1,opt,name=error_code,json=errorCode,proto3" json:"error_code,omitempty"`           // Error code (e.g., "INVALID_DATA", "NETWORK_ERROR")
	ErrorMessage  string                 `protobuf:"bytes,2,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`  // Human-readable error message
	ReportId      string                 `protobuf:"bytes,3,opt,name=report_id,json=reportId,proto3" json:"report_id,omitempty"`              // Specific report ID that caused error (optional)
	FieldPath     string                 `protobuf:"bytes,4,opt,name=field_path,json=fieldPath,proto3" json:"field_path,omitempty"`           // Field path that caused error (optional)
	OccurredAt    string                 `protobuf:"bytes,5,opt,name=occurred_at,json=occurredAt,proto3" json:"occurred_at,omitempty"`        // ISO8601 timestamp when error occurred
	IsRetryable   bool                   `protobuf:"varint,6,opt,name=is_retryable,json=isRetryable,proto3" json:"is_retryable,omitempty"`    // Whether this error type is retryable
	RetryAttempt  int32                  `protobuf:"varint,7,opt,name=retry_attempt,json=retryAttempt,proto3" json:"retry_attempt,omitempty"` // Which retry attempt this error occurred on (0 = initial)
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JobError) Reset() {
	*x = JobError{}
	mi := &file_hero_etl_v1_etl_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JobError) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobError) ProtoMessage() {}

func (x *JobError) ProtoReflect() protoreflect.Message {
	mi := &file_hero_etl_v1_etl_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobError.ProtoReflect.Descriptor instead.
func (*JobError) Descriptor() ([]byte, []int) {
	return file_hero_etl_v1_etl_proto_rawDescGZIP(), []int{1}
}

func (x *JobError) GetErrorCode() string {
	if x != nil {
		return x.ErrorCode
	}
	return ""
}

func (x *JobError) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

func (x *JobError) GetReportId() string {
	if x != nil {
		return x.ReportId
	}
	return ""
}

func (x *JobError) GetFieldPath() string {
	if x != nil {
		return x.FieldPath
	}
	return ""
}

func (x *JobError) GetOccurredAt() string {
	if x != nil {
		return x.OccurredAt
	}
	return ""
}

func (x *JobError) GetIsRetryable() bool {
	if x != nil {
		return x.IsRetryable
	}
	return false
}

func (x *JobError) GetRetryAttempt() int32 {
	if x != nil {
		return x.RetryAttempt
	}
	return 0
}

// Process reports for compliance reporting (supports both specific reports and date ranges)
type ProcessReportsRequest struct {
	state        protoimpl.MessageState `protogen:"open.v1"`
	AgencyId     string                 `protobuf:"bytes,1,opt,name=agency_id,json=agencyId,proto3" json:"agency_id,omitempty"`                                            // Target agency/recipient ID
	OutputFormat OutputFormat           `protobuf:"varint,2,opt,name=output_format,json=outputFormat,proto3,enum=hero.etl.v1.OutputFormat" json:"output_format,omitempty"` // Data standard/format (NIBRS, etc.)
	PreviewOnly  bool                   `protobuf:"varint,3,opt,name=preview_only,json=previewOnly,proto3" json:"preview_only,omitempty"`                                  // Generate but don't send to agency
	JobName      string                 `protobuf:"bytes,4,opt,name=job_name,json=jobName,proto3" json:"job_name,omitempty"`                                               // Optional human-readable job name
	// EITHER specify reports OR date range (not both)
	ReportIds []string `protobuf:"bytes,5,rep,name=report_ids,json=reportIds,proto3" json:"report_ids,omitempty"` // Specific reports to process
	// OR use date range with optional filters
	DateFrom  string   `protobuf:"bytes,6,opt,name=date_from,json=dateFrom,proto3" json:"date_from,omitempty"`    // ISO8601 start date (optional)
	DateTo    string   `protobuf:"bytes,7,opt,name=date_to,json=dateTo,proto3" json:"date_to,omitempty"`          // ISO8601 end date (optional)
	CaseTypes []string `protobuf:"bytes,8,rep,name=case_types,json=caseTypes,proto3" json:"case_types,omitempty"` // Filter by case types (optional)
	// Processing options
	SkipInvalidReports  bool `protobuf:"varint,9,opt,name=skip_invalid_reports,json=skipInvalidReports,proto3" json:"skip_invalid_reports,omitempty"`     // Skip reports with validation errors
	IncludeDraftReports bool `protobuf:"varint,10,opt,name=include_draft_reports,json=includeDraftReports,proto3" json:"include_draft_reports,omitempty"` // Include draft/incomplete reports
	// Retry configuration
	MaxRetries       int32 `protobuf:"varint,11,opt,name=max_retries,json=maxRetries,proto3" json:"max_retries,omitempty"`                     // Maximum retry attempts (default: 3)
	AutoRetryEnabled bool  `protobuf:"varint,12,opt,name=auto_retry_enabled,json=autoRetryEnabled,proto3" json:"auto_retry_enabled,omitempty"` // Enable automatic retries on failure (default: true)
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *ProcessReportsRequest) Reset() {
	*x = ProcessReportsRequest{}
	mi := &file_hero_etl_v1_etl_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProcessReportsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessReportsRequest) ProtoMessage() {}

func (x *ProcessReportsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_etl_v1_etl_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessReportsRequest.ProtoReflect.Descriptor instead.
func (*ProcessReportsRequest) Descriptor() ([]byte, []int) {
	return file_hero_etl_v1_etl_proto_rawDescGZIP(), []int{2}
}

func (x *ProcessReportsRequest) GetAgencyId() string {
	if x != nil {
		return x.AgencyId
	}
	return ""
}

func (x *ProcessReportsRequest) GetOutputFormat() OutputFormat {
	if x != nil {
		return x.OutputFormat
	}
	return OutputFormat_OUTPUT_FORMAT_UNSPECIFIED
}

func (x *ProcessReportsRequest) GetPreviewOnly() bool {
	if x != nil {
		return x.PreviewOnly
	}
	return false
}

func (x *ProcessReportsRequest) GetJobName() string {
	if x != nil {
		return x.JobName
	}
	return ""
}

func (x *ProcessReportsRequest) GetReportIds() []string {
	if x != nil {
		return x.ReportIds
	}
	return nil
}

func (x *ProcessReportsRequest) GetDateFrom() string {
	if x != nil {
		return x.DateFrom
	}
	return ""
}

func (x *ProcessReportsRequest) GetDateTo() string {
	if x != nil {
		return x.DateTo
	}
	return ""
}

func (x *ProcessReportsRequest) GetCaseTypes() []string {
	if x != nil {
		return x.CaseTypes
	}
	return nil
}

func (x *ProcessReportsRequest) GetSkipInvalidReports() bool {
	if x != nil {
		return x.SkipInvalidReports
	}
	return false
}

func (x *ProcessReportsRequest) GetIncludeDraftReports() bool {
	if x != nil {
		return x.IncludeDraftReports
	}
	return false
}

func (x *ProcessReportsRequest) GetMaxRetries() int32 {
	if x != nil {
		return x.MaxRetries
	}
	return 0
}

func (x *ProcessReportsRequest) GetAutoRetryEnabled() bool {
	if x != nil {
		return x.AutoRetryEnabled
	}
	return false
}

type ProcessReportsResponse struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	Job                  *ETLJob                `protobuf:"bytes,1,opt,name=job,proto3" json:"job,omitempty"`                                                                  // Created job
	EstimatedReportCount int32                  `protobuf:"varint,2,opt,name=estimated_report_count,json=estimatedReportCount,proto3" json:"estimated_report_count,omitempty"` // Estimated number of reports (if using date range)
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *ProcessReportsResponse) Reset() {
	*x = ProcessReportsResponse{}
	mi := &file_hero_etl_v1_etl_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProcessReportsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessReportsResponse) ProtoMessage() {}

func (x *ProcessReportsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_etl_v1_etl_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessReportsResponse.ProtoReflect.Descriptor instead.
func (*ProcessReportsResponse) Descriptor() ([]byte, []int) {
	return file_hero_etl_v1_etl_proto_rawDescGZIP(), []int{3}
}

func (x *ProcessReportsResponse) GetJob() *ETLJob {
	if x != nil {
		return x.Job
	}
	return nil
}

func (x *ProcessReportsResponse) GetEstimatedReportCount() int32 {
	if x != nil {
		return x.EstimatedReportCount
	}
	return 0
}

// Get job status and results
type GetJobRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	JobId         string                 `protobuf:"bytes,1,opt,name=job_id,json=jobId,proto3" json:"job_id,omitempty"` // Job ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetJobRequest) Reset() {
	*x = GetJobRequest{}
	mi := &file_hero_etl_v1_etl_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJobRequest) ProtoMessage() {}

func (x *GetJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_etl_v1_etl_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJobRequest.ProtoReflect.Descriptor instead.
func (*GetJobRequest) Descriptor() ([]byte, []int) {
	return file_hero_etl_v1_etl_proto_rawDescGZIP(), []int{4}
}

func (x *GetJobRequest) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

type GetJobResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Job           *ETLJob                `protobuf:"bytes,1,opt,name=job,proto3" json:"job,omitempty"` // Job details
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetJobResponse) Reset() {
	*x = GetJobResponse{}
	mi := &file_hero_etl_v1_etl_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetJobResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJobResponse) ProtoMessage() {}

func (x *GetJobResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_etl_v1_etl_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJobResponse.ProtoReflect.Descriptor instead.
func (*GetJobResponse) Descriptor() ([]byte, []int) {
	return file_hero_etl_v1_etl_proto_rawDescGZIP(), []int{5}
}

func (x *GetJobResponse) GetJob() *ETLJob {
	if x != nil {
		return x.Job
	}
	return nil
}

// List jobs with filtering
type ListJobsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AgencyId      string                 `protobuf:"bytes,1,opt,name=agency_id,json=agencyId,proto3" json:"agency_id,omitempty"`          // Filter by agency (optional)
	Status        JobStatus              `protobuf:"varint,2,opt,name=status,proto3,enum=hero.etl.v1.JobStatus" json:"status,omitempty"`  // Filter by status (optional)
	CreatedFrom   string                 `protobuf:"bytes,3,opt,name=created_from,json=createdFrom,proto3" json:"created_from,omitempty"` // ISO8601 start of creation date range (optional)
	CreatedTo     string                 `protobuf:"bytes,4,opt,name=created_to,json=createdTo,proto3" json:"created_to,omitempty"`       // ISO8601 end of creation date range (optional)
	PageSize      int32                  `protobuf:"varint,5,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`         // Max results per page
	PageToken     string                 `protobuf:"bytes,6,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`       // Pagination token
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListJobsRequest) Reset() {
	*x = ListJobsRequest{}
	mi := &file_hero_etl_v1_etl_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListJobsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListJobsRequest) ProtoMessage() {}

func (x *ListJobsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_etl_v1_etl_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListJobsRequest.ProtoReflect.Descriptor instead.
func (*ListJobsRequest) Descriptor() ([]byte, []int) {
	return file_hero_etl_v1_etl_proto_rawDescGZIP(), []int{6}
}

func (x *ListJobsRequest) GetAgencyId() string {
	if x != nil {
		return x.AgencyId
	}
	return ""
}

func (x *ListJobsRequest) GetStatus() JobStatus {
	if x != nil {
		return x.Status
	}
	return JobStatus_JOB_STATUS_UNSPECIFIED
}

func (x *ListJobsRequest) GetCreatedFrom() string {
	if x != nil {
		return x.CreatedFrom
	}
	return ""
}

func (x *ListJobsRequest) GetCreatedTo() string {
	if x != nil {
		return x.CreatedTo
	}
	return ""
}

func (x *ListJobsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListJobsRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

type ListJobsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Jobs          []*ETLJob              `protobuf:"bytes,1,rep,name=jobs,proto3" json:"jobs,omitempty"`                                          // Jobs
	NextPageToken string                 `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"` // Next page token
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListJobsResponse) Reset() {
	*x = ListJobsResponse{}
	mi := &file_hero_etl_v1_etl_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListJobsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListJobsResponse) ProtoMessage() {}

func (x *ListJobsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_etl_v1_etl_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListJobsResponse.ProtoReflect.Descriptor instead.
func (*ListJobsResponse) Descriptor() ([]byte, []int) {
	return file_hero_etl_v1_etl_proto_rawDescGZIP(), []int{7}
}

func (x *ListJobsResponse) GetJobs() []*ETLJob {
	if x != nil {
		return x.Jobs
	}
	return nil
}

func (x *ListJobsResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

// Cancel a running job
type CancelJobRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	JobId         string                 `protobuf:"bytes,1,opt,name=job_id,json=jobId,proto3" json:"job_id,omitempty"` // Job to cancel
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CancelJobRequest) Reset() {
	*x = CancelJobRequest{}
	mi := &file_hero_etl_v1_etl_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CancelJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelJobRequest) ProtoMessage() {}

func (x *CancelJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_etl_v1_etl_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelJobRequest.ProtoReflect.Descriptor instead.
func (*CancelJobRequest) Descriptor() ([]byte, []int) {
	return file_hero_etl_v1_etl_proto_rawDescGZIP(), []int{8}
}

func (x *CancelJobRequest) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

type CancelJobResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Job           *ETLJob                `protobuf:"bytes,1,opt,name=job,proto3" json:"job,omitempty"` // Updated job
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CancelJobResponse) Reset() {
	*x = CancelJobResponse{}
	mi := &file_hero_etl_v1_etl_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CancelJobResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelJobResponse) ProtoMessage() {}

func (x *CancelJobResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_etl_v1_etl_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelJobResponse.ProtoReflect.Descriptor instead.
func (*CancelJobResponse) Descriptor() ([]byte, []int) {
	return file_hero_etl_v1_etl_proto_rawDescGZIP(), []int{9}
}

func (x *CancelJobResponse) GetJob() *ETLJob {
	if x != nil {
		return x.Job
	}
	return nil
}

// Retry a failed job
type RetryJobRequest struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	JobId              string                 `protobuf:"bytes,1,opt,name=job_id,json=jobId,proto3" json:"job_id,omitempty"`                                           // Job to retry
	ForceRetry         bool                   `protobuf:"varint,2,opt,name=force_retry,json=forceRetry,proto3" json:"force_retry,omitempty"`                           // Force retry even if max retries exceeded
	OverrideMaxRetries int32                  `protobuf:"varint,3,opt,name=override_max_retries,json=overrideMaxRetries,proto3" json:"override_max_retries,omitempty"` // Override max retries for this attempt (optional)
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *RetryJobRequest) Reset() {
	*x = RetryJobRequest{}
	mi := &file_hero_etl_v1_etl_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RetryJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetryJobRequest) ProtoMessage() {}

func (x *RetryJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_etl_v1_etl_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetryJobRequest.ProtoReflect.Descriptor instead.
func (*RetryJobRequest) Descriptor() ([]byte, []int) {
	return file_hero_etl_v1_etl_proto_rawDescGZIP(), []int{10}
}

func (x *RetryJobRequest) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

func (x *RetryJobRequest) GetForceRetry() bool {
	if x != nil {
		return x.ForceRetry
	}
	return false
}

func (x *RetryJobRequest) GetOverrideMaxRetries() int32 {
	if x != nil {
		return x.OverrideMaxRetries
	}
	return 0
}

type RetryJobResponse struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Job            *ETLJob                `protobuf:"bytes,1,opt,name=job,proto3" json:"job,omitempty"`                                              // Updated job with retry information
	RetryScheduled bool                   `protobuf:"varint,2,opt,name=retry_scheduled,json=retryScheduled,proto3" json:"retry_scheduled,omitempty"` // Whether retry was successfully scheduled
	RetryReason    string                 `protobuf:"bytes,3,opt,name=retry_reason,json=retryReason,proto3" json:"retry_reason,omitempty"`           // Reason if retry was not scheduled
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *RetryJobResponse) Reset() {
	*x = RetryJobResponse{}
	mi := &file_hero_etl_v1_etl_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RetryJobResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetryJobResponse) ProtoMessage() {}

func (x *RetryJobResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_etl_v1_etl_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetryJobResponse.ProtoReflect.Descriptor instead.
func (*RetryJobResponse) Descriptor() ([]byte, []int) {
	return file_hero_etl_v1_etl_proto_rawDescGZIP(), []int{11}
}

func (x *RetryJobResponse) GetJob() *ETLJob {
	if x != nil {
		return x.Job
	}
	return nil
}

func (x *RetryJobResponse) GetRetryScheduled() bool {
	if x != nil {
		return x.RetryScheduled
	}
	return false
}

func (x *RetryJobResponse) GetRetryReason() string {
	if x != nil {
		return x.RetryReason
	}
	return ""
}

// Download generated content
type DownloadJobContentRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	JobId         string                 `protobuf:"bytes,1,opt,name=job_id,json=jobId,proto3" json:"job_id,omitempty"` // Job ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DownloadJobContentRequest) Reset() {
	*x = DownloadJobContentRequest{}
	mi := &file_hero_etl_v1_etl_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DownloadJobContentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DownloadJobContentRequest) ProtoMessage() {}

func (x *DownloadJobContentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_etl_v1_etl_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DownloadJobContentRequest.ProtoReflect.Descriptor instead.
func (*DownloadJobContentRequest) Descriptor() ([]byte, []int) {
	return file_hero_etl_v1_etl_proto_rawDescGZIP(), []int{12}
}

func (x *DownloadJobContentRequest) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

type DownloadJobContentResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Content       []byte                 `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`                            // Generated content
	ContentType   string                 `protobuf:"bytes,2,opt,name=content_type,json=contentType,proto3" json:"content_type,omitempty"` // MIME type
	Filename      string                 `protobuf:"bytes,3,opt,name=filename,proto3" json:"filename,omitempty"`                          // Suggested filename
	SizeBytes     int64                  `protobuf:"varint,4,opt,name=size_bytes,json=sizeBytes,proto3" json:"size_bytes,omitempty"`      // Content size
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DownloadJobContentResponse) Reset() {
	*x = DownloadJobContentResponse{}
	mi := &file_hero_etl_v1_etl_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DownloadJobContentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DownloadJobContentResponse) ProtoMessage() {}

func (x *DownloadJobContentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_etl_v1_etl_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DownloadJobContentResponse.ProtoReflect.Descriptor instead.
func (*DownloadJobContentResponse) Descriptor() ([]byte, []int) {
	return file_hero_etl_v1_etl_proto_rawDescGZIP(), []int{13}
}

func (x *DownloadJobContentResponse) GetContent() []byte {
	if x != nil {
		return x.Content
	}
	return nil
}

func (x *DownloadJobContentResponse) GetContentType() string {
	if x != nil {
		return x.ContentType
	}
	return ""
}

func (x *DownloadJobContentResponse) GetFilename() string {
	if x != nil {
		return x.Filename
	}
	return ""
}

func (x *DownloadJobContentResponse) GetSizeBytes() int64 {
	if x != nil {
		return x.SizeBytes
	}
	return 0
}

// Get list of available agencies
type ListAgenciesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAgenciesRequest) Reset() {
	*x = ListAgenciesRequest{}
	mi := &file_hero_etl_v1_etl_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAgenciesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAgenciesRequest) ProtoMessage() {}

func (x *ListAgenciesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_etl_v1_etl_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAgenciesRequest.ProtoReflect.Descriptor instead.
func (*ListAgenciesRequest) Descriptor() ([]byte, []int) {
	return file_hero_etl_v1_etl_proto_rawDescGZIP(), []int{14}
}

type ListAgenciesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Agencies      []*AgencyInfo          `protobuf:"bytes,1,rep,name=agencies,proto3" json:"agencies,omitempty"` // Available agencies
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAgenciesResponse) Reset() {
	*x = ListAgenciesResponse{}
	mi := &file_hero_etl_v1_etl_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAgenciesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAgenciesResponse) ProtoMessage() {}

func (x *ListAgenciesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_etl_v1_etl_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAgenciesResponse.ProtoReflect.Descriptor instead.
func (*ListAgenciesResponse) Descriptor() ([]byte, []int) {
	return file_hero_etl_v1_etl_proto_rawDescGZIP(), []int{15}
}

func (x *ListAgenciesResponse) GetAgencies() []*AgencyInfo {
	if x != nil {
		return x.Agencies
	}
	return nil
}

// Agency/recipient information with supported data standards
type AgencyInfo struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Id               string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                                                                           // Agency ID (e.g., "FBI", "STATE_POLICE", "UNIVERSITY_POLICE")
	Name             string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                                                                                       // Display name
	SupportedFormats []OutputFormat         `protobuf:"varint,3,rep,packed,name=supported_formats,json=supportedFormats,proto3,enum=hero.etl.v1.OutputFormat" json:"supported_formats,omitempty"` // Supported data standards (NIBRS, etc.)
	Enabled          bool                   `protobuf:"varint,4,opt,name=enabled,proto3" json:"enabled,omitempty"`                                                                                // Whether agency is enabled
	AgencyType       string                 `protobuf:"bytes,5,opt,name=agency_type,json=agencyType,proto3" json:"agency_type,omitempty"`                                                         // Type: "FEDERAL", "STATE", "LOCAL", "UNIVERSITY"
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *AgencyInfo) Reset() {
	*x = AgencyInfo{}
	mi := &file_hero_etl_v1_etl_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AgencyInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgencyInfo) ProtoMessage() {}

func (x *AgencyInfo) ProtoReflect() protoreflect.Message {
	mi := &file_hero_etl_v1_etl_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgencyInfo.ProtoReflect.Descriptor instead.
func (*AgencyInfo) Descriptor() ([]byte, []int) {
	return file_hero_etl_v1_etl_proto_rawDescGZIP(), []int{16}
}

func (x *AgencyInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *AgencyInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AgencyInfo) GetSupportedFormats() []OutputFormat {
	if x != nil {
		return x.SupportedFormats
	}
	return nil
}

func (x *AgencyInfo) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *AgencyInfo) GetAgencyType() string {
	if x != nil {
		return x.AgencyType
	}
	return ""
}

// Extract raw report data without transformation
type ExtractReportDataRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ReportId      string                 `protobuf:"bytes,1,opt,name=report_id,json=reportId,proto3" json:"report_id,omitempty"` // Single report to extract data from
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExtractReportDataRequest) Reset() {
	*x = ExtractReportDataRequest{}
	mi := &file_hero_etl_v1_etl_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExtractReportDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExtractReportDataRequest) ProtoMessage() {}

func (x *ExtractReportDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_etl_v1_etl_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExtractReportDataRequest.ProtoReflect.Descriptor instead.
func (*ExtractReportDataRequest) Descriptor() ([]byte, []int) {
	return file_hero_etl_v1_etl_proto_rawDescGZIP(), []int{17}
}

func (x *ExtractReportDataRequest) GetReportId() string {
	if x != nil {
		return x.ReportId
	}
	return ""
}

type ExtractReportDataResponse struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	ExtractedData  *structpb.Struct       `protobuf:"bytes,1,opt,name=extracted_data,json=extractedData,proto3" json:"extracted_data,omitempty"`    // Complete extracted data as structured object
	ExtractionTime string                 `protobuf:"bytes,2,opt,name=extraction_time,json=extractionTime,proto3" json:"extraction_time,omitempty"` // ISO8601 timestamp when extraction was done
	Warnings       []string               `protobuf:"bytes,3,rep,name=warnings,proto3" json:"warnings,omitempty"`                                   // Extraction warnings
	Success        bool                   `protobuf:"varint,4,opt,name=success,proto3" json:"success,omitempty"`                                    // Whether extraction succeeded
	ErrorMessage   string                 `protobuf:"bytes,5,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`       // Error if failed
	DataSetsCount  int32                  `protobuf:"varint,6,opt,name=data_sets_count,json=dataSetsCount,proto3" json:"data_sets_count,omitempty"` // Number of data sets extracted
	DataSetNames   []string               `protobuf:"bytes,7,rep,name=data_set_names,json=dataSetNames,proto3" json:"data_set_names,omitempty"`     // Names of extracted data sets
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ExtractReportDataResponse) Reset() {
	*x = ExtractReportDataResponse{}
	mi := &file_hero_etl_v1_etl_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExtractReportDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExtractReportDataResponse) ProtoMessage() {}

func (x *ExtractReportDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_etl_v1_etl_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExtractReportDataResponse.ProtoReflect.Descriptor instead.
func (*ExtractReportDataResponse) Descriptor() ([]byte, []int) {
	return file_hero_etl_v1_etl_proto_rawDescGZIP(), []int{18}
}

func (x *ExtractReportDataResponse) GetExtractedData() *structpb.Struct {
	if x != nil {
		return x.ExtractedData
	}
	return nil
}

func (x *ExtractReportDataResponse) GetExtractionTime() string {
	if x != nil {
		return x.ExtractionTime
	}
	return ""
}

func (x *ExtractReportDataResponse) GetWarnings() []string {
	if x != nil {
		return x.Warnings
	}
	return nil
}

func (x *ExtractReportDataResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *ExtractReportDataResponse) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

func (x *ExtractReportDataResponse) GetDataSetsCount() int32 {
	if x != nil {
		return x.DataSetsCount
	}
	return 0
}

func (x *ExtractReportDataResponse) GetDataSetNames() []string {
	if x != nil {
		return x.DataSetNames
	}
	return nil
}

// Test report transformation - complete transformation pipeline with formatting
type TestReportTransformationRequest struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	ReportId          string                 `protobuf:"bytes,1,opt,name=report_id,json=reportId,proto3" json:"report_id,omitempty"`                                            // Single report to test
	OutputFormat      OutputFormat           `protobuf:"varint,2,opt,name=output_format,json=outputFormat,proto3,enum=hero.etl.v1.OutputFormat" json:"output_format,omitempty"` // Target format (NIBRS_XML, etc.)
	Validate          bool                   `protobuf:"varint,3,opt,name=validate,proto3" json:"validate,omitempty"`                                                           // Run validation on output (optional)
	MappingConfigJson string                 `protobuf:"bytes,4,opt,name=mapping_config_json,json=mappingConfigJson,proto3" json:"mapping_config_json,omitempty"`               // Optional: Override mapping config (JSON string)
	TemplateContent   string                 `protobuf:"bytes,5,opt,name=template_content,json=templateContent,proto3" json:"template_content,omitempty"`                       // Optional: Override template content
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *TestReportTransformationRequest) Reset() {
	*x = TestReportTransformationRequest{}
	mi := &file_hero_etl_v1_etl_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TestReportTransformationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestReportTransformationRequest) ProtoMessage() {}

func (x *TestReportTransformationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_etl_v1_etl_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestReportTransformationRequest.ProtoReflect.Descriptor instead.
func (*TestReportTransformationRequest) Descriptor() ([]byte, []int) {
	return file_hero_etl_v1_etl_proto_rawDescGZIP(), []int{19}
}

func (x *TestReportTransformationRequest) GetReportId() string {
	if x != nil {
		return x.ReportId
	}
	return ""
}

func (x *TestReportTransformationRequest) GetOutputFormat() OutputFormat {
	if x != nil {
		return x.OutputFormat
	}
	return OutputFormat_OUTPUT_FORMAT_UNSPECIFIED
}

func (x *TestReportTransformationRequest) GetValidate() bool {
	if x != nil {
		return x.Validate
	}
	return false
}

func (x *TestReportTransformationRequest) GetMappingConfigJson() string {
	if x != nil {
		return x.MappingConfigJson
	}
	return ""
}

func (x *TestReportTransformationRequest) GetTemplateContent() string {
	if x != nil {
		return x.TemplateContent
	}
	return ""
}

type TestReportTransformationResponse struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	TransformedContent []byte                 `protobuf:"bytes,1,opt,name=transformed_content,json=transformedContent,proto3" json:"transformed_content,omitempty"` // Generated content
	ContentType        string                 `protobuf:"bytes,2,opt,name=content_type,json=contentType,proto3" json:"content_type,omitempty"`                      // MIME type
	TransformationTime string                 `protobuf:"bytes,3,opt,name=transformation_time,json=transformationTime,proto3" json:"transformation_time,omitempty"` // ISO8601 timestamp when transformation was done
	Warnings           []string               `protobuf:"bytes,4,rep,name=warnings,proto3" json:"warnings,omitempty"`                                               // Transformation warnings
	Success            bool                   `protobuf:"varint,5,opt,name=success,proto3" json:"success,omitempty"`                                                // Whether transformation succeeded
	ErrorMessage       string                 `protobuf:"bytes,6,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`                   // Error if failed
	ValidationErrors   []*ValidationError     `protobuf:"bytes,7,rep,name=validation_errors,json=validationErrors,proto3" json:"validation_errors,omitempty"`       // Detailed validation errors
	ReadableContent    string                 `protobuf:"bytes,8,opt,name=readable_content,json=readableContent,proto3" json:"readable_content,omitempty"`          // Human-readable version of transformed_content (for debugging)
	MappingConfigUsed  string                 `protobuf:"bytes,9,opt,name=mapping_config_used,json=mappingConfigUsed,proto3" json:"mapping_config_used,omitempty"`  // Mapping config file path used
	TemplateUsed       string                 `protobuf:"bytes,10,opt,name=template_used,json=templateUsed,proto3" json:"template_used,omitempty"`                  // Template file path used for formatting
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *TestReportTransformationResponse) Reset() {
	*x = TestReportTransformationResponse{}
	mi := &file_hero_etl_v1_etl_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TestReportTransformationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestReportTransformationResponse) ProtoMessage() {}

func (x *TestReportTransformationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_etl_v1_etl_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestReportTransformationResponse.ProtoReflect.Descriptor instead.
func (*TestReportTransformationResponse) Descriptor() ([]byte, []int) {
	return file_hero_etl_v1_etl_proto_rawDescGZIP(), []int{20}
}

func (x *TestReportTransformationResponse) GetTransformedContent() []byte {
	if x != nil {
		return x.TransformedContent
	}
	return nil
}

func (x *TestReportTransformationResponse) GetContentType() string {
	if x != nil {
		return x.ContentType
	}
	return ""
}

func (x *TestReportTransformationResponse) GetTransformationTime() string {
	if x != nil {
		return x.TransformationTime
	}
	return ""
}

func (x *TestReportTransformationResponse) GetWarnings() []string {
	if x != nil {
		return x.Warnings
	}
	return nil
}

func (x *TestReportTransformationResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *TestReportTransformationResponse) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

func (x *TestReportTransformationResponse) GetValidationErrors() []*ValidationError {
	if x != nil {
		return x.ValidationErrors
	}
	return nil
}

func (x *TestReportTransformationResponse) GetReadableContent() string {
	if x != nil {
		return x.ReadableContent
	}
	return ""
}

func (x *TestReportTransformationResponse) GetMappingConfigUsed() string {
	if x != nil {
		return x.MappingConfigUsed
	}
	return ""
}

func (x *TestReportTransformationResponse) GetTemplateUsed() string {
	if x != nil {
		return x.TemplateUsed
	}
	return ""
}

// Test mapping configuration transformation without template formatting
type TestReportMappingRequest struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	ReportId          string                 `protobuf:"bytes,1,opt,name=report_id,json=reportId,proto3" json:"report_id,omitempty"`                                            // Single report to test
	OutputFormat      OutputFormat           `protobuf:"varint,2,opt,name=output_format,json=outputFormat,proto3,enum=hero.etl.v1.OutputFormat" json:"output_format,omitempty"` // Output format/standard (NIBRS, etc.)
	MappingConfigJson string                 `protobuf:"bytes,3,opt,name=mapping_config_json,json=mappingConfigJson,proto3" json:"mapping_config_json,omitempty"`               // Optional: Override mapping config (JSON string)
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *TestReportMappingRequest) Reset() {
	*x = TestReportMappingRequest{}
	mi := &file_hero_etl_v1_etl_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TestReportMappingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestReportMappingRequest) ProtoMessage() {}

func (x *TestReportMappingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_etl_v1_etl_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestReportMappingRequest.ProtoReflect.Descriptor instead.
func (*TestReportMappingRequest) Descriptor() ([]byte, []int) {
	return file_hero_etl_v1_etl_proto_rawDescGZIP(), []int{21}
}

func (x *TestReportMappingRequest) GetReportId() string {
	if x != nil {
		return x.ReportId
	}
	return ""
}

func (x *TestReportMappingRequest) GetOutputFormat() OutputFormat {
	if x != nil {
		return x.OutputFormat
	}
	return OutputFormat_OUTPUT_FORMAT_UNSPECIFIED
}

func (x *TestReportMappingRequest) GetMappingConfigJson() string {
	if x != nil {
		return x.MappingConfigJson
	}
	return ""
}

type TestReportMappingResponse struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	MappedData         *structpb.Struct       `protobuf:"bytes,1,opt,name=mapped_data,json=mappedData,proto3" json:"mapped_data,omitempty"`                         // Config-mapped data as structured object
	ContentType        string                 `protobuf:"bytes,2,opt,name=content_type,json=contentType,proto3" json:"content_type,omitempty"`                      // MIME type (application/json)
	MappingConfigUsed  string                 `protobuf:"bytes,3,opt,name=mapping_config_used,json=mappingConfigUsed,proto3" json:"mapping_config_used,omitempty"`  // Config file path used
	TransformationTime string                 `protobuf:"bytes,4,opt,name=transformation_time,json=transformationTime,proto3" json:"transformation_time,omitempty"` // ISO8601 timestamp when mapping was done
	Warnings           []string               `protobuf:"bytes,5,rep,name=warnings,proto3" json:"warnings,omitempty"`                                               // Mapping warnings
	Success            bool                   `protobuf:"varint,6,opt,name=success,proto3" json:"success,omitempty"`                                                // Whether mapping succeeded
	ErrorMessage       string                 `protobuf:"bytes,7,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`                   // Error if failed
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *TestReportMappingResponse) Reset() {
	*x = TestReportMappingResponse{}
	mi := &file_hero_etl_v1_etl_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TestReportMappingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestReportMappingResponse) ProtoMessage() {}

func (x *TestReportMappingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_etl_v1_etl_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestReportMappingResponse.ProtoReflect.Descriptor instead.
func (*TestReportMappingResponse) Descriptor() ([]byte, []int) {
	return file_hero_etl_v1_etl_proto_rawDescGZIP(), []int{22}
}

func (x *TestReportMappingResponse) GetMappedData() *structpb.Struct {
	if x != nil {
		return x.MappedData
	}
	return nil
}

func (x *TestReportMappingResponse) GetContentType() string {
	if x != nil {
		return x.ContentType
	}
	return ""
}

func (x *TestReportMappingResponse) GetMappingConfigUsed() string {
	if x != nil {
		return x.MappingConfigUsed
	}
	return ""
}

func (x *TestReportMappingResponse) GetTransformationTime() string {
	if x != nil {
		return x.TransformationTime
	}
	return ""
}

func (x *TestReportMappingResponse) GetWarnings() []string {
	if x != nil {
		return x.Warnings
	}
	return nil
}

func (x *TestReportMappingResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *TestReportMappingResponse) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

// Validation error details
type ValidationError struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FieldPath     string                 `protobuf:"bytes,1,opt,name=field_path,json=fieldPath,proto3" json:"field_path,omitempty"`             // Field path that failed validation
	ErrorCode     string                 `protobuf:"bytes,2,opt,name=error_code,json=errorCode,proto3" json:"error_code,omitempty"`             // Error code (e.g., "REQUIRED_FIELD", "INVALID_FORMAT")
	ErrorMessage  string                 `protobuf:"bytes,3,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`    // Human-readable error message
	ExpectedValue string                 `protobuf:"bytes,4,opt,name=expected_value,json=expectedValue,proto3" json:"expected_value,omitempty"` // Expected value or format (optional)
	ActualValue   string                 `protobuf:"bytes,5,opt,name=actual_value,json=actualValue,proto3" json:"actual_value,omitempty"`       // Actual value that failed (optional)
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ValidationError) Reset() {
	*x = ValidationError{}
	mi := &file_hero_etl_v1_etl_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ValidationError) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidationError) ProtoMessage() {}

func (x *ValidationError) ProtoReflect() protoreflect.Message {
	mi := &file_hero_etl_v1_etl_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidationError.ProtoReflect.Descriptor instead.
func (*ValidationError) Descriptor() ([]byte, []int) {
	return file_hero_etl_v1_etl_proto_rawDescGZIP(), []int{23}
}

func (x *ValidationError) GetFieldPath() string {
	if x != nil {
		return x.FieldPath
	}
	return ""
}

func (x *ValidationError) GetErrorCode() string {
	if x != nil {
		return x.ErrorCode
	}
	return ""
}

func (x *ValidationError) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

func (x *ValidationError) GetExpectedValue() string {
	if x != nil {
		return x.ExpectedValue
	}
	return ""
}

func (x *ValidationError) GetActualValue() string {
	if x != nil {
		return x.ActualValue
	}
	return ""
}

// Get job statistics
type GetJobStatsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AgencyId      string                 `protobuf:"bytes,1,opt,name=agency_id,json=agencyId,proto3" json:"agency_id,omitempty"`                                            // Filter by agency (optional)
	OutputFormat  OutputFormat           `protobuf:"varint,2,opt,name=output_format,json=outputFormat,proto3,enum=hero.etl.v1.OutputFormat" json:"output_format,omitempty"` // Filter by format (optional)
	DateFrom      string                 `protobuf:"bytes,3,opt,name=date_from,json=dateFrom,proto3" json:"date_from,omitempty"`                                            // ISO8601 start date (optional)
	DateTo        string                 `protobuf:"bytes,4,opt,name=date_to,json=dateTo,proto3" json:"date_to,omitempty"`                                                  // ISO8601 end date (optional)
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetJobStatsRequest) Reset() {
	*x = GetJobStatsRequest{}
	mi := &file_hero_etl_v1_etl_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetJobStatsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJobStatsRequest) ProtoMessage() {}

func (x *GetJobStatsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_etl_v1_etl_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJobStatsRequest.ProtoReflect.Descriptor instead.
func (*GetJobStatsRequest) Descriptor() ([]byte, []int) {
	return file_hero_etl_v1_etl_proto_rawDescGZIP(), []int{24}
}

func (x *GetJobStatsRequest) GetAgencyId() string {
	if x != nil {
		return x.AgencyId
	}
	return ""
}

func (x *GetJobStatsRequest) GetOutputFormat() OutputFormat {
	if x != nil {
		return x.OutputFormat
	}
	return OutputFormat_OUTPUT_FORMAT_UNSPECIFIED
}

func (x *GetJobStatsRequest) GetDateFrom() string {
	if x != nil {
		return x.DateFrom
	}
	return ""
}

func (x *GetJobStatsRequest) GetDateTo() string {
	if x != nil {
		return x.DateTo
	}
	return ""
}

type GetJobStatsResponse struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	TotalJobs             int32                  `protobuf:"varint,1,opt,name=total_jobs,json=totalJobs,proto3" json:"total_jobs,omitempty"`                                         // Total jobs in period
	CompletedJobs         int32                  `protobuf:"varint,2,opt,name=completed_jobs,json=completedJobs,proto3" json:"completed_jobs,omitempty"`                             // Successfully completed jobs
	FailedJobs            int32                  `protobuf:"varint,3,opt,name=failed_jobs,json=failedJobs,proto3" json:"failed_jobs,omitempty"`                                      // Failed jobs
	CancelledJobs         int32                  `protobuf:"varint,4,opt,name=cancelled_jobs,json=cancelledJobs,proto3" json:"cancelled_jobs,omitempty"`                             // Cancelled jobs
	RunningJobs           int32                  `protobuf:"varint,5,opt,name=running_jobs,json=runningJobs,proto3" json:"running_jobs,omitempty"`                                   // Currently running jobs
	TotalReportsProcessed int32                  `protobuf:"varint,6,opt,name=total_reports_processed,json=totalReportsProcessed,proto3" json:"total_reports_processed,omitempty"`   // Total reports processed
	TotalContentSizeBytes int64                  `protobuf:"varint,7,opt,name=total_content_size_bytes,json=totalContentSizeBytes,proto3" json:"total_content_size_bytes,omitempty"` // Total content generated
	AgencyStats           []*AgencyStats         `protobuf:"bytes,8,rep,name=agency_stats,json=agencyStats,proto3" json:"agency_stats,omitempty"`                                    // Per-agency statistics
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *GetJobStatsResponse) Reset() {
	*x = GetJobStatsResponse{}
	mi := &file_hero_etl_v1_etl_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetJobStatsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJobStatsResponse) ProtoMessage() {}

func (x *GetJobStatsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_etl_v1_etl_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJobStatsResponse.ProtoReflect.Descriptor instead.
func (*GetJobStatsResponse) Descriptor() ([]byte, []int) {
	return file_hero_etl_v1_etl_proto_rawDescGZIP(), []int{25}
}

func (x *GetJobStatsResponse) GetTotalJobs() int32 {
	if x != nil {
		return x.TotalJobs
	}
	return 0
}

func (x *GetJobStatsResponse) GetCompletedJobs() int32 {
	if x != nil {
		return x.CompletedJobs
	}
	return 0
}

func (x *GetJobStatsResponse) GetFailedJobs() int32 {
	if x != nil {
		return x.FailedJobs
	}
	return 0
}

func (x *GetJobStatsResponse) GetCancelledJobs() int32 {
	if x != nil {
		return x.CancelledJobs
	}
	return 0
}

func (x *GetJobStatsResponse) GetRunningJobs() int32 {
	if x != nil {
		return x.RunningJobs
	}
	return 0
}

func (x *GetJobStatsResponse) GetTotalReportsProcessed() int32 {
	if x != nil {
		return x.TotalReportsProcessed
	}
	return 0
}

func (x *GetJobStatsResponse) GetTotalContentSizeBytes() int64 {
	if x != nil {
		return x.TotalContentSizeBytes
	}
	return 0
}

func (x *GetJobStatsResponse) GetAgencyStats() []*AgencyStats {
	if x != nil {
		return x.AgencyStats
	}
	return nil
}

// Per-agency statistics
type AgencyStats struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	AgencyId         string                 `protobuf:"bytes,1,opt,name=agency_id,json=agencyId,proto3" json:"agency_id,omitempty"`                            // Agency ID
	AgencyName       string                 `protobuf:"bytes,2,opt,name=agency_name,json=agencyName,proto3" json:"agency_name,omitempty"`                      // Agency name
	JobCount         int32                  `protobuf:"varint,3,opt,name=job_count,json=jobCount,proto3" json:"job_count,omitempty"`                           // Number of jobs for this agency
	ReportCount      int32                  `protobuf:"varint,4,opt,name=report_count,json=reportCount,proto3" json:"report_count,omitempty"`                  // Number of reports processed
	ContentSizeBytes int64                  `protobuf:"varint,5,opt,name=content_size_bytes,json=contentSizeBytes,proto3" json:"content_size_bytes,omitempty"` // Total content size for agency
	LastSubmissionAt string                 `protobuf:"bytes,6,opt,name=last_submission_at,json=lastSubmissionAt,proto3" json:"last_submission_at,omitempty"`  // ISO8601 timestamp of last submission
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *AgencyStats) Reset() {
	*x = AgencyStats{}
	mi := &file_hero_etl_v1_etl_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AgencyStats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgencyStats) ProtoMessage() {}

func (x *AgencyStats) ProtoReflect() protoreflect.Message {
	mi := &file_hero_etl_v1_etl_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgencyStats.ProtoReflect.Descriptor instead.
func (*AgencyStats) Descriptor() ([]byte, []int) {
	return file_hero_etl_v1_etl_proto_rawDescGZIP(), []int{26}
}

func (x *AgencyStats) GetAgencyId() string {
	if x != nil {
		return x.AgencyId
	}
	return ""
}

func (x *AgencyStats) GetAgencyName() string {
	if x != nil {
		return x.AgencyName
	}
	return ""
}

func (x *AgencyStats) GetJobCount() int32 {
	if x != nil {
		return x.JobCount
	}
	return 0
}

func (x *AgencyStats) GetReportCount() int32 {
	if x != nil {
		return x.ReportCount
	}
	return 0
}

func (x *AgencyStats) GetContentSizeBytes() int64 {
	if x != nil {
		return x.ContentSizeBytes
	}
	return 0
}

func (x *AgencyStats) GetLastSubmissionAt() string {
	if x != nil {
		return x.LastSubmissionAt
	}
	return ""
}

// Validate reports before processing
type ValidateReportsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ReportIds     []string               `protobuf:"bytes,1,rep,name=report_ids,json=reportIds,proto3" json:"report_ids,omitempty"`                                         // Reports to validate
	OutputFormat  OutputFormat           `protobuf:"varint,2,opt,name=output_format,json=outputFormat,proto3,enum=hero.etl.v1.OutputFormat" json:"output_format,omitempty"` // Target data standard
	AgencyId      string                 `protobuf:"bytes,3,opt,name=agency_id,json=agencyId,proto3" json:"agency_id,omitempty"`                                            // Target agency (optional)
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ValidateReportsRequest) Reset() {
	*x = ValidateReportsRequest{}
	mi := &file_hero_etl_v1_etl_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ValidateReportsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateReportsRequest) ProtoMessage() {}

func (x *ValidateReportsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_etl_v1_etl_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateReportsRequest.ProtoReflect.Descriptor instead.
func (*ValidateReportsRequest) Descriptor() ([]byte, []int) {
	return file_hero_etl_v1_etl_proto_rawDescGZIP(), []int{27}
}

func (x *ValidateReportsRequest) GetReportIds() []string {
	if x != nil {
		return x.ReportIds
	}
	return nil
}

func (x *ValidateReportsRequest) GetOutputFormat() OutputFormat {
	if x != nil {
		return x.OutputFormat
	}
	return OutputFormat_OUTPUT_FORMAT_UNSPECIFIED
}

func (x *ValidateReportsRequest) GetAgencyId() string {
	if x != nil {
		return x.AgencyId
	}
	return ""
}

type ValidateReportsResponse struct {
	state          protoimpl.MessageState    `protogen:"open.v1"`
	Results        []*ReportValidationResult `protobuf:"bytes,1,rep,name=results,proto3" json:"results,omitempty"`                                      // Validation results per report
	ValidReports   int32                     `protobuf:"varint,2,opt,name=valid_reports,json=validReports,proto3" json:"valid_reports,omitempty"`       // Number of valid reports
	InvalidReports int32                     `protobuf:"varint,3,opt,name=invalid_reports,json=invalidReports,proto3" json:"invalid_reports,omitempty"` // Number of invalid reports
	AllValid       bool                      `protobuf:"varint,4,opt,name=all_valid,json=allValid,proto3" json:"all_valid,omitempty"`                   // Whether all reports are valid
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ValidateReportsResponse) Reset() {
	*x = ValidateReportsResponse{}
	mi := &file_hero_etl_v1_etl_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ValidateReportsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateReportsResponse) ProtoMessage() {}

func (x *ValidateReportsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_etl_v1_etl_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateReportsResponse.ProtoReflect.Descriptor instead.
func (*ValidateReportsResponse) Descriptor() ([]byte, []int) {
	return file_hero_etl_v1_etl_proto_rawDescGZIP(), []int{28}
}

func (x *ValidateReportsResponse) GetResults() []*ReportValidationResult {
	if x != nil {
		return x.Results
	}
	return nil
}

func (x *ValidateReportsResponse) GetValidReports() int32 {
	if x != nil {
		return x.ValidReports
	}
	return 0
}

func (x *ValidateReportsResponse) GetInvalidReports() int32 {
	if x != nil {
		return x.InvalidReports
	}
	return 0
}

func (x *ValidateReportsResponse) GetAllValid() bool {
	if x != nil {
		return x.AllValid
	}
	return false
}

// Validation result for a single report
type ReportValidationResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ReportId      string                 `protobuf:"bytes,1,opt,name=report_id,json=reportId,proto3" json:"report_id,omitempty"` // Report ID
	Valid         bool                   `protobuf:"varint,2,opt,name=valid,proto3" json:"valid,omitempty"`                      // Whether report is valid
	Errors        []*ValidationError     `protobuf:"bytes,3,rep,name=errors,proto3" json:"errors,omitempty"`                     // Validation errors
	Warnings      []string               `protobuf:"bytes,4,rep,name=warnings,proto3" json:"warnings,omitempty"`                 // Validation warnings
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReportValidationResult) Reset() {
	*x = ReportValidationResult{}
	mi := &file_hero_etl_v1_etl_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReportValidationResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportValidationResult) ProtoMessage() {}

func (x *ReportValidationResult) ProtoReflect() protoreflect.Message {
	mi := &file_hero_etl_v1_etl_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportValidationResult.ProtoReflect.Descriptor instead.
func (*ReportValidationResult) Descriptor() ([]byte, []int) {
	return file_hero_etl_v1_etl_proto_rawDescGZIP(), []int{29}
}

func (x *ReportValidationResult) GetReportId() string {
	if x != nil {
		return x.ReportId
	}
	return ""
}

func (x *ReportValidationResult) GetValid() bool {
	if x != nil {
		return x.Valid
	}
	return false
}

func (x *ReportValidationResult) GetErrors() []*ValidationError {
	if x != nil {
		return x.Errors
	}
	return nil
}

func (x *ReportValidationResult) GetWarnings() []string {
	if x != nil {
		return x.Warnings
	}
	return nil
}

// Update job progress counters
type UpdateJobProgressRequest struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	JobId            string                 `protobuf:"bytes,1,opt,name=job_id,json=jobId,proto3" json:"job_id,omitempty"`                                   // Job ID to update
	TotalReports     int32                  `protobuf:"varint,2,opt,name=total_reports,json=totalReports,proto3" json:"total_reports,omitempty"`             // Total reports to process
	ReportsProcessed int32                  `protobuf:"varint,3,opt,name=reports_processed,json=reportsProcessed,proto3" json:"reports_processed,omitempty"` // Reports successfully processed
	ReportsFailed    int32                  `protobuf:"varint,4,opt,name=reports_failed,json=reportsFailed,proto3" json:"reports_failed,omitempty"`          // Reports that failed processing
	ReportsSkipped   int32                  `protobuf:"varint,5,opt,name=reports_skipped,json=reportsSkipped,proto3" json:"reports_skipped,omitempty"`       // Reports skipped (e.g., invalid data)
	ProgressNote     string                 `protobuf:"bytes,6,opt,name=progress_note,json=progressNote,proto3" json:"progress_note,omitempty"`              // Optional progress note or status message
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *UpdateJobProgressRequest) Reset() {
	*x = UpdateJobProgressRequest{}
	mi := &file_hero_etl_v1_etl_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateJobProgressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateJobProgressRequest) ProtoMessage() {}

func (x *UpdateJobProgressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_etl_v1_etl_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateJobProgressRequest.ProtoReflect.Descriptor instead.
func (*UpdateJobProgressRequest) Descriptor() ([]byte, []int) {
	return file_hero_etl_v1_etl_proto_rawDescGZIP(), []int{30}
}

func (x *UpdateJobProgressRequest) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

func (x *UpdateJobProgressRequest) GetTotalReports() int32 {
	if x != nil {
		return x.TotalReports
	}
	return 0
}

func (x *UpdateJobProgressRequest) GetReportsProcessed() int32 {
	if x != nil {
		return x.ReportsProcessed
	}
	return 0
}

func (x *UpdateJobProgressRequest) GetReportsFailed() int32 {
	if x != nil {
		return x.ReportsFailed
	}
	return 0
}

func (x *UpdateJobProgressRequest) GetReportsSkipped() int32 {
	if x != nil {
		return x.ReportsSkipped
	}
	return 0
}

func (x *UpdateJobProgressRequest) GetProgressNote() string {
	if x != nil {
		return x.ProgressNote
	}
	return ""
}

type UpdateJobProgressResponse struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Job             *ETLJob                `protobuf:"bytes,1,opt,name=job,proto3" json:"job,omitempty"`                                                 // Updated job with new progress
	ProgressUpdated bool                   `protobuf:"varint,2,opt,name=progress_updated,json=progressUpdated,proto3" json:"progress_updated,omitempty"` // Whether progress was successfully updated
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *UpdateJobProgressResponse) Reset() {
	*x = UpdateJobProgressResponse{}
	mi := &file_hero_etl_v1_etl_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateJobProgressResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateJobProgressResponse) ProtoMessage() {}

func (x *UpdateJobProgressResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_etl_v1_etl_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateJobProgressResponse.ProtoReflect.Descriptor instead.
func (*UpdateJobProgressResponse) Descriptor() ([]byte, []int) {
	return file_hero_etl_v1_etl_proto_rawDescGZIP(), []int{31}
}

func (x *UpdateJobProgressResponse) GetJob() *ETLJob {
	if x != nil {
		return x.Job
	}
	return nil
}

func (x *UpdateJobProgressResponse) GetProgressUpdated() bool {
	if x != nil {
		return x.ProgressUpdated
	}
	return false
}

// Get statistics for a specific agency
type GetAgencyStatsRequest struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	AgencyId          string                 `protobuf:"bytes,1,opt,name=agency_id,json=agencyId,proto3" json:"agency_id,omitempty"`                               // Agency ID (required)
	DateFrom          string                 `protobuf:"bytes,2,opt,name=date_from,json=dateFrom,proto3" json:"date_from,omitempty"`                               // ISO8601 start date (optional)
	DateTo            string                 `protobuf:"bytes,3,opt,name=date_to,json=dateTo,proto3" json:"date_to,omitempty"`                                     // ISO8601 end date (optional)
	IncludeJobDetails bool                   `protobuf:"varint,4,opt,name=include_job_details,json=includeJobDetails,proto3" json:"include_job_details,omitempty"` // Include detailed job breakdown (optional)
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *GetAgencyStatsRequest) Reset() {
	*x = GetAgencyStatsRequest{}
	mi := &file_hero_etl_v1_etl_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAgencyStatsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAgencyStatsRequest) ProtoMessage() {}

func (x *GetAgencyStatsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_etl_v1_etl_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAgencyStatsRequest.ProtoReflect.Descriptor instead.
func (*GetAgencyStatsRequest) Descriptor() ([]byte, []int) {
	return file_hero_etl_v1_etl_proto_rawDescGZIP(), []int{32}
}

func (x *GetAgencyStatsRequest) GetAgencyId() string {
	if x != nil {
		return x.AgencyId
	}
	return ""
}

func (x *GetAgencyStatsRequest) GetDateFrom() string {
	if x != nil {
		return x.DateFrom
	}
	return ""
}

func (x *GetAgencyStatsRequest) GetDateTo() string {
	if x != nil {
		return x.DateTo
	}
	return ""
}

func (x *GetAgencyStatsRequest) GetIncludeJobDetails() bool {
	if x != nil {
		return x.IncludeJobDetails
	}
	return false
}

type GetAgencyStatsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AgencyStats   *AgencyStats           `protobuf:"bytes,1,opt,name=agency_stats,json=agencyStats,proto3" json:"agency_stats,omitempty"` // Agency statistics
	RecentJobs    []*JobSummary          `protobuf:"bytes,2,rep,name=recent_jobs,json=recentJobs,proto3" json:"recent_jobs,omitempty"`    // Recent jobs for this agency (if include_job_details=true)
	GeneratedAt   string                 `protobuf:"bytes,3,opt,name=generated_at,json=generatedAt,proto3" json:"generated_at,omitempty"` // ISO8601 timestamp when stats were generated
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAgencyStatsResponse) Reset() {
	*x = GetAgencyStatsResponse{}
	mi := &file_hero_etl_v1_etl_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAgencyStatsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAgencyStatsResponse) ProtoMessage() {}

func (x *GetAgencyStatsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_etl_v1_etl_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAgencyStatsResponse.ProtoReflect.Descriptor instead.
func (*GetAgencyStatsResponse) Descriptor() ([]byte, []int) {
	return file_hero_etl_v1_etl_proto_rawDescGZIP(), []int{33}
}

func (x *GetAgencyStatsResponse) GetAgencyStats() *AgencyStats {
	if x != nil {
		return x.AgencyStats
	}
	return nil
}

func (x *GetAgencyStatsResponse) GetRecentJobs() []*JobSummary {
	if x != nil {
		return x.RecentJobs
	}
	return nil
}

func (x *GetAgencyStatsResponse) GetGeneratedAt() string {
	if x != nil {
		return x.GeneratedAt
	}
	return ""
}

// Summary information for a job (used in agency stats)
type JobSummary struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	JobId            string                 `protobuf:"bytes,1,opt,name=job_id,json=jobId,proto3" json:"job_id,omitempty"`                                                     // Job ID
	Status           JobStatus              `protobuf:"varint,2,opt,name=status,proto3,enum=hero.etl.v1.JobStatus" json:"status,omitempty"`                                    // Job status
	OutputFormat     OutputFormat           `protobuf:"varint,3,opt,name=output_format,json=outputFormat,proto3,enum=hero.etl.v1.OutputFormat" json:"output_format,omitempty"` // Output format
	ReportsProcessed int32                  `protobuf:"varint,4,opt,name=reports_processed,json=reportsProcessed,proto3" json:"reports_processed,omitempty"`                   // Number of reports processed
	CreatedAt        string                 `protobuf:"bytes,5,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`                                         // ISO8601 timestamp when job was created
	CompletedAt      string                 `protobuf:"bytes,6,opt,name=completed_at,json=completedAt,proto3" json:"completed_at,omitempty"`                                   // ISO8601 timestamp when job completed (if applicable)
	ContentSizeBytes int64                  `protobuf:"varint,7,opt,name=content_size_bytes,json=contentSizeBytes,proto3" json:"content_size_bytes,omitempty"`                 // Size of generated content
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *JobSummary) Reset() {
	*x = JobSummary{}
	mi := &file_hero_etl_v1_etl_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JobSummary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobSummary) ProtoMessage() {}

func (x *JobSummary) ProtoReflect() protoreflect.Message {
	mi := &file_hero_etl_v1_etl_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobSummary.ProtoReflect.Descriptor instead.
func (*JobSummary) Descriptor() ([]byte, []int) {
	return file_hero_etl_v1_etl_proto_rawDescGZIP(), []int{34}
}

func (x *JobSummary) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

func (x *JobSummary) GetStatus() JobStatus {
	if x != nil {
		return x.Status
	}
	return JobStatus_JOB_STATUS_UNSPECIFIED
}

func (x *JobSummary) GetOutputFormat() OutputFormat {
	if x != nil {
		return x.OutputFormat
	}
	return OutputFormat_OUTPUT_FORMAT_UNSPECIFIED
}

func (x *JobSummary) GetReportsProcessed() int32 {
	if x != nil {
		return x.ReportsProcessed
	}
	return 0
}

func (x *JobSummary) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *JobSummary) GetCompletedAt() string {
	if x != nil {
		return x.CompletedAt
	}
	return ""
}

func (x *JobSummary) GetContentSizeBytes() int64 {
	if x != nil {
		return x.ContentSizeBytes
	}
	return 0
}

var File_hero_etl_v1_etl_proto protoreflect.FileDescriptor

var file_hero_etl_v1_etl_proto_rawDesc = []byte{
	0x0a, 0x15, 0x68, 0x65, 0x72, 0x6f, 0x2f, 0x65, 0x74, 0x6c, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x74,
	0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x65, 0x74,
	0x6c, 0x2e, 0x76, 0x31, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0xb4, 0x09, 0x0a, 0x06, 0x45, 0x54, 0x4c, 0x4a, 0x6f, 0x62, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x6f, 0x72,
	0x67, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6f, 0x72, 0x67, 0x49,
	0x64, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x49, 0x64, 0x12, 0x3e,
	0x0a, 0x0d, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x65, 0x74, 0x6c,
	0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74,
	0x52, 0x0c, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x2e,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16,
	0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x65, 0x74, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x4a, 0x6f, 0x62,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d,
	0x0a, 0x0a, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x06, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x64, 0x73, 0x12, 0x1b, 0x0a,
	0x09, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x64, 0x61, 0x74, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x12, 0x17, 0x0a, 0x07, 0x64, 0x61,
	0x74, 0x65, 0x5f, 0x74, 0x6f, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x61, 0x74,
	0x65, 0x54, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x63, 0x61, 0x73, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x12, 0x2b, 0x0a, 0x11, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x73, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x65, 0x64, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x10, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x65, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x5f,
	0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x73, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x5f, 0x73, 0x6b, 0x69, 0x70, 0x70, 0x65, 0x64, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x53, 0x6b, 0x69,
	0x70, 0x70, 0x65, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x26, 0x0a, 0x0f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x6c, 0x61, 0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x23, 0x0a,
	0x0d, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x13,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x2d, 0x0a, 0x06, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x18, 0x14, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x15, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x65, 0x74, 0x6c, 0x2e, 0x76, 0x31,
	0x2e, 0x4a, 0x6f, 0x62, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x06, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x73, 0x12, 0x2b, 0x0a, 0x11, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x10, 0x67, 0x65,
	0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x21,
	0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x16,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x2c, 0x0a, 0x12, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x69, 0x7a,
	0x65, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x17, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x42, 0x79, 0x74, 0x65, 0x73, 0x12,
	0x30, 0x0a, 0x14, 0x61, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x61,
	0x67, 0x65, 0x6e, 0x63, 0x79, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x12, 0x2f, 0x0a, 0x13, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12,
	0x73, 0x75, 0x62, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x74,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x74, 0x72, 0x79, 0x5f, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x72, 0x65, 0x74, 0x72,
	0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x61, 0x78, 0x5f, 0x72, 0x65,
	0x74, 0x72, 0x69, 0x65, 0x73, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x6d, 0x61, 0x78,
	0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x73, 0x12, 0x22, 0x0a, 0x0d, 0x6c, 0x61, 0x73, 0x74, 0x5f,
	0x72, 0x65, 0x74, 0x72, 0x79, 0x5f, 0x61, 0x74, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x6c, 0x61, 0x73, 0x74, 0x52, 0x65, 0x74, 0x72, 0x79, 0x41, 0x74, 0x12, 0x2c, 0x0a, 0x12, 0x61,
	0x75, 0x74, 0x6f, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x79, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x64, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x61, 0x75, 0x74, 0x6f, 0x52, 0x65, 0x74,
	0x72, 0x79, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x2d, 0x0a, 0x13, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42,
	0x79, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x5f, 0x6f, 0x6e, 0x6c, 0x79, 0x18, 0x20, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b,
	0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x4f, 0x6e, 0x6c, 0x79, 0x12, 0x19, 0x0a, 0x08, 0x6a,
	0x6f, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x21, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6a,
	0x6f, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xf3, 0x01, 0x0a, 0x08, 0x4a, 0x6f, 0x62, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x70, 0x61,
	0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x50,
	0x61, 0x74, 0x68, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x63, 0x63, 0x75, 0x72, 0x72, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x63, 0x63, 0x75, 0x72, 0x72,
	0x65, 0x64, 0x41, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x79,
	0x61, 0x62, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x52, 0x65,
	0x74, 0x72, 0x79, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x74, 0x72, 0x79,
	0x5f, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c,
	0x72, 0x65, 0x74, 0x72, 0x79, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x22, 0xdb, 0x03, 0x0a,
	0x15, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x67, 0x65, 0x6e, 0x63, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x67, 0x65, 0x6e, 0x63,
	0x79, 0x49, 0x64, 0x12, 0x3e, 0x0a, 0x0d, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x5f, 0x66, 0x6f,
	0x72, 0x6d, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x68, 0x65, 0x72,
	0x6f, 0x2e, 0x65, 0x74, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x46,
	0x6f, 0x72, 0x6d, 0x61, 0x74, 0x52, 0x0c, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x46, 0x6f, 0x72,
	0x6d, 0x61, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x6f,
	0x6e, 0x6c, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x70, 0x72, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x4f, 0x6e, 0x6c, 0x79, 0x12, 0x19, 0x0a, 0x08, 0x6a, 0x6f, 0x62, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6a, 0x6f, 0x62, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x64, 0x73,
	0x12, 0x1b, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x61, 0x74, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x12, 0x17, 0x0a,
	0x07, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x6f, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x64, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x63, 0x61, 0x73, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x30, 0x0a, 0x14, 0x73, 0x6b, 0x69, 0x70, 0x5f, 0x69, 0x6e,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x12, 0x73, 0x6b, 0x69, 0x70, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x12, 0x32, 0x0a, 0x15, 0x69, 0x6e, 0x63, 0x6c, 0x75,
	0x64, 0x65, 0x5f, 0x64, 0x72, 0x61, 0x66, 0x74, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x13, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x44,
	0x72, 0x61, 0x66, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x6d,
	0x61, 0x78, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0a, 0x6d, 0x61, 0x78, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x73, 0x12, 0x2c, 0x0a, 0x12,
	0x61, 0x75, 0x74, 0x6f, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x79, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x61, 0x75, 0x74, 0x6f, 0x52, 0x65,
	0x74, 0x72, 0x79, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x22, 0x75, 0x0a, 0x16, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x25, 0x0a, 0x03, 0x6a, 0x6f, 0x62, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x13, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x65, 0x74, 0x6c, 0x2e, 0x76, 0x31, 0x2e,
	0x45, 0x54, 0x4c, 0x4a, 0x6f, 0x62, 0x52, 0x03, 0x6a, 0x6f, 0x62, 0x12, 0x34, 0x0a, 0x16, 0x65,
	0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x14, 0x65, 0x73, 0x74,
	0x69, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x22, 0x26, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x6a, 0x6f, 0x62, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x6a, 0x6f, 0x62, 0x49, 0x64, 0x22, 0x37, 0x0a, 0x0e, 0x47, 0x65, 0x74,
	0x4a, 0x6f, 0x62, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x25, 0x0a, 0x03, 0x6a,
	0x6f, 0x62, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e,
	0x65, 0x74, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x54, 0x4c, 0x4a, 0x6f, 0x62, 0x52, 0x03, 0x6a,
	0x6f, 0x62, 0x22, 0xdc, 0x01, 0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74, 0x4a, 0x6f, 0x62, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x67, 0x65, 0x6e, 0x63, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x67, 0x65, 0x6e, 0x63,
	0x79, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x65, 0x74, 0x6c, 0x2e, 0x76,
	0x31, 0x2e, 0x4a, 0x6f, 0x62, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x66,
	0x72, 0x6f, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x46, 0x72, 0x6f, 0x6d, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x74, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x54, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69,
	0x7a, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69,
	0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x22, 0x63, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x4a, 0x6f, 0x62, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x27, 0x0a, 0x04, 0x6a, 0x6f, 0x62, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x65, 0x74, 0x6c, 0x2e, 0x76,
	0x31, 0x2e, 0x45, 0x54, 0x4c, 0x4a, 0x6f, 0x62, 0x52, 0x04, 0x6a, 0x6f, 0x62, 0x73, 0x12, 0x26,
	0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50, 0x61, 0x67,
	0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x29, 0x0a, 0x10, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c,
	0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x6a, 0x6f,
	0x62, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6a, 0x6f, 0x62, 0x49,
	0x64, 0x22, 0x3a, 0x0a, 0x11, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x4a, 0x6f, 0x62, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x25, 0x0a, 0x03, 0x6a, 0x6f, 0x62, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x65, 0x74, 0x6c, 0x2e, 0x76,
	0x31, 0x2e, 0x45, 0x54, 0x4c, 0x4a, 0x6f, 0x62, 0x52, 0x03, 0x6a, 0x6f, 0x62, 0x22, 0x7b, 0x0a,
	0x0f, 0x52, 0x65, 0x74, 0x72, 0x79, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x15, 0x0a, 0x06, 0x6a, 0x6f, 0x62, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x6a, 0x6f, 0x62, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x6f, 0x72, 0x63, 0x65,
	0x5f, 0x72, 0x65, 0x74, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x66, 0x6f,
	0x72, 0x63, 0x65, 0x52, 0x65, 0x74, 0x72, 0x79, 0x12, 0x30, 0x0a, 0x14, 0x6f, 0x76, 0x65, 0x72,
	0x72, 0x69, 0x64, 0x65, 0x5f, 0x6d, 0x61, 0x78, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x73,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x12, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65,
	0x4d, 0x61, 0x78, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x73, 0x22, 0x85, 0x01, 0x0a, 0x10, 0x52,
	0x65, 0x74, 0x72, 0x79, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x25, 0x0a, 0x03, 0x6a, 0x6f, 0x62, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x68,
	0x65, 0x72, 0x6f, 0x2e, 0x65, 0x74, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x54, 0x4c, 0x4a, 0x6f,
	0x62, 0x52, 0x03, 0x6a, 0x6f, 0x62, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x74, 0x72, 0x79, 0x5f,
	0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0e, 0x72, 0x65, 0x74, 0x72, 0x79, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x64, 0x12,
	0x21, 0x0a, 0x0c, 0x72, 0x65, 0x74, 0x72, 0x79, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x74, 0x72, 0x79, 0x52, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x22, 0x32, 0x0a, 0x19, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x4a, 0x6f,
	0x62, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x15, 0x0a, 0x06, 0x6a, 0x6f, 0x62, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x6a, 0x6f, 0x62, 0x49, 0x64, 0x22, 0x94, 0x01, 0x0a, 0x1a, 0x44, 0x6f, 0x77, 0x6e, 0x6c,
	0x6f, 0x61, 0x64, 0x4a, 0x6f, 0x62, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12,
	0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x73, 0x69, 0x7a, 0x65, 0x42, 0x79, 0x74, 0x65, 0x73, 0x22, 0x15, 0x0a,
	0x13, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x67, 0x65, 0x6e, 0x63, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x22, 0x4b, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x67, 0x65, 0x6e,
	0x63, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x33, 0x0a, 0x08,
	0x61, 0x67, 0x65, 0x6e, 0x63, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x65, 0x74, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x67, 0x65,
	0x6e, 0x63, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x61, 0x67, 0x65, 0x6e, 0x63, 0x69, 0x65,
	0x73, 0x22, 0xb3, 0x01, 0x0a, 0x0a, 0x41, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x46, 0x0a, 0x11, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x65,
	0x64, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0e, 0x32,
	0x19, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x65, 0x74, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x75,
	0x74, 0x70, 0x75, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x52, 0x10, 0x73, 0x75, 0x70, 0x70,
	0x6f, 0x72, 0x74, 0x65, 0x64, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x73, 0x12, 0x18, 0x0a, 0x07,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x67, 0x65, 0x6e, 0x63, 0x79,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x67, 0x65,
	0x6e, 0x63, 0x79, 0x54, 0x79, 0x70, 0x65, 0x22, 0x37, 0x0a, 0x18, 0x45, 0x78, 0x74, 0x72, 0x61,
	0x63, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x64,
	0x22, 0xad, 0x02, 0x0a, 0x19, 0x45, 0x78, 0x74, 0x72, 0x61, 0x63, 0x74, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3e,
	0x0a, 0x0e, 0x65, 0x78, 0x74, 0x72, 0x61, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52,
	0x0d, 0x65, 0x78, 0x74, 0x72, 0x61, 0x63, 0x74, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12, 0x27,
	0x0a, 0x0f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x65, 0x78, 0x74, 0x72, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x77, 0x61, 0x72, 0x6e, 0x69,
	0x6e, 0x67, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x77, 0x61, 0x72, 0x6e, 0x69,
	0x6e, 0x67, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x23, 0x0a,
	0x0d, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x73, 0x65, 0x74, 0x73, 0x5f,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x64, 0x61, 0x74,
	0x61, 0x53, 0x65, 0x74, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x24, 0x0a, 0x0e, 0x64, 0x61,
	0x74, 0x61, 0x5f, 0x73, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x07, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x0c, 0x64, 0x61, 0x74, 0x61, 0x53, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x73,
	0x22, 0xf5, 0x01, 0x0a, 0x1f, 0x54, 0x65, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49,
	0x64, 0x12, 0x3e, 0x0a, 0x0d, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x5f, 0x66, 0x6f, 0x72, 0x6d,
	0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e,
	0x65, 0x74, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x46, 0x6f, 0x72,
	0x6d, 0x61, 0x74, 0x52, 0x0c, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x61,
	0x74, 0x12, 0x1a, 0x0a, 0x08, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x08, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x12, 0x2e, 0x0a,
	0x13, 0x6d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x6a, 0x73, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x6d, 0x61, 0x70, 0x70,
	0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4a, 0x73, 0x6f, 0x6e, 0x12, 0x29, 0x0a,
	0x10, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0xcd, 0x03, 0x0a, 0x20, 0x54, 0x65, 0x73,
	0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x6f, 0x72, 0x6d,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2f, 0x0a,
	0x13, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x6f, 0x72, 0x6d, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x12, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x66, 0x6f, 0x72, 0x6d, 0x65, 0x64, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x21,
	0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x2f, 0x0a, 0x13, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x77, 0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x77, 0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x18,
	0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x49, 0x0a,
	0x11, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e,
	0x65, 0x74, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x10, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x12, 0x29, 0x0a, 0x10, 0x72, 0x65, 0x61, 0x64,
	0x61, 0x62, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x72, 0x65, 0x61, 0x64, 0x61, 0x62, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x12, 0x2e, 0x0a, 0x13, 0x6d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x75, 0x73, 0x65, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x11, 0x6d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x55,
	0x73, 0x65, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f,
	0x75, 0x73, 0x65, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x74, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x64, 0x22, 0xa7, 0x01, 0x0a, 0x18, 0x54, 0x65, 0x73,
	0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x49, 0x64, 0x12, 0x3e, 0x0a, 0x0d, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x5f, 0x66, 0x6f, 0x72,
	0x6d, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x68, 0x65, 0x72, 0x6f,
	0x2e, 0x65, 0x74, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x46, 0x6f,
	0x72, 0x6d, 0x61, 0x74, 0x52, 0x0c, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x46, 0x6f, 0x72, 0x6d,
	0x61, 0x74, 0x12, 0x2e, 0x0a, 0x13, 0x6d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x6a, 0x73, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x11, 0x6d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4a, 0x73,
	0x6f, 0x6e, 0x22, 0xb4, 0x02, 0x0a, 0x19, 0x54, 0x65, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x38, 0x0a, 0x0b, 0x6d, 0x61, 0x70, 0x70, 0x65, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x0a,
	0x6d, 0x61, 0x70, 0x70, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2e, 0x0a,
	0x13, 0x6d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x75, 0x73, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x6d, 0x61, 0x70, 0x70,
	0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x55, 0x73, 0x65, 0x64, 0x12, 0x2f, 0x0a,
	0x13, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x77, 0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x08, 0x77, 0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0xbe, 0x01, 0x0a, 0x0f, 0x56, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x1d, 0x0a,
	0x0a, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1d, 0x0a, 0x0a,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x25, 0x0a, 0x0e, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74,
	0x65, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x63, 0x74, 0x75, 0x61,
	0x6c, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61,
	0x63, 0x74, 0x75, 0x61, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xa7, 0x01, 0x0a, 0x12, 0x47,
	0x65, 0x74, 0x4a, 0x6f, 0x62, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x49, 0x64, 0x12, 0x3e,
	0x0a, 0x0d, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x65, 0x74, 0x6c,
	0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74,
	0x52, 0x0c, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x1b,
	0x0a, 0x09, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x64, 0x61, 0x74, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x12, 0x17, 0x0a, 0x07, 0x64,
	0x61, 0x74, 0x65, 0x5f, 0x74, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x61,
	0x74, 0x65, 0x54, 0x6f, 0x22, 0xf4, 0x02, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x53,
	0x74, 0x61, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1d, 0x0a, 0x0a,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x6a, 0x6f, 0x62, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x09, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x4a, 0x6f, 0x62, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x63,
	0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x6a, 0x6f, 0x62, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0d, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x4a, 0x6f,
	0x62, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x6a, 0x6f, 0x62,
	0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x4a,
	0x6f, 0x62, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c, 0x65, 0x64,
	0x5f, 0x6a, 0x6f, 0x62, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x63, 0x61, 0x6e,
	0x63, 0x65, 0x6c, 0x6c, 0x65, 0x64, 0x4a, 0x6f, 0x62, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x75,
	0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x6a, 0x6f, 0x62, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0b, 0x72, 0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x4a, 0x6f, 0x62, 0x73, 0x12, 0x36, 0x0a,
	0x17, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x5f, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x65, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x15,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x65, 0x64, 0x12, 0x37, 0x0a, 0x18, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x62, 0x79, 0x74, 0x65,
	0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x15, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x42, 0x79, 0x74, 0x65, 0x73, 0x12, 0x3b,
	0x0a, 0x0c, 0x61, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x73, 0x18, 0x08,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x65, 0x74, 0x6c, 0x2e,
	0x76, 0x31, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x0b,
	0x61, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x53, 0x74, 0x61, 0x74, 0x73, 0x22, 0xe7, 0x01, 0x0a, 0x0b,
	0x41, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x53, 0x74, 0x61, 0x74, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x61,
	0x67, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x61, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x67, 0x65, 0x6e,
	0x63, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61,
	0x67, 0x65, 0x6e, 0x63, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6a, 0x6f, 0x62,
	0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x6a, 0x6f,
	0x62, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2c, 0x0a, 0x12, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x53, 0x69,
	0x7a, 0x65, 0x42, 0x79, 0x74, 0x65, 0x73, 0x12, 0x2c, 0x0a, 0x12, 0x6c, 0x61, 0x73, 0x74, 0x5f,
	0x73, 0x75, 0x62, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x74, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x10, 0x6c, 0x61, 0x73, 0x74, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x41, 0x74, 0x22, 0x94, 0x01, 0x0a, 0x16, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x64, 0x73, 0x12,
	0x3e, 0x0a, 0x0d, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x65, 0x74,
	0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x61,
	0x74, 0x52, 0x0c, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12,
	0x1b, 0x0a, 0x09, 0x61, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x61, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x49, 0x64, 0x22, 0xc3, 0x01, 0x0a,
	0x17, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3d, 0x0a, 0x07, 0x72, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x68, 0x65, 0x72, 0x6f,
	0x2e, 0x65, 0x74, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x56, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x07,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x12, 0x27, 0x0a, 0x0f,
	0x69, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x69, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x6c, 0x6c, 0x5f, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x61, 0x6c, 0x6c, 0x56, 0x61, 0x6c,
	0x69, 0x64, 0x22, 0x9d, 0x01, 0x0a, 0x16, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x56, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x1b, 0x0a,
	0x09, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x12, 0x34, 0x0a, 0x06, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x65, 0x74, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x56,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x06,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x77, 0x61, 0x72, 0x6e, 0x69, 0x6e,
	0x67, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x77, 0x61, 0x72, 0x6e, 0x69, 0x6e,
	0x67, 0x73, 0x22, 0xf8, 0x01, 0x0a, 0x18, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4a, 0x6f, 0x62,
	0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x15, 0x0a, 0x06, 0x6a, 0x6f, 0x62, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x6a, 0x6f, 0x62, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x12, 0x2b, 0x0a, 0x11, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x65, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x65, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x73, 0x5f, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0d, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x12,
	0x27, 0x0a, 0x0f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x5f, 0x73, 0x6b, 0x69, 0x70, 0x70,
	0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x73, 0x53, 0x6b, 0x69, 0x70, 0x70, 0x65, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x70, 0x72, 0x6f, 0x67,
	0x72, 0x65, 0x73, 0x73, 0x5f, 0x6e, 0x6f, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x4e, 0x6f, 0x74, 0x65, 0x22, 0x6d, 0x0a,
	0x19, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65,
	0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x25, 0x0a, 0x03, 0x6a, 0x6f,
	0x62, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x65,
	0x74, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x54, 0x4c, 0x4a, 0x6f, 0x62, 0x52, 0x03, 0x6a, 0x6f,
	0x62, 0x12, 0x29, 0x0a, 0x10, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x70, 0x72, 0x6f,
	0x67, 0x72, 0x65, 0x73, 0x73, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x22, 0x9a, 0x01, 0x0a,
	0x15, 0x47, 0x65, 0x74, 0x41, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x67, 0x65, 0x6e, 0x63, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x67, 0x65, 0x6e, 0x63,
	0x79, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x66, 0x72, 0x6f, 0x6d,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x61, 0x74, 0x65, 0x46, 0x72, 0x6f, 0x6d,
	0x12, 0x17, 0x0a, 0x07, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x64, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x12, 0x2e, 0x0a, 0x13, 0x69, 0x6e, 0x63,
	0x6c, 0x75, 0x64, 0x65, 0x5f, 0x6a, 0x6f, 0x62, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x4a,
	0x6f, 0x62, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0xb2, 0x01, 0x0a, 0x16, 0x47, 0x65,
	0x74, 0x41, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3b, 0x0a, 0x0c, 0x61, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x68, 0x65, 0x72,
	0x6f, 0x2e, 0x65, 0x74, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x53,
	0x74, 0x61, 0x74, 0x73, 0x52, 0x0b, 0x61, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x53, 0x74, 0x61, 0x74,
	0x73, 0x12, 0x38, 0x0a, 0x0b, 0x72, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x5f, 0x6a, 0x6f, 0x62, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x65, 0x74,
	0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x4a, 0x6f, 0x62, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52,
	0x0a, 0x72, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x4a, 0x6f, 0x62, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x67,
	0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0xb0,
	0x02, 0x0a, 0x0a, 0x4a, 0x6f, 0x62, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x15, 0x0a,
	0x06, 0x6a, 0x6f, 0x62, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6a,
	0x6f, 0x62, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x65, 0x74, 0x6c, 0x2e,
	0x76, 0x31, 0x2e, 0x4a, 0x6f, 0x62, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x3e, 0x0a, 0x0d, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x5f, 0x66,
	0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x68, 0x65,
	0x72, 0x6f, 0x2e, 0x65, 0x74, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74,
	0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x52, 0x0c, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x46, 0x6f,
	0x72, 0x6d, 0x61, 0x74, 0x12, 0x2b, 0x0a, 0x11, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x5f,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x10, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x65,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x2c, 0x0a, 0x12, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x73,
	0x69, 0x7a, 0x65, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x10, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x42, 0x79, 0x74, 0x65,
	0x73, 0x2a, 0xda, 0x01, 0x0a, 0x09, 0x4a, 0x6f, 0x62, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x1a, 0x0a, 0x16, 0x4a, 0x4f, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x16, 0x0a, 0x12, 0x4a,
	0x4f, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e,
	0x47, 0x10, 0x01, 0x12, 0x19, 0x0a, 0x15, 0x4a, 0x4f, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x45, 0x58, 0x54, 0x52, 0x41, 0x43, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x1b,
	0x0a, 0x17, 0x4a, 0x4f, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x54, 0x52, 0x41,
	0x4e, 0x53, 0x46, 0x4f, 0x52, 0x4d, 0x49, 0x4e, 0x47, 0x10, 0x03, 0x12, 0x16, 0x0a, 0x12, 0x4a,
	0x4f, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4c, 0x4f, 0x41, 0x44, 0x49, 0x4e,
	0x47, 0x10, 0x04, 0x12, 0x18, 0x0a, 0x14, 0x4a, 0x4f, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x10, 0x05, 0x12, 0x15, 0x0a,
	0x11, 0x4a, 0x4f, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c,
	0x45, 0x44, 0x10, 0x06, 0x12, 0x18, 0x0a, 0x14, 0x4a, 0x4f, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x4c, 0x45, 0x44, 0x10, 0x07, 0x2a, 0x4a,
	0x0a, 0x0c, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x1d,
	0x0a, 0x19, 0x4f, 0x55, 0x54, 0x50, 0x55, 0x54, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x41, 0x54, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1b, 0x0a,
	0x17, 0x4f, 0x55, 0x54, 0x50, 0x55, 0x54, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x41, 0x54, 0x5f, 0x4e,
	0x49, 0x42, 0x52, 0x53, 0x5f, 0x58, 0x4d, 0x4c, 0x10, 0x01, 0x32, 0xf4, 0x09, 0x0a, 0x0a, 0x45,
	0x54, 0x4c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x59, 0x0a, 0x0e, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x12, 0x22, 0x2e, 0x68, 0x65,
	0x72, 0x6f, 0x2e, 0x65, 0x74, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x23, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x65, 0x74, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x62, 0x0a, 0x11, 0x45, 0x78, 0x74, 0x72, 0x61, 0x63, 0x74, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x25, 0x2e, 0x68, 0x65, 0x72, 0x6f,
	0x2e, 0x65, 0x74, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x63, 0x74, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x26, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x65, 0x74, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x45,
	0x78, 0x74, 0x72, 0x61, 0x63, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x62, 0x0a, 0x11, 0x54, 0x65, 0x73, 0x74,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x12, 0x25, 0x2e,
	0x68, 0x65, 0x72, 0x6f, 0x2e, 0x65, 0x74, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x73, 0x74,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x65, 0x74, 0x6c, 0x2e,
	0x76, 0x31, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x4d, 0x61, 0x70,
	0x70, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x77, 0x0a, 0x18,
	0x54, 0x65, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66,
	0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2c, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e,
	0x65, 0x74, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x65, 0x74,
	0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x41, 0x0a, 0x06, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x12,
	0x1a, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x65, 0x74, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x68, 0x65,
	0x72, 0x6f, 0x2e, 0x65, 0x74, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x47, 0x0a, 0x08, 0x4c, 0x69, 0x73, 0x74,
	0x4a, 0x6f, 0x62, 0x73, 0x12, 0x1c, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x65, 0x74, 0x6c, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4a, 0x6f, 0x62, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x65, 0x74, 0x6c, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4a, 0x6f, 0x62, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x4a, 0x0a, 0x09, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x4a, 0x6f, 0x62, 0x12, 0x1d,
	0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x65, 0x74, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6e,
	0x63, 0x65, 0x6c, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e,
	0x68, 0x65, 0x72, 0x6f, 0x2e, 0x65, 0x74, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6e, 0x63,
	0x65, 0x6c, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x47, 0x0a,
	0x08, 0x52, 0x65, 0x74, 0x72, 0x79, 0x4a, 0x6f, 0x62, 0x12, 0x1c, 0x2e, 0x68, 0x65, 0x72, 0x6f,
	0x2e, 0x65, 0x74, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x74, 0x72, 0x79, 0x4a, 0x6f, 0x62,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x65,
	0x74, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x74, 0x72, 0x79, 0x4a, 0x6f, 0x62, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x65, 0x0a, 0x12, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f,
	0x61, 0x64, 0x4a, 0x6f, 0x62, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x26, 0x2e, 0x68,
	0x65, 0x72, 0x6f, 0x2e, 0x65, 0x74, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x6f, 0x77, 0x6e, 0x6c,
	0x6f, 0x61, 0x64, 0x4a, 0x6f, 0x62, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x65, 0x74, 0x6c, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x4a, 0x6f, 0x62, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x53, 0x0a,
	0x0c, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x67, 0x65, 0x6e, 0x63, 0x69, 0x65, 0x73, 0x12, 0x20, 0x2e,
	0x68, 0x65, 0x72, 0x6f, 0x2e, 0x65, 0x74, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x41, 0x67, 0x65, 0x6e, 0x63, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x21, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x65, 0x74, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x41, 0x67, 0x65, 0x6e, 0x63, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x50, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x53, 0x74, 0x61, 0x74,
	0x73, 0x12, 0x1f, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x65, 0x74, 0x6c, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x20, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x65, 0x74, 0x6c, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5c, 0x0a, 0x0f, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x12, 0x23, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x65,
	0x74, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x68,
	0x65, 0x72, 0x6f, 0x2e, 0x65, 0x74, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x62, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x50,
	0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x12, 0x25, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x65,
	0x74, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x50,
	0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26,
	0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x65, 0x74, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x59, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x41, 0x67, 0x65,
	0x6e, 0x63, 0x79, 0x53, 0x74, 0x61, 0x74, 0x73, 0x12, 0x22, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e,
	0x65, 0x74, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x67, 0x65, 0x6e, 0x63, 0x79,
	0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x68,
	0x65, 0x72, 0x6f, 0x2e, 0x65, 0x74, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x67,
	0x65, 0x6e, 0x63, 0x79, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x42, 0x17, 0x5a, 0x15, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x68, 0x65, 0x72, 0x6f, 0x2f,
	0x65, 0x74, 0x6c, 0x2f, 0x76, 0x31, 0x3b, 0x65, 0x74, 0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_hero_etl_v1_etl_proto_rawDescOnce sync.Once
	file_hero_etl_v1_etl_proto_rawDescData = file_hero_etl_v1_etl_proto_rawDesc
)

func file_hero_etl_v1_etl_proto_rawDescGZIP() []byte {
	file_hero_etl_v1_etl_proto_rawDescOnce.Do(func() {
		file_hero_etl_v1_etl_proto_rawDescData = protoimpl.X.CompressGZIP(file_hero_etl_v1_etl_proto_rawDescData)
	})
	return file_hero_etl_v1_etl_proto_rawDescData
}

var file_hero_etl_v1_etl_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_hero_etl_v1_etl_proto_msgTypes = make([]protoimpl.MessageInfo, 35)
var file_hero_etl_v1_etl_proto_goTypes = []any{
	(JobStatus)(0),                           // 0: hero.etl.v1.JobStatus
	(OutputFormat)(0),                        // 1: hero.etl.v1.OutputFormat
	(*ETLJob)(nil),                           // 2: hero.etl.v1.ETLJob
	(*JobError)(nil),                         // 3: hero.etl.v1.JobError
	(*ProcessReportsRequest)(nil),            // 4: hero.etl.v1.ProcessReportsRequest
	(*ProcessReportsResponse)(nil),           // 5: hero.etl.v1.ProcessReportsResponse
	(*GetJobRequest)(nil),                    // 6: hero.etl.v1.GetJobRequest
	(*GetJobResponse)(nil),                   // 7: hero.etl.v1.GetJobResponse
	(*ListJobsRequest)(nil),                  // 8: hero.etl.v1.ListJobsRequest
	(*ListJobsResponse)(nil),                 // 9: hero.etl.v1.ListJobsResponse
	(*CancelJobRequest)(nil),                 // 10: hero.etl.v1.CancelJobRequest
	(*CancelJobResponse)(nil),                // 11: hero.etl.v1.CancelJobResponse
	(*RetryJobRequest)(nil),                  // 12: hero.etl.v1.RetryJobRequest
	(*RetryJobResponse)(nil),                 // 13: hero.etl.v1.RetryJobResponse
	(*DownloadJobContentRequest)(nil),        // 14: hero.etl.v1.DownloadJobContentRequest
	(*DownloadJobContentResponse)(nil),       // 15: hero.etl.v1.DownloadJobContentResponse
	(*ListAgenciesRequest)(nil),              // 16: hero.etl.v1.ListAgenciesRequest
	(*ListAgenciesResponse)(nil),             // 17: hero.etl.v1.ListAgenciesResponse
	(*AgencyInfo)(nil),                       // 18: hero.etl.v1.AgencyInfo
	(*ExtractReportDataRequest)(nil),         // 19: hero.etl.v1.ExtractReportDataRequest
	(*ExtractReportDataResponse)(nil),        // 20: hero.etl.v1.ExtractReportDataResponse
	(*TestReportTransformationRequest)(nil),  // 21: hero.etl.v1.TestReportTransformationRequest
	(*TestReportTransformationResponse)(nil), // 22: hero.etl.v1.TestReportTransformationResponse
	(*TestReportMappingRequest)(nil),         // 23: hero.etl.v1.TestReportMappingRequest
	(*TestReportMappingResponse)(nil),        // 24: hero.etl.v1.TestReportMappingResponse
	(*ValidationError)(nil),                  // 25: hero.etl.v1.ValidationError
	(*GetJobStatsRequest)(nil),               // 26: hero.etl.v1.GetJobStatsRequest
	(*GetJobStatsResponse)(nil),              // 27: hero.etl.v1.GetJobStatsResponse
	(*AgencyStats)(nil),                      // 28: hero.etl.v1.AgencyStats
	(*ValidateReportsRequest)(nil),           // 29: hero.etl.v1.ValidateReportsRequest
	(*ValidateReportsResponse)(nil),          // 30: hero.etl.v1.ValidateReportsResponse
	(*ReportValidationResult)(nil),           // 31: hero.etl.v1.ReportValidationResult
	(*UpdateJobProgressRequest)(nil),         // 32: hero.etl.v1.UpdateJobProgressRequest
	(*UpdateJobProgressResponse)(nil),        // 33: hero.etl.v1.UpdateJobProgressResponse
	(*GetAgencyStatsRequest)(nil),            // 34: hero.etl.v1.GetAgencyStatsRequest
	(*GetAgencyStatsResponse)(nil),           // 35: hero.etl.v1.GetAgencyStatsResponse
	(*JobSummary)(nil),                       // 36: hero.etl.v1.JobSummary
	(*structpb.Struct)(nil),                  // 37: google.protobuf.Struct
}
var file_hero_etl_v1_etl_proto_depIdxs = []int32{
	1,  // 0: hero.etl.v1.ETLJob.output_format:type_name -> hero.etl.v1.OutputFormat
	0,  // 1: hero.etl.v1.ETLJob.status:type_name -> hero.etl.v1.JobStatus
	3,  // 2: hero.etl.v1.ETLJob.errors:type_name -> hero.etl.v1.JobError
	1,  // 3: hero.etl.v1.ProcessReportsRequest.output_format:type_name -> hero.etl.v1.OutputFormat
	2,  // 4: hero.etl.v1.ProcessReportsResponse.job:type_name -> hero.etl.v1.ETLJob
	2,  // 5: hero.etl.v1.GetJobResponse.job:type_name -> hero.etl.v1.ETLJob
	0,  // 6: hero.etl.v1.ListJobsRequest.status:type_name -> hero.etl.v1.JobStatus
	2,  // 7: hero.etl.v1.ListJobsResponse.jobs:type_name -> hero.etl.v1.ETLJob
	2,  // 8: hero.etl.v1.CancelJobResponse.job:type_name -> hero.etl.v1.ETLJob
	2,  // 9: hero.etl.v1.RetryJobResponse.job:type_name -> hero.etl.v1.ETLJob
	18, // 10: hero.etl.v1.ListAgenciesResponse.agencies:type_name -> hero.etl.v1.AgencyInfo
	1,  // 11: hero.etl.v1.AgencyInfo.supported_formats:type_name -> hero.etl.v1.OutputFormat
	37, // 12: hero.etl.v1.ExtractReportDataResponse.extracted_data:type_name -> google.protobuf.Struct
	1,  // 13: hero.etl.v1.TestReportTransformationRequest.output_format:type_name -> hero.etl.v1.OutputFormat
	25, // 14: hero.etl.v1.TestReportTransformationResponse.validation_errors:type_name -> hero.etl.v1.ValidationError
	1,  // 15: hero.etl.v1.TestReportMappingRequest.output_format:type_name -> hero.etl.v1.OutputFormat
	37, // 16: hero.etl.v1.TestReportMappingResponse.mapped_data:type_name -> google.protobuf.Struct
	1,  // 17: hero.etl.v1.GetJobStatsRequest.output_format:type_name -> hero.etl.v1.OutputFormat
	28, // 18: hero.etl.v1.GetJobStatsResponse.agency_stats:type_name -> hero.etl.v1.AgencyStats
	1,  // 19: hero.etl.v1.ValidateReportsRequest.output_format:type_name -> hero.etl.v1.OutputFormat
	31, // 20: hero.etl.v1.ValidateReportsResponse.results:type_name -> hero.etl.v1.ReportValidationResult
	25, // 21: hero.etl.v1.ReportValidationResult.errors:type_name -> hero.etl.v1.ValidationError
	2,  // 22: hero.etl.v1.UpdateJobProgressResponse.job:type_name -> hero.etl.v1.ETLJob
	28, // 23: hero.etl.v1.GetAgencyStatsResponse.agency_stats:type_name -> hero.etl.v1.AgencyStats
	36, // 24: hero.etl.v1.GetAgencyStatsResponse.recent_jobs:type_name -> hero.etl.v1.JobSummary
	0,  // 25: hero.etl.v1.JobSummary.status:type_name -> hero.etl.v1.JobStatus
	1,  // 26: hero.etl.v1.JobSummary.output_format:type_name -> hero.etl.v1.OutputFormat
	4,  // 27: hero.etl.v1.ETLService.ProcessReports:input_type -> hero.etl.v1.ProcessReportsRequest
	19, // 28: hero.etl.v1.ETLService.ExtractReportData:input_type -> hero.etl.v1.ExtractReportDataRequest
	23, // 29: hero.etl.v1.ETLService.TestReportMapping:input_type -> hero.etl.v1.TestReportMappingRequest
	21, // 30: hero.etl.v1.ETLService.TestReportTransformation:input_type -> hero.etl.v1.TestReportTransformationRequest
	6,  // 31: hero.etl.v1.ETLService.GetJob:input_type -> hero.etl.v1.GetJobRequest
	8,  // 32: hero.etl.v1.ETLService.ListJobs:input_type -> hero.etl.v1.ListJobsRequest
	10, // 33: hero.etl.v1.ETLService.CancelJob:input_type -> hero.etl.v1.CancelJobRequest
	12, // 34: hero.etl.v1.ETLService.RetryJob:input_type -> hero.etl.v1.RetryJobRequest
	14, // 35: hero.etl.v1.ETLService.DownloadJobContent:input_type -> hero.etl.v1.DownloadJobContentRequest
	16, // 36: hero.etl.v1.ETLService.ListAgencies:input_type -> hero.etl.v1.ListAgenciesRequest
	26, // 37: hero.etl.v1.ETLService.GetJobStats:input_type -> hero.etl.v1.GetJobStatsRequest
	29, // 38: hero.etl.v1.ETLService.ValidateReports:input_type -> hero.etl.v1.ValidateReportsRequest
	32, // 39: hero.etl.v1.ETLService.UpdateJobProgress:input_type -> hero.etl.v1.UpdateJobProgressRequest
	34, // 40: hero.etl.v1.ETLService.GetAgencyStats:input_type -> hero.etl.v1.GetAgencyStatsRequest
	5,  // 41: hero.etl.v1.ETLService.ProcessReports:output_type -> hero.etl.v1.ProcessReportsResponse
	20, // 42: hero.etl.v1.ETLService.ExtractReportData:output_type -> hero.etl.v1.ExtractReportDataResponse
	24, // 43: hero.etl.v1.ETLService.TestReportMapping:output_type -> hero.etl.v1.TestReportMappingResponse
	22, // 44: hero.etl.v1.ETLService.TestReportTransformation:output_type -> hero.etl.v1.TestReportTransformationResponse
	7,  // 45: hero.etl.v1.ETLService.GetJob:output_type -> hero.etl.v1.GetJobResponse
	9,  // 46: hero.etl.v1.ETLService.ListJobs:output_type -> hero.etl.v1.ListJobsResponse
	11, // 47: hero.etl.v1.ETLService.CancelJob:output_type -> hero.etl.v1.CancelJobResponse
	13, // 48: hero.etl.v1.ETLService.RetryJob:output_type -> hero.etl.v1.RetryJobResponse
	15, // 49: hero.etl.v1.ETLService.DownloadJobContent:output_type -> hero.etl.v1.DownloadJobContentResponse
	17, // 50: hero.etl.v1.ETLService.ListAgencies:output_type -> hero.etl.v1.ListAgenciesResponse
	27, // 51: hero.etl.v1.ETLService.GetJobStats:output_type -> hero.etl.v1.GetJobStatsResponse
	30, // 52: hero.etl.v1.ETLService.ValidateReports:output_type -> hero.etl.v1.ValidateReportsResponse
	33, // 53: hero.etl.v1.ETLService.UpdateJobProgress:output_type -> hero.etl.v1.UpdateJobProgressResponse
	35, // 54: hero.etl.v1.ETLService.GetAgencyStats:output_type -> hero.etl.v1.GetAgencyStatsResponse
	41, // [41:55] is the sub-list for method output_type
	27, // [27:41] is the sub-list for method input_type
	27, // [27:27] is the sub-list for extension type_name
	27, // [27:27] is the sub-list for extension extendee
	0,  // [0:27] is the sub-list for field type_name
}

func init() { file_hero_etl_v1_etl_proto_init() }
func file_hero_etl_v1_etl_proto_init() {
	if File_hero_etl_v1_etl_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_hero_etl_v1_etl_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   35,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_hero_etl_v1_etl_proto_goTypes,
		DependencyIndexes: file_hero_etl_v1_etl_proto_depIdxs,
		EnumInfos:         file_hero_etl_v1_etl_proto_enumTypes,
		MessageInfos:      file_hero_etl_v1_etl_proto_msgTypes,
	}.Build()
	File_hero_etl_v1_etl_proto = out.File
	file_hero_etl_v1_etl_proto_rawDesc = nil
	file_hero_etl_v1_etl_proto_goTypes = nil
	file_hero_etl_v1_etl_proto_depIdxs = nil
}
