// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: hero/property/v1/property.proto

package propertyconnect

import (
	context "context"
	errors "errors"
	http "net/http"
	v1 "proto/hero/property/v1"
	strings "strings"

	connect "connectrpc.com/connect"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// PropertyServiceName is the fully-qualified name of the PropertyService service.
	PropertyServiceName = "hero.property.v1.PropertyService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// PropertyServiceCreatePropertyProcedure is the fully-qualified name of the PropertyService's
	// CreateProperty RPC.
	PropertyServiceCreatePropertyProcedure = "/hero.property.v1.PropertyService/CreateProperty"
	// PropertyServiceGetPropertyProcedure is the fully-qualified name of the PropertyService's
	// GetProperty RPC.
	PropertyServiceGetPropertyProcedure = "/hero.property.v1.PropertyService/GetProperty"
	// PropertyServiceListPropertiesProcedure is the fully-qualified name of the PropertyService's
	// ListProperties RPC.
	PropertyServiceListPropertiesProcedure = "/hero.property.v1.PropertyService/ListProperties"
	// PropertyServiceUpdatePropertyProcedure is the fully-qualified name of the PropertyService's
	// UpdateProperty RPC.
	PropertyServiceUpdatePropertyProcedure = "/hero.property.v1.PropertyService/UpdateProperty"
	// PropertyServiceDeletePropertyProcedure is the fully-qualified name of the PropertyService's
	// DeleteProperty RPC.
	PropertyServiceDeletePropertyProcedure = "/hero.property.v1.PropertyService/DeleteProperty"
	// PropertyServiceSearchPropertiesProcedure is the fully-qualified name of the PropertyService's
	// SearchProperties RPC.
	PropertyServiceSearchPropertiesProcedure = "/hero.property.v1.PropertyService/SearchProperties"
	// PropertyServiceBatchGetPropertiesProcedure is the fully-qualified name of the PropertyService's
	// BatchGetProperties RPC.
	PropertyServiceBatchGetPropertiesProcedure = "/hero.property.v1.PropertyService/BatchGetProperties"
	// PropertyServiceAddCustodyEventProcedure is the fully-qualified name of the PropertyService's
	// AddCustodyEvent RPC.
	PropertyServiceAddCustodyEventProcedure = "/hero.property.v1.PropertyService/AddCustodyEvent"
	// PropertyServiceGetCustodyChainProcedure is the fully-qualified name of the PropertyService's
	// GetCustodyChain RPC.
	PropertyServiceGetCustodyChainProcedure = "/hero.property.v1.PropertyService/GetCustodyChain"
	// PropertyServiceListPropertyFileAttachmentsProcedure is the fully-qualified name of the
	// PropertyService's ListPropertyFileAttachments RPC.
	PropertyServiceListPropertyFileAttachmentsProcedure = "/hero.property.v1.PropertyService/ListPropertyFileAttachments"
	// PropertyServiceAddPropertyFileAttachmentProcedure is the fully-qualified name of the
	// PropertyService's AddPropertyFileAttachment RPC.
	PropertyServiceAddPropertyFileAttachmentProcedure = "/hero.property.v1.PropertyService/AddPropertyFileAttachment"
	// PropertyServiceRemovePropertyFileAttachmentProcedure is the fully-qualified name of the
	// PropertyService's RemovePropertyFileAttachment RPC.
	PropertyServiceRemovePropertyFileAttachmentProcedure = "/hero.property.v1.PropertyService/RemovePropertyFileAttachment"
)

// PropertyServiceClient is a client for the hero.property.v1.PropertyService service.
type PropertyServiceClient interface {
	// CreateProperty creates a new property
	CreateProperty(context.Context, *connect.Request[v1.CreatePropertyRequest]) (*connect.Response[v1.CreatePropertyResponse], error)
	// GetProperty retrieves a property by its ID
	GetProperty(context.Context, *connect.Request[v1.GetPropertyRequest]) (*connect.Response[v1.GetPropertyResponse], error)
	// ListProperties lists properties with pagination and filtering
	ListProperties(context.Context, *connect.Request[v1.ListPropertiesRequest]) (*connect.Response[v1.ListPropertiesResponse], error)
	// UpdateProperty updates an existing property
	UpdateProperty(context.Context, *connect.Request[v1.UpdatePropertyRequest]) (*connect.Response[v1.UpdatePropertyResponse], error)
	// DeleteProperty deletes a property
	DeleteProperty(context.Context, *connect.Request[v1.DeletePropertyRequest]) (*connect.Response[v1.DeletePropertyResponse], error)
	// SearchProperties performs advanced search on properties
	SearchProperties(context.Context, *connect.Request[v1.SearchPropertiesRequest]) (*connect.Response[v1.SearchPropertiesResponse], error)
	// BatchGetProperties retrieves multiple properties by their IDs
	BatchGetProperties(context.Context, *connect.Request[v1.BatchGetPropertiesRequest]) (*connect.Response[v1.BatchGetPropertiesResponse], error)
	// AddCustodyEvent adds a new custody event to a property's chain of custody
	AddCustodyEvent(context.Context, *connect.Request[v1.AddCustodyEventRequest]) (*connect.Response[v1.AddCustodyEventResponse], error)
	// GetCustodyChain returns the complete chain of custody for a property
	GetCustodyChain(context.Context, *connect.Request[v1.GetCustodyChainRequest]) (*connect.Response[v1.GetCustodyChainResponse], error)
	// ListPropertyFileAttachments lists all file attachments for a property
	ListPropertyFileAttachments(context.Context, *connect.Request[v1.ListPropertyFileAttachmentsRequest]) (*connect.Response[v1.ListPropertyFileAttachmentsResponse], error)
	// AddPropertyFileAttachment adds a file attachment to a property
	AddPropertyFileAttachment(context.Context, *connect.Request[v1.AddPropertyFileAttachmentRequest]) (*connect.Response[v1.AddPropertyFileAttachmentResponse], error)
	// RemovePropertyFileAttachment removes a file attachment from a property
	RemovePropertyFileAttachment(context.Context, *connect.Request[v1.RemovePropertyFileAttachmentRequest]) (*connect.Response[v1.RemovePropertyFileAttachmentResponse], error)
}

// NewPropertyServiceClient constructs a client for the hero.property.v1.PropertyService service. By
// default, it uses the Connect protocol with the binary Protobuf Codec, asks for gzipped responses,
// and sends uncompressed requests. To use the gRPC or gRPC-Web protocols, supply the
// connect.WithGRPC() or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewPropertyServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) PropertyServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	propertyServiceMethods := v1.File_hero_property_v1_property_proto.Services().ByName("PropertyService").Methods()
	return &propertyServiceClient{
		createProperty: connect.NewClient[v1.CreatePropertyRequest, v1.CreatePropertyResponse](
			httpClient,
			baseURL+PropertyServiceCreatePropertyProcedure,
			connect.WithSchema(propertyServiceMethods.ByName("CreateProperty")),
			connect.WithClientOptions(opts...),
		),
		getProperty: connect.NewClient[v1.GetPropertyRequest, v1.GetPropertyResponse](
			httpClient,
			baseURL+PropertyServiceGetPropertyProcedure,
			connect.WithSchema(propertyServiceMethods.ByName("GetProperty")),
			connect.WithClientOptions(opts...),
		),
		listProperties: connect.NewClient[v1.ListPropertiesRequest, v1.ListPropertiesResponse](
			httpClient,
			baseURL+PropertyServiceListPropertiesProcedure,
			connect.WithSchema(propertyServiceMethods.ByName("ListProperties")),
			connect.WithClientOptions(opts...),
		),
		updateProperty: connect.NewClient[v1.UpdatePropertyRequest, v1.UpdatePropertyResponse](
			httpClient,
			baseURL+PropertyServiceUpdatePropertyProcedure,
			connect.WithSchema(propertyServiceMethods.ByName("UpdateProperty")),
			connect.WithClientOptions(opts...),
		),
		deleteProperty: connect.NewClient[v1.DeletePropertyRequest, v1.DeletePropertyResponse](
			httpClient,
			baseURL+PropertyServiceDeletePropertyProcedure,
			connect.WithSchema(propertyServiceMethods.ByName("DeleteProperty")),
			connect.WithClientOptions(opts...),
		),
		searchProperties: connect.NewClient[v1.SearchPropertiesRequest, v1.SearchPropertiesResponse](
			httpClient,
			baseURL+PropertyServiceSearchPropertiesProcedure,
			connect.WithSchema(propertyServiceMethods.ByName("SearchProperties")),
			connect.WithClientOptions(opts...),
		),
		batchGetProperties: connect.NewClient[v1.BatchGetPropertiesRequest, v1.BatchGetPropertiesResponse](
			httpClient,
			baseURL+PropertyServiceBatchGetPropertiesProcedure,
			connect.WithSchema(propertyServiceMethods.ByName("BatchGetProperties")),
			connect.WithClientOptions(opts...),
		),
		addCustodyEvent: connect.NewClient[v1.AddCustodyEventRequest, v1.AddCustodyEventResponse](
			httpClient,
			baseURL+PropertyServiceAddCustodyEventProcedure,
			connect.WithSchema(propertyServiceMethods.ByName("AddCustodyEvent")),
			connect.WithClientOptions(opts...),
		),
		getCustodyChain: connect.NewClient[v1.GetCustodyChainRequest, v1.GetCustodyChainResponse](
			httpClient,
			baseURL+PropertyServiceGetCustodyChainProcedure,
			connect.WithSchema(propertyServiceMethods.ByName("GetCustodyChain")),
			connect.WithClientOptions(opts...),
		),
		listPropertyFileAttachments: connect.NewClient[v1.ListPropertyFileAttachmentsRequest, v1.ListPropertyFileAttachmentsResponse](
			httpClient,
			baseURL+PropertyServiceListPropertyFileAttachmentsProcedure,
			connect.WithSchema(propertyServiceMethods.ByName("ListPropertyFileAttachments")),
			connect.WithClientOptions(opts...),
		),
		addPropertyFileAttachment: connect.NewClient[v1.AddPropertyFileAttachmentRequest, v1.AddPropertyFileAttachmentResponse](
			httpClient,
			baseURL+PropertyServiceAddPropertyFileAttachmentProcedure,
			connect.WithSchema(propertyServiceMethods.ByName("AddPropertyFileAttachment")),
			connect.WithClientOptions(opts...),
		),
		removePropertyFileAttachment: connect.NewClient[v1.RemovePropertyFileAttachmentRequest, v1.RemovePropertyFileAttachmentResponse](
			httpClient,
			baseURL+PropertyServiceRemovePropertyFileAttachmentProcedure,
			connect.WithSchema(propertyServiceMethods.ByName("RemovePropertyFileAttachment")),
			connect.WithClientOptions(opts...),
		),
	}
}

// propertyServiceClient implements PropertyServiceClient.
type propertyServiceClient struct {
	createProperty               *connect.Client[v1.CreatePropertyRequest, v1.CreatePropertyResponse]
	getProperty                  *connect.Client[v1.GetPropertyRequest, v1.GetPropertyResponse]
	listProperties               *connect.Client[v1.ListPropertiesRequest, v1.ListPropertiesResponse]
	updateProperty               *connect.Client[v1.UpdatePropertyRequest, v1.UpdatePropertyResponse]
	deleteProperty               *connect.Client[v1.DeletePropertyRequest, v1.DeletePropertyResponse]
	searchProperties             *connect.Client[v1.SearchPropertiesRequest, v1.SearchPropertiesResponse]
	batchGetProperties           *connect.Client[v1.BatchGetPropertiesRequest, v1.BatchGetPropertiesResponse]
	addCustodyEvent              *connect.Client[v1.AddCustodyEventRequest, v1.AddCustodyEventResponse]
	getCustodyChain              *connect.Client[v1.GetCustodyChainRequest, v1.GetCustodyChainResponse]
	listPropertyFileAttachments  *connect.Client[v1.ListPropertyFileAttachmentsRequest, v1.ListPropertyFileAttachmentsResponse]
	addPropertyFileAttachment    *connect.Client[v1.AddPropertyFileAttachmentRequest, v1.AddPropertyFileAttachmentResponse]
	removePropertyFileAttachment *connect.Client[v1.RemovePropertyFileAttachmentRequest, v1.RemovePropertyFileAttachmentResponse]
}

// CreateProperty calls hero.property.v1.PropertyService.CreateProperty.
func (c *propertyServiceClient) CreateProperty(ctx context.Context, req *connect.Request[v1.CreatePropertyRequest]) (*connect.Response[v1.CreatePropertyResponse], error) {
	return c.createProperty.CallUnary(ctx, req)
}

// GetProperty calls hero.property.v1.PropertyService.GetProperty.
func (c *propertyServiceClient) GetProperty(ctx context.Context, req *connect.Request[v1.GetPropertyRequest]) (*connect.Response[v1.GetPropertyResponse], error) {
	return c.getProperty.CallUnary(ctx, req)
}

// ListProperties calls hero.property.v1.PropertyService.ListProperties.
func (c *propertyServiceClient) ListProperties(ctx context.Context, req *connect.Request[v1.ListPropertiesRequest]) (*connect.Response[v1.ListPropertiesResponse], error) {
	return c.listProperties.CallUnary(ctx, req)
}

// UpdateProperty calls hero.property.v1.PropertyService.UpdateProperty.
func (c *propertyServiceClient) UpdateProperty(ctx context.Context, req *connect.Request[v1.UpdatePropertyRequest]) (*connect.Response[v1.UpdatePropertyResponse], error) {
	return c.updateProperty.CallUnary(ctx, req)
}

// DeleteProperty calls hero.property.v1.PropertyService.DeleteProperty.
func (c *propertyServiceClient) DeleteProperty(ctx context.Context, req *connect.Request[v1.DeletePropertyRequest]) (*connect.Response[v1.DeletePropertyResponse], error) {
	return c.deleteProperty.CallUnary(ctx, req)
}

// SearchProperties calls hero.property.v1.PropertyService.SearchProperties.
func (c *propertyServiceClient) SearchProperties(ctx context.Context, req *connect.Request[v1.SearchPropertiesRequest]) (*connect.Response[v1.SearchPropertiesResponse], error) {
	return c.searchProperties.CallUnary(ctx, req)
}

// BatchGetProperties calls hero.property.v1.PropertyService.BatchGetProperties.
func (c *propertyServiceClient) BatchGetProperties(ctx context.Context, req *connect.Request[v1.BatchGetPropertiesRequest]) (*connect.Response[v1.BatchGetPropertiesResponse], error) {
	return c.batchGetProperties.CallUnary(ctx, req)
}

// AddCustodyEvent calls hero.property.v1.PropertyService.AddCustodyEvent.
func (c *propertyServiceClient) AddCustodyEvent(ctx context.Context, req *connect.Request[v1.AddCustodyEventRequest]) (*connect.Response[v1.AddCustodyEventResponse], error) {
	return c.addCustodyEvent.CallUnary(ctx, req)
}

// GetCustodyChain calls hero.property.v1.PropertyService.GetCustodyChain.
func (c *propertyServiceClient) GetCustodyChain(ctx context.Context, req *connect.Request[v1.GetCustodyChainRequest]) (*connect.Response[v1.GetCustodyChainResponse], error) {
	return c.getCustodyChain.CallUnary(ctx, req)
}

// ListPropertyFileAttachments calls hero.property.v1.PropertyService.ListPropertyFileAttachments.
func (c *propertyServiceClient) ListPropertyFileAttachments(ctx context.Context, req *connect.Request[v1.ListPropertyFileAttachmentsRequest]) (*connect.Response[v1.ListPropertyFileAttachmentsResponse], error) {
	return c.listPropertyFileAttachments.CallUnary(ctx, req)
}

// AddPropertyFileAttachment calls hero.property.v1.PropertyService.AddPropertyFileAttachment.
func (c *propertyServiceClient) AddPropertyFileAttachment(ctx context.Context, req *connect.Request[v1.AddPropertyFileAttachmentRequest]) (*connect.Response[v1.AddPropertyFileAttachmentResponse], error) {
	return c.addPropertyFileAttachment.CallUnary(ctx, req)
}

// RemovePropertyFileAttachment calls hero.property.v1.PropertyService.RemovePropertyFileAttachment.
func (c *propertyServiceClient) RemovePropertyFileAttachment(ctx context.Context, req *connect.Request[v1.RemovePropertyFileAttachmentRequest]) (*connect.Response[v1.RemovePropertyFileAttachmentResponse], error) {
	return c.removePropertyFileAttachment.CallUnary(ctx, req)
}

// PropertyServiceHandler is an implementation of the hero.property.v1.PropertyService service.
type PropertyServiceHandler interface {
	// CreateProperty creates a new property
	CreateProperty(context.Context, *connect.Request[v1.CreatePropertyRequest]) (*connect.Response[v1.CreatePropertyResponse], error)
	// GetProperty retrieves a property by its ID
	GetProperty(context.Context, *connect.Request[v1.GetPropertyRequest]) (*connect.Response[v1.GetPropertyResponse], error)
	// ListProperties lists properties with pagination and filtering
	ListProperties(context.Context, *connect.Request[v1.ListPropertiesRequest]) (*connect.Response[v1.ListPropertiesResponse], error)
	// UpdateProperty updates an existing property
	UpdateProperty(context.Context, *connect.Request[v1.UpdatePropertyRequest]) (*connect.Response[v1.UpdatePropertyResponse], error)
	// DeleteProperty deletes a property
	DeleteProperty(context.Context, *connect.Request[v1.DeletePropertyRequest]) (*connect.Response[v1.DeletePropertyResponse], error)
	// SearchProperties performs advanced search on properties
	SearchProperties(context.Context, *connect.Request[v1.SearchPropertiesRequest]) (*connect.Response[v1.SearchPropertiesResponse], error)
	// BatchGetProperties retrieves multiple properties by their IDs
	BatchGetProperties(context.Context, *connect.Request[v1.BatchGetPropertiesRequest]) (*connect.Response[v1.BatchGetPropertiesResponse], error)
	// AddCustodyEvent adds a new custody event to a property's chain of custody
	AddCustodyEvent(context.Context, *connect.Request[v1.AddCustodyEventRequest]) (*connect.Response[v1.AddCustodyEventResponse], error)
	// GetCustodyChain returns the complete chain of custody for a property
	GetCustodyChain(context.Context, *connect.Request[v1.GetCustodyChainRequest]) (*connect.Response[v1.GetCustodyChainResponse], error)
	// ListPropertyFileAttachments lists all file attachments for a property
	ListPropertyFileAttachments(context.Context, *connect.Request[v1.ListPropertyFileAttachmentsRequest]) (*connect.Response[v1.ListPropertyFileAttachmentsResponse], error)
	// AddPropertyFileAttachment adds a file attachment to a property
	AddPropertyFileAttachment(context.Context, *connect.Request[v1.AddPropertyFileAttachmentRequest]) (*connect.Response[v1.AddPropertyFileAttachmentResponse], error)
	// RemovePropertyFileAttachment removes a file attachment from a property
	RemovePropertyFileAttachment(context.Context, *connect.Request[v1.RemovePropertyFileAttachmentRequest]) (*connect.Response[v1.RemovePropertyFileAttachmentResponse], error)
}

// NewPropertyServiceHandler builds an HTTP handler from the service implementation. It returns the
// path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewPropertyServiceHandler(svc PropertyServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	propertyServiceMethods := v1.File_hero_property_v1_property_proto.Services().ByName("PropertyService").Methods()
	propertyServiceCreatePropertyHandler := connect.NewUnaryHandler(
		PropertyServiceCreatePropertyProcedure,
		svc.CreateProperty,
		connect.WithSchema(propertyServiceMethods.ByName("CreateProperty")),
		connect.WithHandlerOptions(opts...),
	)
	propertyServiceGetPropertyHandler := connect.NewUnaryHandler(
		PropertyServiceGetPropertyProcedure,
		svc.GetProperty,
		connect.WithSchema(propertyServiceMethods.ByName("GetProperty")),
		connect.WithHandlerOptions(opts...),
	)
	propertyServiceListPropertiesHandler := connect.NewUnaryHandler(
		PropertyServiceListPropertiesProcedure,
		svc.ListProperties,
		connect.WithSchema(propertyServiceMethods.ByName("ListProperties")),
		connect.WithHandlerOptions(opts...),
	)
	propertyServiceUpdatePropertyHandler := connect.NewUnaryHandler(
		PropertyServiceUpdatePropertyProcedure,
		svc.UpdateProperty,
		connect.WithSchema(propertyServiceMethods.ByName("UpdateProperty")),
		connect.WithHandlerOptions(opts...),
	)
	propertyServiceDeletePropertyHandler := connect.NewUnaryHandler(
		PropertyServiceDeletePropertyProcedure,
		svc.DeleteProperty,
		connect.WithSchema(propertyServiceMethods.ByName("DeleteProperty")),
		connect.WithHandlerOptions(opts...),
	)
	propertyServiceSearchPropertiesHandler := connect.NewUnaryHandler(
		PropertyServiceSearchPropertiesProcedure,
		svc.SearchProperties,
		connect.WithSchema(propertyServiceMethods.ByName("SearchProperties")),
		connect.WithHandlerOptions(opts...),
	)
	propertyServiceBatchGetPropertiesHandler := connect.NewUnaryHandler(
		PropertyServiceBatchGetPropertiesProcedure,
		svc.BatchGetProperties,
		connect.WithSchema(propertyServiceMethods.ByName("BatchGetProperties")),
		connect.WithHandlerOptions(opts...),
	)
	propertyServiceAddCustodyEventHandler := connect.NewUnaryHandler(
		PropertyServiceAddCustodyEventProcedure,
		svc.AddCustodyEvent,
		connect.WithSchema(propertyServiceMethods.ByName("AddCustodyEvent")),
		connect.WithHandlerOptions(opts...),
	)
	propertyServiceGetCustodyChainHandler := connect.NewUnaryHandler(
		PropertyServiceGetCustodyChainProcedure,
		svc.GetCustodyChain,
		connect.WithSchema(propertyServiceMethods.ByName("GetCustodyChain")),
		connect.WithHandlerOptions(opts...),
	)
	propertyServiceListPropertyFileAttachmentsHandler := connect.NewUnaryHandler(
		PropertyServiceListPropertyFileAttachmentsProcedure,
		svc.ListPropertyFileAttachments,
		connect.WithSchema(propertyServiceMethods.ByName("ListPropertyFileAttachments")),
		connect.WithHandlerOptions(opts...),
	)
	propertyServiceAddPropertyFileAttachmentHandler := connect.NewUnaryHandler(
		PropertyServiceAddPropertyFileAttachmentProcedure,
		svc.AddPropertyFileAttachment,
		connect.WithSchema(propertyServiceMethods.ByName("AddPropertyFileAttachment")),
		connect.WithHandlerOptions(opts...),
	)
	propertyServiceRemovePropertyFileAttachmentHandler := connect.NewUnaryHandler(
		PropertyServiceRemovePropertyFileAttachmentProcedure,
		svc.RemovePropertyFileAttachment,
		connect.WithSchema(propertyServiceMethods.ByName("RemovePropertyFileAttachment")),
		connect.WithHandlerOptions(opts...),
	)
	return "/hero.property.v1.PropertyService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case PropertyServiceCreatePropertyProcedure:
			propertyServiceCreatePropertyHandler.ServeHTTP(w, r)
		case PropertyServiceGetPropertyProcedure:
			propertyServiceGetPropertyHandler.ServeHTTP(w, r)
		case PropertyServiceListPropertiesProcedure:
			propertyServiceListPropertiesHandler.ServeHTTP(w, r)
		case PropertyServiceUpdatePropertyProcedure:
			propertyServiceUpdatePropertyHandler.ServeHTTP(w, r)
		case PropertyServiceDeletePropertyProcedure:
			propertyServiceDeletePropertyHandler.ServeHTTP(w, r)
		case PropertyServiceSearchPropertiesProcedure:
			propertyServiceSearchPropertiesHandler.ServeHTTP(w, r)
		case PropertyServiceBatchGetPropertiesProcedure:
			propertyServiceBatchGetPropertiesHandler.ServeHTTP(w, r)
		case PropertyServiceAddCustodyEventProcedure:
			propertyServiceAddCustodyEventHandler.ServeHTTP(w, r)
		case PropertyServiceGetCustodyChainProcedure:
			propertyServiceGetCustodyChainHandler.ServeHTTP(w, r)
		case PropertyServiceListPropertyFileAttachmentsProcedure:
			propertyServiceListPropertyFileAttachmentsHandler.ServeHTTP(w, r)
		case PropertyServiceAddPropertyFileAttachmentProcedure:
			propertyServiceAddPropertyFileAttachmentHandler.ServeHTTP(w, r)
		case PropertyServiceRemovePropertyFileAttachmentProcedure:
			propertyServiceRemovePropertyFileAttachmentHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedPropertyServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedPropertyServiceHandler struct{}

func (UnimplementedPropertyServiceHandler) CreateProperty(context.Context, *connect.Request[v1.CreatePropertyRequest]) (*connect.Response[v1.CreatePropertyResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.property.v1.PropertyService.CreateProperty is not implemented"))
}

func (UnimplementedPropertyServiceHandler) GetProperty(context.Context, *connect.Request[v1.GetPropertyRequest]) (*connect.Response[v1.GetPropertyResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.property.v1.PropertyService.GetProperty is not implemented"))
}

func (UnimplementedPropertyServiceHandler) ListProperties(context.Context, *connect.Request[v1.ListPropertiesRequest]) (*connect.Response[v1.ListPropertiesResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.property.v1.PropertyService.ListProperties is not implemented"))
}

func (UnimplementedPropertyServiceHandler) UpdateProperty(context.Context, *connect.Request[v1.UpdatePropertyRequest]) (*connect.Response[v1.UpdatePropertyResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.property.v1.PropertyService.UpdateProperty is not implemented"))
}

func (UnimplementedPropertyServiceHandler) DeleteProperty(context.Context, *connect.Request[v1.DeletePropertyRequest]) (*connect.Response[v1.DeletePropertyResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.property.v1.PropertyService.DeleteProperty is not implemented"))
}

func (UnimplementedPropertyServiceHandler) SearchProperties(context.Context, *connect.Request[v1.SearchPropertiesRequest]) (*connect.Response[v1.SearchPropertiesResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.property.v1.PropertyService.SearchProperties is not implemented"))
}

func (UnimplementedPropertyServiceHandler) BatchGetProperties(context.Context, *connect.Request[v1.BatchGetPropertiesRequest]) (*connect.Response[v1.BatchGetPropertiesResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.property.v1.PropertyService.BatchGetProperties is not implemented"))
}

func (UnimplementedPropertyServiceHandler) AddCustodyEvent(context.Context, *connect.Request[v1.AddCustodyEventRequest]) (*connect.Response[v1.AddCustodyEventResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.property.v1.PropertyService.AddCustodyEvent is not implemented"))
}

func (UnimplementedPropertyServiceHandler) GetCustodyChain(context.Context, *connect.Request[v1.GetCustodyChainRequest]) (*connect.Response[v1.GetCustodyChainResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.property.v1.PropertyService.GetCustodyChain is not implemented"))
}

func (UnimplementedPropertyServiceHandler) ListPropertyFileAttachments(context.Context, *connect.Request[v1.ListPropertyFileAttachmentsRequest]) (*connect.Response[v1.ListPropertyFileAttachmentsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.property.v1.PropertyService.ListPropertyFileAttachments is not implemented"))
}

func (UnimplementedPropertyServiceHandler) AddPropertyFileAttachment(context.Context, *connect.Request[v1.AddPropertyFileAttachmentRequest]) (*connect.Response[v1.AddPropertyFileAttachmentResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.property.v1.PropertyService.AddPropertyFileAttachment is not implemented"))
}

func (UnimplementedPropertyServiceHandler) RemovePropertyFileAttachment(context.Context, *connect.Request[v1.RemovePropertyFileAttachmentRequest]) (*connect.Response[v1.RemovePropertyFileAttachmentResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.property.v1.PropertyService.RemovePropertyFileAttachment is not implemented"))
}
