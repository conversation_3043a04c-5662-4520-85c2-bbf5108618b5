syntax = "proto3";

package hero.property.v1;

option go_package = "proto/hero/property/v1;property";

import "google/protobuf/struct.proto";





// -----------------------------------------------------------------------------
// Property-Specific Metadata and Chain of Custody
// -----------------------------------------------------------------------------

// PropertyType enumerates the types of property handling
enum NIBRSPropertyType {
  PROPERTY_TYPE_UNSPECIFIED = 0;
  PROPERTY_TYPE_NONE = 1;
  PROPERTY_TYPE_BURNED = 2;
  PROPERTY_TYPE_FORGED = 3;
  PROPERTY_TYPE_DAMAGED = 4;
  PROPERTY_TYPE_RECOVERED = 5;
  PROPERTY_TYPE_SEIZED = 6;
  PROPERTY_TYPE_STOLEN = 7;
  PROPERTY_TYPE_UNKNOWN = 8;
  PROPERTY_TYPE_FOUND = 9;
}

// PropertyStatus enumerates the status of property items
enum PropertyStatus {
  PROPERTY_STATUS_UNSPECIFIED = 0;
  PROPERTY_STATUS_INTAKE_PENDING = 1;
  PROPERTY_STATUS_COLLECTED = 2;           // Property collected into custody
  PROPERTY_STATUS_CHECKED_IN = 3;
  PROPERTY_STATUS_CHECKED_OUT = 4;
  PROPERTY_STATUS_RECOVERED = 5;
  PROPERTY_STATUS_FOUND = 6;
  PROPERTY_STATUS_SAFEKEEPING = 7;
  PROPERTY_STATUS_AWAITING_DISPOSITION = 8;
  PROPERTY_STATUS_DISPOSED = 9;
  PROPERTY_STATUS_MISSING = 10;
  PROPERTY_STATUS_STOLEN = 11;
}

// PropertyDisposalType enumerates how property was disposed
enum PropertyDisposalType {
  PROPERTY_DISPOSAL_TYPE_UNSPECIFIED = 0;
  PROPERTY_DISPOSAL_TYPE_RELEASED = 1;
  PROPERTY_DISPOSAL_TYPE_DESTROYED = 2;
  PROPERTY_DISPOSAL_TYPE_AUCTIONED = 3;
  PROPERTY_DISPOSAL_TYPE_AGENCY_RETAIN = 4;
  PROPERTY_DISPOSAL_TYPE_TRANSFERRED = 5;
}

// CustodyActionType enumerates the types of custody changes
enum CustodyActionType {
  CUSTODY_ACTION_TYPE_UNSPECIFIED = 0;
  CUSTODY_ACTION_TYPE_COLLECTED = 1;        // Property initially collected into custody
  CUSTODY_ACTION_TYPE_CHECKED_IN = 2;       // Property checked into evidence room
  CUSTODY_ACTION_TYPE_CHECKED_OUT = 3;      // Property checked out for analysis/court
  CUSTODY_ACTION_TYPE_TRANSFERRED = 4;      // Property transferred to another agency/person
  CUSTODY_ACTION_TYPE_RELEASED = 5;         // Property released to owner
  CUSTODY_ACTION_TYPE_DISPOSED = 6;         // Property disposed/destroyed
  CUSTODY_ACTION_TYPE_LOGGED = 7;           // Property reported but not in physical custody
  CUSTODY_ACTION_TYPE_MOVED = 8;            // Property moved to a new location
}

// EntityType enumerates the types of entities that can receive property
enum CustodyEntityType {
  CUSTODY_ENTITY_TYPE_UNSPECIFIED = 0;
  CUSTODY_ENTITY_TYPE_PERSON = 1;
  CUSTODY_ENTITY_TYPE_CRIME_LAB = 2;
  CUSTODY_ENTITY_TYPE_COURT = 3;
  CUSTODY_ENTITY_TYPE_SPECIALIST = 4;
  CUSTODY_ENTITY_TYPE_CSI = 5;
  CUSTODY_ENTITY_TYPE_ORGANIZATION = 6;
  CUSTODY_ENTITY_TYPE_OTHER = 7;
}

// AgencyType enumerates the types of agencies that can receive property
enum CustodyAgencyType {
  CUSTODY_AGENCY_TYPE_UNSPECIFIED = 0;
  CUSTODY_AGENCY_TYPE_PD = 1;           // Police Department
  CUSTODY_AGENCY_TYPE_OTHER = 2;        // Other agency type
}

// CustodyEvent represents a single chain of custody entry
message CustodyEvent {
  string timestamp = 1;                    // ISO8601 timestamp of the custody change
  string transferring_user_id = 2;         // User/Officer transferring the item
  string transferring_agency = 3;          // Agency transferring the item
  string receiving_user_id = 4;            // User/Officer/Agency receiving the item (optional for disposal)
  string receiving_agency = 5;             // Agency receiving the item (if different from current org)
  string new_location = 6;                 // New location (e.g., "Evidence Room A-12", "Lab XYZ")
  CustodyActionType action_type = 7;       // Type of custody action
  string notes = 8;                        // Reason/purpose for the change
  string case_number = 9;                  // Associated case number
  string evidence_number = 10;             // Evidence tracking number

  // Enhanced fields for detailed custody information
  string performing_officer_id = 11;       // Officer performing the action (may differ from transferring_user_id)
  CustodyEntityType receiving_entity_type = 12; // Type of receiving entity (Person, Crime Lab, Court, etc.)
  string receiving_entity_name = 13;       // Name of the receiving entity
  string checkout_length = 14;             // Length of check out (e.g., "2 weeks", "until court date")
  string reason = 15;                      // Specific reason for the action
  CustodyAgencyType receiving_agency_type = 16; // Type of receiving agency (PD, Other)
  bool confirmation_received = 17;         // Whether confirmation of receipt was obtained
  string collection_location = 18;         // Location where property was originally collected from
  PropertyDisposalType disposal_type = 19; // Type of disposal (for DISPOSED actions)
}

// NIBRSPropertyDescription enumerates NIBRS property description codes
enum NIBRSPropertyDescription {
  NIBRS_PROPERTY_DESCRIPTION_UNSPECIFIED = 0;
  NIBRS_PROPERTY_DESCRIPTION_AIRCRAFT = 1;                              // 01 = Aircraft
  NIBRS_PROPERTY_DESCRIPTION_ALCOHOL = 2;                               // 02 = Alcohol  
  NIBRS_PROPERTY_DESCRIPTION_AUTOMOBILES = 3;                           // 03 = Automobiles
  NIBRS_PROPERTY_DESCRIPTION_BICYCLES = 4;                              // 04 = Bicycles
  NIBRS_PROPERTY_DESCRIPTION_BUSES = 5;                                 // 05 = Buses
  NIBRS_PROPERTY_DESCRIPTION_CLOTHES_FURS = 6;                          // 06 = Clothes/Furs
  NIBRS_PROPERTY_DESCRIPTION_COMPUTER_HARDWARE_SOFTWARE = 7;            // 07 = Computer Hardware/Software
  NIBRS_PROPERTY_DESCRIPTION_CONSUMABLE_GOODS = 8;                      // 08 = Consumable Goods
  NIBRS_PROPERTY_DESCRIPTION_CREDIT_DEBIT_CARDS = 9;                    // 09 = Credit/Debit Cards
  NIBRS_PROPERTY_DESCRIPTION_DRUGS_NARCOTICS = 10;                      // 10 = Drugs/Narcotics
  NIBRS_PROPERTY_DESCRIPTION_DRUG_NARCOTIC_EQUIPMENT = 11;              // 11 = Drug/Narcotic Equipment
  NIBRS_PROPERTY_DESCRIPTION_FARM_EQUIPMENT = 12;                       // 12 = Farm Equipment
  NIBRS_PROPERTY_DESCRIPTION_FIREARMS = 13;                             // 13 = Firearms
  NIBRS_PROPERTY_DESCRIPTION_GAMBLING_EQUIPMENT = 14;                   // 14 = Gambling Equipment
  NIBRS_PROPERTY_DESCRIPTION_HEAVY_CONSTRUCTION_INDUSTRIAL_EQUIPMENT = 15; // 15 = Heavy Construction/Industrial Equipment
  NIBRS_PROPERTY_DESCRIPTION_HOUSEHOLD_GOODS = 16;                      // 16 = Household Goods
  NIBRS_PROPERTY_DESCRIPTION_JEWELRY_PRECIOUS_METALS_GEMS = 17;         // 17 = Jewelry/Precious Metals/Gems
  NIBRS_PROPERTY_DESCRIPTION_LIVESTOCK = 18;                            // 18 = Livestock
  NIBRS_PROPERTY_DESCRIPTION_MERCHANDISE = 19;                          // 19 = Merchandise
  NIBRS_PROPERTY_DESCRIPTION_MONEY = 20;                                // 20 = Money
  NIBRS_PROPERTY_DESCRIPTION_NEGOTIABLE_INSTRUMENTS = 21;               // 21 = Negotiable Instruments
  NIBRS_PROPERTY_DESCRIPTION_NONNEGOTIABLE_INSTRUMENTS = 22;            // 22 = Nonnegotiable Instruments
  NIBRS_PROPERTY_DESCRIPTION_OFFICE_TYPE_EQUIPMENT = 23;                // 23 = Office-type Equipment
  NIBRS_PROPERTY_DESCRIPTION_OTHER_MOTOR_VEHICLES = 24;                 // 24 = Other Motor Vehicles
  NIBRS_PROPERTY_DESCRIPTION_PURSES_HANDBAGS_WALLETS = 25;              // 25 = Purses/Handbags/Wallets
  NIBRS_PROPERTY_DESCRIPTION_RADIOS_TVS_VCRS = 26;                      // 26 = Radios/TVs/VCRs
  NIBRS_PROPERTY_DESCRIPTION_RECORDINGS_AUDIO_VISUAL = 27;              // 27 = Recordings–Audio/Visual
  NIBRS_PROPERTY_DESCRIPTION_RECREATIONAL_VEHICLES = 28;                // 28 = Recreational Vehicles
  NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_SINGLE_OCCUPANCY_DWELLINGS = 29; // 29 = Structures–Single Occupancy Dwellings
  NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_OTHER_DWELLINGS = 30;           // 30 = Structures–Other Dwellings
  NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_OTHER_COMMERCIAL_BUSINESS = 31; // 31 = Structures–Other Commercial/Business
  NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_INDUSTRIAL_MANUFACTURING = 32;  // 32 = Structures–Industrial/Manufacturing
  NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_PUBLIC_COMMUNITY = 33;          // 33 = Structures–Public/Community
  NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_STORAGE = 34;                   // 34 = Structures–Storage
  NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_OTHER = 35;                     // 35 = Structures–Other
  NIBRS_PROPERTY_DESCRIPTION_TOOLS = 36;                                // 36 = Tools
  NIBRS_PROPERTY_DESCRIPTION_TRUCKS = 37;                               // 37 = Trucks
  NIBRS_PROPERTY_DESCRIPTION_VEHICLE_PARTS_ACCESSORIES = 38;            // 38 = Vehicle Parts/Accessories
  NIBRS_PROPERTY_DESCRIPTION_WATERCRAFT = 39;                           // 39 = Watercraft
  NIBRS_PROPERTY_DESCRIPTION_AIRCRAFT_PARTS_ACCESSORIES = 41;           // 41 = Aircraft Parts/Accessories
  NIBRS_PROPERTY_DESCRIPTION_ARTISTIC_SUPPLIES_ACCESSORIES = 42;        // 42 = Artistic Supplies/Accessories
  NIBRS_PROPERTY_DESCRIPTION_BUILDING_MATERIALS = 43;                   // 43 = Building Materials
  NIBRS_PROPERTY_DESCRIPTION_CAMPING_HUNTING_FISHING_EQUIPMENT = 44;    // 44 = Camping/Hunting/Fishing Equipment/Supplies
  NIBRS_PROPERTY_DESCRIPTION_CHEMICALS = 45;                            // 45 = Chemicals
  NIBRS_PROPERTY_DESCRIPTION_COLLECTIONS_COLLECTIBLES = 46;             // 46 = Collections/Collectibles
  NIBRS_PROPERTY_DESCRIPTION_CROPS = 47;                                // 47 = Crops
  NIBRS_PROPERTY_DESCRIPTION_DOCUMENTS_PERSONAL_OR_BUSINESS = 48;       // 48 = Documents/Personal or Business
  NIBRS_PROPERTY_DESCRIPTION_EXPLOSIVES = 49;                           // 49 = Explosives
  NIBRS_PROPERTY_DESCRIPTION_FIREARM_ACCESSORIES = 59;                  // 59 = Firearm Accessories
  NIBRS_PROPERTY_DESCRIPTION_FUEL = 64;                                 // 64 = Fuel
  NIBRS_PROPERTY_DESCRIPTION_IDENTITY_DOCUMENTS = 65;                   // 65 = Identity Documents
  NIBRS_PROPERTY_DESCRIPTION_IDENTITY_INTANGIBLE = 66;                  // 66 = Identity–Intangible
  NIBRS_PROPERTY_DESCRIPTION_LAW_ENFORCEMENT_EQUIPMENT = 67;            // 67 = Law Enforcement Equipment
  NIBRS_PROPERTY_DESCRIPTION_LAWN_YARD_GARDEN_EQUIPMENT = 68;           // 68 = Lawn/Yard/Garden Equipment
  NIBRS_PROPERTY_DESCRIPTION_LOGGING_EQUIPMENT = 69;                    // 69 = Logging Equipment
  NIBRS_PROPERTY_DESCRIPTION_MEDICAL_MEDICAL_LAB_EQUIPMENT = 70;        // 70 = Medical/Medical Lab Equipment
  NIBRS_PROPERTY_DESCRIPTION_METALS_NON_PRECIOUS = 71;                  // 71 = Metals, Non-Precious
  NIBRS_PROPERTY_DESCRIPTION_MUSICAL_INSTRUMENTS = 72;                  // 72 = Musical Instruments
  NIBRS_PROPERTY_DESCRIPTION_PETS = 73;                                 // 73 = Pets
  NIBRS_PROPERTY_DESCRIPTION_PHOTOGRAPHIC_OPTICAL_EQUIPMENT = 74;       // 74 = Photographic/Optical Equipment
  NIBRS_PROPERTY_DESCRIPTION_PORTABLE_ELECTRONIC_COMMUNICATIONS = 75;   // 75 = Portable Electronic Communications
  NIBRS_PROPERTY_DESCRIPTION_RECREATIONAL_SPORTS_EQUIPMENT = 76;        // 76 = Recreational/Sports Equipment
  NIBRS_PROPERTY_DESCRIPTION_OTHER = 77;                                // 77 = Other
  NIBRS_PROPERTY_DESCRIPTION_TRAILERS = 78;                             // 78 = Trailers
  NIBRS_PROPERTY_DESCRIPTION_WATERCRAFT_EQUIPMENT_PARTS_ACCESSORIES = 79; // 79 = Watercraft Equipment/Parts/Accessories
  NIBRS_PROPERTY_DESCRIPTION_WEAPONS_OTHER = 80;                        // 80 = Weapons–Other
  NIBRS_PROPERTY_DESCRIPTION_PENDING_INVENTORY = 88;                    // 88 = Pending Inventory
  NIBRS_PROPERTY_DESCRIPTION_BLANK = 99;                                // 99 = ( blank )
}


// -----------------------------------------------------------------------------
// Property Service Definition
// -----------------------------------------------------------------------------

// Property represents a property entity
message Property {
  string id = 1;                           // Unique identifier for the property
  int32 org_id = 2;                        // Organization identifier
  string property_number = 3;              // Property/Evidence number for tracking
  PropertyStatus property_status = 4;       // Current status of the property
  bool is_evidence = 5;                    // Flag indicating whether this is evidence
  string retention_period = 6;             // Retention period for evidence
  PropertyDisposalType disposal_type = 7;  // How property was disposed
  string notes = 8;                        // Additional notes about the property 
  string current_custodian = 9;           // Current person/agency with custody
  string current_location = 10;            // Current location of the property
  repeated CustodyEvent custody_chain = 11; // Complete chain of custody history

  // Link to the dynamic schema used for this property (reuses Entity Schema registry)
  string schema_id = 12;                   // ID of the schema this property conforms to
  int32 schema_version = 13;               // Version of the schema used

  // Dynamic payload validated/rendered by the resolved schema
  google.protobuf.Struct details = 14;
  string create_time = 15;                 // ISO8601 timestamp when the property was created
  string update_time = 16;                 // ISO8601 timestamp when the property was last updated
  string created_by = 17;                  // User ID of the creator
  string updated_by = 18;                  // User ID of the last updater
  int32 version = 19;                      // Version number (for audit/history purposes)
  string resource_type = 20;               // Fixed value "PROPERTY"
  NIBRSPropertyType nibrs_property_type = 21;    // NIBRS property type classification
}

// DateRange represents a time range for filtering search results
message DateRange {
  string from = 1;  // Start time in RFC3339 format (inclusive)
  string to = 2;    // End time in RFC3339 format (inclusive)
}

// FieldQuery represents a field-specific search query
message FieldQuery {
  string field = 1;  // Field to search in (id, notes, location, current_custodian, current_location)
  string query = 2;  // Search term for this specific field
}

// HighlightResult represents highlighted search results for a property
message HighlightResult {
  string field = 1;                    // Field name that had a match
  repeated string fragments = 2;        // Highlighted fragments with matched terms
}

// SearchOrderBy defines the ordering options for search results
enum SearchOrderBy {
  SEARCH_ORDER_BY_UNSPECIFIED = 0;
  SEARCH_ORDER_BY_RELEVANCE = 1;
  SEARCH_ORDER_BY_CREATED_AT = 2;
  SEARCH_ORDER_BY_UPDATED_AT = 3;
  SEARCH_ORDER_BY_STATUS = 4;
}

// Request/Response Messages for CreateProperty
message CreatePropertyRequest {
  Property property = 1;
}

message CreatePropertyResponse {
  Property property = 1;
}

// Request/Response Messages for GetProperty
message GetPropertyRequest {
  string id = 1;
}

message GetPropertyResponse {
  Property property = 1;
}



// Request/Response Messages for ListProperties
message ListPropertiesRequest {
  int32 page_size = 1;
  string page_token = 2;
  NIBRSPropertyType nibrs_property_type = 3;
  PropertyStatus property_status = 4;
  string order_by = 5;
}

message ListPropertiesResponse {
  repeated Property properties = 1;
  string next_page_token = 2;
}

// Request/Response Messages for UpdateProperty
message UpdatePropertyRequest {
  Property property = 1;
}

message UpdatePropertyResponse {
  Property property = 1;
}

// Request/Response Messages for DeleteProperty
message DeletePropertyRequest {
  string id = 1;
}

message DeletePropertyResponse {
}

// Request/Response Messages for SearchProperties
message SearchPropertiesRequest {
  string query = 1;
  repeated FieldQuery field_queries = 2;
  DateRange date_range = 3;
  NIBRSPropertyType nibrs_property_type = 4;
  PropertyStatus property_status = 5;
  SearchOrderBy order_by = 6;
  int32 page_size = 7;
  string page_token = 8;
}

message SearchPropertiesResponse {
  repeated Property properties = 1;
  repeated HighlightResult highlights = 2;
  string next_page_token = 3;
  int32 total_count = 4;
}

// Request/Response Messages for BatchGetProperties
message BatchGetPropertiesRequest {
  repeated string ids = 1;
}

message BatchGetPropertiesResponse {
  repeated Property properties = 1;
}

// Request/Response Messages for AddCustodyEvent
message AddCustodyEventRequest {
  string property_id = 1;
  CustodyEvent custody_event = 2;
}

message AddCustodyEventResponse {
}

// Request/Response Messages for GetCustodyChain
message GetCustodyChainRequest {
  string property_id = 1;
}

message GetCustodyChainResponse {
  repeated CustodyEvent custody_chain = 1;
}

// Request/Response Messages for ListPropertyFileAttachments
message ListPropertyFileAttachmentsRequest {
  string property_id = 1;
  string file_category = 2;
  int32 page_size = 3;
  string page_token = 4;
}

message ListPropertyFileAttachmentsResponse {
  repeated PropertyFileReference file_attachments = 1;
  string next_page_token = 2;
}

// PropertyFileReference represents a file attachment for a property
message PropertyFileReference {
  string id = 1;
  string property_id = 2;
  string file_id = 3;
  string caption = 4;
  string display_name = 5;
  int32 display_order = 6;
  string file_category = 7;
  google.protobuf.Struct metadata = 8;
}

// Request/Response Messages for AddPropertyFileAttachment
message AddPropertyFileAttachmentRequest {
  string property_id = 1;
  PropertyFileReference file_attachment = 2;
}

message AddPropertyFileAttachmentResponse {
  PropertyFileReference file_attachment = 1;
}

// Request/Response Messages for RemovePropertyFileAttachment
message RemovePropertyFileAttachmentRequest {
  string property_id = 1;
  string attachment_id = 2;
}

message RemovePropertyFileAttachmentResponse {
}







// -----------------------------------------------------------------------------
// Property Service Definition
// -----------------------------------------------------------------------------

// PropertyService defines operations for managing property and evidence items.
// This service handles chain of custody, property status tracking, and evidence management.
service PropertyService {
  // CreateProperty creates a new property
  rpc CreateProperty(CreatePropertyRequest) returns (CreatePropertyResponse);
  
  // GetProperty retrieves a property by its ID
  rpc GetProperty(GetPropertyRequest) returns (GetPropertyResponse);
  

  
  // ListProperties lists properties with pagination and filtering
  rpc ListProperties(ListPropertiesRequest) returns (ListPropertiesResponse);
  
  // UpdateProperty updates an existing property
  rpc UpdateProperty(UpdatePropertyRequest) returns (UpdatePropertyResponse);
  
  // DeleteProperty deletes a property
  rpc DeleteProperty(DeletePropertyRequest) returns (DeletePropertyResponse);
  
  // SearchProperties performs advanced search on properties
  rpc SearchProperties(SearchPropertiesRequest) returns (SearchPropertiesResponse);
  
  // BatchGetProperties retrieves multiple properties by their IDs
  rpc BatchGetProperties(BatchGetPropertiesRequest) returns (BatchGetPropertiesResponse);
  
  // AddCustodyEvent adds a new custody event to a property's chain of custody
  rpc AddCustodyEvent(AddCustodyEventRequest) returns (AddCustodyEventResponse);
  
  // GetCustodyChain returns the complete chain of custody for a property
  rpc GetCustodyChain(GetCustodyChainRequest) returns (GetCustodyChainResponse);
  
  // ListPropertyFileAttachments lists all file attachments for a property
  rpc ListPropertyFileAttachments(ListPropertyFileAttachmentsRequest) returns (ListPropertyFileAttachmentsResponse);
  
  // AddPropertyFileAttachment adds a file attachment to a property
  rpc AddPropertyFileAttachment(AddPropertyFileAttachmentRequest) returns (AddPropertyFileAttachmentResponse);
  
  // RemovePropertyFileAttachment removes a file attachment from a property
  rpc RemovePropertyFileAttachment(RemovePropertyFileAttachmentRequest) returns (RemovePropertyFileAttachmentResponse);
}

// -----------------------------------------------------------------------------
// Cross-service lightweight reference for linking properties to other objects
// -----------------------------------------------------------------------------

// PropertyReference is a denormalized pointer to a Property used in other
// services (e.g., cases). It intentionally mirrors hero.entity.v1.Reference
// shape where appropriate while remaining property-specific.
message PropertyReference {
  string id = 1;             // hero.property.v1.Property.id
  int32  version = 2;        // Snapshot/version number of the property
  string display_name = 3;   // Cached human label for convenience
  string relation_type = 4;  // Caller-defined relation (e.g., "evidence", "involved")
}
