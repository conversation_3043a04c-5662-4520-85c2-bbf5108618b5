// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.3
// 	protoc        (unknown)
// source: hero/property/v1/property.proto

package property

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// PropertyType enumerates the types of property handling
type NIBRSPropertyType int32

const (
	NIBRSPropertyType_PROPERTY_TYPE_UNSPECIFIED NIBRSPropertyType = 0
	NIBRSPropertyType_PROPERTY_TYPE_NONE        NIBRSPropertyType = 1
	NIBRSPropertyType_PROPERTY_TYPE_BURNED      NIBRSPropertyType = 2
	NIBRSPropertyType_PROPERTY_TYPE_FORGED      NIBRSPropertyType = 3
	NIBRSPropertyType_PROPERTY_TYPE_DAMAGED     NIBRSPropertyType = 4
	NIBRSPropertyType_PROPERTY_TYPE_RECOVERED   NIBRSPropertyType = 5
	NIBRSPropertyType_PROPERTY_TYPE_SEIZED      NIBRSPropertyType = 6
	NIBRSPropertyType_PROPERTY_TYPE_STOLEN      NIBRSPropertyType = 7
	NIBRSPropertyType_PROPERTY_TYPE_UNKNOWN     NIBRSPropertyType = 8
	NIBRSPropertyType_PROPERTY_TYPE_FOUND       NIBRSPropertyType = 9
)

// Enum value maps for NIBRSPropertyType.
var (
	NIBRSPropertyType_name = map[int32]string{
		0: "PROPERTY_TYPE_UNSPECIFIED",
		1: "PROPERTY_TYPE_NONE",
		2: "PROPERTY_TYPE_BURNED",
		3: "PROPERTY_TYPE_FORGED",
		4: "PROPERTY_TYPE_DAMAGED",
		5: "PROPERTY_TYPE_RECOVERED",
		6: "PROPERTY_TYPE_SEIZED",
		7: "PROPERTY_TYPE_STOLEN",
		8: "PROPERTY_TYPE_UNKNOWN",
		9: "PROPERTY_TYPE_FOUND",
	}
	NIBRSPropertyType_value = map[string]int32{
		"PROPERTY_TYPE_UNSPECIFIED": 0,
		"PROPERTY_TYPE_NONE":        1,
		"PROPERTY_TYPE_BURNED":      2,
		"PROPERTY_TYPE_FORGED":      3,
		"PROPERTY_TYPE_DAMAGED":     4,
		"PROPERTY_TYPE_RECOVERED":   5,
		"PROPERTY_TYPE_SEIZED":      6,
		"PROPERTY_TYPE_STOLEN":      7,
		"PROPERTY_TYPE_UNKNOWN":     8,
		"PROPERTY_TYPE_FOUND":       9,
	}
)

func (x NIBRSPropertyType) Enum() *NIBRSPropertyType {
	p := new(NIBRSPropertyType)
	*p = x
	return p
}

func (x NIBRSPropertyType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NIBRSPropertyType) Descriptor() protoreflect.EnumDescriptor {
	return file_hero_property_v1_property_proto_enumTypes[0].Descriptor()
}

func (NIBRSPropertyType) Type() protoreflect.EnumType {
	return &file_hero_property_v1_property_proto_enumTypes[0]
}

func (x NIBRSPropertyType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NIBRSPropertyType.Descriptor instead.
func (NIBRSPropertyType) EnumDescriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{0}
}

// PropertyStatus enumerates the status of property items
type PropertyStatus int32

const (
	PropertyStatus_PROPERTY_STATUS_UNSPECIFIED          PropertyStatus = 0
	PropertyStatus_PROPERTY_STATUS_INTAKE_PENDING       PropertyStatus = 1
	PropertyStatus_PROPERTY_STATUS_COLLECTED            PropertyStatus = 2 // Property collected into custody
	PropertyStatus_PROPERTY_STATUS_CHECKED_IN           PropertyStatus = 3
	PropertyStatus_PROPERTY_STATUS_CHECKED_OUT          PropertyStatus = 4
	PropertyStatus_PROPERTY_STATUS_RECOVERED            PropertyStatus = 5
	PropertyStatus_PROPERTY_STATUS_FOUND                PropertyStatus = 6
	PropertyStatus_PROPERTY_STATUS_SAFEKEEPING          PropertyStatus = 7
	PropertyStatus_PROPERTY_STATUS_AWAITING_DISPOSITION PropertyStatus = 8
	PropertyStatus_PROPERTY_STATUS_DISPOSED             PropertyStatus = 9
	PropertyStatus_PROPERTY_STATUS_MISSING              PropertyStatus = 10
	PropertyStatus_PROPERTY_STATUS_STOLEN               PropertyStatus = 11
)

// Enum value maps for PropertyStatus.
var (
	PropertyStatus_name = map[int32]string{
		0:  "PROPERTY_STATUS_UNSPECIFIED",
		1:  "PROPERTY_STATUS_INTAKE_PENDING",
		2:  "PROPERTY_STATUS_COLLECTED",
		3:  "PROPERTY_STATUS_CHECKED_IN",
		4:  "PROPERTY_STATUS_CHECKED_OUT",
		5:  "PROPERTY_STATUS_RECOVERED",
		6:  "PROPERTY_STATUS_FOUND",
		7:  "PROPERTY_STATUS_SAFEKEEPING",
		8:  "PROPERTY_STATUS_AWAITING_DISPOSITION",
		9:  "PROPERTY_STATUS_DISPOSED",
		10: "PROPERTY_STATUS_MISSING",
		11: "PROPERTY_STATUS_STOLEN",
	}
	PropertyStatus_value = map[string]int32{
		"PROPERTY_STATUS_UNSPECIFIED":          0,
		"PROPERTY_STATUS_INTAKE_PENDING":       1,
		"PROPERTY_STATUS_COLLECTED":            2,
		"PROPERTY_STATUS_CHECKED_IN":           3,
		"PROPERTY_STATUS_CHECKED_OUT":          4,
		"PROPERTY_STATUS_RECOVERED":            5,
		"PROPERTY_STATUS_FOUND":                6,
		"PROPERTY_STATUS_SAFEKEEPING":          7,
		"PROPERTY_STATUS_AWAITING_DISPOSITION": 8,
		"PROPERTY_STATUS_DISPOSED":             9,
		"PROPERTY_STATUS_MISSING":              10,
		"PROPERTY_STATUS_STOLEN":               11,
	}
)

func (x PropertyStatus) Enum() *PropertyStatus {
	p := new(PropertyStatus)
	*p = x
	return p
}

func (x PropertyStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PropertyStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_hero_property_v1_property_proto_enumTypes[1].Descriptor()
}

func (PropertyStatus) Type() protoreflect.EnumType {
	return &file_hero_property_v1_property_proto_enumTypes[1]
}

func (x PropertyStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PropertyStatus.Descriptor instead.
func (PropertyStatus) EnumDescriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{1}
}

// PropertyDisposalType enumerates how property was disposed
type PropertyDisposalType int32

const (
	PropertyDisposalType_PROPERTY_DISPOSAL_TYPE_UNSPECIFIED   PropertyDisposalType = 0
	PropertyDisposalType_PROPERTY_DISPOSAL_TYPE_RELEASED      PropertyDisposalType = 1
	PropertyDisposalType_PROPERTY_DISPOSAL_TYPE_DESTROYED     PropertyDisposalType = 2
	PropertyDisposalType_PROPERTY_DISPOSAL_TYPE_AUCTIONED     PropertyDisposalType = 3
	PropertyDisposalType_PROPERTY_DISPOSAL_TYPE_AGENCY_RETAIN PropertyDisposalType = 4
	PropertyDisposalType_PROPERTY_DISPOSAL_TYPE_TRANSFERRED   PropertyDisposalType = 5
)

// Enum value maps for PropertyDisposalType.
var (
	PropertyDisposalType_name = map[int32]string{
		0: "PROPERTY_DISPOSAL_TYPE_UNSPECIFIED",
		1: "PROPERTY_DISPOSAL_TYPE_RELEASED",
		2: "PROPERTY_DISPOSAL_TYPE_DESTROYED",
		3: "PROPERTY_DISPOSAL_TYPE_AUCTIONED",
		4: "PROPERTY_DISPOSAL_TYPE_AGENCY_RETAIN",
		5: "PROPERTY_DISPOSAL_TYPE_TRANSFERRED",
	}
	PropertyDisposalType_value = map[string]int32{
		"PROPERTY_DISPOSAL_TYPE_UNSPECIFIED":   0,
		"PROPERTY_DISPOSAL_TYPE_RELEASED":      1,
		"PROPERTY_DISPOSAL_TYPE_DESTROYED":     2,
		"PROPERTY_DISPOSAL_TYPE_AUCTIONED":     3,
		"PROPERTY_DISPOSAL_TYPE_AGENCY_RETAIN": 4,
		"PROPERTY_DISPOSAL_TYPE_TRANSFERRED":   5,
	}
)

func (x PropertyDisposalType) Enum() *PropertyDisposalType {
	p := new(PropertyDisposalType)
	*p = x
	return p
}

func (x PropertyDisposalType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PropertyDisposalType) Descriptor() protoreflect.EnumDescriptor {
	return file_hero_property_v1_property_proto_enumTypes[2].Descriptor()
}

func (PropertyDisposalType) Type() protoreflect.EnumType {
	return &file_hero_property_v1_property_proto_enumTypes[2]
}

func (x PropertyDisposalType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PropertyDisposalType.Descriptor instead.
func (PropertyDisposalType) EnumDescriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{2}
}

// CustodyActionType enumerates the types of custody changes
type CustodyActionType int32

const (
	CustodyActionType_CUSTODY_ACTION_TYPE_UNSPECIFIED CustodyActionType = 0
	CustodyActionType_CUSTODY_ACTION_TYPE_COLLECTED   CustodyActionType = 1 // Property initially collected into custody
	CustodyActionType_CUSTODY_ACTION_TYPE_CHECKED_IN  CustodyActionType = 2 // Property checked into evidence room
	CustodyActionType_CUSTODY_ACTION_TYPE_CHECKED_OUT CustodyActionType = 3 // Property checked out for analysis/court
	CustodyActionType_CUSTODY_ACTION_TYPE_TRANSFERRED CustodyActionType = 4 // Property transferred to another agency/person
	CustodyActionType_CUSTODY_ACTION_TYPE_RELEASED    CustodyActionType = 5 // Property released to owner
	CustodyActionType_CUSTODY_ACTION_TYPE_DISPOSED    CustodyActionType = 6 // Property disposed/destroyed
	CustodyActionType_CUSTODY_ACTION_TYPE_LOGGED      CustodyActionType = 7 // Property reported but not in physical custody
	CustodyActionType_CUSTODY_ACTION_TYPE_MOVED       CustodyActionType = 8 // Property moved to a new location
)

// Enum value maps for CustodyActionType.
var (
	CustodyActionType_name = map[int32]string{
		0: "CUSTODY_ACTION_TYPE_UNSPECIFIED",
		1: "CUSTODY_ACTION_TYPE_COLLECTED",
		2: "CUSTODY_ACTION_TYPE_CHECKED_IN",
		3: "CUSTODY_ACTION_TYPE_CHECKED_OUT",
		4: "CUSTODY_ACTION_TYPE_TRANSFERRED",
		5: "CUSTODY_ACTION_TYPE_RELEASED",
		6: "CUSTODY_ACTION_TYPE_DISPOSED",
		7: "CUSTODY_ACTION_TYPE_LOGGED",
		8: "CUSTODY_ACTION_TYPE_MOVED",
	}
	CustodyActionType_value = map[string]int32{
		"CUSTODY_ACTION_TYPE_UNSPECIFIED": 0,
		"CUSTODY_ACTION_TYPE_COLLECTED":   1,
		"CUSTODY_ACTION_TYPE_CHECKED_IN":  2,
		"CUSTODY_ACTION_TYPE_CHECKED_OUT": 3,
		"CUSTODY_ACTION_TYPE_TRANSFERRED": 4,
		"CUSTODY_ACTION_TYPE_RELEASED":    5,
		"CUSTODY_ACTION_TYPE_DISPOSED":    6,
		"CUSTODY_ACTION_TYPE_LOGGED":      7,
		"CUSTODY_ACTION_TYPE_MOVED":       8,
	}
)

func (x CustodyActionType) Enum() *CustodyActionType {
	p := new(CustodyActionType)
	*p = x
	return p
}

func (x CustodyActionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CustodyActionType) Descriptor() protoreflect.EnumDescriptor {
	return file_hero_property_v1_property_proto_enumTypes[3].Descriptor()
}

func (CustodyActionType) Type() protoreflect.EnumType {
	return &file_hero_property_v1_property_proto_enumTypes[3]
}

func (x CustodyActionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CustodyActionType.Descriptor instead.
func (CustodyActionType) EnumDescriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{3}
}

// EntityType enumerates the types of entities that can receive property
type CustodyEntityType int32

const (
	CustodyEntityType_CUSTODY_ENTITY_TYPE_UNSPECIFIED  CustodyEntityType = 0
	CustodyEntityType_CUSTODY_ENTITY_TYPE_PERSON       CustodyEntityType = 1
	CustodyEntityType_CUSTODY_ENTITY_TYPE_CRIME_LAB    CustodyEntityType = 2
	CustodyEntityType_CUSTODY_ENTITY_TYPE_COURT        CustodyEntityType = 3
	CustodyEntityType_CUSTODY_ENTITY_TYPE_SPECIALIST   CustodyEntityType = 4
	CustodyEntityType_CUSTODY_ENTITY_TYPE_CSI          CustodyEntityType = 5
	CustodyEntityType_CUSTODY_ENTITY_TYPE_ORGANIZATION CustodyEntityType = 6
	CustodyEntityType_CUSTODY_ENTITY_TYPE_OTHER        CustodyEntityType = 7
)

// Enum value maps for CustodyEntityType.
var (
	CustodyEntityType_name = map[int32]string{
		0: "CUSTODY_ENTITY_TYPE_UNSPECIFIED",
		1: "CUSTODY_ENTITY_TYPE_PERSON",
		2: "CUSTODY_ENTITY_TYPE_CRIME_LAB",
		3: "CUSTODY_ENTITY_TYPE_COURT",
		4: "CUSTODY_ENTITY_TYPE_SPECIALIST",
		5: "CUSTODY_ENTITY_TYPE_CSI",
		6: "CUSTODY_ENTITY_TYPE_ORGANIZATION",
		7: "CUSTODY_ENTITY_TYPE_OTHER",
	}
	CustodyEntityType_value = map[string]int32{
		"CUSTODY_ENTITY_TYPE_UNSPECIFIED":  0,
		"CUSTODY_ENTITY_TYPE_PERSON":       1,
		"CUSTODY_ENTITY_TYPE_CRIME_LAB":    2,
		"CUSTODY_ENTITY_TYPE_COURT":        3,
		"CUSTODY_ENTITY_TYPE_SPECIALIST":   4,
		"CUSTODY_ENTITY_TYPE_CSI":          5,
		"CUSTODY_ENTITY_TYPE_ORGANIZATION": 6,
		"CUSTODY_ENTITY_TYPE_OTHER":        7,
	}
)

func (x CustodyEntityType) Enum() *CustodyEntityType {
	p := new(CustodyEntityType)
	*p = x
	return p
}

func (x CustodyEntityType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CustodyEntityType) Descriptor() protoreflect.EnumDescriptor {
	return file_hero_property_v1_property_proto_enumTypes[4].Descriptor()
}

func (CustodyEntityType) Type() protoreflect.EnumType {
	return &file_hero_property_v1_property_proto_enumTypes[4]
}

func (x CustodyEntityType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CustodyEntityType.Descriptor instead.
func (CustodyEntityType) EnumDescriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{4}
}

// AgencyType enumerates the types of agencies that can receive property
type CustodyAgencyType int32

const (
	CustodyAgencyType_CUSTODY_AGENCY_TYPE_UNSPECIFIED CustodyAgencyType = 0
	CustodyAgencyType_CUSTODY_AGENCY_TYPE_PD          CustodyAgencyType = 1 // Police Department
	CustodyAgencyType_CUSTODY_AGENCY_TYPE_OTHER       CustodyAgencyType = 2 // Other agency type
)

// Enum value maps for CustodyAgencyType.
var (
	CustodyAgencyType_name = map[int32]string{
		0: "CUSTODY_AGENCY_TYPE_UNSPECIFIED",
		1: "CUSTODY_AGENCY_TYPE_PD",
		2: "CUSTODY_AGENCY_TYPE_OTHER",
	}
	CustodyAgencyType_value = map[string]int32{
		"CUSTODY_AGENCY_TYPE_UNSPECIFIED": 0,
		"CUSTODY_AGENCY_TYPE_PD":          1,
		"CUSTODY_AGENCY_TYPE_OTHER":       2,
	}
)

func (x CustodyAgencyType) Enum() *CustodyAgencyType {
	p := new(CustodyAgencyType)
	*p = x
	return p
}

func (x CustodyAgencyType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CustodyAgencyType) Descriptor() protoreflect.EnumDescriptor {
	return file_hero_property_v1_property_proto_enumTypes[5].Descriptor()
}

func (CustodyAgencyType) Type() protoreflect.EnumType {
	return &file_hero_property_v1_property_proto_enumTypes[5]
}

func (x CustodyAgencyType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CustodyAgencyType.Descriptor instead.
func (CustodyAgencyType) EnumDescriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{5}
}

// NIBRSPropertyDescription enumerates NIBRS property description codes
type NIBRSPropertyDescription int32

const (
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_UNSPECIFIED                             NIBRSPropertyDescription = 0
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_AIRCRAFT                                NIBRSPropertyDescription = 1  // 01 = Aircraft
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_ALCOHOL                                 NIBRSPropertyDescription = 2  // 02 = Alcohol
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_AUTOMOBILES                             NIBRSPropertyDescription = 3  // 03 = Automobiles
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_BICYCLES                                NIBRSPropertyDescription = 4  // 04 = Bicycles
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_BUSES                                   NIBRSPropertyDescription = 5  // 05 = Buses
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_CLOTHES_FURS                            NIBRSPropertyDescription = 6  // 06 = Clothes/Furs
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_COMPUTER_HARDWARE_SOFTWARE              NIBRSPropertyDescription = 7  // 07 = Computer Hardware/Software
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_CONSUMABLE_GOODS                        NIBRSPropertyDescription = 8  // 08 = Consumable Goods
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_CREDIT_DEBIT_CARDS                      NIBRSPropertyDescription = 9  // 09 = Credit/Debit Cards
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_DRUGS_NARCOTICS                         NIBRSPropertyDescription = 10 // 10 = Drugs/Narcotics
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_DRUG_NARCOTIC_EQUIPMENT                 NIBRSPropertyDescription = 11 // 11 = Drug/Narcotic Equipment
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_FARM_EQUIPMENT                          NIBRSPropertyDescription = 12 // 12 = Farm Equipment
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_FIREARMS                                NIBRSPropertyDescription = 13 // 13 = Firearms
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_GAMBLING_EQUIPMENT                      NIBRSPropertyDescription = 14 // 14 = Gambling Equipment
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_HEAVY_CONSTRUCTION_INDUSTRIAL_EQUIPMENT NIBRSPropertyDescription = 15 // 15 = Heavy Construction/Industrial Equipment
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_HOUSEHOLD_GOODS                         NIBRSPropertyDescription = 16 // 16 = Household Goods
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_JEWELRY_PRECIOUS_METALS_GEMS            NIBRSPropertyDescription = 17 // 17 = Jewelry/Precious Metals/Gems
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_LIVESTOCK                               NIBRSPropertyDescription = 18 // 18 = Livestock
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_MERCHANDISE                             NIBRSPropertyDescription = 19 // 19 = Merchandise
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_MONEY                                   NIBRSPropertyDescription = 20 // 20 = Money
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_NEGOTIABLE_INSTRUMENTS                  NIBRSPropertyDescription = 21 // 21 = Negotiable Instruments
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_NONNEGOTIABLE_INSTRUMENTS               NIBRSPropertyDescription = 22 // 22 = Nonnegotiable Instruments
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_OFFICE_TYPE_EQUIPMENT                   NIBRSPropertyDescription = 23 // 23 = Office-type Equipment
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_OTHER_MOTOR_VEHICLES                    NIBRSPropertyDescription = 24 // 24 = Other Motor Vehicles
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_PURSES_HANDBAGS_WALLETS                 NIBRSPropertyDescription = 25 // 25 = Purses/Handbags/Wallets
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_RADIOS_TVS_VCRS                         NIBRSPropertyDescription = 26 // 26 = Radios/TVs/VCRs
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_RECORDINGS_AUDIO_VISUAL                 NIBRSPropertyDescription = 27 // 27 = Recordings–Audio/Visual
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_RECREATIONAL_VEHICLES                   NIBRSPropertyDescription = 28 // 28 = Recreational Vehicles
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_SINGLE_OCCUPANCY_DWELLINGS   NIBRSPropertyDescription = 29 // 29 = Structures–Single Occupancy Dwellings
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_OTHER_DWELLINGS              NIBRSPropertyDescription = 30 // 30 = Structures–Other Dwellings
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_OTHER_COMMERCIAL_BUSINESS    NIBRSPropertyDescription = 31 // 31 = Structures–Other Commercial/Business
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_INDUSTRIAL_MANUFACTURING     NIBRSPropertyDescription = 32 // 32 = Structures–Industrial/Manufacturing
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_PUBLIC_COMMUNITY             NIBRSPropertyDescription = 33 // 33 = Structures–Public/Community
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_STORAGE                      NIBRSPropertyDescription = 34 // 34 = Structures–Storage
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_OTHER                        NIBRSPropertyDescription = 35 // 35 = Structures–Other
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_TOOLS                                   NIBRSPropertyDescription = 36 // 36 = Tools
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_TRUCKS                                  NIBRSPropertyDescription = 37 // 37 = Trucks
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_VEHICLE_PARTS_ACCESSORIES               NIBRSPropertyDescription = 38 // 38 = Vehicle Parts/Accessories
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_WATERCRAFT                              NIBRSPropertyDescription = 39 // 39 = Watercraft
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_AIRCRAFT_PARTS_ACCESSORIES              NIBRSPropertyDescription = 41 // 41 = Aircraft Parts/Accessories
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_ARTISTIC_SUPPLIES_ACCESSORIES           NIBRSPropertyDescription = 42 // 42 = Artistic Supplies/Accessories
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_BUILDING_MATERIALS                      NIBRSPropertyDescription = 43 // 43 = Building Materials
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_CAMPING_HUNTING_FISHING_EQUIPMENT       NIBRSPropertyDescription = 44 // 44 = Camping/Hunting/Fishing Equipment/Supplies
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_CHEMICALS                               NIBRSPropertyDescription = 45 // 45 = Chemicals
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_COLLECTIONS_COLLECTIBLES                NIBRSPropertyDescription = 46 // 46 = Collections/Collectibles
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_CROPS                                   NIBRSPropertyDescription = 47 // 47 = Crops
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_DOCUMENTS_PERSONAL_OR_BUSINESS          NIBRSPropertyDescription = 48 // 48 = Documents/Personal or Business
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_EXPLOSIVES                              NIBRSPropertyDescription = 49 // 49 = Explosives
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_FIREARM_ACCESSORIES                     NIBRSPropertyDescription = 59 // 59 = Firearm Accessories
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_FUEL                                    NIBRSPropertyDescription = 64 // 64 = Fuel
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_IDENTITY_DOCUMENTS                      NIBRSPropertyDescription = 65 // 65 = Identity Documents
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_IDENTITY_INTANGIBLE                     NIBRSPropertyDescription = 66 // 66 = Identity–Intangible
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_LAW_ENFORCEMENT_EQUIPMENT               NIBRSPropertyDescription = 67 // 67 = Law Enforcement Equipment
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_LAWN_YARD_GARDEN_EQUIPMENT              NIBRSPropertyDescription = 68 // 68 = Lawn/Yard/Garden Equipment
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_LOGGING_EQUIPMENT                       NIBRSPropertyDescription = 69 // 69 = Logging Equipment
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_MEDICAL_MEDICAL_LAB_EQUIPMENT           NIBRSPropertyDescription = 70 // 70 = Medical/Medical Lab Equipment
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_METALS_NON_PRECIOUS                     NIBRSPropertyDescription = 71 // 71 = Metals, Non-Precious
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_MUSICAL_INSTRUMENTS                     NIBRSPropertyDescription = 72 // 72 = Musical Instruments
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_PETS                                    NIBRSPropertyDescription = 73 // 73 = Pets
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_PHOTOGRAPHIC_OPTICAL_EQUIPMENT          NIBRSPropertyDescription = 74 // 74 = Photographic/Optical Equipment
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_PORTABLE_ELECTRONIC_COMMUNICATIONS      NIBRSPropertyDescription = 75 // 75 = Portable Electronic Communications
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_RECREATIONAL_SPORTS_EQUIPMENT           NIBRSPropertyDescription = 76 // 76 = Recreational/Sports Equipment
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_OTHER                                   NIBRSPropertyDescription = 77 // 77 = Other
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_TRAILERS                                NIBRSPropertyDescription = 78 // 78 = Trailers
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_WATERCRAFT_EQUIPMENT_PARTS_ACCESSORIES  NIBRSPropertyDescription = 79 // 79 = Watercraft Equipment/Parts/Accessories
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_WEAPONS_OTHER                           NIBRSPropertyDescription = 80 // 80 = Weapons–Other
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_PENDING_INVENTORY                       NIBRSPropertyDescription = 88 // 88 = Pending Inventory
	NIBRSPropertyDescription_NIBRS_PROPERTY_DESCRIPTION_BLANK                                   NIBRSPropertyDescription = 99 // 99 = ( blank )
)

// Enum value maps for NIBRSPropertyDescription.
var (
	NIBRSPropertyDescription_name = map[int32]string{
		0:  "NIBRS_PROPERTY_DESCRIPTION_UNSPECIFIED",
		1:  "NIBRS_PROPERTY_DESCRIPTION_AIRCRAFT",
		2:  "NIBRS_PROPERTY_DESCRIPTION_ALCOHOL",
		3:  "NIBRS_PROPERTY_DESCRIPTION_AUTOMOBILES",
		4:  "NIBRS_PROPERTY_DESCRIPTION_BICYCLES",
		5:  "NIBRS_PROPERTY_DESCRIPTION_BUSES",
		6:  "NIBRS_PROPERTY_DESCRIPTION_CLOTHES_FURS",
		7:  "NIBRS_PROPERTY_DESCRIPTION_COMPUTER_HARDWARE_SOFTWARE",
		8:  "NIBRS_PROPERTY_DESCRIPTION_CONSUMABLE_GOODS",
		9:  "NIBRS_PROPERTY_DESCRIPTION_CREDIT_DEBIT_CARDS",
		10: "NIBRS_PROPERTY_DESCRIPTION_DRUGS_NARCOTICS",
		11: "NIBRS_PROPERTY_DESCRIPTION_DRUG_NARCOTIC_EQUIPMENT",
		12: "NIBRS_PROPERTY_DESCRIPTION_FARM_EQUIPMENT",
		13: "NIBRS_PROPERTY_DESCRIPTION_FIREARMS",
		14: "NIBRS_PROPERTY_DESCRIPTION_GAMBLING_EQUIPMENT",
		15: "NIBRS_PROPERTY_DESCRIPTION_HEAVY_CONSTRUCTION_INDUSTRIAL_EQUIPMENT",
		16: "NIBRS_PROPERTY_DESCRIPTION_HOUSEHOLD_GOODS",
		17: "NIBRS_PROPERTY_DESCRIPTION_JEWELRY_PRECIOUS_METALS_GEMS",
		18: "NIBRS_PROPERTY_DESCRIPTION_LIVESTOCK",
		19: "NIBRS_PROPERTY_DESCRIPTION_MERCHANDISE",
		20: "NIBRS_PROPERTY_DESCRIPTION_MONEY",
		21: "NIBRS_PROPERTY_DESCRIPTION_NEGOTIABLE_INSTRUMENTS",
		22: "NIBRS_PROPERTY_DESCRIPTION_NONNEGOTIABLE_INSTRUMENTS",
		23: "NIBRS_PROPERTY_DESCRIPTION_OFFICE_TYPE_EQUIPMENT",
		24: "NIBRS_PROPERTY_DESCRIPTION_OTHER_MOTOR_VEHICLES",
		25: "NIBRS_PROPERTY_DESCRIPTION_PURSES_HANDBAGS_WALLETS",
		26: "NIBRS_PROPERTY_DESCRIPTION_RADIOS_TVS_VCRS",
		27: "NIBRS_PROPERTY_DESCRIPTION_RECORDINGS_AUDIO_VISUAL",
		28: "NIBRS_PROPERTY_DESCRIPTION_RECREATIONAL_VEHICLES",
		29: "NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_SINGLE_OCCUPANCY_DWELLINGS",
		30: "NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_OTHER_DWELLINGS",
		31: "NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_OTHER_COMMERCIAL_BUSINESS",
		32: "NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_INDUSTRIAL_MANUFACTURING",
		33: "NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_PUBLIC_COMMUNITY",
		34: "NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_STORAGE",
		35: "NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_OTHER",
		36: "NIBRS_PROPERTY_DESCRIPTION_TOOLS",
		37: "NIBRS_PROPERTY_DESCRIPTION_TRUCKS",
		38: "NIBRS_PROPERTY_DESCRIPTION_VEHICLE_PARTS_ACCESSORIES",
		39: "NIBRS_PROPERTY_DESCRIPTION_WATERCRAFT",
		41: "NIBRS_PROPERTY_DESCRIPTION_AIRCRAFT_PARTS_ACCESSORIES",
		42: "NIBRS_PROPERTY_DESCRIPTION_ARTISTIC_SUPPLIES_ACCESSORIES",
		43: "NIBRS_PROPERTY_DESCRIPTION_BUILDING_MATERIALS",
		44: "NIBRS_PROPERTY_DESCRIPTION_CAMPING_HUNTING_FISHING_EQUIPMENT",
		45: "NIBRS_PROPERTY_DESCRIPTION_CHEMICALS",
		46: "NIBRS_PROPERTY_DESCRIPTION_COLLECTIONS_COLLECTIBLES",
		47: "NIBRS_PROPERTY_DESCRIPTION_CROPS",
		48: "NIBRS_PROPERTY_DESCRIPTION_DOCUMENTS_PERSONAL_OR_BUSINESS",
		49: "NIBRS_PROPERTY_DESCRIPTION_EXPLOSIVES",
		59: "NIBRS_PROPERTY_DESCRIPTION_FIREARM_ACCESSORIES",
		64: "NIBRS_PROPERTY_DESCRIPTION_FUEL",
		65: "NIBRS_PROPERTY_DESCRIPTION_IDENTITY_DOCUMENTS",
		66: "NIBRS_PROPERTY_DESCRIPTION_IDENTITY_INTANGIBLE",
		67: "NIBRS_PROPERTY_DESCRIPTION_LAW_ENFORCEMENT_EQUIPMENT",
		68: "NIBRS_PROPERTY_DESCRIPTION_LAWN_YARD_GARDEN_EQUIPMENT",
		69: "NIBRS_PROPERTY_DESCRIPTION_LOGGING_EQUIPMENT",
		70: "NIBRS_PROPERTY_DESCRIPTION_MEDICAL_MEDICAL_LAB_EQUIPMENT",
		71: "NIBRS_PROPERTY_DESCRIPTION_METALS_NON_PRECIOUS",
		72: "NIBRS_PROPERTY_DESCRIPTION_MUSICAL_INSTRUMENTS",
		73: "NIBRS_PROPERTY_DESCRIPTION_PETS",
		74: "NIBRS_PROPERTY_DESCRIPTION_PHOTOGRAPHIC_OPTICAL_EQUIPMENT",
		75: "NIBRS_PROPERTY_DESCRIPTION_PORTABLE_ELECTRONIC_COMMUNICATIONS",
		76: "NIBRS_PROPERTY_DESCRIPTION_RECREATIONAL_SPORTS_EQUIPMENT",
		77: "NIBRS_PROPERTY_DESCRIPTION_OTHER",
		78: "NIBRS_PROPERTY_DESCRIPTION_TRAILERS",
		79: "NIBRS_PROPERTY_DESCRIPTION_WATERCRAFT_EQUIPMENT_PARTS_ACCESSORIES",
		80: "NIBRS_PROPERTY_DESCRIPTION_WEAPONS_OTHER",
		88: "NIBRS_PROPERTY_DESCRIPTION_PENDING_INVENTORY",
		99: "NIBRS_PROPERTY_DESCRIPTION_BLANK",
	}
	NIBRSPropertyDescription_value = map[string]int32{
		"NIBRS_PROPERTY_DESCRIPTION_UNSPECIFIED":                             0,
		"NIBRS_PROPERTY_DESCRIPTION_AIRCRAFT":                                1,
		"NIBRS_PROPERTY_DESCRIPTION_ALCOHOL":                                 2,
		"NIBRS_PROPERTY_DESCRIPTION_AUTOMOBILES":                             3,
		"NIBRS_PROPERTY_DESCRIPTION_BICYCLES":                                4,
		"NIBRS_PROPERTY_DESCRIPTION_BUSES":                                   5,
		"NIBRS_PROPERTY_DESCRIPTION_CLOTHES_FURS":                            6,
		"NIBRS_PROPERTY_DESCRIPTION_COMPUTER_HARDWARE_SOFTWARE":              7,
		"NIBRS_PROPERTY_DESCRIPTION_CONSUMABLE_GOODS":                        8,
		"NIBRS_PROPERTY_DESCRIPTION_CREDIT_DEBIT_CARDS":                      9,
		"NIBRS_PROPERTY_DESCRIPTION_DRUGS_NARCOTICS":                         10,
		"NIBRS_PROPERTY_DESCRIPTION_DRUG_NARCOTIC_EQUIPMENT":                 11,
		"NIBRS_PROPERTY_DESCRIPTION_FARM_EQUIPMENT":                          12,
		"NIBRS_PROPERTY_DESCRIPTION_FIREARMS":                                13,
		"NIBRS_PROPERTY_DESCRIPTION_GAMBLING_EQUIPMENT":                      14,
		"NIBRS_PROPERTY_DESCRIPTION_HEAVY_CONSTRUCTION_INDUSTRIAL_EQUIPMENT": 15,
		"NIBRS_PROPERTY_DESCRIPTION_HOUSEHOLD_GOODS":                         16,
		"NIBRS_PROPERTY_DESCRIPTION_JEWELRY_PRECIOUS_METALS_GEMS":            17,
		"NIBRS_PROPERTY_DESCRIPTION_LIVESTOCK":                               18,
		"NIBRS_PROPERTY_DESCRIPTION_MERCHANDISE":                             19,
		"NIBRS_PROPERTY_DESCRIPTION_MONEY":                                   20,
		"NIBRS_PROPERTY_DESCRIPTION_NEGOTIABLE_INSTRUMENTS":                  21,
		"NIBRS_PROPERTY_DESCRIPTION_NONNEGOTIABLE_INSTRUMENTS":               22,
		"NIBRS_PROPERTY_DESCRIPTION_OFFICE_TYPE_EQUIPMENT":                   23,
		"NIBRS_PROPERTY_DESCRIPTION_OTHER_MOTOR_VEHICLES":                    24,
		"NIBRS_PROPERTY_DESCRIPTION_PURSES_HANDBAGS_WALLETS":                 25,
		"NIBRS_PROPERTY_DESCRIPTION_RADIOS_TVS_VCRS":                         26,
		"NIBRS_PROPERTY_DESCRIPTION_RECORDINGS_AUDIO_VISUAL":                 27,
		"NIBRS_PROPERTY_DESCRIPTION_RECREATIONAL_VEHICLES":                   28,
		"NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_SINGLE_OCCUPANCY_DWELLINGS":   29,
		"NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_OTHER_DWELLINGS":              30,
		"NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_OTHER_COMMERCIAL_BUSINESS":    31,
		"NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_INDUSTRIAL_MANUFACTURING":     32,
		"NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_PUBLIC_COMMUNITY":             33,
		"NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_STORAGE":                      34,
		"NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_OTHER":                        35,
		"NIBRS_PROPERTY_DESCRIPTION_TOOLS":                                   36,
		"NIBRS_PROPERTY_DESCRIPTION_TRUCKS":                                  37,
		"NIBRS_PROPERTY_DESCRIPTION_VEHICLE_PARTS_ACCESSORIES":               38,
		"NIBRS_PROPERTY_DESCRIPTION_WATERCRAFT":                              39,
		"NIBRS_PROPERTY_DESCRIPTION_AIRCRAFT_PARTS_ACCESSORIES":              41,
		"NIBRS_PROPERTY_DESCRIPTION_ARTISTIC_SUPPLIES_ACCESSORIES":           42,
		"NIBRS_PROPERTY_DESCRIPTION_BUILDING_MATERIALS":                      43,
		"NIBRS_PROPERTY_DESCRIPTION_CAMPING_HUNTING_FISHING_EQUIPMENT":       44,
		"NIBRS_PROPERTY_DESCRIPTION_CHEMICALS":                               45,
		"NIBRS_PROPERTY_DESCRIPTION_COLLECTIONS_COLLECTIBLES":                46,
		"NIBRS_PROPERTY_DESCRIPTION_CROPS":                                   47,
		"NIBRS_PROPERTY_DESCRIPTION_DOCUMENTS_PERSONAL_OR_BUSINESS":          48,
		"NIBRS_PROPERTY_DESCRIPTION_EXPLOSIVES":                              49,
		"NIBRS_PROPERTY_DESCRIPTION_FIREARM_ACCESSORIES":                     59,
		"NIBRS_PROPERTY_DESCRIPTION_FUEL":                                    64,
		"NIBRS_PROPERTY_DESCRIPTION_IDENTITY_DOCUMENTS":                      65,
		"NIBRS_PROPERTY_DESCRIPTION_IDENTITY_INTANGIBLE":                     66,
		"NIBRS_PROPERTY_DESCRIPTION_LAW_ENFORCEMENT_EQUIPMENT":               67,
		"NIBRS_PROPERTY_DESCRIPTION_LAWN_YARD_GARDEN_EQUIPMENT":              68,
		"NIBRS_PROPERTY_DESCRIPTION_LOGGING_EQUIPMENT":                       69,
		"NIBRS_PROPERTY_DESCRIPTION_MEDICAL_MEDICAL_LAB_EQUIPMENT":           70,
		"NIBRS_PROPERTY_DESCRIPTION_METALS_NON_PRECIOUS":                     71,
		"NIBRS_PROPERTY_DESCRIPTION_MUSICAL_INSTRUMENTS":                     72,
		"NIBRS_PROPERTY_DESCRIPTION_PETS":                                    73,
		"NIBRS_PROPERTY_DESCRIPTION_PHOTOGRAPHIC_OPTICAL_EQUIPMENT":          74,
		"NIBRS_PROPERTY_DESCRIPTION_PORTABLE_ELECTRONIC_COMMUNICATIONS":      75,
		"NIBRS_PROPERTY_DESCRIPTION_RECREATIONAL_SPORTS_EQUIPMENT":           76,
		"NIBRS_PROPERTY_DESCRIPTION_OTHER":                                   77,
		"NIBRS_PROPERTY_DESCRIPTION_TRAILERS":                                78,
		"NIBRS_PROPERTY_DESCRIPTION_WATERCRAFT_EQUIPMENT_PARTS_ACCESSORIES":  79,
		"NIBRS_PROPERTY_DESCRIPTION_WEAPONS_OTHER":                           80,
		"NIBRS_PROPERTY_DESCRIPTION_PENDING_INVENTORY":                       88,
		"NIBRS_PROPERTY_DESCRIPTION_BLANK":                                   99,
	}
)

func (x NIBRSPropertyDescription) Enum() *NIBRSPropertyDescription {
	p := new(NIBRSPropertyDescription)
	*p = x
	return p
}

func (x NIBRSPropertyDescription) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NIBRSPropertyDescription) Descriptor() protoreflect.EnumDescriptor {
	return file_hero_property_v1_property_proto_enumTypes[6].Descriptor()
}

func (NIBRSPropertyDescription) Type() protoreflect.EnumType {
	return &file_hero_property_v1_property_proto_enumTypes[6]
}

func (x NIBRSPropertyDescription) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NIBRSPropertyDescription.Descriptor instead.
func (NIBRSPropertyDescription) EnumDescriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{6}
}

// SearchOrderBy defines the ordering options for search results
type SearchOrderBy int32

const (
	SearchOrderBy_SEARCH_ORDER_BY_UNSPECIFIED SearchOrderBy = 0
	SearchOrderBy_SEARCH_ORDER_BY_RELEVANCE   SearchOrderBy = 1
	SearchOrderBy_SEARCH_ORDER_BY_CREATED_AT  SearchOrderBy = 2
	SearchOrderBy_SEARCH_ORDER_BY_UPDATED_AT  SearchOrderBy = 3
	SearchOrderBy_SEARCH_ORDER_BY_STATUS      SearchOrderBy = 4
)

// Enum value maps for SearchOrderBy.
var (
	SearchOrderBy_name = map[int32]string{
		0: "SEARCH_ORDER_BY_UNSPECIFIED",
		1: "SEARCH_ORDER_BY_RELEVANCE",
		2: "SEARCH_ORDER_BY_CREATED_AT",
		3: "SEARCH_ORDER_BY_UPDATED_AT",
		4: "SEARCH_ORDER_BY_STATUS",
	}
	SearchOrderBy_value = map[string]int32{
		"SEARCH_ORDER_BY_UNSPECIFIED": 0,
		"SEARCH_ORDER_BY_RELEVANCE":   1,
		"SEARCH_ORDER_BY_CREATED_AT":  2,
		"SEARCH_ORDER_BY_UPDATED_AT":  3,
		"SEARCH_ORDER_BY_STATUS":      4,
	}
)

func (x SearchOrderBy) Enum() *SearchOrderBy {
	p := new(SearchOrderBy)
	*p = x
	return p
}

func (x SearchOrderBy) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SearchOrderBy) Descriptor() protoreflect.EnumDescriptor {
	return file_hero_property_v1_property_proto_enumTypes[7].Descriptor()
}

func (SearchOrderBy) Type() protoreflect.EnumType {
	return &file_hero_property_v1_property_proto_enumTypes[7]
}

func (x SearchOrderBy) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SearchOrderBy.Descriptor instead.
func (SearchOrderBy) EnumDescriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{7}
}

// CustodyEvent represents a single chain of custody entry
type CustodyEvent struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	Timestamp          string                 `protobuf:"bytes,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`                                                              // ISO8601 timestamp of the custody change
	TransferringUserId string                 `protobuf:"bytes,2,opt,name=transferring_user_id,json=transferringUserId,proto3" json:"transferring_user_id,omitempty"`                // User/Officer transferring the item
	TransferringAgency string                 `protobuf:"bytes,3,opt,name=transferring_agency,json=transferringAgency,proto3" json:"transferring_agency,omitempty"`                  // Agency transferring the item
	ReceivingUserId    string                 `protobuf:"bytes,4,opt,name=receiving_user_id,json=receivingUserId,proto3" json:"receiving_user_id,omitempty"`                         // User/Officer/Agency receiving the item (optional for disposal)
	ReceivingAgency    string                 `protobuf:"bytes,5,opt,name=receiving_agency,json=receivingAgency,proto3" json:"receiving_agency,omitempty"`                           // Agency receiving the item (if different from current org)
	NewLocation        string                 `protobuf:"bytes,6,opt,name=new_location,json=newLocation,proto3" json:"new_location,omitempty"`                                       // New location (e.g., "Evidence Room A-12", "Lab XYZ")
	ActionType         CustodyActionType      `protobuf:"varint,7,opt,name=action_type,json=actionType,proto3,enum=hero.property.v1.CustodyActionType" json:"action_type,omitempty"` // Type of custody action
	Notes              string                 `protobuf:"bytes,8,opt,name=notes,proto3" json:"notes,omitempty"`                                                                      // Reason/purpose for the change
	CaseNumber         string                 `protobuf:"bytes,9,opt,name=case_number,json=caseNumber,proto3" json:"case_number,omitempty"`                                          // Associated case number
	EvidenceNumber     string                 `protobuf:"bytes,10,opt,name=evidence_number,json=evidenceNumber,proto3" json:"evidence_number,omitempty"`                             // Evidence tracking number
	// Enhanced fields for detailed custody information
	PerformingOfficerId  string               `protobuf:"bytes,11,opt,name=performing_officer_id,json=performingOfficerId,proto3" json:"performing_officer_id,omitempty"`                                          // Officer performing the action (may differ from transferring_user_id)
	ReceivingEntityType  CustodyEntityType    `protobuf:"varint,12,opt,name=receiving_entity_type,json=receivingEntityType,proto3,enum=hero.property.v1.CustodyEntityType" json:"receiving_entity_type,omitempty"` // Type of receiving entity (Person, Crime Lab, Court, etc.)
	ReceivingEntityName  string               `protobuf:"bytes,13,opt,name=receiving_entity_name,json=receivingEntityName,proto3" json:"receiving_entity_name,omitempty"`                                          // Name of the receiving entity
	CheckoutLength       string               `protobuf:"bytes,14,opt,name=checkout_length,json=checkoutLength,proto3" json:"checkout_length,omitempty"`                                                           // Length of check out (e.g., "2 weeks", "until court date")
	Reason               string               `protobuf:"bytes,15,opt,name=reason,proto3" json:"reason,omitempty"`                                                                                                 // Specific reason for the action
	ReceivingAgencyType  CustodyAgencyType    `protobuf:"varint,16,opt,name=receiving_agency_type,json=receivingAgencyType,proto3,enum=hero.property.v1.CustodyAgencyType" json:"receiving_agency_type,omitempty"` // Type of receiving agency (PD, Other)
	ConfirmationReceived bool                 `protobuf:"varint,17,opt,name=confirmation_received,json=confirmationReceived,proto3" json:"confirmation_received,omitempty"`                                        // Whether confirmation of receipt was obtained
	CollectionLocation   string               `protobuf:"bytes,18,opt,name=collection_location,json=collectionLocation,proto3" json:"collection_location,omitempty"`                                               // Location where property was originally collected from
	DisposalType         PropertyDisposalType `protobuf:"varint,19,opt,name=disposal_type,json=disposalType,proto3,enum=hero.property.v1.PropertyDisposalType" json:"disposal_type,omitempty"`                     // Type of disposal (for DISPOSED actions)
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *CustodyEvent) Reset() {
	*x = CustodyEvent{}
	mi := &file_hero_property_v1_property_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustodyEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustodyEvent) ProtoMessage() {}

func (x *CustodyEvent) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustodyEvent.ProtoReflect.Descriptor instead.
func (*CustodyEvent) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{0}
}

func (x *CustodyEvent) GetTimestamp() string {
	if x != nil {
		return x.Timestamp
	}
	return ""
}

func (x *CustodyEvent) GetTransferringUserId() string {
	if x != nil {
		return x.TransferringUserId
	}
	return ""
}

func (x *CustodyEvent) GetTransferringAgency() string {
	if x != nil {
		return x.TransferringAgency
	}
	return ""
}

func (x *CustodyEvent) GetReceivingUserId() string {
	if x != nil {
		return x.ReceivingUserId
	}
	return ""
}

func (x *CustodyEvent) GetReceivingAgency() string {
	if x != nil {
		return x.ReceivingAgency
	}
	return ""
}

func (x *CustodyEvent) GetNewLocation() string {
	if x != nil {
		return x.NewLocation
	}
	return ""
}

func (x *CustodyEvent) GetActionType() CustodyActionType {
	if x != nil {
		return x.ActionType
	}
	return CustodyActionType_CUSTODY_ACTION_TYPE_UNSPECIFIED
}

func (x *CustodyEvent) GetNotes() string {
	if x != nil {
		return x.Notes
	}
	return ""
}

func (x *CustodyEvent) GetCaseNumber() string {
	if x != nil {
		return x.CaseNumber
	}
	return ""
}

func (x *CustodyEvent) GetEvidenceNumber() string {
	if x != nil {
		return x.EvidenceNumber
	}
	return ""
}

func (x *CustodyEvent) GetPerformingOfficerId() string {
	if x != nil {
		return x.PerformingOfficerId
	}
	return ""
}

func (x *CustodyEvent) GetReceivingEntityType() CustodyEntityType {
	if x != nil {
		return x.ReceivingEntityType
	}
	return CustodyEntityType_CUSTODY_ENTITY_TYPE_UNSPECIFIED
}

func (x *CustodyEvent) GetReceivingEntityName() string {
	if x != nil {
		return x.ReceivingEntityName
	}
	return ""
}

func (x *CustodyEvent) GetCheckoutLength() string {
	if x != nil {
		return x.CheckoutLength
	}
	return ""
}

func (x *CustodyEvent) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *CustodyEvent) GetReceivingAgencyType() CustodyAgencyType {
	if x != nil {
		return x.ReceivingAgencyType
	}
	return CustodyAgencyType_CUSTODY_AGENCY_TYPE_UNSPECIFIED
}

func (x *CustodyEvent) GetConfirmationReceived() bool {
	if x != nil {
		return x.ConfirmationReceived
	}
	return false
}

func (x *CustodyEvent) GetCollectionLocation() string {
	if x != nil {
		return x.CollectionLocation
	}
	return ""
}

func (x *CustodyEvent) GetDisposalType() PropertyDisposalType {
	if x != nil {
		return x.DisposalType
	}
	return PropertyDisposalType_PROPERTY_DISPOSAL_TYPE_UNSPECIFIED
}

// Property represents a property entity
type Property struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Id               string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                                                                     // Unique identifier for the property
	OrgId            int32                  `protobuf:"varint,2,opt,name=org_id,json=orgId,proto3" json:"org_id,omitempty"`                                                                 // Organization identifier
	PropertyNumber   string                 `protobuf:"bytes,3,opt,name=property_number,json=propertyNumber,proto3" json:"property_number,omitempty"`                                       // Property/Evidence number for tracking
	PropertyStatus   PropertyStatus         `protobuf:"varint,4,opt,name=property_status,json=propertyStatus,proto3,enum=hero.property.v1.PropertyStatus" json:"property_status,omitempty"` // Current status of the property
	IsEvidence       bool                   `protobuf:"varint,5,opt,name=is_evidence,json=isEvidence,proto3" json:"is_evidence,omitempty"`                                                  // Flag indicating whether this is evidence
	RetentionPeriod  string                 `protobuf:"bytes,6,opt,name=retention_period,json=retentionPeriod,proto3" json:"retention_period,omitempty"`                                    // Retention period for evidence
	DisposalType     PropertyDisposalType   `protobuf:"varint,7,opt,name=disposal_type,json=disposalType,proto3,enum=hero.property.v1.PropertyDisposalType" json:"disposal_type,omitempty"` // How property was disposed
	Notes            string                 `protobuf:"bytes,8,opt,name=notes,proto3" json:"notes,omitempty"`                                                                               // Additional notes about the property
	CurrentCustodian string                 `protobuf:"bytes,9,opt,name=current_custodian,json=currentCustodian,proto3" json:"current_custodian,omitempty"`                                 // Current person/agency with custody
	CurrentLocation  string                 `protobuf:"bytes,10,opt,name=current_location,json=currentLocation,proto3" json:"current_location,omitempty"`                                   // Current location of the property
	CustodyChain     []*CustodyEvent        `protobuf:"bytes,11,rep,name=custody_chain,json=custodyChain,proto3" json:"custody_chain,omitempty"`                                            // Complete chain of custody history
	// Link to the dynamic schema used for this property (reuses Entity Schema registry)
	SchemaId      string `protobuf:"bytes,12,opt,name=schema_id,json=schemaId,proto3" json:"schema_id,omitempty"`                 // ID of the schema this property conforms to
	SchemaVersion int32  `protobuf:"varint,13,opt,name=schema_version,json=schemaVersion,proto3" json:"schema_version,omitempty"` // Version of the schema used
	// Dynamic payload validated/rendered by the resolved schema
	Details           *structpb.Struct  `protobuf:"bytes,14,opt,name=details,proto3" json:"details,omitempty"`
	CreateTime        string            `protobuf:"bytes,15,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`                                                                 // ISO8601 timestamp when the property was created
	UpdateTime        string            `protobuf:"bytes,16,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`                                                                 // ISO8601 timestamp when the property was last updated
	CreatedBy         string            `protobuf:"bytes,17,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`                                                                    // User ID of the creator
	UpdatedBy         string            `protobuf:"bytes,18,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`                                                                    // User ID of the last updater
	Version           int32             `protobuf:"varint,19,opt,name=version,proto3" json:"version,omitempty"`                                                                                        // Version number (for audit/history purposes)
	ResourceType      string            `protobuf:"bytes,20,opt,name=resource_type,json=resourceType,proto3" json:"resource_type,omitempty"`                                                           // Fixed value "PROPERTY"
	NibrsPropertyType NIBRSPropertyType `protobuf:"varint,21,opt,name=nibrs_property_type,json=nibrsPropertyType,proto3,enum=hero.property.v1.NIBRSPropertyType" json:"nibrs_property_type,omitempty"` // NIBRS property type classification
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *Property) Reset() {
	*x = Property{}
	mi := &file_hero_property_v1_property_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Property) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Property) ProtoMessage() {}

func (x *Property) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Property.ProtoReflect.Descriptor instead.
func (*Property) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{1}
}

func (x *Property) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Property) GetOrgId() int32 {
	if x != nil {
		return x.OrgId
	}
	return 0
}

func (x *Property) GetPropertyNumber() string {
	if x != nil {
		return x.PropertyNumber
	}
	return ""
}

func (x *Property) GetPropertyStatus() PropertyStatus {
	if x != nil {
		return x.PropertyStatus
	}
	return PropertyStatus_PROPERTY_STATUS_UNSPECIFIED
}

func (x *Property) GetIsEvidence() bool {
	if x != nil {
		return x.IsEvidence
	}
	return false
}

func (x *Property) GetRetentionPeriod() string {
	if x != nil {
		return x.RetentionPeriod
	}
	return ""
}

func (x *Property) GetDisposalType() PropertyDisposalType {
	if x != nil {
		return x.DisposalType
	}
	return PropertyDisposalType_PROPERTY_DISPOSAL_TYPE_UNSPECIFIED
}

func (x *Property) GetNotes() string {
	if x != nil {
		return x.Notes
	}
	return ""
}

func (x *Property) GetCurrentCustodian() string {
	if x != nil {
		return x.CurrentCustodian
	}
	return ""
}

func (x *Property) GetCurrentLocation() string {
	if x != nil {
		return x.CurrentLocation
	}
	return ""
}

func (x *Property) GetCustodyChain() []*CustodyEvent {
	if x != nil {
		return x.CustodyChain
	}
	return nil
}

func (x *Property) GetSchemaId() string {
	if x != nil {
		return x.SchemaId
	}
	return ""
}

func (x *Property) GetSchemaVersion() int32 {
	if x != nil {
		return x.SchemaVersion
	}
	return 0
}

func (x *Property) GetDetails() *structpb.Struct {
	if x != nil {
		return x.Details
	}
	return nil
}

func (x *Property) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *Property) GetUpdateTime() string {
	if x != nil {
		return x.UpdateTime
	}
	return ""
}

func (x *Property) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *Property) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *Property) GetVersion() int32 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *Property) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *Property) GetNibrsPropertyType() NIBRSPropertyType {
	if x != nil {
		return x.NibrsPropertyType
	}
	return NIBRSPropertyType_PROPERTY_TYPE_UNSPECIFIED
}

// DateRange represents a time range for filtering search results
type DateRange struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	From          string                 `protobuf:"bytes,1,opt,name=from,proto3" json:"from,omitempty"` // Start time in RFC3339 format (inclusive)
	To            string                 `protobuf:"bytes,2,opt,name=to,proto3" json:"to,omitempty"`     // End time in RFC3339 format (inclusive)
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DateRange) Reset() {
	*x = DateRange{}
	mi := &file_hero_property_v1_property_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DateRange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DateRange) ProtoMessage() {}

func (x *DateRange) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DateRange.ProtoReflect.Descriptor instead.
func (*DateRange) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{2}
}

func (x *DateRange) GetFrom() string {
	if x != nil {
		return x.From
	}
	return ""
}

func (x *DateRange) GetTo() string {
	if x != nil {
		return x.To
	}
	return ""
}

// FieldQuery represents a field-specific search query
type FieldQuery struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Field         string                 `protobuf:"bytes,1,opt,name=field,proto3" json:"field,omitempty"` // Field to search in (id, notes, location, current_custodian, current_location)
	Query         string                 `protobuf:"bytes,2,opt,name=query,proto3" json:"query,omitempty"` // Search term for this specific field
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FieldQuery) Reset() {
	*x = FieldQuery{}
	mi := &file_hero_property_v1_property_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FieldQuery) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FieldQuery) ProtoMessage() {}

func (x *FieldQuery) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FieldQuery.ProtoReflect.Descriptor instead.
func (*FieldQuery) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{3}
}

func (x *FieldQuery) GetField() string {
	if x != nil {
		return x.Field
	}
	return ""
}

func (x *FieldQuery) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

// HighlightResult represents highlighted search results for a property
type HighlightResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Field         string                 `protobuf:"bytes,1,opt,name=field,proto3" json:"field,omitempty"`         // Field name that had a match
	Fragments     []string               `protobuf:"bytes,2,rep,name=fragments,proto3" json:"fragments,omitempty"` // Highlighted fragments with matched terms
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HighlightResult) Reset() {
	*x = HighlightResult{}
	mi := &file_hero_property_v1_property_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HighlightResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HighlightResult) ProtoMessage() {}

func (x *HighlightResult) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HighlightResult.ProtoReflect.Descriptor instead.
func (*HighlightResult) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{4}
}

func (x *HighlightResult) GetField() string {
	if x != nil {
		return x.Field
	}
	return ""
}

func (x *HighlightResult) GetFragments() []string {
	if x != nil {
		return x.Fragments
	}
	return nil
}

// Request/Response Messages for CreateProperty
type CreatePropertyRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Property      *Property              `protobuf:"bytes,1,opt,name=property,proto3" json:"property,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreatePropertyRequest) Reset() {
	*x = CreatePropertyRequest{}
	mi := &file_hero_property_v1_property_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreatePropertyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePropertyRequest) ProtoMessage() {}

func (x *CreatePropertyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePropertyRequest.ProtoReflect.Descriptor instead.
func (*CreatePropertyRequest) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{5}
}

func (x *CreatePropertyRequest) GetProperty() *Property {
	if x != nil {
		return x.Property
	}
	return nil
}

type CreatePropertyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Property      *Property              `protobuf:"bytes,1,opt,name=property,proto3" json:"property,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreatePropertyResponse) Reset() {
	*x = CreatePropertyResponse{}
	mi := &file_hero_property_v1_property_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreatePropertyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePropertyResponse) ProtoMessage() {}

func (x *CreatePropertyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePropertyResponse.ProtoReflect.Descriptor instead.
func (*CreatePropertyResponse) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{6}
}

func (x *CreatePropertyResponse) GetProperty() *Property {
	if x != nil {
		return x.Property
	}
	return nil
}

// Request/Response Messages for GetProperty
type GetPropertyRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPropertyRequest) Reset() {
	*x = GetPropertyRequest{}
	mi := &file_hero_property_v1_property_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPropertyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPropertyRequest) ProtoMessage() {}

func (x *GetPropertyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPropertyRequest.ProtoReflect.Descriptor instead.
func (*GetPropertyRequest) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{7}
}

func (x *GetPropertyRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type GetPropertyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Property      *Property              `protobuf:"bytes,1,opt,name=property,proto3" json:"property,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPropertyResponse) Reset() {
	*x = GetPropertyResponse{}
	mi := &file_hero_property_v1_property_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPropertyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPropertyResponse) ProtoMessage() {}

func (x *GetPropertyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPropertyResponse.ProtoReflect.Descriptor instead.
func (*GetPropertyResponse) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{8}
}

func (x *GetPropertyResponse) GetProperty() *Property {
	if x != nil {
		return x.Property
	}
	return nil
}

// Request/Response Messages for ListProperties
type ListPropertiesRequest struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	PageSize          int32                  `protobuf:"varint,1,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	PageToken         string                 `protobuf:"bytes,2,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	NibrsPropertyType NIBRSPropertyType      `protobuf:"varint,3,opt,name=nibrs_property_type,json=nibrsPropertyType,proto3,enum=hero.property.v1.NIBRSPropertyType" json:"nibrs_property_type,omitempty"`
	PropertyStatus    PropertyStatus         `protobuf:"varint,4,opt,name=property_status,json=propertyStatus,proto3,enum=hero.property.v1.PropertyStatus" json:"property_status,omitempty"`
	OrderBy           string                 `protobuf:"bytes,5,opt,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *ListPropertiesRequest) Reset() {
	*x = ListPropertiesRequest{}
	mi := &file_hero_property_v1_property_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListPropertiesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPropertiesRequest) ProtoMessage() {}

func (x *ListPropertiesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPropertiesRequest.ProtoReflect.Descriptor instead.
func (*ListPropertiesRequest) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{9}
}

func (x *ListPropertiesRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListPropertiesRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListPropertiesRequest) GetNibrsPropertyType() NIBRSPropertyType {
	if x != nil {
		return x.NibrsPropertyType
	}
	return NIBRSPropertyType_PROPERTY_TYPE_UNSPECIFIED
}

func (x *ListPropertiesRequest) GetPropertyStatus() PropertyStatus {
	if x != nil {
		return x.PropertyStatus
	}
	return PropertyStatus_PROPERTY_STATUS_UNSPECIFIED
}

func (x *ListPropertiesRequest) GetOrderBy() string {
	if x != nil {
		return x.OrderBy
	}
	return ""
}

type ListPropertiesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Properties    []*Property            `protobuf:"bytes,1,rep,name=properties,proto3" json:"properties,omitempty"`
	NextPageToken string                 `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListPropertiesResponse) Reset() {
	*x = ListPropertiesResponse{}
	mi := &file_hero_property_v1_property_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListPropertiesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPropertiesResponse) ProtoMessage() {}

func (x *ListPropertiesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPropertiesResponse.ProtoReflect.Descriptor instead.
func (*ListPropertiesResponse) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{10}
}

func (x *ListPropertiesResponse) GetProperties() []*Property {
	if x != nil {
		return x.Properties
	}
	return nil
}

func (x *ListPropertiesResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

// Request/Response Messages for UpdateProperty
type UpdatePropertyRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Property      *Property              `protobuf:"bytes,1,opt,name=property,proto3" json:"property,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdatePropertyRequest) Reset() {
	*x = UpdatePropertyRequest{}
	mi := &file_hero_property_v1_property_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdatePropertyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePropertyRequest) ProtoMessage() {}

func (x *UpdatePropertyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePropertyRequest.ProtoReflect.Descriptor instead.
func (*UpdatePropertyRequest) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{11}
}

func (x *UpdatePropertyRequest) GetProperty() *Property {
	if x != nil {
		return x.Property
	}
	return nil
}

type UpdatePropertyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Property      *Property              `protobuf:"bytes,1,opt,name=property,proto3" json:"property,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdatePropertyResponse) Reset() {
	*x = UpdatePropertyResponse{}
	mi := &file_hero_property_v1_property_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdatePropertyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePropertyResponse) ProtoMessage() {}

func (x *UpdatePropertyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePropertyResponse.ProtoReflect.Descriptor instead.
func (*UpdatePropertyResponse) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{12}
}

func (x *UpdatePropertyResponse) GetProperty() *Property {
	if x != nil {
		return x.Property
	}
	return nil
}

// Request/Response Messages for DeleteProperty
type DeletePropertyRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeletePropertyRequest) Reset() {
	*x = DeletePropertyRequest{}
	mi := &file_hero_property_v1_property_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeletePropertyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePropertyRequest) ProtoMessage() {}

func (x *DeletePropertyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePropertyRequest.ProtoReflect.Descriptor instead.
func (*DeletePropertyRequest) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{13}
}

func (x *DeletePropertyRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type DeletePropertyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeletePropertyResponse) Reset() {
	*x = DeletePropertyResponse{}
	mi := &file_hero_property_v1_property_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeletePropertyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePropertyResponse) ProtoMessage() {}

func (x *DeletePropertyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePropertyResponse.ProtoReflect.Descriptor instead.
func (*DeletePropertyResponse) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{14}
}

// Request/Response Messages for SearchProperties
type SearchPropertiesRequest struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Query             string                 `protobuf:"bytes,1,opt,name=query,proto3" json:"query,omitempty"`
	FieldQueries      []*FieldQuery          `protobuf:"bytes,2,rep,name=field_queries,json=fieldQueries,proto3" json:"field_queries,omitempty"`
	DateRange         *DateRange             `protobuf:"bytes,3,opt,name=date_range,json=dateRange,proto3" json:"date_range,omitempty"`
	NibrsPropertyType NIBRSPropertyType      `protobuf:"varint,4,opt,name=nibrs_property_type,json=nibrsPropertyType,proto3,enum=hero.property.v1.NIBRSPropertyType" json:"nibrs_property_type,omitempty"`
	PropertyStatus    PropertyStatus         `protobuf:"varint,5,opt,name=property_status,json=propertyStatus,proto3,enum=hero.property.v1.PropertyStatus" json:"property_status,omitempty"`
	OrderBy           SearchOrderBy          `protobuf:"varint,6,opt,name=order_by,json=orderBy,proto3,enum=hero.property.v1.SearchOrderBy" json:"order_by,omitempty"`
	PageSize          int32                  `protobuf:"varint,7,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	PageToken         string                 `protobuf:"bytes,8,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *SearchPropertiesRequest) Reset() {
	*x = SearchPropertiesRequest{}
	mi := &file_hero_property_v1_property_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchPropertiesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchPropertiesRequest) ProtoMessage() {}

func (x *SearchPropertiesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchPropertiesRequest.ProtoReflect.Descriptor instead.
func (*SearchPropertiesRequest) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{15}
}

func (x *SearchPropertiesRequest) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *SearchPropertiesRequest) GetFieldQueries() []*FieldQuery {
	if x != nil {
		return x.FieldQueries
	}
	return nil
}

func (x *SearchPropertiesRequest) GetDateRange() *DateRange {
	if x != nil {
		return x.DateRange
	}
	return nil
}

func (x *SearchPropertiesRequest) GetNibrsPropertyType() NIBRSPropertyType {
	if x != nil {
		return x.NibrsPropertyType
	}
	return NIBRSPropertyType_PROPERTY_TYPE_UNSPECIFIED
}

func (x *SearchPropertiesRequest) GetPropertyStatus() PropertyStatus {
	if x != nil {
		return x.PropertyStatus
	}
	return PropertyStatus_PROPERTY_STATUS_UNSPECIFIED
}

func (x *SearchPropertiesRequest) GetOrderBy() SearchOrderBy {
	if x != nil {
		return x.OrderBy
	}
	return SearchOrderBy_SEARCH_ORDER_BY_UNSPECIFIED
}

func (x *SearchPropertiesRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *SearchPropertiesRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

type SearchPropertiesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Properties    []*Property            `protobuf:"bytes,1,rep,name=properties,proto3" json:"properties,omitempty"`
	Highlights    []*HighlightResult     `protobuf:"bytes,2,rep,name=highlights,proto3" json:"highlights,omitempty"`
	NextPageToken string                 `protobuf:"bytes,3,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	TotalCount    int32                  `protobuf:"varint,4,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchPropertiesResponse) Reset() {
	*x = SearchPropertiesResponse{}
	mi := &file_hero_property_v1_property_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchPropertiesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchPropertiesResponse) ProtoMessage() {}

func (x *SearchPropertiesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchPropertiesResponse.ProtoReflect.Descriptor instead.
func (*SearchPropertiesResponse) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{16}
}

func (x *SearchPropertiesResponse) GetProperties() []*Property {
	if x != nil {
		return x.Properties
	}
	return nil
}

func (x *SearchPropertiesResponse) GetHighlights() []*HighlightResult {
	if x != nil {
		return x.Highlights
	}
	return nil
}

func (x *SearchPropertiesResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *SearchPropertiesResponse) GetTotalCount() int32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

// Request/Response Messages for BatchGetProperties
type BatchGetPropertiesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ids           []string               `protobuf:"bytes,1,rep,name=ids,proto3" json:"ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchGetPropertiesRequest) Reset() {
	*x = BatchGetPropertiesRequest{}
	mi := &file_hero_property_v1_property_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchGetPropertiesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetPropertiesRequest) ProtoMessage() {}

func (x *BatchGetPropertiesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetPropertiesRequest.ProtoReflect.Descriptor instead.
func (*BatchGetPropertiesRequest) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{17}
}

func (x *BatchGetPropertiesRequest) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

type BatchGetPropertiesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Properties    []*Property            `protobuf:"bytes,1,rep,name=properties,proto3" json:"properties,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchGetPropertiesResponse) Reset() {
	*x = BatchGetPropertiesResponse{}
	mi := &file_hero_property_v1_property_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchGetPropertiesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetPropertiesResponse) ProtoMessage() {}

func (x *BatchGetPropertiesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetPropertiesResponse.ProtoReflect.Descriptor instead.
func (*BatchGetPropertiesResponse) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{18}
}

func (x *BatchGetPropertiesResponse) GetProperties() []*Property {
	if x != nil {
		return x.Properties
	}
	return nil
}

// Request/Response Messages for AddCustodyEvent
type AddCustodyEventRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PropertyId    string                 `protobuf:"bytes,1,opt,name=property_id,json=propertyId,proto3" json:"property_id,omitempty"`
	CustodyEvent  *CustodyEvent          `protobuf:"bytes,2,opt,name=custody_event,json=custodyEvent,proto3" json:"custody_event,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddCustodyEventRequest) Reset() {
	*x = AddCustodyEventRequest{}
	mi := &file_hero_property_v1_property_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddCustodyEventRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddCustodyEventRequest) ProtoMessage() {}

func (x *AddCustodyEventRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddCustodyEventRequest.ProtoReflect.Descriptor instead.
func (*AddCustodyEventRequest) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{19}
}

func (x *AddCustodyEventRequest) GetPropertyId() string {
	if x != nil {
		return x.PropertyId
	}
	return ""
}

func (x *AddCustodyEventRequest) GetCustodyEvent() *CustodyEvent {
	if x != nil {
		return x.CustodyEvent
	}
	return nil
}

type AddCustodyEventResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddCustodyEventResponse) Reset() {
	*x = AddCustodyEventResponse{}
	mi := &file_hero_property_v1_property_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddCustodyEventResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddCustodyEventResponse) ProtoMessage() {}

func (x *AddCustodyEventResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddCustodyEventResponse.ProtoReflect.Descriptor instead.
func (*AddCustodyEventResponse) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{20}
}

// Request/Response Messages for GetCustodyChain
type GetCustodyChainRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PropertyId    string                 `protobuf:"bytes,1,opt,name=property_id,json=propertyId,proto3" json:"property_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCustodyChainRequest) Reset() {
	*x = GetCustodyChainRequest{}
	mi := &file_hero_property_v1_property_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCustodyChainRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustodyChainRequest) ProtoMessage() {}

func (x *GetCustodyChainRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustodyChainRequest.ProtoReflect.Descriptor instead.
func (*GetCustodyChainRequest) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{21}
}

func (x *GetCustodyChainRequest) GetPropertyId() string {
	if x != nil {
		return x.PropertyId
	}
	return ""
}

type GetCustodyChainResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CustodyChain  []*CustodyEvent        `protobuf:"bytes,1,rep,name=custody_chain,json=custodyChain,proto3" json:"custody_chain,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCustodyChainResponse) Reset() {
	*x = GetCustodyChainResponse{}
	mi := &file_hero_property_v1_property_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCustodyChainResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustodyChainResponse) ProtoMessage() {}

func (x *GetCustodyChainResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustodyChainResponse.ProtoReflect.Descriptor instead.
func (*GetCustodyChainResponse) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{22}
}

func (x *GetCustodyChainResponse) GetCustodyChain() []*CustodyEvent {
	if x != nil {
		return x.CustodyChain
	}
	return nil
}

// Request/Response Messages for ListPropertyFileAttachments
type ListPropertyFileAttachmentsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PropertyId    string                 `protobuf:"bytes,1,opt,name=property_id,json=propertyId,proto3" json:"property_id,omitempty"`
	FileCategory  string                 `protobuf:"bytes,2,opt,name=file_category,json=fileCategory,proto3" json:"file_category,omitempty"`
	PageSize      int32                  `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	PageToken     string                 `protobuf:"bytes,4,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListPropertyFileAttachmentsRequest) Reset() {
	*x = ListPropertyFileAttachmentsRequest{}
	mi := &file_hero_property_v1_property_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListPropertyFileAttachmentsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPropertyFileAttachmentsRequest) ProtoMessage() {}

func (x *ListPropertyFileAttachmentsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPropertyFileAttachmentsRequest.ProtoReflect.Descriptor instead.
func (*ListPropertyFileAttachmentsRequest) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{23}
}

func (x *ListPropertyFileAttachmentsRequest) GetPropertyId() string {
	if x != nil {
		return x.PropertyId
	}
	return ""
}

func (x *ListPropertyFileAttachmentsRequest) GetFileCategory() string {
	if x != nil {
		return x.FileCategory
	}
	return ""
}

func (x *ListPropertyFileAttachmentsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListPropertyFileAttachmentsRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

type ListPropertyFileAttachmentsResponse struct {
	state           protoimpl.MessageState   `protogen:"open.v1"`
	FileAttachments []*PropertyFileReference `protobuf:"bytes,1,rep,name=file_attachments,json=fileAttachments,proto3" json:"file_attachments,omitempty"`
	NextPageToken   string                   `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ListPropertyFileAttachmentsResponse) Reset() {
	*x = ListPropertyFileAttachmentsResponse{}
	mi := &file_hero_property_v1_property_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListPropertyFileAttachmentsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPropertyFileAttachmentsResponse) ProtoMessage() {}

func (x *ListPropertyFileAttachmentsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPropertyFileAttachmentsResponse.ProtoReflect.Descriptor instead.
func (*ListPropertyFileAttachmentsResponse) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{24}
}

func (x *ListPropertyFileAttachmentsResponse) GetFileAttachments() []*PropertyFileReference {
	if x != nil {
		return x.FileAttachments
	}
	return nil
}

func (x *ListPropertyFileAttachmentsResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

// PropertyFileReference represents a file attachment for a property
type PropertyFileReference struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	PropertyId    string                 `protobuf:"bytes,2,opt,name=property_id,json=propertyId,proto3" json:"property_id,omitempty"`
	FileId        string                 `protobuf:"bytes,3,opt,name=file_id,json=fileId,proto3" json:"file_id,omitempty"`
	Caption       string                 `protobuf:"bytes,4,opt,name=caption,proto3" json:"caption,omitempty"`
	DisplayName   string                 `protobuf:"bytes,5,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	DisplayOrder  int32                  `protobuf:"varint,6,opt,name=display_order,json=displayOrder,proto3" json:"display_order,omitempty"`
	FileCategory  string                 `protobuf:"bytes,7,opt,name=file_category,json=fileCategory,proto3" json:"file_category,omitempty"`
	Metadata      *structpb.Struct       `protobuf:"bytes,8,opt,name=metadata,proto3" json:"metadata,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PropertyFileReference) Reset() {
	*x = PropertyFileReference{}
	mi := &file_hero_property_v1_property_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PropertyFileReference) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PropertyFileReference) ProtoMessage() {}

func (x *PropertyFileReference) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PropertyFileReference.ProtoReflect.Descriptor instead.
func (*PropertyFileReference) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{25}
}

func (x *PropertyFileReference) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *PropertyFileReference) GetPropertyId() string {
	if x != nil {
		return x.PropertyId
	}
	return ""
}

func (x *PropertyFileReference) GetFileId() string {
	if x != nil {
		return x.FileId
	}
	return ""
}

func (x *PropertyFileReference) GetCaption() string {
	if x != nil {
		return x.Caption
	}
	return ""
}

func (x *PropertyFileReference) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *PropertyFileReference) GetDisplayOrder() int32 {
	if x != nil {
		return x.DisplayOrder
	}
	return 0
}

func (x *PropertyFileReference) GetFileCategory() string {
	if x != nil {
		return x.FileCategory
	}
	return ""
}

func (x *PropertyFileReference) GetMetadata() *structpb.Struct {
	if x != nil {
		return x.Metadata
	}
	return nil
}

// Request/Response Messages for AddPropertyFileAttachment
type AddPropertyFileAttachmentRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	PropertyId     string                 `protobuf:"bytes,1,opt,name=property_id,json=propertyId,proto3" json:"property_id,omitempty"`
	FileAttachment *PropertyFileReference `protobuf:"bytes,2,opt,name=file_attachment,json=fileAttachment,proto3" json:"file_attachment,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *AddPropertyFileAttachmentRequest) Reset() {
	*x = AddPropertyFileAttachmentRequest{}
	mi := &file_hero_property_v1_property_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddPropertyFileAttachmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddPropertyFileAttachmentRequest) ProtoMessage() {}

func (x *AddPropertyFileAttachmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddPropertyFileAttachmentRequest.ProtoReflect.Descriptor instead.
func (*AddPropertyFileAttachmentRequest) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{26}
}

func (x *AddPropertyFileAttachmentRequest) GetPropertyId() string {
	if x != nil {
		return x.PropertyId
	}
	return ""
}

func (x *AddPropertyFileAttachmentRequest) GetFileAttachment() *PropertyFileReference {
	if x != nil {
		return x.FileAttachment
	}
	return nil
}

type AddPropertyFileAttachmentResponse struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	FileAttachment *PropertyFileReference `protobuf:"bytes,1,opt,name=file_attachment,json=fileAttachment,proto3" json:"file_attachment,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *AddPropertyFileAttachmentResponse) Reset() {
	*x = AddPropertyFileAttachmentResponse{}
	mi := &file_hero_property_v1_property_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddPropertyFileAttachmentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddPropertyFileAttachmentResponse) ProtoMessage() {}

func (x *AddPropertyFileAttachmentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddPropertyFileAttachmentResponse.ProtoReflect.Descriptor instead.
func (*AddPropertyFileAttachmentResponse) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{27}
}

func (x *AddPropertyFileAttachmentResponse) GetFileAttachment() *PropertyFileReference {
	if x != nil {
		return x.FileAttachment
	}
	return nil
}

// Request/Response Messages for RemovePropertyFileAttachment
type RemovePropertyFileAttachmentRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PropertyId    string                 `protobuf:"bytes,1,opt,name=property_id,json=propertyId,proto3" json:"property_id,omitempty"`
	AttachmentId  string                 `protobuf:"bytes,2,opt,name=attachment_id,json=attachmentId,proto3" json:"attachment_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RemovePropertyFileAttachmentRequest) Reset() {
	*x = RemovePropertyFileAttachmentRequest{}
	mi := &file_hero_property_v1_property_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RemovePropertyFileAttachmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemovePropertyFileAttachmentRequest) ProtoMessage() {}

func (x *RemovePropertyFileAttachmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemovePropertyFileAttachmentRequest.ProtoReflect.Descriptor instead.
func (*RemovePropertyFileAttachmentRequest) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{28}
}

func (x *RemovePropertyFileAttachmentRequest) GetPropertyId() string {
	if x != nil {
		return x.PropertyId
	}
	return ""
}

func (x *RemovePropertyFileAttachmentRequest) GetAttachmentId() string {
	if x != nil {
		return x.AttachmentId
	}
	return ""
}

type RemovePropertyFileAttachmentResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RemovePropertyFileAttachmentResponse) Reset() {
	*x = RemovePropertyFileAttachmentResponse{}
	mi := &file_hero_property_v1_property_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RemovePropertyFileAttachmentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemovePropertyFileAttachmentResponse) ProtoMessage() {}

func (x *RemovePropertyFileAttachmentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemovePropertyFileAttachmentResponse.ProtoReflect.Descriptor instead.
func (*RemovePropertyFileAttachmentResponse) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{29}
}

// PropertyReference is a denormalized pointer to a Property used in other
// services (e.g., cases). It intentionally mirrors hero.entity.v1.Reference
// shape where appropriate while remaining property-specific.
type PropertyReference struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                         // hero.property.v1.Property.id
	Version       int32                  `protobuf:"varint,2,opt,name=version,proto3" json:"version,omitempty"`                              // Snapshot/version number of the property
	DisplayName   string                 `protobuf:"bytes,3,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`    // Cached human label for convenience
	RelationType  string                 `protobuf:"bytes,4,opt,name=relation_type,json=relationType,proto3" json:"relation_type,omitempty"` // Caller-defined relation (e.g., "evidence", "involved")
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PropertyReference) Reset() {
	*x = PropertyReference{}
	mi := &file_hero_property_v1_property_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PropertyReference) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PropertyReference) ProtoMessage() {}

func (x *PropertyReference) ProtoReflect() protoreflect.Message {
	mi := &file_hero_property_v1_property_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PropertyReference.ProtoReflect.Descriptor instead.
func (*PropertyReference) Descriptor() ([]byte, []int) {
	return file_hero_property_v1_property_proto_rawDescGZIP(), []int{30}
}

func (x *PropertyReference) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *PropertyReference) GetVersion() int32 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *PropertyReference) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *PropertyReference) GetRelationType() string {
	if x != nil {
		return x.RelationType
	}
	return ""
}

var File_hero_property_v1_property_proto protoreflect.FileDescriptor

var file_hero_property_v1_property_proto_rawDesc = []byte{
	0x0a, 0x1f, 0x68, 0x65, 0x72, 0x6f, 0x2f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2f,
	0x76, 0x31, 0x2f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x10, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79,
	0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0xbd, 0x07, 0x0a, 0x0c, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x64, 0x79, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x12, 0x30, 0x0a, 0x14, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x72, 0x69, 0x6e, 0x67,
	0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x55, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x2f, 0x0a, 0x13, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x72, 0x69,
	0x6e, 0x67, 0x5f, 0x61, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x12, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x41, 0x67, 0x65,
	0x6e, 0x63, 0x79, 0x12, 0x2a, 0x0a, 0x11, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x69, 0x6e, 0x67,
	0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x69, 0x6e, 0x67, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x29, 0x0a, 0x10, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x67, 0x65,
	0x6e, 0x63, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x72, 0x65, 0x63, 0x65, 0x69,
	0x76, 0x69, 0x6e, 0x67, 0x41, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x21, 0x0a, 0x0c, 0x6e, 0x65,
	0x77, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x6e, 0x65, 0x77, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x44, 0x0a,
	0x0b, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x23, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x64, 0x79, 0x41, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x73,
	0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x63, 0x61, 0x73, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x27, 0x0a, 0x0f, 0x65, 0x76,
	0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x65, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x12, 0x32, 0x0a, 0x15, 0x70, 0x65, 0x72, 0x66, 0x6f, 0x72, 0x6d, 0x69, 0x6e,
	0x67, 0x5f, 0x6f, 0x66, 0x66, 0x69, 0x63, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x13, 0x70, 0x65, 0x72, 0x66, 0x6f, 0x72, 0x6d, 0x69, 0x6e, 0x67, 0x4f, 0x66,
	0x66, 0x69, 0x63, 0x65, 0x72, 0x49, 0x64, 0x12, 0x57, 0x0a, 0x15, 0x72, 0x65, 0x63, 0x65, 0x69,
	0x76, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x64,
	0x79, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x13, 0x72, 0x65, 0x63,
	0x65, 0x69, 0x76, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x32, 0x0a, 0x15, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x13, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x6f, 0x75, 0x74,
	0x5f, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63,
	0x68, 0x65, 0x63, 0x6b, 0x6f, 0x75, 0x74, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x12, 0x16, 0x0a,
	0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x57, 0x0a, 0x15, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x69,
	0x6e, 0x67, 0x5f, 0x61, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x10,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70,
	0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x64, 0x79, 0x41,
	0x67, 0x65, 0x6e, 0x63, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x13, 0x72, 0x65, 0x63, 0x65, 0x69,
	0x76, 0x69, 0x6e, 0x67, 0x41, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x33,
	0x0a, 0x15, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72,
	0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x64, 0x18, 0x11, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x65, 0x69,
	0x76, 0x65, 0x64, 0x12, 0x2f, 0x0a, 0x13, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x12, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4b, 0x0a, 0x0d, 0x64, 0x69, 0x73, 0x70, 0x6f, 0x73, 0x61, 0x6c,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x68, 0x65,
	0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x50,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x44, 0x69, 0x73, 0x70, 0x6f, 0x73, 0x61, 0x6c, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6f, 0x73, 0x61, 0x6c, 0x54, 0x79, 0x70,
	0x65, 0x22, 0xfc, 0x06, 0x0a, 0x08, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x15,
	0x0a, 0x06, 0x6f, 0x72, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05,
	0x6f, 0x72, 0x67, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x79, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x49,
	0x0a, 0x0f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x70, 0x65,
	0x72, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0e, 0x70, 0x72, 0x6f, 0x70, 0x65,
	0x72, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x73, 0x5f,
	0x65, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a,
	0x69, 0x73, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x72, 0x65,
	0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x72, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x50,
	0x65, 0x72, 0x69, 0x6f, 0x64, 0x12, 0x4b, 0x0a, 0x0d, 0x64, 0x69, 0x73, 0x70, 0x6f, 0x73, 0x61,
	0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x68,
	0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e,
	0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x44, 0x69, 0x73, 0x70, 0x6f, 0x73, 0x61, 0x6c,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6f, 0x73, 0x61, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x12, 0x2b, 0x0a, 0x11, 0x63, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x74, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x64, 0x69, 0x61, 0x6e, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x10, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x64, 0x69, 0x61, 0x6e, 0x12, 0x29, 0x0a, 0x10, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74,
	0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x43, 0x0a, 0x0d, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x64, 0x79, 0x5f, 0x63, 0x68, 0x61, 0x69,
	0x6e, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x64, 0x79, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x64, 0x79,
	0x43, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x5f,
	0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61,
	0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x5f, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x73, 0x63, 0x68, 0x65,
	0x6d, 0x61, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x31, 0x0a, 0x07, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72,
	0x75, 0x63, 0x74, 0x52, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x1f, 0x0a, 0x0b,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a,
	0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x11, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a,
	0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x12, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x18, 0x0a, 0x07,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x13, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x53, 0x0a, 0x13, 0x6e,
	0x69, 0x62, 0x72, 0x73, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e,
	0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x49, 0x42, 0x52,
	0x53, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x11, 0x6e,
	0x69, 0x62, 0x72, 0x73, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65,
	0x22, 0x2f, 0x0a, 0x09, 0x44, 0x61, 0x74, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x66, 0x72, 0x6f,
	0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x74, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x74,
	0x6f, 0x22, 0x38, 0x0a, 0x0a, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x66, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x22, 0x45, 0x0a, 0x0f, 0x48,
	0x69, 0x67, 0x68, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x14,
	0x0a, 0x05, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x66,
	0x69, 0x65, 0x6c, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x66, 0x72, 0x61, 0x67, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x66, 0x72, 0x61, 0x67, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x22, 0x4f, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x70,
	0x65, 0x72, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x36, 0x0a, 0x08, 0x70,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31,
	0x2e, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x70, 0x65,
	0x72, 0x74, 0x79, 0x22, 0x50, 0x0a, 0x16, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x36, 0x0a,
	0x08, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e,
	0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x08, 0x70, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x79, 0x22, 0x24, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x70,
	0x65, 0x72, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x4d, 0x0a, 0x13, 0x47,
	0x65, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x36, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70,
	0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79,
	0x52, 0x08, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x22, 0x8e, 0x02, 0x0a, 0x15, 0x4c,
	0x69, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a,
	0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x12, 0x53, 0x0a, 0x13, 0x6e, 0x69, 0x62, 0x72, 0x73, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e,
	0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31,
	0x2e, 0x4e, 0x49, 0x42, 0x52, 0x53, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x11, 0x6e, 0x69, 0x62, 0x72, 0x73, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x49, 0x0a, 0x0f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x79, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20,
	0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x0e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x22, 0x7c, 0x0a, 0x16, 0x4c,
	0x69, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3a, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x68, 0x65, 0x72, 0x6f,
	0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65,
	0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74,
	0x50, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x4f, 0x0a, 0x15, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x36, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70,
	0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79,
	0x52, 0x08, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x22, 0x50, 0x0a, 0x16, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x36, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x79, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x22, 0x27, 0x0a, 0x15,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x18, 0x0a, 0x16, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0xc6, 0x03, 0x0a, 0x17, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x71,
	0x75, 0x65, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x71, 0x75, 0x65, 0x72,
	0x79, 0x12, 0x41, 0x0a, 0x0d, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x71, 0x75, 0x65, 0x72, 0x69,
	0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e,
	0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69, 0x65, 0x6c,
	0x64, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x0c, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x51, 0x75, 0x65,
	0x72, 0x69, 0x65, 0x73, 0x12, 0x3a, 0x0a, 0x0a, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x72, 0x61, 0x6e,
	0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e,
	0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x74, 0x65,
	0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x09, 0x64, 0x61, 0x74, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65,
	0x12, 0x53, 0x0a, 0x13, 0x6e, 0x69, 0x62, 0x72, 0x73, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e,
	0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31,
	0x2e, 0x4e, 0x49, 0x42, 0x52, 0x53, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x11, 0x6e, 0x69, 0x62, 0x72, 0x73, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x49, 0x0a, 0x0f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x79, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20,
	0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x0e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x3a, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x42, 0x79, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x12, 0x1b, 0x0a, 0x09,
	0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67,
	0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70,
	0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0xe2, 0x01, 0x0a, 0x18, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3a, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x68, 0x65, 0x72, 0x6f,
	0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65,
	0x73, 0x12, 0x41, 0x0a, 0x0a, 0x68, 0x69, 0x67, 0x68, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x48, 0x69, 0x67, 0x68, 0x6c, 0x69, 0x67,
	0x68, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x0a, 0x68, 0x69, 0x67, 0x68, 0x6c, 0x69,
	0x67, 0x68, 0x74, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x67,
	0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6e,
	0x65, 0x78, 0x74, 0x50, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1f, 0x0a, 0x0b,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x2d, 0x0a,
	0x19, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0x58, 0x0a, 0x1a,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69,
	0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3a, 0x0a, 0x0a, 0x70, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x70,
	0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x22, 0x7e, 0x0a, 0x16, 0x41, 0x64, 0x64, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x64, 0x79, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x49,
	0x64, 0x12, 0x43, 0x0a, 0x0d, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x64, 0x79, 0x5f, 0x65, 0x76, 0x65,
	0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e,
	0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x64, 0x79, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x64,
	0x79, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x22, 0x19, 0x0a, 0x17, 0x41, 0x64, 0x64, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x64, 0x79, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x39, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x64, 0x79, 0x43,
	0x68, 0x61, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x70,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x49, 0x64, 0x22, 0x5e, 0x0a, 0x17,
	0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x64, 0x79, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x43, 0x0a, 0x0d, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x64, 0x79, 0x5f, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e,
	0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x64, 0x79, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x0c,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x64, 0x79, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x22, 0xa6, 0x01, 0x0a,
	0x22, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x46, 0x69, 0x6c,
	0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x79, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x66, 0x69, 0x6c,
	0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67,
	0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61,
	0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0xa1, 0x01, 0x0a, 0x23, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x52, 0x0a,
	0x10, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x70, 0x65,
	0x72, 0x74, 0x79, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x52, 0x0f, 0x66, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74,
	0x50, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x9d, 0x02, 0x0a, 0x15, 0x50, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x79, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x18, 0x0a,
	0x07, 0x63, 0x61, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x63, 0x61, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64,
	0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x64, 0x69,
	0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12,
	0x23, 0x0a, 0x0d, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x66, 0x69, 0x6c, 0x65, 0x43, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x12, 0x33, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52,
	0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x22, 0x95, 0x01, 0x0a, 0x20, 0x41, 0x64,
	0x64, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74,
	0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f,
	0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x49, 0x64, 0x12,
	0x50, 0x0a, 0x0f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65,
	0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e,
	0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x70,
	0x65, 0x72, 0x74, 0x79, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x52, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e,
	0x74, 0x22, 0x75, 0x0a, 0x21, 0x41, 0x64, 0x64, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79,
	0x46, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x50, 0x0a, 0x0f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x61,
	0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x27, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e,
	0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x46, 0x69, 0x6c, 0x65, 0x52,
	0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x41, 0x74,
	0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x6b, 0x0a, 0x23, 0x52, 0x65, 0x6d, 0x6f,
	0x76, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x74,
	0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x1f, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x49, 0x64,
	0x12, 0x23, 0x0a, 0x0d, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d,
	0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x26, 0x0a, 0x24, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x50,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63,
	0x68, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x85, 0x01,
	0x0a, 0x11, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a,
	0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x2a, 0x9e, 0x02, 0x0a, 0x11, 0x4e, 0x49, 0x42, 0x52, 0x53, 0x50,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x19, 0x50,
	0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x16, 0x0a, 0x12, 0x50, 0x52,
	0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4e, 0x4f, 0x4e, 0x45,
	0x10, 0x01, 0x12, 0x18, 0x0a, 0x14, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x42, 0x55, 0x52, 0x4e, 0x45, 0x44, 0x10, 0x02, 0x12, 0x18, 0x0a, 0x14,
	0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x46, 0x4f,
	0x52, 0x47, 0x45, 0x44, 0x10, 0x03, 0x12, 0x19, 0x0a, 0x15, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52,
	0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x41, 0x4d, 0x41, 0x47, 0x45, 0x44, 0x10,
	0x04, 0x12, 0x1b, 0x0a, 0x17, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x52, 0x45, 0x43, 0x4f, 0x56, 0x45, 0x52, 0x45, 0x44, 0x10, 0x05, 0x12, 0x18,
	0x0a, 0x14, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x53, 0x45, 0x49, 0x5a, 0x45, 0x44, 0x10, 0x06, 0x12, 0x18, 0x0a, 0x14, 0x50, 0x52, 0x4f, 0x50,
	0x45, 0x52, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x54, 0x4f, 0x4c, 0x45, 0x4e,
	0x10, 0x07, 0x12, 0x19, 0x0a, 0x15, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x08, 0x12, 0x17, 0x0a,
	0x13, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x46,
	0x4f, 0x55, 0x4e, 0x44, 0x10, 0x09, 0x2a, 0x91, 0x03, 0x0a, 0x0e, 0x50, 0x72, 0x6f, 0x70, 0x65,
	0x72, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a, 0x1b, 0x50, 0x52, 0x4f,
	0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x22, 0x0a, 0x1e, 0x50, 0x52,
	0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e,
	0x54, 0x41, 0x4b, 0x45, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x1d,
	0x0a, 0x19, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x43, 0x4f, 0x4c, 0x4c, 0x45, 0x43, 0x54, 0x45, 0x44, 0x10, 0x02, 0x12, 0x1e, 0x0a,
	0x1a, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x45, 0x44, 0x5f, 0x49, 0x4e, 0x10, 0x03, 0x12, 0x1f, 0x0a,
	0x1b, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x45, 0x44, 0x5f, 0x4f, 0x55, 0x54, 0x10, 0x04, 0x12, 0x1d,
	0x0a, 0x19, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x52, 0x45, 0x43, 0x4f, 0x56, 0x45, 0x52, 0x45, 0x44, 0x10, 0x05, 0x12, 0x19, 0x0a,
	0x15, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x06, 0x12, 0x1f, 0x0a, 0x1b, 0x50, 0x52, 0x4f, 0x50,
	0x45, 0x52, 0x54, 0x59, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x41, 0x46, 0x45,
	0x4b, 0x45, 0x45, 0x50, 0x49, 0x4e, 0x47, 0x10, 0x07, 0x12, 0x28, 0x0a, 0x24, 0x50, 0x52, 0x4f,
	0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x57, 0x41,
	0x49, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x49, 0x4f,
	0x4e, 0x10, 0x08, 0x12, 0x1c, 0x0a, 0x18, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x4f, 0x53, 0x45, 0x44, 0x10,
	0x09, 0x12, 0x1b, 0x0a, 0x17, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4e, 0x47, 0x10, 0x0a, 0x12, 0x1a,
	0x0a, 0x16, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x53, 0x54, 0x4f, 0x4c, 0x45, 0x4e, 0x10, 0x0b, 0x2a, 0x81, 0x02, 0x0a, 0x14, 0x50,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x44, 0x69, 0x73, 0x70, 0x6f, 0x73, 0x61, 0x6c, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x26, 0x0a, 0x22, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f,
	0x44, 0x49, 0x53, 0x50, 0x4f, 0x53, 0x41, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x23, 0x0a, 0x1f, 0x50,
	0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x4f, 0x53, 0x41, 0x4c,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x45, 0x4c, 0x45, 0x41, 0x53, 0x45, 0x44, 0x10, 0x01,
	0x12, 0x24, 0x0a, 0x20, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x44, 0x49, 0x53,
	0x50, 0x4f, 0x53, 0x41, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x45, 0x53, 0x54, 0x52,
	0x4f, 0x59, 0x45, 0x44, 0x10, 0x02, 0x12, 0x24, 0x0a, 0x20, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52,
	0x54, 0x59, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x4f, 0x53, 0x41, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x41, 0x55, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x45, 0x44, 0x10, 0x03, 0x12, 0x28, 0x0a, 0x24,
	0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x4f, 0x53, 0x41,
	0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x47, 0x45, 0x4e, 0x43, 0x59, 0x5f, 0x52, 0x45,
	0x54, 0x41, 0x49, 0x4e, 0x10, 0x04, 0x12, 0x26, 0x0a, 0x22, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52,
	0x54, 0x59, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x4f, 0x53, 0x41, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x46, 0x45, 0x52, 0x52, 0x45, 0x44, 0x10, 0x05, 0x2a, 0xcc,
	0x02, 0x0a, 0x11, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x64, 0x79, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x23, 0x0a, 0x1f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x44, 0x59, 0x5f,
	0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x21, 0x0a, 0x1d, 0x43, 0x55, 0x53,
	0x54, 0x4f, 0x44, 0x59, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x43, 0x4f, 0x4c, 0x4c, 0x45, 0x43, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x22, 0x0a, 0x1e,
	0x43, 0x55, 0x53, 0x54, 0x4f, 0x44, 0x59, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x45, 0x44, 0x5f, 0x49, 0x4e, 0x10, 0x02,
	0x12, 0x23, 0x0a, 0x1f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x44, 0x59, 0x5f, 0x41, 0x43, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x45, 0x44, 0x5f,
	0x4f, 0x55, 0x54, 0x10, 0x03, 0x12, 0x23, 0x0a, 0x1f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x44, 0x59,
	0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x52, 0x41,
	0x4e, 0x53, 0x46, 0x45, 0x52, 0x52, 0x45, 0x44, 0x10, 0x04, 0x12, 0x20, 0x0a, 0x1c, 0x43, 0x55,
	0x53, 0x54, 0x4f, 0x44, 0x59, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x52, 0x45, 0x4c, 0x45, 0x41, 0x53, 0x45, 0x44, 0x10, 0x05, 0x12, 0x20, 0x0a, 0x1c,
	0x43, 0x55, 0x53, 0x54, 0x4f, 0x44, 0x59, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x4f, 0x53, 0x45, 0x44, 0x10, 0x06, 0x12, 0x1e,
	0x0a, 0x1a, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x44, 0x59, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4c, 0x4f, 0x47, 0x47, 0x45, 0x44, 0x10, 0x07, 0x12, 0x1d,
	0x0a, 0x19, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x44, 0x59, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d, 0x4f, 0x56, 0x45, 0x44, 0x10, 0x08, 0x2a, 0xa0, 0x02,
	0x0a, 0x11, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x64, 0x79, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x23, 0x0a, 0x1f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x44, 0x59, 0x5f, 0x45,
	0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1e, 0x0a, 0x1a, 0x43, 0x55, 0x53, 0x54,
	0x4f, 0x44, 0x59, 0x5f, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x50, 0x45, 0x52, 0x53, 0x4f, 0x4e, 0x10, 0x01, 0x12, 0x21, 0x0a, 0x1d, 0x43, 0x55, 0x53, 0x54,
	0x4f, 0x44, 0x59, 0x5f, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x43, 0x52, 0x49, 0x4d, 0x45, 0x5f, 0x4c, 0x41, 0x42, 0x10, 0x02, 0x12, 0x1d, 0x0a, 0x19, 0x43,
	0x55, 0x53, 0x54, 0x4f, 0x44, 0x59, 0x5f, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x43, 0x4f, 0x55, 0x52, 0x54, 0x10, 0x03, 0x12, 0x22, 0x0a, 0x1e, 0x43, 0x55,
	0x53, 0x54, 0x4f, 0x44, 0x59, 0x5f, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x49, 0x41, 0x4c, 0x49, 0x53, 0x54, 0x10, 0x04, 0x12, 0x1b,
	0x0a, 0x17, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x44, 0x59, 0x5f, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x53, 0x49, 0x10, 0x05, 0x12, 0x24, 0x0a, 0x20, 0x43,
	0x55, 0x53, 0x54, 0x4f, 0x44, 0x59, 0x5f, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x4f, 0x52, 0x47, 0x41, 0x4e, 0x49, 0x5a, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10,
	0x06, 0x12, 0x1d, 0x0a, 0x19, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x44, 0x59, 0x5f, 0x45, 0x4e, 0x54,
	0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x10, 0x07,
	0x2a, 0x73, 0x0a, 0x11, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x64, 0x79, 0x41, 0x67, 0x65, 0x6e, 0x63,
	0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x23, 0x0a, 0x1f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x44, 0x59,
	0x5f, 0x41, 0x47, 0x45, 0x4e, 0x43, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1a, 0x0a, 0x16, 0x43, 0x55,
	0x53, 0x54, 0x4f, 0x44, 0x59, 0x5f, 0x41, 0x47, 0x45, 0x4e, 0x43, 0x59, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x50, 0x44, 0x10, 0x01, 0x12, 0x1d, 0x0a, 0x19, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x44,
	0x59, 0x5f, 0x41, 0x47, 0x45, 0x4e, 0x43, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4f, 0x54,
	0x48, 0x45, 0x52, 0x10, 0x02, 0x2a, 0xff, 0x1b, 0x0a, 0x18, 0x4e, 0x49, 0x42, 0x52, 0x53, 0x50,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x2a, 0x0a, 0x26, 0x4e, 0x49, 0x42, 0x52, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x50,
	0x45, 0x52, 0x54, 0x59, 0x5f, 0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x27,
	0x0a, 0x23, 0x4e, 0x49, 0x42, 0x52, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59,
	0x5f, 0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x49, 0x52,
	0x43, 0x52, 0x41, 0x46, 0x54, 0x10, 0x01, 0x12, 0x26, 0x0a, 0x22, 0x4e, 0x49, 0x42, 0x52, 0x53,
	0x5f, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x44, 0x45, 0x53, 0x43, 0x52, 0x49,
	0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x4c, 0x43, 0x4f, 0x48, 0x4f, 0x4c, 0x10, 0x02, 0x12,
	0x2a, 0x0a, 0x26, 0x4e, 0x49, 0x42, 0x52, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54,
	0x59, 0x5f, 0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x55,
	0x54, 0x4f, 0x4d, 0x4f, 0x42, 0x49, 0x4c, 0x45, 0x53, 0x10, 0x03, 0x12, 0x27, 0x0a, 0x23, 0x4e,
	0x49, 0x42, 0x52, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x44, 0x45,
	0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x42, 0x49, 0x43, 0x59, 0x43, 0x4c,
	0x45, 0x53, 0x10, 0x04, 0x12, 0x24, 0x0a, 0x20, 0x4e, 0x49, 0x42, 0x52, 0x53, 0x5f, 0x50, 0x52,
	0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x42, 0x55, 0x53, 0x45, 0x53, 0x10, 0x05, 0x12, 0x2b, 0x0a, 0x27, 0x4e, 0x49,
	0x42, 0x52, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x44, 0x45, 0x53,
	0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x4c, 0x4f, 0x54, 0x48, 0x45, 0x53,
	0x5f, 0x46, 0x55, 0x52, 0x53, 0x10, 0x06, 0x12, 0x39, 0x0a, 0x35, 0x4e, 0x49, 0x42, 0x52, 0x53,
	0x5f, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x44, 0x45, 0x53, 0x43, 0x52, 0x49,
	0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x55, 0x54, 0x45, 0x52, 0x5f, 0x48,
	0x41, 0x52, 0x44, 0x57, 0x41, 0x52, 0x45, 0x5f, 0x53, 0x4f, 0x46, 0x54, 0x57, 0x41, 0x52, 0x45,
	0x10, 0x07, 0x12, 0x2f, 0x0a, 0x2b, 0x4e, 0x49, 0x42, 0x52, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x50,
	0x45, 0x52, 0x54, 0x59, 0x5f, 0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x55, 0x4d, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x47, 0x4f, 0x4f, 0x44,
	0x53, 0x10, 0x08, 0x12, 0x31, 0x0a, 0x2d, 0x4e, 0x49, 0x42, 0x52, 0x53, 0x5f, 0x50, 0x52, 0x4f,
	0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x43,
	0x41, 0x52, 0x44, 0x53, 0x10, 0x09, 0x12, 0x2e, 0x0a, 0x2a, 0x4e, 0x49, 0x42, 0x52, 0x53, 0x5f,
	0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x52, 0x55, 0x47, 0x53, 0x5f, 0x4e, 0x41, 0x52, 0x43, 0x4f,
	0x54, 0x49, 0x43, 0x53, 0x10, 0x0a, 0x12, 0x36, 0x0a, 0x32, 0x4e, 0x49, 0x42, 0x52, 0x53, 0x5f,
	0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x52, 0x55, 0x47, 0x5f, 0x4e, 0x41, 0x52, 0x43, 0x4f, 0x54,
	0x49, 0x43, 0x5f, 0x45, 0x51, 0x55, 0x49, 0x50, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x0b, 0x12, 0x2d,
	0x0a, 0x29, 0x4e, 0x49, 0x42, 0x52, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59,
	0x5f, 0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x41, 0x52,
	0x4d, 0x5f, 0x45, 0x51, 0x55, 0x49, 0x50, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x0c, 0x12, 0x27, 0x0a,
	0x23, 0x4e, 0x49, 0x42, 0x52, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f,
	0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x52, 0x45,
	0x41, 0x52, 0x4d, 0x53, 0x10, 0x0d, 0x12, 0x31, 0x0a, 0x2d, 0x4e, 0x49, 0x42, 0x52, 0x53, 0x5f,
	0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x47, 0x41, 0x4d, 0x42, 0x4c, 0x49, 0x4e, 0x47, 0x5f, 0x45, 0x51,
	0x55, 0x49, 0x50, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x0e, 0x12, 0x46, 0x0a, 0x42, 0x4e, 0x49, 0x42,
	0x52, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x44, 0x45, 0x53, 0x43,
	0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x48, 0x45, 0x41, 0x56, 0x59, 0x5f, 0x43, 0x4f,
	0x4e, 0x53, 0x54, 0x52, 0x55, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x49, 0x4e, 0x44, 0x55, 0x53,
	0x54, 0x52, 0x49, 0x41, 0x4c, 0x5f, 0x45, 0x51, 0x55, 0x49, 0x50, 0x4d, 0x45, 0x4e, 0x54, 0x10,
	0x0f, 0x12, 0x2e, 0x0a, 0x2a, 0x4e, 0x49, 0x42, 0x52, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x50, 0x45,
	0x52, 0x54, 0x59, 0x5f, 0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x48, 0x4f, 0x55, 0x53, 0x45, 0x48, 0x4f, 0x4c, 0x44, 0x5f, 0x47, 0x4f, 0x4f, 0x44, 0x53, 0x10,
	0x10, 0x12, 0x3b, 0x0a, 0x37, 0x4e, 0x49, 0x42, 0x52, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x50, 0x45,
	0x52, 0x54, 0x59, 0x5f, 0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x4a, 0x45, 0x57, 0x45, 0x4c, 0x52, 0x59, 0x5f, 0x50, 0x52, 0x45, 0x43, 0x49, 0x4f, 0x55, 0x53,
	0x5f, 0x4d, 0x45, 0x54, 0x41, 0x4c, 0x53, 0x5f, 0x47, 0x45, 0x4d, 0x53, 0x10, 0x11, 0x12, 0x28,
	0x0a, 0x24, 0x4e, 0x49, 0x42, 0x52, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59,
	0x5f, 0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4c, 0x49, 0x56,
	0x45, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x10, 0x12, 0x12, 0x2a, 0x0a, 0x26, 0x4e, 0x49, 0x42, 0x52,
	0x53, 0x5f, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x44, 0x45, 0x53, 0x43, 0x52,
	0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4d, 0x45, 0x52, 0x43, 0x48, 0x41, 0x4e, 0x44, 0x49,
	0x53, 0x45, 0x10, 0x13, 0x12, 0x24, 0x0a, 0x20, 0x4e, 0x49, 0x42, 0x52, 0x53, 0x5f, 0x50, 0x52,
	0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x4d, 0x4f, 0x4e, 0x45, 0x59, 0x10, 0x14, 0x12, 0x35, 0x0a, 0x31, 0x4e, 0x49,
	0x42, 0x52, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x44, 0x45, 0x53,
	0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4e, 0x45, 0x47, 0x4f, 0x54, 0x49, 0x41,
	0x42, 0x4c, 0x45, 0x5f, 0x49, 0x4e, 0x53, 0x54, 0x52, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x53, 0x10,
	0x15, 0x12, 0x38, 0x0a, 0x34, 0x4e, 0x49, 0x42, 0x52, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x50, 0x45,
	0x52, 0x54, 0x59, 0x5f, 0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x4e, 0x4f, 0x4e, 0x4e, 0x45, 0x47, 0x4f, 0x54, 0x49, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x49, 0x4e,
	0x53, 0x54, 0x52, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x53, 0x10, 0x16, 0x12, 0x34, 0x0a, 0x30, 0x4e,
	0x49, 0x42, 0x52, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x44, 0x45,
	0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x49, 0x43, 0x45,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x45, 0x51, 0x55, 0x49, 0x50, 0x4d, 0x45, 0x4e, 0x54, 0x10,
	0x17, 0x12, 0x33, 0x0a, 0x2f, 0x4e, 0x49, 0x42, 0x52, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x50, 0x45,
	0x52, 0x54, 0x59, 0x5f, 0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x4f, 0x54, 0x48, 0x45, 0x52, 0x5f, 0x4d, 0x4f, 0x54, 0x4f, 0x52, 0x5f, 0x56, 0x45, 0x48, 0x49,
	0x43, 0x4c, 0x45, 0x53, 0x10, 0x18, 0x12, 0x36, 0x0a, 0x32, 0x4e, 0x49, 0x42, 0x52, 0x53, 0x5f,
	0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x55, 0x52, 0x53, 0x45, 0x53, 0x5f, 0x48, 0x41, 0x4e, 0x44,
	0x42, 0x41, 0x47, 0x53, 0x5f, 0x57, 0x41, 0x4c, 0x4c, 0x45, 0x54, 0x53, 0x10, 0x19, 0x12, 0x2e,
	0x0a, 0x2a, 0x4e, 0x49, 0x42, 0x52, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59,
	0x5f, 0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x41, 0x44,
	0x49, 0x4f, 0x53, 0x5f, 0x54, 0x56, 0x53, 0x5f, 0x56, 0x43, 0x52, 0x53, 0x10, 0x1a, 0x12, 0x36,
	0x0a, 0x32, 0x4e, 0x49, 0x42, 0x52, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59,
	0x5f, 0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x43,
	0x4f, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x53, 0x5f, 0x41, 0x55, 0x44, 0x49, 0x4f, 0x5f, 0x56, 0x49,
	0x53, 0x55, 0x41, 0x4c, 0x10, 0x1b, 0x12, 0x34, 0x0a, 0x30, 0x4e, 0x49, 0x42, 0x52, 0x53, 0x5f,
	0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x41,
	0x4c, 0x5f, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x53, 0x10, 0x1c, 0x12, 0x44, 0x0a, 0x40,
	0x4e, 0x49, 0x42, 0x52, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x44,
	0x45, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x52, 0x55, 0x43,
	0x54, 0x55, 0x52, 0x45, 0x53, 0x5f, 0x53, 0x49, 0x4e, 0x47, 0x4c, 0x45, 0x5f, 0x4f, 0x43, 0x43,
	0x55, 0x50, 0x41, 0x4e, 0x43, 0x59, 0x5f, 0x44, 0x57, 0x45, 0x4c, 0x4c, 0x49, 0x4e, 0x47, 0x53,
	0x10, 0x1d, 0x12, 0x39, 0x0a, 0x35, 0x4e, 0x49, 0x42, 0x52, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x50,
	0x45, 0x52, 0x54, 0x59, 0x5f, 0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x53, 0x54, 0x52, 0x55, 0x43, 0x54, 0x55, 0x52, 0x45, 0x53, 0x5f, 0x4f, 0x54, 0x48, 0x45,
	0x52, 0x5f, 0x44, 0x57, 0x45, 0x4c, 0x4c, 0x49, 0x4e, 0x47, 0x53, 0x10, 0x1e, 0x12, 0x43, 0x0a,
	0x3f, 0x4e, 0x49, 0x42, 0x52, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f,
	0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x52, 0x55,
	0x43, 0x54, 0x55, 0x52, 0x45, 0x53, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x5f, 0x43, 0x4f, 0x4d,
	0x4d, 0x45, 0x52, 0x43, 0x49, 0x41, 0x4c, 0x5f, 0x42, 0x55, 0x53, 0x49, 0x4e, 0x45, 0x53, 0x53,
	0x10, 0x1f, 0x12, 0x42, 0x0a, 0x3e, 0x4e, 0x49, 0x42, 0x52, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x50,
	0x45, 0x52, 0x54, 0x59, 0x5f, 0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x53, 0x54, 0x52, 0x55, 0x43, 0x54, 0x55, 0x52, 0x45, 0x53, 0x5f, 0x49, 0x4e, 0x44, 0x55,
	0x53, 0x54, 0x52, 0x49, 0x41, 0x4c, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x46, 0x41, 0x43, 0x54, 0x55,
	0x52, 0x49, 0x4e, 0x47, 0x10, 0x20, 0x12, 0x3a, 0x0a, 0x36, 0x4e, 0x49, 0x42, 0x52, 0x53, 0x5f,
	0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x52, 0x55, 0x43, 0x54, 0x55, 0x52, 0x45, 0x53, 0x5f,
	0x50, 0x55, 0x42, 0x4c, 0x49, 0x43, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x55, 0x4e, 0x49, 0x54, 0x59,
	0x10, 0x21, 0x12, 0x31, 0x0a, 0x2d, 0x4e, 0x49, 0x42, 0x52, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x50,
	0x45, 0x52, 0x54, 0x59, 0x5f, 0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x53, 0x54, 0x52, 0x55, 0x43, 0x54, 0x55, 0x52, 0x45, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x52,
	0x41, 0x47, 0x45, 0x10, 0x22, 0x12, 0x2f, 0x0a, 0x2b, 0x4e, 0x49, 0x42, 0x52, 0x53, 0x5f, 0x50,
	0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x52, 0x55, 0x43, 0x54, 0x55, 0x52, 0x45, 0x53, 0x5f, 0x4f,
	0x54, 0x48, 0x45, 0x52, 0x10, 0x23, 0x12, 0x24, 0x0a, 0x20, 0x4e, 0x49, 0x42, 0x52, 0x53, 0x5f,
	0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x4f, 0x4f, 0x4c, 0x53, 0x10, 0x24, 0x12, 0x25, 0x0a, 0x21,
	0x4e, 0x49, 0x42, 0x52, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x44,
	0x45, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x52, 0x55, 0x43, 0x4b,
	0x53, 0x10, 0x25, 0x12, 0x38, 0x0a, 0x34, 0x4e, 0x49, 0x42, 0x52, 0x53, 0x5f, 0x50, 0x52, 0x4f,
	0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x50, 0x41, 0x52, 0x54, 0x53, 0x5f,
	0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x4f, 0x52, 0x49, 0x45, 0x53, 0x10, 0x26, 0x12, 0x29, 0x0a,
	0x25, 0x4e, 0x49, 0x42, 0x52, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f,
	0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x57, 0x41, 0x54, 0x45,
	0x52, 0x43, 0x52, 0x41, 0x46, 0x54, 0x10, 0x27, 0x12, 0x39, 0x0a, 0x35, 0x4e, 0x49, 0x42, 0x52,
	0x53, 0x5f, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x44, 0x45, 0x53, 0x43, 0x52,
	0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x49, 0x52, 0x43, 0x52, 0x41, 0x46, 0x54, 0x5f,
	0x50, 0x41, 0x52, 0x54, 0x53, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x4f, 0x52, 0x49, 0x45,
	0x53, 0x10, 0x29, 0x12, 0x3c, 0x0a, 0x38, 0x4e, 0x49, 0x42, 0x52, 0x53, 0x5f, 0x50, 0x52, 0x4f,
	0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x41, 0x52, 0x54, 0x49, 0x53, 0x54, 0x49, 0x43, 0x5f, 0x53, 0x55, 0x50, 0x50, 0x4c,
	0x49, 0x45, 0x53, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x4f, 0x52, 0x49, 0x45, 0x53, 0x10,
	0x2a, 0x12, 0x31, 0x0a, 0x2d, 0x4e, 0x49, 0x42, 0x52, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x50, 0x45,
	0x52, 0x54, 0x59, 0x5f, 0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x42, 0x55, 0x49, 0x4c, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x4d, 0x41, 0x54, 0x45, 0x52, 0x49, 0x41,
	0x4c, 0x53, 0x10, 0x2b, 0x12, 0x40, 0x0a, 0x3c, 0x4e, 0x49, 0x42, 0x52, 0x53, 0x5f, 0x50, 0x52,
	0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x43, 0x41, 0x4d, 0x50, 0x49, 0x4e, 0x47, 0x5f, 0x48, 0x55, 0x4e, 0x54, 0x49,
	0x4e, 0x47, 0x5f, 0x46, 0x49, 0x53, 0x48, 0x49, 0x4e, 0x47, 0x5f, 0x45, 0x51, 0x55, 0x49, 0x50,
	0x4d, 0x45, 0x4e, 0x54, 0x10, 0x2c, 0x12, 0x28, 0x0a, 0x24, 0x4e, 0x49, 0x42, 0x52, 0x53, 0x5f,
	0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x48, 0x45, 0x4d, 0x49, 0x43, 0x41, 0x4c, 0x53, 0x10, 0x2d,
	0x12, 0x37, 0x0a, 0x33, 0x4e, 0x49, 0x42, 0x52, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52,
	0x54, 0x59, 0x5f, 0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43,
	0x4f, 0x4c, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x5f, 0x43, 0x4f, 0x4c, 0x4c, 0x45,
	0x43, 0x54, 0x49, 0x42, 0x4c, 0x45, 0x53, 0x10, 0x2e, 0x12, 0x24, 0x0a, 0x20, 0x4e, 0x49, 0x42,
	0x52, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x44, 0x45, 0x53, 0x43,
	0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x52, 0x4f, 0x50, 0x53, 0x10, 0x2f, 0x12,
	0x3d, 0x0a, 0x39, 0x4e, 0x49, 0x42, 0x52, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54,
	0x59, 0x5f, 0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x4f,
	0x43, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x53, 0x5f, 0x50, 0x45, 0x52, 0x53, 0x4f, 0x4e, 0x41, 0x4c,
	0x5f, 0x4f, 0x52, 0x5f, 0x42, 0x55, 0x53, 0x49, 0x4e, 0x45, 0x53, 0x53, 0x10, 0x30, 0x12, 0x29,
	0x0a, 0x25, 0x4e, 0x49, 0x42, 0x52, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59,
	0x5f, 0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x45, 0x58, 0x50,
	0x4c, 0x4f, 0x53, 0x49, 0x56, 0x45, 0x53, 0x10, 0x31, 0x12, 0x32, 0x0a, 0x2e, 0x4e, 0x49, 0x42,
	0x52, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x44, 0x45, 0x53, 0x43,
	0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x52, 0x45, 0x41, 0x52, 0x4d, 0x5f,
	0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x4f, 0x52, 0x49, 0x45, 0x53, 0x10, 0x3b, 0x12, 0x23, 0x0a,
	0x1f, 0x4e, 0x49, 0x42, 0x52, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f,
	0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x55, 0x45, 0x4c,
	0x10, 0x40, 0x12, 0x31, 0x0a, 0x2d, 0x4e, 0x49, 0x42, 0x52, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x50,
	0x45, 0x52, 0x54, 0x59, 0x5f, 0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x44, 0x4f, 0x43, 0x55, 0x4d, 0x45,
	0x4e, 0x54, 0x53, 0x10, 0x41, 0x12, 0x32, 0x0a, 0x2e, 0x4e, 0x49, 0x42, 0x52, 0x53, 0x5f, 0x50,
	0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x49, 0x4e, 0x54,
	0x41, 0x4e, 0x47, 0x49, 0x42, 0x4c, 0x45, 0x10, 0x42, 0x12, 0x38, 0x0a, 0x34, 0x4e, 0x49, 0x42,
	0x52, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x44, 0x45, 0x53, 0x43,
	0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4c, 0x41, 0x57, 0x5f, 0x45, 0x4e, 0x46, 0x4f,
	0x52, 0x43, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x45, 0x51, 0x55, 0x49, 0x50, 0x4d, 0x45, 0x4e,
	0x54, 0x10, 0x43, 0x12, 0x39, 0x0a, 0x35, 0x4e, 0x49, 0x42, 0x52, 0x53, 0x5f, 0x50, 0x52, 0x4f,
	0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x4c, 0x41, 0x57, 0x4e, 0x5f, 0x59, 0x41, 0x52, 0x44, 0x5f, 0x47, 0x41, 0x52, 0x44,
	0x45, 0x4e, 0x5f, 0x45, 0x51, 0x55, 0x49, 0x50, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x44, 0x12, 0x30,
	0x0a, 0x2c, 0x4e, 0x49, 0x42, 0x52, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59,
	0x5f, 0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4c, 0x4f, 0x47,
	0x47, 0x49, 0x4e, 0x47, 0x5f, 0x45, 0x51, 0x55, 0x49, 0x50, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x45,
	0x12, 0x3c, 0x0a, 0x38, 0x4e, 0x49, 0x42, 0x52, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52,
	0x54, 0x59, 0x5f, 0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4d,
	0x45, 0x44, 0x49, 0x43, 0x41, 0x4c, 0x5f, 0x4d, 0x45, 0x44, 0x49, 0x43, 0x41, 0x4c, 0x5f, 0x4c,
	0x41, 0x42, 0x5f, 0x45, 0x51, 0x55, 0x49, 0x50, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x46, 0x12, 0x32,
	0x0a, 0x2e, 0x4e, 0x49, 0x42, 0x52, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59,
	0x5f, 0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4d, 0x45, 0x54,
	0x41, 0x4c, 0x53, 0x5f, 0x4e, 0x4f, 0x4e, 0x5f, 0x50, 0x52, 0x45, 0x43, 0x49, 0x4f, 0x55, 0x53,
	0x10, 0x47, 0x12, 0x32, 0x0a, 0x2e, 0x4e, 0x49, 0x42, 0x52, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x50,
	0x45, 0x52, 0x54, 0x59, 0x5f, 0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x4d, 0x55, 0x53, 0x49, 0x43, 0x41, 0x4c, 0x5f, 0x49, 0x4e, 0x53, 0x54, 0x52, 0x55, 0x4d,
	0x45, 0x4e, 0x54, 0x53, 0x10, 0x48, 0x12, 0x23, 0x0a, 0x1f, 0x4e, 0x49, 0x42, 0x52, 0x53, 0x5f,
	0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x45, 0x54, 0x53, 0x10, 0x49, 0x12, 0x3d, 0x0a, 0x39, 0x4e,
	0x49, 0x42, 0x52, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x44, 0x45,
	0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x48, 0x4f, 0x54, 0x4f, 0x47,
	0x52, 0x41, 0x50, 0x48, 0x49, 0x43, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x43, 0x41, 0x4c, 0x5f, 0x45,
	0x51, 0x55, 0x49, 0x50, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x4a, 0x12, 0x41, 0x0a, 0x3d, 0x4e, 0x49,
	0x42, 0x52, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x44, 0x45, 0x53,
	0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x4f, 0x52, 0x54, 0x41, 0x42, 0x4c,
	0x45, 0x5f, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x52, 0x4f, 0x4e, 0x49, 0x43, 0x5f, 0x43, 0x4f, 0x4d,
	0x4d, 0x55, 0x4e, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0x4b, 0x12, 0x3c, 0x0a,
	0x38, 0x4e, 0x49, 0x42, 0x52, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f,
	0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x43, 0x52,
	0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x53, 0x50, 0x4f, 0x52, 0x54, 0x53, 0x5f,
	0x45, 0x51, 0x55, 0x49, 0x50, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x4c, 0x12, 0x24, 0x0a, 0x20, 0x4e,
	0x49, 0x42, 0x52, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x44, 0x45,
	0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x10,
	0x4d, 0x12, 0x27, 0x0a, 0x23, 0x4e, 0x49, 0x42, 0x52, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x50, 0x45,
	0x52, 0x54, 0x59, 0x5f, 0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x54, 0x52, 0x41, 0x49, 0x4c, 0x45, 0x52, 0x53, 0x10, 0x4e, 0x12, 0x45, 0x0a, 0x41, 0x4e, 0x49,
	0x42, 0x52, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x5f, 0x44, 0x45, 0x53,
	0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x57, 0x41, 0x54, 0x45, 0x52, 0x43, 0x52,
	0x41, 0x46, 0x54, 0x5f, 0x45, 0x51, 0x55, 0x49, 0x50, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x50, 0x41,
	0x52, 0x54, 0x53, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x4f, 0x52, 0x49, 0x45, 0x53, 0x10,
	0x4f, 0x12, 0x2c, 0x0a, 0x28, 0x4e, 0x49, 0x42, 0x52, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x50, 0x45,
	0x52, 0x54, 0x59, 0x5f, 0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x57, 0x45, 0x41, 0x50, 0x4f, 0x4e, 0x53, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x10, 0x50, 0x12,
	0x30, 0x0a, 0x2c, 0x4e, 0x49, 0x42, 0x52, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54,
	0x59, 0x5f, 0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x45,
	0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x4e, 0x54, 0x4f, 0x52, 0x59, 0x10,
	0x58, 0x12, 0x24, 0x0a, 0x20, 0x4e, 0x49, 0x42, 0x52, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x50, 0x45,
	0x52, 0x54, 0x59, 0x5f, 0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x42, 0x4c, 0x41, 0x4e, 0x4b, 0x10, 0x63, 0x2a, 0xab, 0x01, 0x0a, 0x0d, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x12, 0x1f, 0x0a, 0x1b, 0x53, 0x45, 0x41,
	0x52, 0x43, 0x48, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x42, 0x59, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1d, 0x0a, 0x19, 0x53, 0x45,
	0x41, 0x52, 0x43, 0x48, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x42, 0x59, 0x5f, 0x52, 0x45,
	0x4c, 0x45, 0x56, 0x41, 0x4e, 0x43, 0x45, 0x10, 0x01, 0x12, 0x1e, 0x0a, 0x1a, 0x53, 0x45, 0x41,
	0x52, 0x43, 0x48, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x42, 0x59, 0x5f, 0x43, 0x52, 0x45,
	0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x02, 0x12, 0x1e, 0x0a, 0x1a, 0x53, 0x45, 0x41,
	0x52, 0x43, 0x48, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x42, 0x59, 0x5f, 0x55, 0x50, 0x44,
	0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x03, 0x12, 0x1a, 0x0a, 0x16, 0x53, 0x45, 0x41,
	0x52, 0x43, 0x48, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x42, 0x59, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x10, 0x04, 0x32, 0xd1, 0x0a, 0x0a, 0x0f, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x79, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x63, 0x0a, 0x0e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x12, 0x27, 0x2e, 0x68, 0x65,
	0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70,
	0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5a,
	0x0a, 0x0b, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x12, 0x24, 0x2e,
	0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65,
	0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x63, 0x0a, 0x0e, 0x4c, 0x69,
	0x73, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x12, 0x27, 0x2e, 0x68,
	0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x63, 0x0a, 0x0e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x79, 0x12, 0x27, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x79, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65,
	0x72, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x68, 0x65, 0x72,
	0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x63, 0x0a, 0x0e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x12, 0x27, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x28, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x69, 0x0a, 0x10, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x12, 0x29, 0x2e,
	0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e,
	0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6f, 0x0a, 0x12, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74,
	0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x12, 0x2b, 0x2e, 0x68, 0x65, 0x72,
	0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61,
	0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68,
	0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x66, 0x0a, 0x0f, 0x41, 0x64, 0x64, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x64, 0x79, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x28, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e,
	0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x64, 0x79, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x29, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x64, 0x79,
	0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x66, 0x0a,
	0x0f, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x64, 0x79, 0x43, 0x68, 0x61, 0x69, 0x6e,
	0x12, 0x28, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x64, 0x79, 0x43, 0x68,
	0x61, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x68, 0x65, 0x72,
	0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x64, 0x79, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8a, 0x01, 0x0a, 0x1b, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x34, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x79, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e, 0x68, 0x65,
	0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x46, 0x69, 0x6c, 0x65, 0x41,
	0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x84, 0x01, 0x0a, 0x19, 0x41, 0x64, 0x64, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x79, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74,
	0x12, 0x32, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79,
	0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x46,
	0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70,
	0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x50, 0x72, 0x6f, 0x70, 0x65,
	0x72, 0x74, 0x79, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8d, 0x01, 0x0a, 0x1c, 0x52, 0x65,
	0x6d, 0x6f, 0x76, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x46, 0x69, 0x6c, 0x65,
	0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x35, 0x2e, 0x68, 0x65, 0x72,
	0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65,
	0x6d, 0x6f, 0x76, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x46, 0x69, 0x6c, 0x65,
	0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x36, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x79, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65,
	0x72, 0x74, 0x79, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x21, 0x5a, 0x1f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2f, 0x68, 0x65, 0x72, 0x6f, 0x2f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79,
	0x2f, 0x76, 0x31, 0x3b, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_hero_property_v1_property_proto_rawDescOnce sync.Once
	file_hero_property_v1_property_proto_rawDescData = file_hero_property_v1_property_proto_rawDesc
)

func file_hero_property_v1_property_proto_rawDescGZIP() []byte {
	file_hero_property_v1_property_proto_rawDescOnce.Do(func() {
		file_hero_property_v1_property_proto_rawDescData = protoimpl.X.CompressGZIP(file_hero_property_v1_property_proto_rawDescData)
	})
	return file_hero_property_v1_property_proto_rawDescData
}

var file_hero_property_v1_property_proto_enumTypes = make([]protoimpl.EnumInfo, 8)
var file_hero_property_v1_property_proto_msgTypes = make([]protoimpl.MessageInfo, 31)
var file_hero_property_v1_property_proto_goTypes = []any{
	(NIBRSPropertyType)(0),                       // 0: hero.property.v1.NIBRSPropertyType
	(PropertyStatus)(0),                          // 1: hero.property.v1.PropertyStatus
	(PropertyDisposalType)(0),                    // 2: hero.property.v1.PropertyDisposalType
	(CustodyActionType)(0),                       // 3: hero.property.v1.CustodyActionType
	(CustodyEntityType)(0),                       // 4: hero.property.v1.CustodyEntityType
	(CustodyAgencyType)(0),                       // 5: hero.property.v1.CustodyAgencyType
	(NIBRSPropertyDescription)(0),                // 6: hero.property.v1.NIBRSPropertyDescription
	(SearchOrderBy)(0),                           // 7: hero.property.v1.SearchOrderBy
	(*CustodyEvent)(nil),                         // 8: hero.property.v1.CustodyEvent
	(*Property)(nil),                             // 9: hero.property.v1.Property
	(*DateRange)(nil),                            // 10: hero.property.v1.DateRange
	(*FieldQuery)(nil),                           // 11: hero.property.v1.FieldQuery
	(*HighlightResult)(nil),                      // 12: hero.property.v1.HighlightResult
	(*CreatePropertyRequest)(nil),                // 13: hero.property.v1.CreatePropertyRequest
	(*CreatePropertyResponse)(nil),               // 14: hero.property.v1.CreatePropertyResponse
	(*GetPropertyRequest)(nil),                   // 15: hero.property.v1.GetPropertyRequest
	(*GetPropertyResponse)(nil),                  // 16: hero.property.v1.GetPropertyResponse
	(*ListPropertiesRequest)(nil),                // 17: hero.property.v1.ListPropertiesRequest
	(*ListPropertiesResponse)(nil),               // 18: hero.property.v1.ListPropertiesResponse
	(*UpdatePropertyRequest)(nil),                // 19: hero.property.v1.UpdatePropertyRequest
	(*UpdatePropertyResponse)(nil),               // 20: hero.property.v1.UpdatePropertyResponse
	(*DeletePropertyRequest)(nil),                // 21: hero.property.v1.DeletePropertyRequest
	(*DeletePropertyResponse)(nil),               // 22: hero.property.v1.DeletePropertyResponse
	(*SearchPropertiesRequest)(nil),              // 23: hero.property.v1.SearchPropertiesRequest
	(*SearchPropertiesResponse)(nil),             // 24: hero.property.v1.SearchPropertiesResponse
	(*BatchGetPropertiesRequest)(nil),            // 25: hero.property.v1.BatchGetPropertiesRequest
	(*BatchGetPropertiesResponse)(nil),           // 26: hero.property.v1.BatchGetPropertiesResponse
	(*AddCustodyEventRequest)(nil),               // 27: hero.property.v1.AddCustodyEventRequest
	(*AddCustodyEventResponse)(nil),              // 28: hero.property.v1.AddCustodyEventResponse
	(*GetCustodyChainRequest)(nil),               // 29: hero.property.v1.GetCustodyChainRequest
	(*GetCustodyChainResponse)(nil),              // 30: hero.property.v1.GetCustodyChainResponse
	(*ListPropertyFileAttachmentsRequest)(nil),   // 31: hero.property.v1.ListPropertyFileAttachmentsRequest
	(*ListPropertyFileAttachmentsResponse)(nil),  // 32: hero.property.v1.ListPropertyFileAttachmentsResponse
	(*PropertyFileReference)(nil),                // 33: hero.property.v1.PropertyFileReference
	(*AddPropertyFileAttachmentRequest)(nil),     // 34: hero.property.v1.AddPropertyFileAttachmentRequest
	(*AddPropertyFileAttachmentResponse)(nil),    // 35: hero.property.v1.AddPropertyFileAttachmentResponse
	(*RemovePropertyFileAttachmentRequest)(nil),  // 36: hero.property.v1.RemovePropertyFileAttachmentRequest
	(*RemovePropertyFileAttachmentResponse)(nil), // 37: hero.property.v1.RemovePropertyFileAttachmentResponse
	(*PropertyReference)(nil),                    // 38: hero.property.v1.PropertyReference
	(*structpb.Struct)(nil),                      // 39: google.protobuf.Struct
}
var file_hero_property_v1_property_proto_depIdxs = []int32{
	3,  // 0: hero.property.v1.CustodyEvent.action_type:type_name -> hero.property.v1.CustodyActionType
	4,  // 1: hero.property.v1.CustodyEvent.receiving_entity_type:type_name -> hero.property.v1.CustodyEntityType
	5,  // 2: hero.property.v1.CustodyEvent.receiving_agency_type:type_name -> hero.property.v1.CustodyAgencyType
	2,  // 3: hero.property.v1.CustodyEvent.disposal_type:type_name -> hero.property.v1.PropertyDisposalType
	1,  // 4: hero.property.v1.Property.property_status:type_name -> hero.property.v1.PropertyStatus
	2,  // 5: hero.property.v1.Property.disposal_type:type_name -> hero.property.v1.PropertyDisposalType
	8,  // 6: hero.property.v1.Property.custody_chain:type_name -> hero.property.v1.CustodyEvent
	39, // 7: hero.property.v1.Property.details:type_name -> google.protobuf.Struct
	0,  // 8: hero.property.v1.Property.nibrs_property_type:type_name -> hero.property.v1.NIBRSPropertyType
	9,  // 9: hero.property.v1.CreatePropertyRequest.property:type_name -> hero.property.v1.Property
	9,  // 10: hero.property.v1.CreatePropertyResponse.property:type_name -> hero.property.v1.Property
	9,  // 11: hero.property.v1.GetPropertyResponse.property:type_name -> hero.property.v1.Property
	0,  // 12: hero.property.v1.ListPropertiesRequest.nibrs_property_type:type_name -> hero.property.v1.NIBRSPropertyType
	1,  // 13: hero.property.v1.ListPropertiesRequest.property_status:type_name -> hero.property.v1.PropertyStatus
	9,  // 14: hero.property.v1.ListPropertiesResponse.properties:type_name -> hero.property.v1.Property
	9,  // 15: hero.property.v1.UpdatePropertyRequest.property:type_name -> hero.property.v1.Property
	9,  // 16: hero.property.v1.UpdatePropertyResponse.property:type_name -> hero.property.v1.Property
	11, // 17: hero.property.v1.SearchPropertiesRequest.field_queries:type_name -> hero.property.v1.FieldQuery
	10, // 18: hero.property.v1.SearchPropertiesRequest.date_range:type_name -> hero.property.v1.DateRange
	0,  // 19: hero.property.v1.SearchPropertiesRequest.nibrs_property_type:type_name -> hero.property.v1.NIBRSPropertyType
	1,  // 20: hero.property.v1.SearchPropertiesRequest.property_status:type_name -> hero.property.v1.PropertyStatus
	7,  // 21: hero.property.v1.SearchPropertiesRequest.order_by:type_name -> hero.property.v1.SearchOrderBy
	9,  // 22: hero.property.v1.SearchPropertiesResponse.properties:type_name -> hero.property.v1.Property
	12, // 23: hero.property.v1.SearchPropertiesResponse.highlights:type_name -> hero.property.v1.HighlightResult
	9,  // 24: hero.property.v1.BatchGetPropertiesResponse.properties:type_name -> hero.property.v1.Property
	8,  // 25: hero.property.v1.AddCustodyEventRequest.custody_event:type_name -> hero.property.v1.CustodyEvent
	8,  // 26: hero.property.v1.GetCustodyChainResponse.custody_chain:type_name -> hero.property.v1.CustodyEvent
	33, // 27: hero.property.v1.ListPropertyFileAttachmentsResponse.file_attachments:type_name -> hero.property.v1.PropertyFileReference
	39, // 28: hero.property.v1.PropertyFileReference.metadata:type_name -> google.protobuf.Struct
	33, // 29: hero.property.v1.AddPropertyFileAttachmentRequest.file_attachment:type_name -> hero.property.v1.PropertyFileReference
	33, // 30: hero.property.v1.AddPropertyFileAttachmentResponse.file_attachment:type_name -> hero.property.v1.PropertyFileReference
	13, // 31: hero.property.v1.PropertyService.CreateProperty:input_type -> hero.property.v1.CreatePropertyRequest
	15, // 32: hero.property.v1.PropertyService.GetProperty:input_type -> hero.property.v1.GetPropertyRequest
	17, // 33: hero.property.v1.PropertyService.ListProperties:input_type -> hero.property.v1.ListPropertiesRequest
	19, // 34: hero.property.v1.PropertyService.UpdateProperty:input_type -> hero.property.v1.UpdatePropertyRequest
	21, // 35: hero.property.v1.PropertyService.DeleteProperty:input_type -> hero.property.v1.DeletePropertyRequest
	23, // 36: hero.property.v1.PropertyService.SearchProperties:input_type -> hero.property.v1.SearchPropertiesRequest
	25, // 37: hero.property.v1.PropertyService.BatchGetProperties:input_type -> hero.property.v1.BatchGetPropertiesRequest
	27, // 38: hero.property.v1.PropertyService.AddCustodyEvent:input_type -> hero.property.v1.AddCustodyEventRequest
	29, // 39: hero.property.v1.PropertyService.GetCustodyChain:input_type -> hero.property.v1.GetCustodyChainRequest
	31, // 40: hero.property.v1.PropertyService.ListPropertyFileAttachments:input_type -> hero.property.v1.ListPropertyFileAttachmentsRequest
	34, // 41: hero.property.v1.PropertyService.AddPropertyFileAttachment:input_type -> hero.property.v1.AddPropertyFileAttachmentRequest
	36, // 42: hero.property.v1.PropertyService.RemovePropertyFileAttachment:input_type -> hero.property.v1.RemovePropertyFileAttachmentRequest
	14, // 43: hero.property.v1.PropertyService.CreateProperty:output_type -> hero.property.v1.CreatePropertyResponse
	16, // 44: hero.property.v1.PropertyService.GetProperty:output_type -> hero.property.v1.GetPropertyResponse
	18, // 45: hero.property.v1.PropertyService.ListProperties:output_type -> hero.property.v1.ListPropertiesResponse
	20, // 46: hero.property.v1.PropertyService.UpdateProperty:output_type -> hero.property.v1.UpdatePropertyResponse
	22, // 47: hero.property.v1.PropertyService.DeleteProperty:output_type -> hero.property.v1.DeletePropertyResponse
	24, // 48: hero.property.v1.PropertyService.SearchProperties:output_type -> hero.property.v1.SearchPropertiesResponse
	26, // 49: hero.property.v1.PropertyService.BatchGetProperties:output_type -> hero.property.v1.BatchGetPropertiesResponse
	28, // 50: hero.property.v1.PropertyService.AddCustodyEvent:output_type -> hero.property.v1.AddCustodyEventResponse
	30, // 51: hero.property.v1.PropertyService.GetCustodyChain:output_type -> hero.property.v1.GetCustodyChainResponse
	32, // 52: hero.property.v1.PropertyService.ListPropertyFileAttachments:output_type -> hero.property.v1.ListPropertyFileAttachmentsResponse
	35, // 53: hero.property.v1.PropertyService.AddPropertyFileAttachment:output_type -> hero.property.v1.AddPropertyFileAttachmentResponse
	37, // 54: hero.property.v1.PropertyService.RemovePropertyFileAttachment:output_type -> hero.property.v1.RemovePropertyFileAttachmentResponse
	43, // [43:55] is the sub-list for method output_type
	31, // [31:43] is the sub-list for method input_type
	31, // [31:31] is the sub-list for extension type_name
	31, // [31:31] is the sub-list for extension extendee
	0,  // [0:31] is the sub-list for field type_name
}

func init() { file_hero_property_v1_property_proto_init() }
func file_hero_property_v1_property_proto_init() {
	if File_hero_property_v1_property_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_hero_property_v1_property_proto_rawDesc,
			NumEnums:      8,
			NumMessages:   31,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_hero_property_v1_property_proto_goTypes,
		DependencyIndexes: file_hero_property_v1_property_proto_depIdxs,
		EnumInfos:         file_hero_property_v1_property_proto_enumTypes,
		MessageInfos:      file_hero_property_v1_property_proto_msgTypes,
	}.Build()
	File_hero_property_v1_property_proto = out.File
	file_hero_property_v1_property_proto_rawDesc = nil
	file_hero_property_v1_property_proto_goTypes = nil
	file_hero_property_v1_property_proto_depIdxs = nil
}
