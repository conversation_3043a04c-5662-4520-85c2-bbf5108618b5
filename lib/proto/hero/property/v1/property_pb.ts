// @generated by protoc-gen-es v2.7.0 with parameter "target=ts"
// @generated from file hero/property/v1/property.proto (package hero.property.v1, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv2";
import { file_google_protobuf_struct } from "@bufbuild/protobuf/wkt";
import type { JsonObject, Message } from "@bufbuild/protobuf";

/**
 * Describes the file hero/property/v1/property.proto.
 */
export const file_hero_property_v1_property: GenFile = /*@__PURE__*/
  fileDesc("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", [file_google_protobuf_struct]);

/**
 * CustodyEvent represents a single chain of custody entry
 *
 * @generated from message hero.property.v1.CustodyEvent
 */
export type CustodyEvent = Message<"hero.property.v1.CustodyEvent"> & {
  /**
   * ISO8601 timestamp of the custody change
   *
   * @generated from field: string timestamp = 1;
   */
  timestamp: string;

  /**
   * User/Officer transferring the item
   *
   * @generated from field: string transferring_user_id = 2;
   */
  transferringUserId: string;

  /**
   * Agency transferring the item
   *
   * @generated from field: string transferring_agency = 3;
   */
  transferringAgency: string;

  /**
   * User/Officer/Agency receiving the item (optional for disposal)
   *
   * @generated from field: string receiving_user_id = 4;
   */
  receivingUserId: string;

  /**
   * Agency receiving the item (if different from current org)
   *
   * @generated from field: string receiving_agency = 5;
   */
  receivingAgency: string;

  /**
   * New location (e.g., "Evidence Room A-12", "Lab XYZ")
   *
   * @generated from field: string new_location = 6;
   */
  newLocation: string;

  /**
   * Type of custody action
   *
   * @generated from field: hero.property.v1.CustodyActionType action_type = 7;
   */
  actionType: CustodyActionType;

  /**
   * Reason/purpose for the change
   *
   * @generated from field: string notes = 8;
   */
  notes: string;

  /**
   * Associated case number
   *
   * @generated from field: string case_number = 9;
   */
  caseNumber: string;

  /**
   * Evidence tracking number
   *
   * @generated from field: string evidence_number = 10;
   */
  evidenceNumber: string;

  /**
   * Enhanced fields for detailed custody information
   *
   * Officer performing the action (may differ from transferring_user_id)
   *
   * @generated from field: string performing_officer_id = 11;
   */
  performingOfficerId: string;

  /**
   * Type of receiving entity (Person, Crime Lab, Court, etc.)
   *
   * @generated from field: hero.property.v1.CustodyEntityType receiving_entity_type = 12;
   */
  receivingEntityType: CustodyEntityType;

  /**
   * Name of the receiving entity
   *
   * @generated from field: string receiving_entity_name = 13;
   */
  receivingEntityName: string;

  /**
   * Length of check out (e.g., "2 weeks", "until court date")
   *
   * @generated from field: string checkout_length = 14;
   */
  checkoutLength: string;

  /**
   * Specific reason for the action
   *
   * @generated from field: string reason = 15;
   */
  reason: string;

  /**
   * Type of receiving agency (PD, Other)
   *
   * @generated from field: hero.property.v1.CustodyAgencyType receiving_agency_type = 16;
   */
  receivingAgencyType: CustodyAgencyType;

  /**
   * Whether confirmation of receipt was obtained
   *
   * @generated from field: bool confirmation_received = 17;
   */
  confirmationReceived: boolean;

  /**
   * Location where property was originally collected from
   *
   * @generated from field: string collection_location = 18;
   */
  collectionLocation: string;

  /**
   * Type of disposal (for DISPOSED actions)
   *
   * @generated from field: hero.property.v1.PropertyDisposalType disposal_type = 19;
   */
  disposalType: PropertyDisposalType;
};

/**
 * Describes the message hero.property.v1.CustodyEvent.
 * Use `create(CustodyEventSchema)` to create a new message.
 */
export const CustodyEventSchema: GenMessage<CustodyEvent> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 0);

/**
 * Property represents a property entity
 *
 * @generated from message hero.property.v1.Property
 */
export type Property = Message<"hero.property.v1.Property"> & {
  /**
   * Unique identifier for the property
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * Organization identifier
   *
   * @generated from field: int32 org_id = 2;
   */
  orgId: number;

  /**
   * Property/Evidence number for tracking
   *
   * @generated from field: string property_number = 3;
   */
  propertyNumber: string;

  /**
   * Current status of the property
   *
   * @generated from field: hero.property.v1.PropertyStatus property_status = 4;
   */
  propertyStatus: PropertyStatus;

  /**
   * Flag indicating whether this is evidence
   *
   * @generated from field: bool is_evidence = 5;
   */
  isEvidence: boolean;

  /**
   * Retention period for evidence
   *
   * @generated from field: string retention_period = 6;
   */
  retentionPeriod: string;

  /**
   * How property was disposed
   *
   * @generated from field: hero.property.v1.PropertyDisposalType disposal_type = 7;
   */
  disposalType: PropertyDisposalType;

  /**
   * Additional notes about the property 
   *
   * @generated from field: string notes = 8;
   */
  notes: string;

  /**
   * Current person/agency with custody
   *
   * @generated from field: string current_custodian = 9;
   */
  currentCustodian: string;

  /**
   * Current location of the property
   *
   * @generated from field: string current_location = 10;
   */
  currentLocation: string;

  /**
   * Complete chain of custody history
   *
   * @generated from field: repeated hero.property.v1.CustodyEvent custody_chain = 11;
   */
  custodyChain: CustodyEvent[];

  /**
   * Link to the dynamic schema used for this property (reuses Entity Schema registry)
   *
   * ID of the schema this property conforms to
   *
   * @generated from field: string schema_id = 12;
   */
  schemaId: string;

  /**
   * Version of the schema used
   *
   * @generated from field: int32 schema_version = 13;
   */
  schemaVersion: number;

  /**
   * Dynamic payload validated/rendered by the resolved schema
   *
   * @generated from field: google.protobuf.Struct details = 14;
   */
  details?: JsonObject;

  /**
   * ISO8601 timestamp when the property was created
   *
   * @generated from field: string create_time = 15;
   */
  createTime: string;

  /**
   * ISO8601 timestamp when the property was last updated
   *
   * @generated from field: string update_time = 16;
   */
  updateTime: string;

  /**
   * User ID of the creator
   *
   * @generated from field: string created_by = 17;
   */
  createdBy: string;

  /**
   * User ID of the last updater
   *
   * @generated from field: string updated_by = 18;
   */
  updatedBy: string;

  /**
   * Version number (for audit/history purposes)
   *
   * @generated from field: int32 version = 19;
   */
  version: number;

  /**
   * Fixed value "PROPERTY"
   *
   * @generated from field: string resource_type = 20;
   */
  resourceType: string;

  /**
   * NIBRS property type classification
   *
   * @generated from field: hero.property.v1.NIBRSPropertyType nibrs_property_type = 21;
   */
  nibrsPropertyType: NIBRSPropertyType;
};

/**
 * Describes the message hero.property.v1.Property.
 * Use `create(PropertySchema)` to create a new message.
 */
export const PropertySchema: GenMessage<Property> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 1);

/**
 * DateRange represents a time range for filtering search results
 *
 * @generated from message hero.property.v1.DateRange
 */
export type DateRange = Message<"hero.property.v1.DateRange"> & {
  /**
   * Start time in RFC3339 format (inclusive)
   *
   * @generated from field: string from = 1;
   */
  from: string;

  /**
   * End time in RFC3339 format (inclusive)
   *
   * @generated from field: string to = 2;
   */
  to: string;
};

/**
 * Describes the message hero.property.v1.DateRange.
 * Use `create(DateRangeSchema)` to create a new message.
 */
export const DateRangeSchema: GenMessage<DateRange> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 2);

/**
 * FieldQuery represents a field-specific search query
 *
 * @generated from message hero.property.v1.FieldQuery
 */
export type FieldQuery = Message<"hero.property.v1.FieldQuery"> & {
  /**
   * Field to search in (id, notes, location, current_custodian, current_location)
   *
   * @generated from field: string field = 1;
   */
  field: string;

  /**
   * Search term for this specific field
   *
   * @generated from field: string query = 2;
   */
  query: string;
};

/**
 * Describes the message hero.property.v1.FieldQuery.
 * Use `create(FieldQuerySchema)` to create a new message.
 */
export const FieldQuerySchema: GenMessage<FieldQuery> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 3);

/**
 * HighlightResult represents highlighted search results for a property
 *
 * @generated from message hero.property.v1.HighlightResult
 */
export type HighlightResult = Message<"hero.property.v1.HighlightResult"> & {
  /**
   * Field name that had a match
   *
   * @generated from field: string field = 1;
   */
  field: string;

  /**
   * Highlighted fragments with matched terms
   *
   * @generated from field: repeated string fragments = 2;
   */
  fragments: string[];
};

/**
 * Describes the message hero.property.v1.HighlightResult.
 * Use `create(HighlightResultSchema)` to create a new message.
 */
export const HighlightResultSchema: GenMessage<HighlightResult> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 4);

/**
 * Request/Response Messages for CreateProperty
 *
 * @generated from message hero.property.v1.CreatePropertyRequest
 */
export type CreatePropertyRequest = Message<"hero.property.v1.CreatePropertyRequest"> & {
  /**
   * @generated from field: hero.property.v1.Property property = 1;
   */
  property?: Property;
};

/**
 * Describes the message hero.property.v1.CreatePropertyRequest.
 * Use `create(CreatePropertyRequestSchema)` to create a new message.
 */
export const CreatePropertyRequestSchema: GenMessage<CreatePropertyRequest> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 5);

/**
 * @generated from message hero.property.v1.CreatePropertyResponse
 */
export type CreatePropertyResponse = Message<"hero.property.v1.CreatePropertyResponse"> & {
  /**
   * @generated from field: hero.property.v1.Property property = 1;
   */
  property?: Property;
};

/**
 * Describes the message hero.property.v1.CreatePropertyResponse.
 * Use `create(CreatePropertyResponseSchema)` to create a new message.
 */
export const CreatePropertyResponseSchema: GenMessage<CreatePropertyResponse> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 6);

/**
 * Request/Response Messages for GetProperty
 *
 * @generated from message hero.property.v1.GetPropertyRequest
 */
export type GetPropertyRequest = Message<"hero.property.v1.GetPropertyRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;
};

/**
 * Describes the message hero.property.v1.GetPropertyRequest.
 * Use `create(GetPropertyRequestSchema)` to create a new message.
 */
export const GetPropertyRequestSchema: GenMessage<GetPropertyRequest> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 7);

/**
 * @generated from message hero.property.v1.GetPropertyResponse
 */
export type GetPropertyResponse = Message<"hero.property.v1.GetPropertyResponse"> & {
  /**
   * @generated from field: hero.property.v1.Property property = 1;
   */
  property?: Property;
};

/**
 * Describes the message hero.property.v1.GetPropertyResponse.
 * Use `create(GetPropertyResponseSchema)` to create a new message.
 */
export const GetPropertyResponseSchema: GenMessage<GetPropertyResponse> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 8);

/**
 * Request/Response Messages for ListProperties
 *
 * @generated from message hero.property.v1.ListPropertiesRequest
 */
export type ListPropertiesRequest = Message<"hero.property.v1.ListPropertiesRequest"> & {
  /**
   * @generated from field: int32 page_size = 1;
   */
  pageSize: number;

  /**
   * @generated from field: string page_token = 2;
   */
  pageToken: string;

  /**
   * @generated from field: hero.property.v1.NIBRSPropertyType nibrs_property_type = 3;
   */
  nibrsPropertyType: NIBRSPropertyType;

  /**
   * @generated from field: hero.property.v1.PropertyStatus property_status = 4;
   */
  propertyStatus: PropertyStatus;

  /**
   * @generated from field: string order_by = 5;
   */
  orderBy: string;
};

/**
 * Describes the message hero.property.v1.ListPropertiesRequest.
 * Use `create(ListPropertiesRequestSchema)` to create a new message.
 */
export const ListPropertiesRequestSchema: GenMessage<ListPropertiesRequest> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 9);

/**
 * @generated from message hero.property.v1.ListPropertiesResponse
 */
export type ListPropertiesResponse = Message<"hero.property.v1.ListPropertiesResponse"> & {
  /**
   * @generated from field: repeated hero.property.v1.Property properties = 1;
   */
  properties: Property[];

  /**
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;
};

/**
 * Describes the message hero.property.v1.ListPropertiesResponse.
 * Use `create(ListPropertiesResponseSchema)` to create a new message.
 */
export const ListPropertiesResponseSchema: GenMessage<ListPropertiesResponse> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 10);

/**
 * Request/Response Messages for UpdateProperty
 *
 * @generated from message hero.property.v1.UpdatePropertyRequest
 */
export type UpdatePropertyRequest = Message<"hero.property.v1.UpdatePropertyRequest"> & {
  /**
   * @generated from field: hero.property.v1.Property property = 1;
   */
  property?: Property;
};

/**
 * Describes the message hero.property.v1.UpdatePropertyRequest.
 * Use `create(UpdatePropertyRequestSchema)` to create a new message.
 */
export const UpdatePropertyRequestSchema: GenMessage<UpdatePropertyRequest> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 11);

/**
 * @generated from message hero.property.v1.UpdatePropertyResponse
 */
export type UpdatePropertyResponse = Message<"hero.property.v1.UpdatePropertyResponse"> & {
  /**
   * @generated from field: hero.property.v1.Property property = 1;
   */
  property?: Property;
};

/**
 * Describes the message hero.property.v1.UpdatePropertyResponse.
 * Use `create(UpdatePropertyResponseSchema)` to create a new message.
 */
export const UpdatePropertyResponseSchema: GenMessage<UpdatePropertyResponse> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 12);

/**
 * Request/Response Messages for DeleteProperty
 *
 * @generated from message hero.property.v1.DeletePropertyRequest
 */
export type DeletePropertyRequest = Message<"hero.property.v1.DeletePropertyRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;
};

/**
 * Describes the message hero.property.v1.DeletePropertyRequest.
 * Use `create(DeletePropertyRequestSchema)` to create a new message.
 */
export const DeletePropertyRequestSchema: GenMessage<DeletePropertyRequest> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 13);

/**
 * @generated from message hero.property.v1.DeletePropertyResponse
 */
export type DeletePropertyResponse = Message<"hero.property.v1.DeletePropertyResponse"> & {
};

/**
 * Describes the message hero.property.v1.DeletePropertyResponse.
 * Use `create(DeletePropertyResponseSchema)` to create a new message.
 */
export const DeletePropertyResponseSchema: GenMessage<DeletePropertyResponse> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 14);

/**
 * Request/Response Messages for SearchProperties
 *
 * @generated from message hero.property.v1.SearchPropertiesRequest
 */
export type SearchPropertiesRequest = Message<"hero.property.v1.SearchPropertiesRequest"> & {
  /**
   * @generated from field: string query = 1;
   */
  query: string;

  /**
   * @generated from field: repeated hero.property.v1.FieldQuery field_queries = 2;
   */
  fieldQueries: FieldQuery[];

  /**
   * @generated from field: hero.property.v1.DateRange date_range = 3;
   */
  dateRange?: DateRange;

  /**
   * @generated from field: hero.property.v1.NIBRSPropertyType nibrs_property_type = 4;
   */
  nibrsPropertyType: NIBRSPropertyType;

  /**
   * @generated from field: hero.property.v1.PropertyStatus property_status = 5;
   */
  propertyStatus: PropertyStatus;

  /**
   * @generated from field: hero.property.v1.SearchOrderBy order_by = 6;
   */
  orderBy: SearchOrderBy;

  /**
   * @generated from field: int32 page_size = 7;
   */
  pageSize: number;

  /**
   * @generated from field: string page_token = 8;
   */
  pageToken: string;
};

/**
 * Describes the message hero.property.v1.SearchPropertiesRequest.
 * Use `create(SearchPropertiesRequestSchema)` to create a new message.
 */
export const SearchPropertiesRequestSchema: GenMessage<SearchPropertiesRequest> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 15);

/**
 * @generated from message hero.property.v1.SearchPropertiesResponse
 */
export type SearchPropertiesResponse = Message<"hero.property.v1.SearchPropertiesResponse"> & {
  /**
   * @generated from field: repeated hero.property.v1.Property properties = 1;
   */
  properties: Property[];

  /**
   * @generated from field: repeated hero.property.v1.HighlightResult highlights = 2;
   */
  highlights: HighlightResult[];

  /**
   * @generated from field: string next_page_token = 3;
   */
  nextPageToken: string;

  /**
   * @generated from field: int32 total_count = 4;
   */
  totalCount: number;
};

/**
 * Describes the message hero.property.v1.SearchPropertiesResponse.
 * Use `create(SearchPropertiesResponseSchema)` to create a new message.
 */
export const SearchPropertiesResponseSchema: GenMessage<SearchPropertiesResponse> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 16);

/**
 * Request/Response Messages for BatchGetProperties
 *
 * @generated from message hero.property.v1.BatchGetPropertiesRequest
 */
export type BatchGetPropertiesRequest = Message<"hero.property.v1.BatchGetPropertiesRequest"> & {
  /**
   * @generated from field: repeated string ids = 1;
   */
  ids: string[];
};

/**
 * Describes the message hero.property.v1.BatchGetPropertiesRequest.
 * Use `create(BatchGetPropertiesRequestSchema)` to create a new message.
 */
export const BatchGetPropertiesRequestSchema: GenMessage<BatchGetPropertiesRequest> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 17);

/**
 * @generated from message hero.property.v1.BatchGetPropertiesResponse
 */
export type BatchGetPropertiesResponse = Message<"hero.property.v1.BatchGetPropertiesResponse"> & {
  /**
   * @generated from field: repeated hero.property.v1.Property properties = 1;
   */
  properties: Property[];
};

/**
 * Describes the message hero.property.v1.BatchGetPropertiesResponse.
 * Use `create(BatchGetPropertiesResponseSchema)` to create a new message.
 */
export const BatchGetPropertiesResponseSchema: GenMessage<BatchGetPropertiesResponse> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 18);

/**
 * Request/Response Messages for AddCustodyEvent
 *
 * @generated from message hero.property.v1.AddCustodyEventRequest
 */
export type AddCustodyEventRequest = Message<"hero.property.v1.AddCustodyEventRequest"> & {
  /**
   * @generated from field: string property_id = 1;
   */
  propertyId: string;

  /**
   * @generated from field: hero.property.v1.CustodyEvent custody_event = 2;
   */
  custodyEvent?: CustodyEvent;
};

/**
 * Describes the message hero.property.v1.AddCustodyEventRequest.
 * Use `create(AddCustodyEventRequestSchema)` to create a new message.
 */
export const AddCustodyEventRequestSchema: GenMessage<AddCustodyEventRequest> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 19);

/**
 * @generated from message hero.property.v1.AddCustodyEventResponse
 */
export type AddCustodyEventResponse = Message<"hero.property.v1.AddCustodyEventResponse"> & {
};

/**
 * Describes the message hero.property.v1.AddCustodyEventResponse.
 * Use `create(AddCustodyEventResponseSchema)` to create a new message.
 */
export const AddCustodyEventResponseSchema: GenMessage<AddCustodyEventResponse> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 20);

/**
 * Request/Response Messages for GetCustodyChain
 *
 * @generated from message hero.property.v1.GetCustodyChainRequest
 */
export type GetCustodyChainRequest = Message<"hero.property.v1.GetCustodyChainRequest"> & {
  /**
   * @generated from field: string property_id = 1;
   */
  propertyId: string;
};

/**
 * Describes the message hero.property.v1.GetCustodyChainRequest.
 * Use `create(GetCustodyChainRequestSchema)` to create a new message.
 */
export const GetCustodyChainRequestSchema: GenMessage<GetCustodyChainRequest> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 21);

/**
 * @generated from message hero.property.v1.GetCustodyChainResponse
 */
export type GetCustodyChainResponse = Message<"hero.property.v1.GetCustodyChainResponse"> & {
  /**
   * @generated from field: repeated hero.property.v1.CustodyEvent custody_chain = 1;
   */
  custodyChain: CustodyEvent[];
};

/**
 * Describes the message hero.property.v1.GetCustodyChainResponse.
 * Use `create(GetCustodyChainResponseSchema)` to create a new message.
 */
export const GetCustodyChainResponseSchema: GenMessage<GetCustodyChainResponse> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 22);

/**
 * Request/Response Messages for ListPropertyFileAttachments
 *
 * @generated from message hero.property.v1.ListPropertyFileAttachmentsRequest
 */
export type ListPropertyFileAttachmentsRequest = Message<"hero.property.v1.ListPropertyFileAttachmentsRequest"> & {
  /**
   * @generated from field: string property_id = 1;
   */
  propertyId: string;

  /**
   * @generated from field: string file_category = 2;
   */
  fileCategory: string;

  /**
   * @generated from field: int32 page_size = 3;
   */
  pageSize: number;

  /**
   * @generated from field: string page_token = 4;
   */
  pageToken: string;
};

/**
 * Describes the message hero.property.v1.ListPropertyFileAttachmentsRequest.
 * Use `create(ListPropertyFileAttachmentsRequestSchema)` to create a new message.
 */
export const ListPropertyFileAttachmentsRequestSchema: GenMessage<ListPropertyFileAttachmentsRequest> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 23);

/**
 * @generated from message hero.property.v1.ListPropertyFileAttachmentsResponse
 */
export type ListPropertyFileAttachmentsResponse = Message<"hero.property.v1.ListPropertyFileAttachmentsResponse"> & {
  /**
   * @generated from field: repeated hero.property.v1.PropertyFileReference file_attachments = 1;
   */
  fileAttachments: PropertyFileReference[];

  /**
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;
};

/**
 * Describes the message hero.property.v1.ListPropertyFileAttachmentsResponse.
 * Use `create(ListPropertyFileAttachmentsResponseSchema)` to create a new message.
 */
export const ListPropertyFileAttachmentsResponseSchema: GenMessage<ListPropertyFileAttachmentsResponse> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 24);

/**
 * PropertyFileReference represents a file attachment for a property
 *
 * @generated from message hero.property.v1.PropertyFileReference
 */
export type PropertyFileReference = Message<"hero.property.v1.PropertyFileReference"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string property_id = 2;
   */
  propertyId: string;

  /**
   * @generated from field: string file_id = 3;
   */
  fileId: string;

  /**
   * @generated from field: string caption = 4;
   */
  caption: string;

  /**
   * @generated from field: string display_name = 5;
   */
  displayName: string;

  /**
   * @generated from field: int32 display_order = 6;
   */
  displayOrder: number;

  /**
   * @generated from field: string file_category = 7;
   */
  fileCategory: string;

  /**
   * @generated from field: google.protobuf.Struct metadata = 8;
   */
  metadata?: JsonObject;
};

/**
 * Describes the message hero.property.v1.PropertyFileReference.
 * Use `create(PropertyFileReferenceSchema)` to create a new message.
 */
export const PropertyFileReferenceSchema: GenMessage<PropertyFileReference> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 25);

/**
 * Request/Response Messages for AddPropertyFileAttachment
 *
 * @generated from message hero.property.v1.AddPropertyFileAttachmentRequest
 */
export type AddPropertyFileAttachmentRequest = Message<"hero.property.v1.AddPropertyFileAttachmentRequest"> & {
  /**
   * @generated from field: string property_id = 1;
   */
  propertyId: string;

  /**
   * @generated from field: hero.property.v1.PropertyFileReference file_attachment = 2;
   */
  fileAttachment?: PropertyFileReference;
};

/**
 * Describes the message hero.property.v1.AddPropertyFileAttachmentRequest.
 * Use `create(AddPropertyFileAttachmentRequestSchema)` to create a new message.
 */
export const AddPropertyFileAttachmentRequestSchema: GenMessage<AddPropertyFileAttachmentRequest> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 26);

/**
 * @generated from message hero.property.v1.AddPropertyFileAttachmentResponse
 */
export type AddPropertyFileAttachmentResponse = Message<"hero.property.v1.AddPropertyFileAttachmentResponse"> & {
  /**
   * @generated from field: hero.property.v1.PropertyFileReference file_attachment = 1;
   */
  fileAttachment?: PropertyFileReference;
};

/**
 * Describes the message hero.property.v1.AddPropertyFileAttachmentResponse.
 * Use `create(AddPropertyFileAttachmentResponseSchema)` to create a new message.
 */
export const AddPropertyFileAttachmentResponseSchema: GenMessage<AddPropertyFileAttachmentResponse> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 27);

/**
 * Request/Response Messages for RemovePropertyFileAttachment
 *
 * @generated from message hero.property.v1.RemovePropertyFileAttachmentRequest
 */
export type RemovePropertyFileAttachmentRequest = Message<"hero.property.v1.RemovePropertyFileAttachmentRequest"> & {
  /**
   * @generated from field: string property_id = 1;
   */
  propertyId: string;

  /**
   * @generated from field: string attachment_id = 2;
   */
  attachmentId: string;
};

/**
 * Describes the message hero.property.v1.RemovePropertyFileAttachmentRequest.
 * Use `create(RemovePropertyFileAttachmentRequestSchema)` to create a new message.
 */
export const RemovePropertyFileAttachmentRequestSchema: GenMessage<RemovePropertyFileAttachmentRequest> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 28);

/**
 * @generated from message hero.property.v1.RemovePropertyFileAttachmentResponse
 */
export type RemovePropertyFileAttachmentResponse = Message<"hero.property.v1.RemovePropertyFileAttachmentResponse"> & {
};

/**
 * Describes the message hero.property.v1.RemovePropertyFileAttachmentResponse.
 * Use `create(RemovePropertyFileAttachmentResponseSchema)` to create a new message.
 */
export const RemovePropertyFileAttachmentResponseSchema: GenMessage<RemovePropertyFileAttachmentResponse> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 29);

/**
 * PropertyReference is a denormalized pointer to a Property used in other
 * services (e.g., cases). It intentionally mirrors hero.entity.v1.Reference
 * shape where appropriate while remaining property-specific.
 *
 * @generated from message hero.property.v1.PropertyReference
 */
export type PropertyReference = Message<"hero.property.v1.PropertyReference"> & {
  /**
   * hero.property.v1.Property.id
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * Snapshot/version number of the property
   *
   * @generated from field: int32 version = 2;
   */
  version: number;

  /**
   * Cached human label for convenience
   *
   * @generated from field: string display_name = 3;
   */
  displayName: string;

  /**
   * Caller-defined relation (e.g., "evidence", "involved")
   *
   * @generated from field: string relation_type = 4;
   */
  relationType: string;
};

/**
 * Describes the message hero.property.v1.PropertyReference.
 * Use `create(PropertyReferenceSchema)` to create a new message.
 */
export const PropertyReferenceSchema: GenMessage<PropertyReference> = /*@__PURE__*/
  messageDesc(file_hero_property_v1_property, 30);

/**
 * PropertyType enumerates the types of property handling
 *
 * @generated from enum hero.property.v1.NIBRSPropertyType
 */
export enum NIBRSPropertyType {
  /**
   * @generated from enum value: PROPERTY_TYPE_UNSPECIFIED = 0;
   */
  PROPERTY_TYPE_UNSPECIFIED = 0,

  /**
   * @generated from enum value: PROPERTY_TYPE_NONE = 1;
   */
  PROPERTY_TYPE_NONE = 1,

  /**
   * @generated from enum value: PROPERTY_TYPE_BURNED = 2;
   */
  PROPERTY_TYPE_BURNED = 2,

  /**
   * @generated from enum value: PROPERTY_TYPE_FORGED = 3;
   */
  PROPERTY_TYPE_FORGED = 3,

  /**
   * @generated from enum value: PROPERTY_TYPE_DAMAGED = 4;
   */
  PROPERTY_TYPE_DAMAGED = 4,

  /**
   * @generated from enum value: PROPERTY_TYPE_RECOVERED = 5;
   */
  PROPERTY_TYPE_RECOVERED = 5,

  /**
   * @generated from enum value: PROPERTY_TYPE_SEIZED = 6;
   */
  PROPERTY_TYPE_SEIZED = 6,

  /**
   * @generated from enum value: PROPERTY_TYPE_STOLEN = 7;
   */
  PROPERTY_TYPE_STOLEN = 7,

  /**
   * @generated from enum value: PROPERTY_TYPE_UNKNOWN = 8;
   */
  PROPERTY_TYPE_UNKNOWN = 8,

  /**
   * @generated from enum value: PROPERTY_TYPE_FOUND = 9;
   */
  PROPERTY_TYPE_FOUND = 9,
}

/**
 * Describes the enum hero.property.v1.NIBRSPropertyType.
 */
export const NIBRSPropertyTypeSchema: GenEnum<NIBRSPropertyType> = /*@__PURE__*/
  enumDesc(file_hero_property_v1_property, 0);

/**
 * PropertyStatus enumerates the status of property items
 *
 * @generated from enum hero.property.v1.PropertyStatus
 */
export enum PropertyStatus {
  /**
   * @generated from enum value: PROPERTY_STATUS_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: PROPERTY_STATUS_INTAKE_PENDING = 1;
   */
  INTAKE_PENDING = 1,

  /**
   * Property collected into custody
   *
   * @generated from enum value: PROPERTY_STATUS_COLLECTED = 2;
   */
  COLLECTED = 2,

  /**
   * @generated from enum value: PROPERTY_STATUS_CHECKED_IN = 3;
   */
  CHECKED_IN = 3,

  /**
   * @generated from enum value: PROPERTY_STATUS_CHECKED_OUT = 4;
   */
  CHECKED_OUT = 4,

  /**
   * @generated from enum value: PROPERTY_STATUS_RECOVERED = 5;
   */
  RECOVERED = 5,

  /**
   * @generated from enum value: PROPERTY_STATUS_FOUND = 6;
   */
  FOUND = 6,

  /**
   * @generated from enum value: PROPERTY_STATUS_SAFEKEEPING = 7;
   */
  SAFEKEEPING = 7,

  /**
   * @generated from enum value: PROPERTY_STATUS_AWAITING_DISPOSITION = 8;
   */
  AWAITING_DISPOSITION = 8,

  /**
   * @generated from enum value: PROPERTY_STATUS_DISPOSED = 9;
   */
  DISPOSED = 9,

  /**
   * @generated from enum value: PROPERTY_STATUS_MISSING = 10;
   */
  MISSING = 10,

  /**
   * @generated from enum value: PROPERTY_STATUS_STOLEN = 11;
   */
  STOLEN = 11,
}

/**
 * Describes the enum hero.property.v1.PropertyStatus.
 */
export const PropertyStatusSchema: GenEnum<PropertyStatus> = /*@__PURE__*/
  enumDesc(file_hero_property_v1_property, 1);

/**
 * PropertyDisposalType enumerates how property was disposed
 *
 * @generated from enum hero.property.v1.PropertyDisposalType
 */
export enum PropertyDisposalType {
  /**
   * @generated from enum value: PROPERTY_DISPOSAL_TYPE_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: PROPERTY_DISPOSAL_TYPE_RELEASED = 1;
   */
  RELEASED = 1,

  /**
   * @generated from enum value: PROPERTY_DISPOSAL_TYPE_DESTROYED = 2;
   */
  DESTROYED = 2,

  /**
   * @generated from enum value: PROPERTY_DISPOSAL_TYPE_AUCTIONED = 3;
   */
  AUCTIONED = 3,

  /**
   * @generated from enum value: PROPERTY_DISPOSAL_TYPE_AGENCY_RETAIN = 4;
   */
  AGENCY_RETAIN = 4,

  /**
   * @generated from enum value: PROPERTY_DISPOSAL_TYPE_TRANSFERRED = 5;
   */
  TRANSFERRED = 5,
}

/**
 * Describes the enum hero.property.v1.PropertyDisposalType.
 */
export const PropertyDisposalTypeSchema: GenEnum<PropertyDisposalType> = /*@__PURE__*/
  enumDesc(file_hero_property_v1_property, 2);

/**
 * CustodyActionType enumerates the types of custody changes
 *
 * @generated from enum hero.property.v1.CustodyActionType
 */
export enum CustodyActionType {
  /**
   * @generated from enum value: CUSTODY_ACTION_TYPE_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * Property initially collected into custody
   *
   * @generated from enum value: CUSTODY_ACTION_TYPE_COLLECTED = 1;
   */
  COLLECTED = 1,

  /**
   * Property checked into evidence room
   *
   * @generated from enum value: CUSTODY_ACTION_TYPE_CHECKED_IN = 2;
   */
  CHECKED_IN = 2,

  /**
   * Property checked out for analysis/court
   *
   * @generated from enum value: CUSTODY_ACTION_TYPE_CHECKED_OUT = 3;
   */
  CHECKED_OUT = 3,

  /**
   * Property transferred to another agency/person
   *
   * @generated from enum value: CUSTODY_ACTION_TYPE_TRANSFERRED = 4;
   */
  TRANSFERRED = 4,

  /**
   * Property released to owner
   *
   * @generated from enum value: CUSTODY_ACTION_TYPE_RELEASED = 5;
   */
  RELEASED = 5,

  /**
   * Property disposed/destroyed
   *
   * @generated from enum value: CUSTODY_ACTION_TYPE_DISPOSED = 6;
   */
  DISPOSED = 6,

  /**
   * Property reported but not in physical custody
   *
   * @generated from enum value: CUSTODY_ACTION_TYPE_LOGGED = 7;
   */
  LOGGED = 7,

  /**
   * Property moved to a new location
   *
   * @generated from enum value: CUSTODY_ACTION_TYPE_MOVED = 8;
   */
  MOVED = 8,
}

/**
 * Describes the enum hero.property.v1.CustodyActionType.
 */
export const CustodyActionTypeSchema: GenEnum<CustodyActionType> = /*@__PURE__*/
  enumDesc(file_hero_property_v1_property, 3);

/**
 * EntityType enumerates the types of entities that can receive property
 *
 * @generated from enum hero.property.v1.CustodyEntityType
 */
export enum CustodyEntityType {
  /**
   * @generated from enum value: CUSTODY_ENTITY_TYPE_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: CUSTODY_ENTITY_TYPE_PERSON = 1;
   */
  PERSON = 1,

  /**
   * @generated from enum value: CUSTODY_ENTITY_TYPE_CRIME_LAB = 2;
   */
  CRIME_LAB = 2,

  /**
   * @generated from enum value: CUSTODY_ENTITY_TYPE_COURT = 3;
   */
  COURT = 3,

  /**
   * @generated from enum value: CUSTODY_ENTITY_TYPE_SPECIALIST = 4;
   */
  SPECIALIST = 4,

  /**
   * @generated from enum value: CUSTODY_ENTITY_TYPE_CSI = 5;
   */
  CSI = 5,

  /**
   * @generated from enum value: CUSTODY_ENTITY_TYPE_ORGANIZATION = 6;
   */
  ORGANIZATION = 6,

  /**
   * @generated from enum value: CUSTODY_ENTITY_TYPE_OTHER = 7;
   */
  OTHER = 7,
}

/**
 * Describes the enum hero.property.v1.CustodyEntityType.
 */
export const CustodyEntityTypeSchema: GenEnum<CustodyEntityType> = /*@__PURE__*/
  enumDesc(file_hero_property_v1_property, 4);

/**
 * AgencyType enumerates the types of agencies that can receive property
 *
 * @generated from enum hero.property.v1.CustodyAgencyType
 */
export enum CustodyAgencyType {
  /**
   * @generated from enum value: CUSTODY_AGENCY_TYPE_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * Police Department
   *
   * @generated from enum value: CUSTODY_AGENCY_TYPE_PD = 1;
   */
  PD = 1,

  /**
   * Other agency type
   *
   * @generated from enum value: CUSTODY_AGENCY_TYPE_OTHER = 2;
   */
  OTHER = 2,
}

/**
 * Describes the enum hero.property.v1.CustodyAgencyType.
 */
export const CustodyAgencyTypeSchema: GenEnum<CustodyAgencyType> = /*@__PURE__*/
  enumDesc(file_hero_property_v1_property, 5);

/**
 * NIBRSPropertyDescription enumerates NIBRS property description codes
 *
 * @generated from enum hero.property.v1.NIBRSPropertyDescription
 */
export enum NIBRSPropertyDescription {
  /**
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_UNSPECIFIED = 0;
   */
  NIBRS_PROPERTY_DESCRIPTION_UNSPECIFIED = 0,

  /**
   * 01 = Aircraft
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_AIRCRAFT = 1;
   */
  NIBRS_PROPERTY_DESCRIPTION_AIRCRAFT = 1,

  /**
   * 02 = Alcohol  
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_ALCOHOL = 2;
   */
  NIBRS_PROPERTY_DESCRIPTION_ALCOHOL = 2,

  /**
   * 03 = Automobiles
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_AUTOMOBILES = 3;
   */
  NIBRS_PROPERTY_DESCRIPTION_AUTOMOBILES = 3,

  /**
   * 04 = Bicycles
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_BICYCLES = 4;
   */
  NIBRS_PROPERTY_DESCRIPTION_BICYCLES = 4,

  /**
   * 05 = Buses
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_BUSES = 5;
   */
  NIBRS_PROPERTY_DESCRIPTION_BUSES = 5,

  /**
   * 06 = Clothes/Furs
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_CLOTHES_FURS = 6;
   */
  NIBRS_PROPERTY_DESCRIPTION_CLOTHES_FURS = 6,

  /**
   * 07 = Computer Hardware/Software
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_COMPUTER_HARDWARE_SOFTWARE = 7;
   */
  NIBRS_PROPERTY_DESCRIPTION_COMPUTER_HARDWARE_SOFTWARE = 7,

  /**
   * 08 = Consumable Goods
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_CONSUMABLE_GOODS = 8;
   */
  NIBRS_PROPERTY_DESCRIPTION_CONSUMABLE_GOODS = 8,

  /**
   * 09 = Credit/Debit Cards
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_CREDIT_DEBIT_CARDS = 9;
   */
  NIBRS_PROPERTY_DESCRIPTION_CREDIT_DEBIT_CARDS = 9,

  /**
   * 10 = Drugs/Narcotics
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_DRUGS_NARCOTICS = 10;
   */
  NIBRS_PROPERTY_DESCRIPTION_DRUGS_NARCOTICS = 10,

  /**
   * 11 = Drug/Narcotic Equipment
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_DRUG_NARCOTIC_EQUIPMENT = 11;
   */
  NIBRS_PROPERTY_DESCRIPTION_DRUG_NARCOTIC_EQUIPMENT = 11,

  /**
   * 12 = Farm Equipment
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_FARM_EQUIPMENT = 12;
   */
  NIBRS_PROPERTY_DESCRIPTION_FARM_EQUIPMENT = 12,

  /**
   * 13 = Firearms
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_FIREARMS = 13;
   */
  NIBRS_PROPERTY_DESCRIPTION_FIREARMS = 13,

  /**
   * 14 = Gambling Equipment
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_GAMBLING_EQUIPMENT = 14;
   */
  NIBRS_PROPERTY_DESCRIPTION_GAMBLING_EQUIPMENT = 14,

  /**
   * 15 = Heavy Construction/Industrial Equipment
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_HEAVY_CONSTRUCTION_INDUSTRIAL_EQUIPMENT = 15;
   */
  NIBRS_PROPERTY_DESCRIPTION_HEAVY_CONSTRUCTION_INDUSTRIAL_EQUIPMENT = 15,

  /**
   * 16 = Household Goods
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_HOUSEHOLD_GOODS = 16;
   */
  NIBRS_PROPERTY_DESCRIPTION_HOUSEHOLD_GOODS = 16,

  /**
   * 17 = Jewelry/Precious Metals/Gems
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_JEWELRY_PRECIOUS_METALS_GEMS = 17;
   */
  NIBRS_PROPERTY_DESCRIPTION_JEWELRY_PRECIOUS_METALS_GEMS = 17,

  /**
   * 18 = Livestock
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_LIVESTOCK = 18;
   */
  NIBRS_PROPERTY_DESCRIPTION_LIVESTOCK = 18,

  /**
   * 19 = Merchandise
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_MERCHANDISE = 19;
   */
  NIBRS_PROPERTY_DESCRIPTION_MERCHANDISE = 19,

  /**
   * 20 = Money
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_MONEY = 20;
   */
  NIBRS_PROPERTY_DESCRIPTION_MONEY = 20,

  /**
   * 21 = Negotiable Instruments
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_NEGOTIABLE_INSTRUMENTS = 21;
   */
  NIBRS_PROPERTY_DESCRIPTION_NEGOTIABLE_INSTRUMENTS = 21,

  /**
   * 22 = Nonnegotiable Instruments
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_NONNEGOTIABLE_INSTRUMENTS = 22;
   */
  NIBRS_PROPERTY_DESCRIPTION_NONNEGOTIABLE_INSTRUMENTS = 22,

  /**
   * 23 = Office-type Equipment
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_OFFICE_TYPE_EQUIPMENT = 23;
   */
  NIBRS_PROPERTY_DESCRIPTION_OFFICE_TYPE_EQUIPMENT = 23,

  /**
   * 24 = Other Motor Vehicles
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_OTHER_MOTOR_VEHICLES = 24;
   */
  NIBRS_PROPERTY_DESCRIPTION_OTHER_MOTOR_VEHICLES = 24,

  /**
   * 25 = Purses/Handbags/Wallets
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_PURSES_HANDBAGS_WALLETS = 25;
   */
  NIBRS_PROPERTY_DESCRIPTION_PURSES_HANDBAGS_WALLETS = 25,

  /**
   * 26 = Radios/TVs/VCRs
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_RADIOS_TVS_VCRS = 26;
   */
  NIBRS_PROPERTY_DESCRIPTION_RADIOS_TVS_VCRS = 26,

  /**
   * 27 = Recordings–Audio/Visual
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_RECORDINGS_AUDIO_VISUAL = 27;
   */
  NIBRS_PROPERTY_DESCRIPTION_RECORDINGS_AUDIO_VISUAL = 27,

  /**
   * 28 = Recreational Vehicles
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_RECREATIONAL_VEHICLES = 28;
   */
  NIBRS_PROPERTY_DESCRIPTION_RECREATIONAL_VEHICLES = 28,

  /**
   * 29 = Structures–Single Occupancy Dwellings
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_SINGLE_OCCUPANCY_DWELLINGS = 29;
   */
  NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_SINGLE_OCCUPANCY_DWELLINGS = 29,

  /**
   * 30 = Structures–Other Dwellings
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_OTHER_DWELLINGS = 30;
   */
  NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_OTHER_DWELLINGS = 30,

  /**
   * 31 = Structures–Other Commercial/Business
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_OTHER_COMMERCIAL_BUSINESS = 31;
   */
  NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_OTHER_COMMERCIAL_BUSINESS = 31,

  /**
   * 32 = Structures–Industrial/Manufacturing
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_INDUSTRIAL_MANUFACTURING = 32;
   */
  NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_INDUSTRIAL_MANUFACTURING = 32,

  /**
   * 33 = Structures–Public/Community
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_PUBLIC_COMMUNITY = 33;
   */
  NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_PUBLIC_COMMUNITY = 33,

  /**
   * 34 = Structures–Storage
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_STORAGE = 34;
   */
  NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_STORAGE = 34,

  /**
   * 35 = Structures–Other
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_OTHER = 35;
   */
  NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_OTHER = 35,

  /**
   * 36 = Tools
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_TOOLS = 36;
   */
  NIBRS_PROPERTY_DESCRIPTION_TOOLS = 36,

  /**
   * 37 = Trucks
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_TRUCKS = 37;
   */
  NIBRS_PROPERTY_DESCRIPTION_TRUCKS = 37,

  /**
   * 38 = Vehicle Parts/Accessories
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_VEHICLE_PARTS_ACCESSORIES = 38;
   */
  NIBRS_PROPERTY_DESCRIPTION_VEHICLE_PARTS_ACCESSORIES = 38,

  /**
   * 39 = Watercraft
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_WATERCRAFT = 39;
   */
  NIBRS_PROPERTY_DESCRIPTION_WATERCRAFT = 39,

  /**
   * 41 = Aircraft Parts/Accessories
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_AIRCRAFT_PARTS_ACCESSORIES = 41;
   */
  NIBRS_PROPERTY_DESCRIPTION_AIRCRAFT_PARTS_ACCESSORIES = 41,

  /**
   * 42 = Artistic Supplies/Accessories
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_ARTISTIC_SUPPLIES_ACCESSORIES = 42;
   */
  NIBRS_PROPERTY_DESCRIPTION_ARTISTIC_SUPPLIES_ACCESSORIES = 42,

  /**
   * 43 = Building Materials
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_BUILDING_MATERIALS = 43;
   */
  NIBRS_PROPERTY_DESCRIPTION_BUILDING_MATERIALS = 43,

  /**
   * 44 = Camping/Hunting/Fishing Equipment/Supplies
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_CAMPING_HUNTING_FISHING_EQUIPMENT = 44;
   */
  NIBRS_PROPERTY_DESCRIPTION_CAMPING_HUNTING_FISHING_EQUIPMENT = 44,

  /**
   * 45 = Chemicals
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_CHEMICALS = 45;
   */
  NIBRS_PROPERTY_DESCRIPTION_CHEMICALS = 45,

  /**
   * 46 = Collections/Collectibles
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_COLLECTIONS_COLLECTIBLES = 46;
   */
  NIBRS_PROPERTY_DESCRIPTION_COLLECTIONS_COLLECTIBLES = 46,

  /**
   * 47 = Crops
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_CROPS = 47;
   */
  NIBRS_PROPERTY_DESCRIPTION_CROPS = 47,

  /**
   * 48 = Documents/Personal or Business
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_DOCUMENTS_PERSONAL_OR_BUSINESS = 48;
   */
  NIBRS_PROPERTY_DESCRIPTION_DOCUMENTS_PERSONAL_OR_BUSINESS = 48,

  /**
   * 49 = Explosives
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_EXPLOSIVES = 49;
   */
  NIBRS_PROPERTY_DESCRIPTION_EXPLOSIVES = 49,

  /**
   * 59 = Firearm Accessories
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_FIREARM_ACCESSORIES = 59;
   */
  NIBRS_PROPERTY_DESCRIPTION_FIREARM_ACCESSORIES = 59,

  /**
   * 64 = Fuel
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_FUEL = 64;
   */
  NIBRS_PROPERTY_DESCRIPTION_FUEL = 64,

  /**
   * 65 = Identity Documents
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_IDENTITY_DOCUMENTS = 65;
   */
  NIBRS_PROPERTY_DESCRIPTION_IDENTITY_DOCUMENTS = 65,

  /**
   * 66 = Identity–Intangible
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_IDENTITY_INTANGIBLE = 66;
   */
  NIBRS_PROPERTY_DESCRIPTION_IDENTITY_INTANGIBLE = 66,

  /**
   * 67 = Law Enforcement Equipment
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_LAW_ENFORCEMENT_EQUIPMENT = 67;
   */
  NIBRS_PROPERTY_DESCRIPTION_LAW_ENFORCEMENT_EQUIPMENT = 67,

  /**
   * 68 = Lawn/Yard/Garden Equipment
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_LAWN_YARD_GARDEN_EQUIPMENT = 68;
   */
  NIBRS_PROPERTY_DESCRIPTION_LAWN_YARD_GARDEN_EQUIPMENT = 68,

  /**
   * 69 = Logging Equipment
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_LOGGING_EQUIPMENT = 69;
   */
  NIBRS_PROPERTY_DESCRIPTION_LOGGING_EQUIPMENT = 69,

  /**
   * 70 = Medical/Medical Lab Equipment
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_MEDICAL_MEDICAL_LAB_EQUIPMENT = 70;
   */
  NIBRS_PROPERTY_DESCRIPTION_MEDICAL_MEDICAL_LAB_EQUIPMENT = 70,

  /**
   * 71 = Metals, Non-Precious
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_METALS_NON_PRECIOUS = 71;
   */
  NIBRS_PROPERTY_DESCRIPTION_METALS_NON_PRECIOUS = 71,

  /**
   * 72 = Musical Instruments
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_MUSICAL_INSTRUMENTS = 72;
   */
  NIBRS_PROPERTY_DESCRIPTION_MUSICAL_INSTRUMENTS = 72,

  /**
   * 73 = Pets
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_PETS = 73;
   */
  NIBRS_PROPERTY_DESCRIPTION_PETS = 73,

  /**
   * 74 = Photographic/Optical Equipment
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_PHOTOGRAPHIC_OPTICAL_EQUIPMENT = 74;
   */
  NIBRS_PROPERTY_DESCRIPTION_PHOTOGRAPHIC_OPTICAL_EQUIPMENT = 74,

  /**
   * 75 = Portable Electronic Communications
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_PORTABLE_ELECTRONIC_COMMUNICATIONS = 75;
   */
  NIBRS_PROPERTY_DESCRIPTION_PORTABLE_ELECTRONIC_COMMUNICATIONS = 75,

  /**
   * 76 = Recreational/Sports Equipment
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_RECREATIONAL_SPORTS_EQUIPMENT = 76;
   */
  NIBRS_PROPERTY_DESCRIPTION_RECREATIONAL_SPORTS_EQUIPMENT = 76,

  /**
   * 77 = Other
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_OTHER = 77;
   */
  NIBRS_PROPERTY_DESCRIPTION_OTHER = 77,

  /**
   * 78 = Trailers
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_TRAILERS = 78;
   */
  NIBRS_PROPERTY_DESCRIPTION_TRAILERS = 78,

  /**
   * 79 = Watercraft Equipment/Parts/Accessories
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_WATERCRAFT_EQUIPMENT_PARTS_ACCESSORIES = 79;
   */
  NIBRS_PROPERTY_DESCRIPTION_WATERCRAFT_EQUIPMENT_PARTS_ACCESSORIES = 79,

  /**
   * 80 = Weapons–Other
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_WEAPONS_OTHER = 80;
   */
  NIBRS_PROPERTY_DESCRIPTION_WEAPONS_OTHER = 80,

  /**
   * 88 = Pending Inventory
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_PENDING_INVENTORY = 88;
   */
  NIBRS_PROPERTY_DESCRIPTION_PENDING_INVENTORY = 88,

  /**
   * 99 = ( blank )
   *
   * @generated from enum value: NIBRS_PROPERTY_DESCRIPTION_BLANK = 99;
   */
  NIBRS_PROPERTY_DESCRIPTION_BLANK = 99,
}

/**
 * Describes the enum hero.property.v1.NIBRSPropertyDescription.
 */
export const NIBRSPropertyDescriptionSchema: GenEnum<NIBRSPropertyDescription> = /*@__PURE__*/
  enumDesc(file_hero_property_v1_property, 6);

/**
 * SearchOrderBy defines the ordering options for search results
 *
 * @generated from enum hero.property.v1.SearchOrderBy
 */
export enum SearchOrderBy {
  /**
   * @generated from enum value: SEARCH_ORDER_BY_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: SEARCH_ORDER_BY_RELEVANCE = 1;
   */
  RELEVANCE = 1,

  /**
   * @generated from enum value: SEARCH_ORDER_BY_CREATED_AT = 2;
   */
  CREATED_AT = 2,

  /**
   * @generated from enum value: SEARCH_ORDER_BY_UPDATED_AT = 3;
   */
  UPDATED_AT = 3,

  /**
   * @generated from enum value: SEARCH_ORDER_BY_STATUS = 4;
   */
  STATUS = 4,
}

/**
 * Describes the enum hero.property.v1.SearchOrderBy.
 */
export const SearchOrderBySchema: GenEnum<SearchOrderBy> = /*@__PURE__*/
  enumDesc(file_hero_property_v1_property, 7);

/**
 * PropertyService defines operations for managing property and evidence items.
 * This service handles chain of custody, property status tracking, and evidence management.
 *
 * @generated from service hero.property.v1.PropertyService
 */
export const PropertyService: GenService<{
  /**
   * CreateProperty creates a new property
   *
   * @generated from rpc hero.property.v1.PropertyService.CreateProperty
   */
  createProperty: {
    methodKind: "unary";
    input: typeof CreatePropertyRequestSchema;
    output: typeof CreatePropertyResponseSchema;
  },
  /**
   * GetProperty retrieves a property by its ID
   *
   * @generated from rpc hero.property.v1.PropertyService.GetProperty
   */
  getProperty: {
    methodKind: "unary";
    input: typeof GetPropertyRequestSchema;
    output: typeof GetPropertyResponseSchema;
  },
  /**
   * ListProperties lists properties with pagination and filtering
   *
   * @generated from rpc hero.property.v1.PropertyService.ListProperties
   */
  listProperties: {
    methodKind: "unary";
    input: typeof ListPropertiesRequestSchema;
    output: typeof ListPropertiesResponseSchema;
  },
  /**
   * UpdateProperty updates an existing property
   *
   * @generated from rpc hero.property.v1.PropertyService.UpdateProperty
   */
  updateProperty: {
    methodKind: "unary";
    input: typeof UpdatePropertyRequestSchema;
    output: typeof UpdatePropertyResponseSchema;
  },
  /**
   * DeleteProperty deletes a property
   *
   * @generated from rpc hero.property.v1.PropertyService.DeleteProperty
   */
  deleteProperty: {
    methodKind: "unary";
    input: typeof DeletePropertyRequestSchema;
    output: typeof DeletePropertyResponseSchema;
  },
  /**
   * SearchProperties performs advanced search on properties
   *
   * @generated from rpc hero.property.v1.PropertyService.SearchProperties
   */
  searchProperties: {
    methodKind: "unary";
    input: typeof SearchPropertiesRequestSchema;
    output: typeof SearchPropertiesResponseSchema;
  },
  /**
   * BatchGetProperties retrieves multiple properties by their IDs
   *
   * @generated from rpc hero.property.v1.PropertyService.BatchGetProperties
   */
  batchGetProperties: {
    methodKind: "unary";
    input: typeof BatchGetPropertiesRequestSchema;
    output: typeof BatchGetPropertiesResponseSchema;
  },
  /**
   * AddCustodyEvent adds a new custody event to a property's chain of custody
   *
   * @generated from rpc hero.property.v1.PropertyService.AddCustodyEvent
   */
  addCustodyEvent: {
    methodKind: "unary";
    input: typeof AddCustodyEventRequestSchema;
    output: typeof AddCustodyEventResponseSchema;
  },
  /**
   * GetCustodyChain returns the complete chain of custody for a property
   *
   * @generated from rpc hero.property.v1.PropertyService.GetCustodyChain
   */
  getCustodyChain: {
    methodKind: "unary";
    input: typeof GetCustodyChainRequestSchema;
    output: typeof GetCustodyChainResponseSchema;
  },
  /**
   * ListPropertyFileAttachments lists all file attachments for a property
   *
   * @generated from rpc hero.property.v1.PropertyService.ListPropertyFileAttachments
   */
  listPropertyFileAttachments: {
    methodKind: "unary";
    input: typeof ListPropertyFileAttachmentsRequestSchema;
    output: typeof ListPropertyFileAttachmentsResponseSchema;
  },
  /**
   * AddPropertyFileAttachment adds a file attachment to a property
   *
   * @generated from rpc hero.property.v1.PropertyService.AddPropertyFileAttachment
   */
  addPropertyFileAttachment: {
    methodKind: "unary";
    input: typeof AddPropertyFileAttachmentRequestSchema;
    output: typeof AddPropertyFileAttachmentResponseSchema;
  },
  /**
   * RemovePropertyFileAttachment removes a file attachment from a property
   *
   * @generated from rpc hero.property.v1.PropertyService.RemovePropertyFileAttachment
   */
  removePropertyFileAttachment: {
    methodKind: "unary";
    input: typeof RemovePropertyFileAttachmentRequestSchema;
    output: typeof RemovePropertyFileAttachmentResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_hero_property_v1_property, 0);

