// @generated by protoc-gen-es v2.7.0 with parameter "target=ts"
// @generated from file hero/featureflags/v1/featureflags.proto (package hero.featureflags.v1, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file hero/featureflags/v1/featureflags.proto.
 */
export const file_hero_featureflags_v1_featureflags: GenFile = /*@__PURE__*/
  fileDesc("CidoZXJvL2ZlYXR1cmVmbGFncy92MS9mZWF0dXJlZmxhZ3MucHJvdG8SFGhlcm8uZmVhdHVyZWZsYWdzLnYxImQKEElzRW5hYmxlZFJlcXVlc3QSDgoGb3JnX2lkGAEgASgFEi4KB2ZlYXR1cmUYAiABKA4yHS5oZXJvLmZlYXR1cmVmbGFncy52MS5GZWF0dXJlEhAKCGFzc2V0X2lkGAMgASgJIkcKEUlzRW5hYmxlZFJlc3BvbnNlEg8KB2VuYWJsZWQYASABKAgSEQoJZXZhbHVhdGVkGAIgASgIEg4KBnJlYXNvbhgDIAEoCSJ8ChdTZXRGZWF0dXJlVGFyZ2V0UmVxdWVzdBIOCgZvcmdfaWQYASABKAUSLgoHZmVhdHVyZRgCIAEoDjIdLmhlcm8uZmVhdHVyZWZsYWdzLnYxLkZlYXR1cmUSEAoIYXNzZXRfaWQYAyABKAkSDwoHZW5hYmxlZBgEIAEoCCIrChhTZXRGZWF0dXJlVGFyZ2V0UmVzcG9uc2USDwoHc3VjY2VzcxgBIAEoCCKvAQoaU2V0TXVsdGlwbGVGZWF0dXJlc1JlcXVlc3QSDgoGb3JnX2lkGAEgASgFEhAKCGFzc2V0X2lkGAIgASgJEjYKD2VuYWJsZV9mZWF0dXJlcxgDIAMoDjIdLmhlcm8uZmVhdHVyZWZsYWdzLnYxLkZlYXR1cmUSNwoQZGlzYWJsZV9mZWF0dXJlcxgEIAMoDjIdLmhlcm8uZmVhdHVyZWZsYWdzLnYxLkZlYXR1cmUiLgobU2V0TXVsdGlwbGVGZWF0dXJlc1Jlc3BvbnNlEg8KB3N1Y2Nlc3MYASABKAgiPQoZR2V0RW5hYmxlZEZlYXR1cmVzUmVxdWVzdBIOCgZvcmdfaWQYASABKAUSEAoIYXNzZXRfaWQYAiABKAkiVQoaR2V0RW5hYmxlZEZlYXR1cmVzUmVzcG9uc2USNwoQZW5hYmxlZF9mZWF0dXJlcxgBIAMoDjIdLmhlcm8uZmVhdHVyZWZsYWdzLnYxLkZlYXR1cmUqiQEKB0ZlYXR1cmUSFwoTRkVBVFVSRV9VTlNQRUNJRklFRBAAEh8KG0ZFQVRVUkVfRVhQRVJJTUVOVEFMX0NBTUVSQRABEiUKIUZFQVRVUkVfRVhQRVJJTUVOVEFMX1BVU0hfVE9fVEFMSxACEh0KGUZFQVRVUkVfRVhQRVJJTUVOVEFMX0RFTU8QAzLjAwoTRmVhdHVyZUZsYWdzU2VydmljZRJeCglJc0VuYWJsZWQSJi5oZXJvLmZlYXR1cmVmbGFncy52MS5Jc0VuYWJsZWRSZXF1ZXN0GicuaGVyby5mZWF0dXJlZmxhZ3MudjEuSXNFbmFibGVkUmVzcG9uc2UiABJzChBTZXRGZWF0dXJlVGFyZ2V0Ei0uaGVyby5mZWF0dXJlZmxhZ3MudjEuU2V0RmVhdHVyZVRhcmdldFJlcXVlc3QaLi5oZXJvLmZlYXR1cmVmbGFncy52MS5TZXRGZWF0dXJlVGFyZ2V0UmVzcG9uc2UiABJ8ChNTZXRNdWx0aXBsZUZlYXR1cmVzEjAuaGVyby5mZWF0dXJlZmxhZ3MudjEuU2V0TXVsdGlwbGVGZWF0dXJlc1JlcXVlc3QaMS5oZXJvLmZlYXR1cmVmbGFncy52MS5TZXRNdWx0aXBsZUZlYXR1cmVzUmVzcG9uc2UiABJ5ChJHZXRFbmFibGVkRmVhdHVyZXMSLy5oZXJvLmZlYXR1cmVmbGFncy52MS5HZXRFbmFibGVkRmVhdHVyZXNSZXF1ZXN0GjAuaGVyby5mZWF0dXJlZmxhZ3MudjEuR2V0RW5hYmxlZEZlYXR1cmVzUmVzcG9uc2UiAEIpWidwcm90by9oZXJvL2ZlYXR1cmVmbGFncy92MTtmZWF0dXJlZmxhZ3NiBnByb3RvMw");

/**
 * Request/Response messages
 *
 * @generated from message hero.featureflags.v1.IsEnabledRequest
 */
export type IsEnabledRequest = Message<"hero.featureflags.v1.IsEnabledRequest"> & {
  /**
   * @generated from field: int32 org_id = 1;
   */
  orgId: number;

  /**
   * @generated from field: hero.featureflags.v1.Feature feature = 2;
   */
  feature: Feature;

  /**
   * Optional: check for specific asset/user
   *
   * @generated from field: string asset_id = 3;
   */
  assetId: string;
};

/**
 * Describes the message hero.featureflags.v1.IsEnabledRequest.
 * Use `create(IsEnabledRequestSchema)` to create a new message.
 */
export const IsEnabledRequestSchema: GenMessage<IsEnabledRequest> = /*@__PURE__*/
  messageDesc(file_hero_featureflags_v1_featureflags, 0);

/**
 * @generated from message hero.featureflags.v1.IsEnabledResponse
 */
export type IsEnabledResponse = Message<"hero.featureflags.v1.IsEnabledResponse"> & {
  /**
   * @generated from field: bool enabled = 1;
   */
  enabled: boolean;

  /**
   * Always true to indicate evaluation happened
   *
   * @generated from field: bool evaluated = 2;
   */
  evaluated: boolean;

  /**
   * "default", "org_setting", "asset_setting"
   *
   * @generated from field: string reason = 3;
   */
  reason: string;
};

/**
 * Describes the message hero.featureflags.v1.IsEnabledResponse.
 * Use `create(IsEnabledResponseSchema)` to create a new message.
 */
export const IsEnabledResponseSchema: GenMessage<IsEnabledResponse> = /*@__PURE__*/
  messageDesc(file_hero_featureflags_v1_featureflags, 1);

/**
 * @generated from message hero.featureflags.v1.SetFeatureTargetRequest
 */
export type SetFeatureTargetRequest = Message<"hero.featureflags.v1.SetFeatureTargetRequest"> & {
  /**
   * @generated from field: int32 org_id = 1;
   */
  orgId: number;

  /**
   * @generated from field: hero.featureflags.v1.Feature feature = 2;
   */
  feature: Feature;

  /**
   * Empty string means org-wide setting
   *
   * @generated from field: string asset_id = 3;
   */
  assetId: string;

  /**
   * @generated from field: bool enabled = 4;
   */
  enabled: boolean;
};

/**
 * Describes the message hero.featureflags.v1.SetFeatureTargetRequest.
 * Use `create(SetFeatureTargetRequestSchema)` to create a new message.
 */
export const SetFeatureTargetRequestSchema: GenMessage<SetFeatureTargetRequest> = /*@__PURE__*/
  messageDesc(file_hero_featureflags_v1_featureflags, 2);

/**
 * @generated from message hero.featureflags.v1.SetFeatureTargetResponse
 */
export type SetFeatureTargetResponse = Message<"hero.featureflags.v1.SetFeatureTargetResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;
};

/**
 * Describes the message hero.featureflags.v1.SetFeatureTargetResponse.
 * Use `create(SetFeatureTargetResponseSchema)` to create a new message.
 */
export const SetFeatureTargetResponseSchema: GenMessage<SetFeatureTargetResponse> = /*@__PURE__*/
  messageDesc(file_hero_featureflags_v1_featureflags, 3);

/**
 * @generated from message hero.featureflags.v1.SetMultipleFeaturesRequest
 */
export type SetMultipleFeaturesRequest = Message<"hero.featureflags.v1.SetMultipleFeaturesRequest"> & {
  /**
   * @generated from field: int32 org_id = 1;
   */
  orgId: number;

  /**
   * Empty string means org-wide setting
   *
   * @generated from field: string asset_id = 2;
   */
  assetId: string;

  /**
   * Features to enable
   *
   * @generated from field: repeated hero.featureflags.v1.Feature enable_features = 3;
   */
  enableFeatures: Feature[];

  /**
   * Features to disable
   *
   * @generated from field: repeated hero.featureflags.v1.Feature disable_features = 4;
   */
  disableFeatures: Feature[];
};

/**
 * Describes the message hero.featureflags.v1.SetMultipleFeaturesRequest.
 * Use `create(SetMultipleFeaturesRequestSchema)` to create a new message.
 */
export const SetMultipleFeaturesRequestSchema: GenMessage<SetMultipleFeaturesRequest> = /*@__PURE__*/
  messageDesc(file_hero_featureflags_v1_featureflags, 4);

/**
 * @generated from message hero.featureflags.v1.SetMultipleFeaturesResponse
 */
export type SetMultipleFeaturesResponse = Message<"hero.featureflags.v1.SetMultipleFeaturesResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;
};

/**
 * Describes the message hero.featureflags.v1.SetMultipleFeaturesResponse.
 * Use `create(SetMultipleFeaturesResponseSchema)` to create a new message.
 */
export const SetMultipleFeaturesResponseSchema: GenMessage<SetMultipleFeaturesResponse> = /*@__PURE__*/
  messageDesc(file_hero_featureflags_v1_featureflags, 5);

/**
 * @generated from message hero.featureflags.v1.GetEnabledFeaturesRequest
 */
export type GetEnabledFeaturesRequest = Message<"hero.featureflags.v1.GetEnabledFeaturesRequest"> & {
  /**
   * @generated from field: int32 org_id = 1;
   */
  orgId: number;

  /**
   * Optional
   *
   * @generated from field: string asset_id = 2;
   */
  assetId: string;
};

/**
 * Describes the message hero.featureflags.v1.GetEnabledFeaturesRequest.
 * Use `create(GetEnabledFeaturesRequestSchema)` to create a new message.
 */
export const GetEnabledFeaturesRequestSchema: GenMessage<GetEnabledFeaturesRequest> = /*@__PURE__*/
  messageDesc(file_hero_featureflags_v1_featureflags, 6);

/**
 * @generated from message hero.featureflags.v1.GetEnabledFeaturesResponse
 */
export type GetEnabledFeaturesResponse = Message<"hero.featureflags.v1.GetEnabledFeaturesResponse"> & {
  /**
   * @generated from field: repeated hero.featureflags.v1.Feature enabled_features = 1;
   */
  enabledFeatures: Feature[];
};

/**
 * Describes the message hero.featureflags.v1.GetEnabledFeaturesResponse.
 * Use `create(GetEnabledFeaturesResponseSchema)` to create a new message.
 */
export const GetEnabledFeaturesResponseSchema: GenMessage<GetEnabledFeaturesResponse> = /*@__PURE__*/
  messageDesc(file_hero_featureflags_v1_featureflags, 7);

/**
 * Define all feature flags as an enum
 *
 * @generated from enum hero.featureflags.v1.Feature
 */
export enum Feature {
  /**
   * @generated from enum value: FEATURE_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: FEATURE_EXPERIMENTAL_CAMERA = 1;
   */
  EXPERIMENTAL_CAMERA = 1,

  /**
   * @generated from enum value: FEATURE_EXPERIMENTAL_PUSH_TO_TALK = 2;
   */
  EXPERIMENTAL_PUSH_TO_TALK = 2,

  /**
   * Add new features here as needed
   *
   * @generated from enum value: FEATURE_EXPERIMENTAL_DEMO = 3;
   */
  EXPERIMENTAL_DEMO = 3,
}

/**
 * Describes the enum hero.featureflags.v1.Feature.
 */
export const FeatureSchema: GenEnum<Feature> = /*@__PURE__*/
  enumDesc(file_hero_featureflags_v1_featureflags, 0);

/**
 * @generated from service hero.featureflags.v1.FeatureFlagsService
 */
export const FeatureFlagsService: GenService<{
  /**
   * Check if a feature is enabled
   *
   * @generated from rpc hero.featureflags.v1.FeatureFlagsService.IsEnabled
   */
  isEnabled: {
    methodKind: "unary";
    input: typeof IsEnabledRequestSchema;
    output: typeof IsEnabledResponseSchema;
  },
  /**
   * Enable/disable feature for specific targets
   *
   * @generated from rpc hero.featureflags.v1.FeatureFlagsService.SetFeatureTarget
   */
  setFeatureTarget: {
    methodKind: "unary";
    input: typeof SetFeatureTargetRequestSchema;
    output: typeof SetFeatureTargetResponseSchema;
  },
  /**
   * Bulk enable/disable multiple features for a target
   *
   * @generated from rpc hero.featureflags.v1.FeatureFlagsService.SetMultipleFeatures
   */
  setMultipleFeatures: {
    methodKind: "unary";
    input: typeof SetMultipleFeaturesRequestSchema;
    output: typeof SetMultipleFeaturesResponseSchema;
  },
  /**
   * Get all enabled features for a target
   *
   * @generated from rpc hero.featureflags.v1.FeatureFlagsService.GetEnabledFeatures
   */
  getEnabledFeatures: {
    methodKind: "unary";
    input: typeof GetEnabledFeaturesRequestSchema;
    output: typeof GetEnabledFeaturesResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_hero_featureflags_v1_featureflags, 0);

