// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: hero/featureflags/v1/featureflags.proto

package featureflagsconnect

import (
	context "context"
	errors "errors"
	http "net/http"
	v1 "proto/hero/featureflags/v1"
	strings "strings"

	connect "connectrpc.com/connect"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// FeatureFlagsServiceName is the fully-qualified name of the FeatureFlagsService service.
	FeatureFlagsServiceName = "hero.featureflags.v1.FeatureFlagsService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// FeatureFlagsServiceIsEnabledProcedure is the fully-qualified name of the FeatureFlagsService's
	// IsEnabled RPC.
	FeatureFlagsServiceIsEnabledProcedure = "/hero.featureflags.v1.FeatureFlagsService/IsEnabled"
	// FeatureFlagsServiceSetFeatureTargetProcedure is the fully-qualified name of the
	// FeatureFlagsService's SetFeatureTarget RPC.
	FeatureFlagsServiceSetFeatureTargetProcedure = "/hero.featureflags.v1.FeatureFlagsService/SetFeatureTarget"
	// FeatureFlagsServiceSetMultipleFeaturesProcedure is the fully-qualified name of the
	// FeatureFlagsService's SetMultipleFeatures RPC.
	FeatureFlagsServiceSetMultipleFeaturesProcedure = "/hero.featureflags.v1.FeatureFlagsService/SetMultipleFeatures"
	// FeatureFlagsServiceGetEnabledFeaturesProcedure is the fully-qualified name of the
	// FeatureFlagsService's GetEnabledFeatures RPC.
	FeatureFlagsServiceGetEnabledFeaturesProcedure = "/hero.featureflags.v1.FeatureFlagsService/GetEnabledFeatures"
)

// FeatureFlagsServiceClient is a client for the hero.featureflags.v1.FeatureFlagsService service.
type FeatureFlagsServiceClient interface {
	// Check if a feature is enabled
	IsEnabled(context.Context, *connect.Request[v1.IsEnabledRequest]) (*connect.Response[v1.IsEnabledResponse], error)
	// Enable/disable feature for specific targets
	SetFeatureTarget(context.Context, *connect.Request[v1.SetFeatureTargetRequest]) (*connect.Response[v1.SetFeatureTargetResponse], error)
	// Bulk enable/disable multiple features for a target
	SetMultipleFeatures(context.Context, *connect.Request[v1.SetMultipleFeaturesRequest]) (*connect.Response[v1.SetMultipleFeaturesResponse], error)
	// Get all enabled features for a target
	GetEnabledFeatures(context.Context, *connect.Request[v1.GetEnabledFeaturesRequest]) (*connect.Response[v1.GetEnabledFeaturesResponse], error)
}

// NewFeatureFlagsServiceClient constructs a client for the hero.featureflags.v1.FeatureFlagsService
// service. By default, it uses the Connect protocol with the binary Protobuf Codec, asks for
// gzipped responses, and sends uncompressed requests. To use the gRPC or gRPC-Web protocols, supply
// the connect.WithGRPC() or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewFeatureFlagsServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) FeatureFlagsServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	featureFlagsServiceMethods := v1.File_hero_featureflags_v1_featureflags_proto.Services().ByName("FeatureFlagsService").Methods()
	return &featureFlagsServiceClient{
		isEnabled: connect.NewClient[v1.IsEnabledRequest, v1.IsEnabledResponse](
			httpClient,
			baseURL+FeatureFlagsServiceIsEnabledProcedure,
			connect.WithSchema(featureFlagsServiceMethods.ByName("IsEnabled")),
			connect.WithClientOptions(opts...),
		),
		setFeatureTarget: connect.NewClient[v1.SetFeatureTargetRequest, v1.SetFeatureTargetResponse](
			httpClient,
			baseURL+FeatureFlagsServiceSetFeatureTargetProcedure,
			connect.WithSchema(featureFlagsServiceMethods.ByName("SetFeatureTarget")),
			connect.WithClientOptions(opts...),
		),
		setMultipleFeatures: connect.NewClient[v1.SetMultipleFeaturesRequest, v1.SetMultipleFeaturesResponse](
			httpClient,
			baseURL+FeatureFlagsServiceSetMultipleFeaturesProcedure,
			connect.WithSchema(featureFlagsServiceMethods.ByName("SetMultipleFeatures")),
			connect.WithClientOptions(opts...),
		),
		getEnabledFeatures: connect.NewClient[v1.GetEnabledFeaturesRequest, v1.GetEnabledFeaturesResponse](
			httpClient,
			baseURL+FeatureFlagsServiceGetEnabledFeaturesProcedure,
			connect.WithSchema(featureFlagsServiceMethods.ByName("GetEnabledFeatures")),
			connect.WithClientOptions(opts...),
		),
	}
}

// featureFlagsServiceClient implements FeatureFlagsServiceClient.
type featureFlagsServiceClient struct {
	isEnabled           *connect.Client[v1.IsEnabledRequest, v1.IsEnabledResponse]
	setFeatureTarget    *connect.Client[v1.SetFeatureTargetRequest, v1.SetFeatureTargetResponse]
	setMultipleFeatures *connect.Client[v1.SetMultipleFeaturesRequest, v1.SetMultipleFeaturesResponse]
	getEnabledFeatures  *connect.Client[v1.GetEnabledFeaturesRequest, v1.GetEnabledFeaturesResponse]
}

// IsEnabled calls hero.featureflags.v1.FeatureFlagsService.IsEnabled.
func (c *featureFlagsServiceClient) IsEnabled(ctx context.Context, req *connect.Request[v1.IsEnabledRequest]) (*connect.Response[v1.IsEnabledResponse], error) {
	return c.isEnabled.CallUnary(ctx, req)
}

// SetFeatureTarget calls hero.featureflags.v1.FeatureFlagsService.SetFeatureTarget.
func (c *featureFlagsServiceClient) SetFeatureTarget(ctx context.Context, req *connect.Request[v1.SetFeatureTargetRequest]) (*connect.Response[v1.SetFeatureTargetResponse], error) {
	return c.setFeatureTarget.CallUnary(ctx, req)
}

// SetMultipleFeatures calls hero.featureflags.v1.FeatureFlagsService.SetMultipleFeatures.
func (c *featureFlagsServiceClient) SetMultipleFeatures(ctx context.Context, req *connect.Request[v1.SetMultipleFeaturesRequest]) (*connect.Response[v1.SetMultipleFeaturesResponse], error) {
	return c.setMultipleFeatures.CallUnary(ctx, req)
}

// GetEnabledFeatures calls hero.featureflags.v1.FeatureFlagsService.GetEnabledFeatures.
func (c *featureFlagsServiceClient) GetEnabledFeatures(ctx context.Context, req *connect.Request[v1.GetEnabledFeaturesRequest]) (*connect.Response[v1.GetEnabledFeaturesResponse], error) {
	return c.getEnabledFeatures.CallUnary(ctx, req)
}

// FeatureFlagsServiceHandler is an implementation of the hero.featureflags.v1.FeatureFlagsService
// service.
type FeatureFlagsServiceHandler interface {
	// Check if a feature is enabled
	IsEnabled(context.Context, *connect.Request[v1.IsEnabledRequest]) (*connect.Response[v1.IsEnabledResponse], error)
	// Enable/disable feature for specific targets
	SetFeatureTarget(context.Context, *connect.Request[v1.SetFeatureTargetRequest]) (*connect.Response[v1.SetFeatureTargetResponse], error)
	// Bulk enable/disable multiple features for a target
	SetMultipleFeatures(context.Context, *connect.Request[v1.SetMultipleFeaturesRequest]) (*connect.Response[v1.SetMultipleFeaturesResponse], error)
	// Get all enabled features for a target
	GetEnabledFeatures(context.Context, *connect.Request[v1.GetEnabledFeaturesRequest]) (*connect.Response[v1.GetEnabledFeaturesResponse], error)
}

// NewFeatureFlagsServiceHandler builds an HTTP handler from the service implementation. It returns
// the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewFeatureFlagsServiceHandler(svc FeatureFlagsServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	featureFlagsServiceMethods := v1.File_hero_featureflags_v1_featureflags_proto.Services().ByName("FeatureFlagsService").Methods()
	featureFlagsServiceIsEnabledHandler := connect.NewUnaryHandler(
		FeatureFlagsServiceIsEnabledProcedure,
		svc.IsEnabled,
		connect.WithSchema(featureFlagsServiceMethods.ByName("IsEnabled")),
		connect.WithHandlerOptions(opts...),
	)
	featureFlagsServiceSetFeatureTargetHandler := connect.NewUnaryHandler(
		FeatureFlagsServiceSetFeatureTargetProcedure,
		svc.SetFeatureTarget,
		connect.WithSchema(featureFlagsServiceMethods.ByName("SetFeatureTarget")),
		connect.WithHandlerOptions(opts...),
	)
	featureFlagsServiceSetMultipleFeaturesHandler := connect.NewUnaryHandler(
		FeatureFlagsServiceSetMultipleFeaturesProcedure,
		svc.SetMultipleFeatures,
		connect.WithSchema(featureFlagsServiceMethods.ByName("SetMultipleFeatures")),
		connect.WithHandlerOptions(opts...),
	)
	featureFlagsServiceGetEnabledFeaturesHandler := connect.NewUnaryHandler(
		FeatureFlagsServiceGetEnabledFeaturesProcedure,
		svc.GetEnabledFeatures,
		connect.WithSchema(featureFlagsServiceMethods.ByName("GetEnabledFeatures")),
		connect.WithHandlerOptions(opts...),
	)
	return "/hero.featureflags.v1.FeatureFlagsService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case FeatureFlagsServiceIsEnabledProcedure:
			featureFlagsServiceIsEnabledHandler.ServeHTTP(w, r)
		case FeatureFlagsServiceSetFeatureTargetProcedure:
			featureFlagsServiceSetFeatureTargetHandler.ServeHTTP(w, r)
		case FeatureFlagsServiceSetMultipleFeaturesProcedure:
			featureFlagsServiceSetMultipleFeaturesHandler.ServeHTTP(w, r)
		case FeatureFlagsServiceGetEnabledFeaturesProcedure:
			featureFlagsServiceGetEnabledFeaturesHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedFeatureFlagsServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedFeatureFlagsServiceHandler struct{}

func (UnimplementedFeatureFlagsServiceHandler) IsEnabled(context.Context, *connect.Request[v1.IsEnabledRequest]) (*connect.Response[v1.IsEnabledResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.featureflags.v1.FeatureFlagsService.IsEnabled is not implemented"))
}

func (UnimplementedFeatureFlagsServiceHandler) SetFeatureTarget(context.Context, *connect.Request[v1.SetFeatureTargetRequest]) (*connect.Response[v1.SetFeatureTargetResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.featureflags.v1.FeatureFlagsService.SetFeatureTarget is not implemented"))
}

func (UnimplementedFeatureFlagsServiceHandler) SetMultipleFeatures(context.Context, *connect.Request[v1.SetMultipleFeaturesRequest]) (*connect.Response[v1.SetMultipleFeaturesResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.featureflags.v1.FeatureFlagsService.SetMultipleFeatures is not implemented"))
}

func (UnimplementedFeatureFlagsServiceHandler) GetEnabledFeatures(context.Context, *connect.Request[v1.GetEnabledFeaturesRequest]) (*connect.Response[v1.GetEnabledFeaturesResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.featureflags.v1.FeatureFlagsService.GetEnabledFeatures is not implemented"))
}
