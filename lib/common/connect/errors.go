package connect

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"strings"

	"common/herosentry"

	"connectrpc.com/connect"
)

// ErrorType represents different categories of errors
type ErrorType int

const (
	ErrorTypeUnknown ErrorType = iota
	ErrorTypeNotFound
	ErrorTypeValidation
	ErrorTypeConflict
	ErrorTypeUnauthorized
	ErrorTypeForbidden
	ErrorTypeInternal
	ErrorTypeUnavailable
	ErrorTypeTimeout
	ErrorTypeUnprocessable
)

// DomainError represents a business logic error with a specific type
type DomainError struct {
	Type    ErrorType
	Message string
	Err     error
}

func (e *DomainError) Error() string {
	if e.Err != nil {
		return fmt.Sprintf("%s: %v", e.Message, e.Err)
	}
	return e.Message
}

func (e *DomainError) Unwrap() error {
	return e.Err
}

// NewDomainError creates a new domain error
func NewDomainError(errorType ErrorType, message string, err error) *DomainError {
	return &DomainError{
		Type:    errorType,
		Message: message,
		Err:     err,
	}
}

// AsConnectError converts any error to a Connect error with proper code and HeroSentry capture
func AsConnectError(ctx context.Context, err error, operation string, optionalErrorType ...herosentry.ErrorType) error {
	if err == nil {
		return nil
	}

	// Determine error type - use provided type or detect from error
	var errorType herosentry.ErrorType
	if len(optionalErrorType) > 0 {
		errorType = optionalErrorType[0]
	} else {
		errorType = determineHerosentryErrorType(err)
	}

	// Capture to HeroSentry with proper error type
	herosentry.CaptureException(ctx, err, errorType, operation)

	// Determine the Connect error code based on error type
	code := errorTypeToConnectCode(errorType)

	// Create detailed error message
	message := createErrorMessage(err, operation)

	return connect.NewError(code, fmt.Errorf("%s", message))
}

// AsConnectErrorWithCode converts an error to a Connect error with a specific code
func AsConnectErrorWithCode(ctx context.Context, err error, code connect.Code, operation string) error {
	if err == nil {
		return nil
	}

	// Determine error type from code for proper Sentry categorization
	errorType := connectCodeToErrorType(code)

	// Capture to HeroSentry with proper error type
	herosentry.CaptureException(ctx, err, errorType, operation)

	// Create detailed error message
	message := createErrorMessage(err, operation)

	return connect.NewError(code, fmt.Errorf("%s", message))
}

// connectCodeToErrorType maps Connect codes back to herosentry ErrorType
func connectCodeToErrorType(code connect.Code) herosentry.ErrorType {
	switch code {
	case connect.CodeInvalidArgument:
		return herosentry.ErrorTypeValidation
	case connect.CodeNotFound:
		return herosentry.ErrorTypeNotFound
	case connect.CodeUnauthenticated:
		return herosentry.ErrorTypeUnauthorized
	case connect.CodePermissionDenied:
		return herosentry.ErrorTypeForbidden
	case connect.CodeAlreadyExists:
		return herosentry.ErrorTypeConflict
	case connect.CodeUnavailable:
		return herosentry.ErrorTypeExternal
	case connect.CodeInternal:
		return herosentry.ErrorTypeInternal
	default:
		return herosentry.ErrorTypeInternal
	}
}

// errorTypeToConnectCode maps herosentry ErrorType to Connect error codes
func errorTypeToConnectCode(errorType herosentry.ErrorType) connect.Code {
	switch errorType {
	case herosentry.ErrorTypeValidation:
		return connect.CodeInvalidArgument
	case herosentry.ErrorTypeNotFound:
		return connect.CodeNotFound
	case herosentry.ErrorTypeUnauthorized:
		return connect.CodeUnauthenticated
	case herosentry.ErrorTypeForbidden:
		return connect.CodePermissionDenied
	case herosentry.ErrorTypeConflict:
		return connect.CodeAlreadyExists
	case herosentry.ErrorTypeDatabase:
		return connect.CodeInternal
	case herosentry.ErrorTypeExternal:
		return connect.CodeUnavailable
	case herosentry.ErrorTypeInternal:
		return connect.CodeInternal
	default:
		return connect.CodeInternal
	}
}

// determineHerosentryErrorType maps errors to herosentry ErrorType for proper categorization
func determineHerosentryErrorType(err error) herosentry.ErrorType {
	// Check for DomainError type
	var domainErr *DomainError
	if errors.As(err, &domainErr) {
		switch domainErr.Type {
		case ErrorTypeNotFound:
			return herosentry.ErrorTypeNotFound
		case ErrorTypeValidation:
			return herosentry.ErrorTypeValidation
		case ErrorTypeConflict:
			return herosentry.ErrorTypeConflict
		case ErrorTypeUnauthorized:
			return herosentry.ErrorTypeUnauthorized
		case ErrorTypeForbidden:
			return herosentry.ErrorTypeForbidden
		case ErrorTypeUnavailable, ErrorTypeTimeout:
			return herosentry.ErrorTypeExternal
		case ErrorTypeUnprocessable:
			return herosentry.ErrorTypeValidation
		default:
			return herosentry.ErrorTypeInternal
		}
	}

	// Check for standard database errors
	if errors.Is(err, sql.ErrNoRows) {
		return herosentry.ErrorTypeNotFound
	}

	// Check for context errors
	if errors.Is(err, context.Canceled) || errors.Is(err, context.DeadlineExceeded) {
		return herosentry.ErrorTypeExternal
	}

	// Check error message patterns for common cases
	errMsg := strings.ToLower(err.Error())

	// Not found patterns
	if strings.Contains(errMsg, "not found") || strings.Contains(errMsg, "does not exist") {
		return herosentry.ErrorTypeNotFound
	}

	// Validation patterns
	if strings.Contains(errMsg, "invalid") || strings.Contains(errMsg, "validation") ||
		strings.Contains(errMsg, "required") || strings.Contains(errMsg, "must be") {
		return herosentry.ErrorTypeValidation
	}

	// Conflict patterns
	if strings.Contains(errMsg, "already exists") || strings.Contains(errMsg, "duplicate") ||
		strings.Contains(errMsg, "conflict") {
		return herosentry.ErrorTypeConflict
	}

	// Permission patterns
	if strings.Contains(errMsg, "unauthorized") || strings.Contains(errMsg, "not authorized") {
		return herosentry.ErrorTypeUnauthorized
	}
	if strings.Contains(errMsg, "forbidden") || strings.Contains(errMsg, "permission") ||
		strings.Contains(errMsg, "access denied") {
		return herosentry.ErrorTypeForbidden
	}

	// Database patterns
	if strings.Contains(errMsg, "database") || strings.Contains(errMsg, "sql") ||
		strings.Contains(errMsg, "postgres") || strings.Contains(errMsg, "connection") {
		return herosentry.ErrorTypeDatabase
	}

	// Default to internal error for unknown cases
	return herosentry.ErrorTypeInternal
}

// determineErrorCode maps errors to appropriate Connect error codes
func determineErrorCode(err error) connect.Code {
	// Check for DomainError type
	var domainErr *DomainError
	if errors.As(err, &domainErr) {
		switch domainErr.Type {
		case ErrorTypeNotFound:
			return connect.CodeNotFound
		case ErrorTypeValidation:
			return connect.CodeInvalidArgument
		case ErrorTypeConflict:
			return connect.CodeAlreadyExists
		case ErrorTypeUnauthorized:
			return connect.CodeUnauthenticated
		case ErrorTypeForbidden:
			return connect.CodePermissionDenied
		case ErrorTypeUnavailable:
			return connect.CodeUnavailable
		case ErrorTypeTimeout:
			return connect.CodeDeadlineExceeded
		case ErrorTypeUnprocessable:
			return connect.CodeFailedPrecondition
		default:
			return connect.CodeInternal
		}
	}

	// Check for standard database errors
	if errors.Is(err, sql.ErrNoRows) {
		return connect.CodeNotFound
	}

	// Check for context errors
	if errors.Is(err, context.Canceled) {
		return connect.CodeCanceled
	}
	if errors.Is(err, context.DeadlineExceeded) {
		return connect.CodeDeadlineExceeded
	}

	// Check error message patterns for common cases
	errMsg := strings.ToLower(err.Error())

	// Not found patterns
	if strings.Contains(errMsg, "not found") || strings.Contains(errMsg, "does not exist") {
		return connect.CodeNotFound
	}

	// Validation patterns
	if strings.Contains(errMsg, "invalid") || strings.Contains(errMsg, "validation") ||
		strings.Contains(errMsg, "required") || strings.Contains(errMsg, "must be") {
		return connect.CodeInvalidArgument
	}

	// Conflict patterns
	if strings.Contains(errMsg, "already exists") || strings.Contains(errMsg, "duplicate") ||
		strings.Contains(errMsg, "conflict") {
		return connect.CodeAlreadyExists
	}

	// Permission patterns
	if strings.Contains(errMsg, "unauthorized") || strings.Contains(errMsg, "not authorized") {
		return connect.CodeUnauthenticated
	}
	if strings.Contains(errMsg, "forbidden") || strings.Contains(errMsg, "permission") ||
		strings.Contains(errMsg, "access denied") {
		return connect.CodePermissionDenied
	}

	// Resource exhausted patterns
	if strings.Contains(errMsg, "quota") || strings.Contains(errMsg, "limit exceeded") ||
		strings.Contains(errMsg, "too many") {
		return connect.CodeResourceExhausted
	}

	// Default to internal error for unknown cases
	return connect.CodeInternal
}

// createErrorMessage creates a user-friendly error message
func createErrorMessage(err error, operation string) string {
	// For DomainError, use the custom message
	var domainErr *DomainError
	if errors.As(err, &domainErr) {
		if domainErr.Message != "" {
			return domainErr.Message
		}
	}

	// For known database errors
	if errors.Is(err, sql.ErrNoRows) {
		return "resource not found"
	}

	// For context errors
	if errors.Is(err, context.Canceled) {
		return "operation was canceled"
	}
	if errors.Is(err, context.DeadlineExceeded) {
		return "operation timed out"
	}

	// Default: return the original error
	return err.Error()
}

// getErrorType returns a string representation of the error type for logging
func getErrorType(err error) string {
	var domainErr *DomainError
	if errors.As(err, &domainErr) {
		switch domainErr.Type {
		case ErrorTypeNotFound:
			return "not_found"
		case ErrorTypeValidation:
			return "validation"
		case ErrorTypeConflict:
			return "conflict"
		case ErrorTypeUnauthorized:
			return "unauthorized"
		case ErrorTypeForbidden:
			return "forbidden"
		case ErrorTypeUnavailable:
			return "unavailable"
		case ErrorTypeTimeout:
			return "timeout"
		case ErrorTypeUnprocessable:
			return "unprocessable"
		case ErrorTypeInternal:
			return "internal"
		default:
			return "unknown"
		}
	}

	// Check for standard errors
	if errors.Is(err, sql.ErrNoRows) {
		return "not_found"
	}
	if errors.Is(err, context.Canceled) {
		return "canceled"
	}
	if errors.Is(err, context.DeadlineExceeded) {
		return "timeout"
	}

	return "internal"
}

// Helper functions for creating specific domain errors

// NotFoundError creates a not found domain error
func NotFoundError(resource string) *DomainError {
	return NewDomainError(ErrorTypeNotFound, fmt.Sprintf("%s not found", resource), nil)
}

// ValidationError creates a validation domain error
func ValidationError(field, message string) *DomainError {
	return NewDomainError(ErrorTypeValidation, fmt.Sprintf("validation error on %s: %s", field, message), nil)
}

// SimpleValidationError creates a validation domain error with just a message
func SimpleValidationError(message string) *DomainError {
	return NewDomainError(ErrorTypeValidation, message, nil)
}

// ConflictError creates a conflict domain error
func ConflictError(resource string) *DomainError {
	return NewDomainError(ErrorTypeConflict, fmt.Sprintf("%s already exists", resource), nil)
}

// InternalError creates an internal domain error
func InternalError(message string, err error) *DomainError {
	return NewDomainError(ErrorTypeInternal, message, err)
}
