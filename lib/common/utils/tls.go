package utils

import (
	"crypto/tls"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"os"
)

// TLSCertificate represents a TLS certificate with its private key
type TLSCertificate struct {
	Certificate string `json:"certificate"`
	PrivateKey  string `json:"private_key"`
}

// LoadTLSCertificateFromEnv loads a TLS certificate from AWS Secrets Manager
// The secret should contain a JSON object with "certificate" and "private_key" fields
// It also supports additional fields like "combined", "cert_chain", and "nginx_key"
func LoadTLSCertificateFromEnv(secretConfig string) (*tls.Certificate, error) {
	var certData TLSCertificate
	if err := json.Unmarshal([]byte(secretConfig), &certData); err != nil {
		return nil, fmt.Errorf("failed to unmarshal certificate data: %w", err)
	}

	// Determine which certificate and key to use
	var certPEM, keyPEM []byte

	if certData.Certificate == "" || certData.PrivateKey == "" {
		return nil, fmt.Errorf("both certificate and private_key fields are required")
	}

	// Decode base64 encoded certificate and key
	certBytes, err := base64.StdEncoding.DecodeString(certData.Certificate)
	if err != nil {
		return nil, fmt.Errorf("failed to decode certificate: %w", err)
	}

	keyBytes, err := base64.StdEncoding.DecodeString(certData.PrivateKey)
	if err != nil {
		return nil, fmt.Errorf("failed to decode private key: %w", err)
	}

	certPEM = certBytes
	keyPEM = keyBytes

	// Load the certificate and private key
	cert, err := tls.X509KeyPair(certPEM, keyPEM)
	if err != nil {
		return nil, fmt.Errorf("failed to load certificate: %w", err)
	}

	return &cert, nil
}

// LoadTLSCertificateFromFiles loads a TLS certificate from files
func LoadTLSCertificateFromFiles(certFile, keyFile string) (*tls.Certificate, error) {
	cert, err := tls.LoadX509KeyPair(certFile, keyFile)
	if err != nil {
		return nil, fmt.Errorf("failed to load certificate from files: %w", err)
	}
	return &cert, nil
}

// CreateTLSConfig creates a TLS configuration for the server
func CreateTLSConfig(cert *tls.Certificate) *tls.Config {
	return &tls.Config{
		Certificates: []tls.Certificate{*cert},
		MinVersion:   tls.VersionTLS12,
		MaxVersion:   tls.VersionTLS13,
		CipherSuites: []uint16{
			tls.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,
			tls.TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305,
			tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,
		},
		PreferServerCipherSuites: true,
		CurvePreferences: []tls.CurveID{
			tls.CurveP256,
			tls.X25519,
		},
	}
}

// GetTLSConfig creates a TLS configuration based on environment variables
// It supports loading certificates from AWS Secrets Manager or local files
func GetTLSConfig() (*tls.Config, error) {
	// Check if TLS is disabled via environment variable
	if os.Getenv("TLS_DISABLED") == "true" {
		return nil, nil
	}

	// Try to load from AWS Secrets Manager first
	if secretConfig := os.Getenv("TLS_CERTIFICATE_SECRET"); secretConfig != "" {
		cert, err := LoadTLSCertificateFromEnv(secretConfig)
		if err != nil {
			return nil, fmt.Errorf("failed to load certificate from secret: %w", err)
		}
		return CreateTLSConfig(cert), nil
	}

	// Try to load from local files
	if certFile := os.Getenv("TLS_CERTIFICATE_FILE"); certFile != "" {
		keyFile := os.Getenv("TLS_PRIVATE_KEY_FILE")
		if keyFile == "" {
			return nil, fmt.Errorf("TLS_CERTIFICATE_FILE is set but TLS_PRIVATE_KEY_FILE is not")
		}

		cert, err := LoadTLSCertificateFromFiles(certFile, keyFile)
		if err != nil {
			return nil, fmt.Errorf("failed to load certificate from files: %w", err)
		}
		return CreateTLSConfig(cert), nil
	}

	return nil, fmt.Errorf("TLS is enabled by default but no certificate source is configured. Set TLS_DISABLED=true to disable TLS or configure TLS_CERTIFICATE_SECRET or TLS_CERTIFICATE_FILE")
}

// IsTLSEnabled checks if TLS is enabled based on environment variables
// Defaults to true unless TLS_DISABLED=true is set
func IsTLSEnabled() bool {
	isEnabled := os.Getenv("TLS_DISABLED") != "true"
	return isEnabled
}

// GetTLSPort returns the TLS port to listen on, defaulting to 443
func GetTLSPort() string {
	port := os.Getenv("TLS_PORT")
	if port == "" {
		port = "443"
	}
	return ":" + port
}

// GetHTTPPort returns the HTTP port to listen on, defaulting to 8080
func GetHTTPPort() string {
	port := os.Getenv("HTTP_PORT")
	if port == "" {
		port = "8080"
	}
	return ":" + port
}
