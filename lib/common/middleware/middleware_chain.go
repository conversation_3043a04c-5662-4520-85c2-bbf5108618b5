// Package middleware provides HTTP middleware for the Hero Core services.
package middleware

import (
	"net/http"

	"common/herosentry"
	"common/middleware/permissions"
)

// MiddlewareChainConfig contains configuration for the middleware chain
type MiddlewareChainConfig struct {
	// EnablePermissions controls whether to apply permissions middleware
	EnablePermissions bool
	// EnableAuth controls whether to apply authentication middleware
	EnableAuth bool
	// EnableTracing controls whether to apply trace context middleware
	EnableTracing bool
}

// ApplyMiddleware<PERSON>hain applies all middleware in the correct order.
//
// Middleware execution order (reverse of application order):
// 1. TraceContextMiddleware (runs first) - Extracts trace headers for distributed tracing
// 2. AuthMiddleware (runs second) - Authenticates the request and sets user context
// 3. PermissionsMiddleware (runs third) - Checks permissions for the authenticated user
// 4. Main handler (runs last) - The actual service handler
//
// This ordering ensures that:
// - Trace context is available to all subsequent middleware
// - Authentication happens before permission checks
// - All middleware can contribute to the distributed trace
func ApplyMiddlewareChain(handler http.Handler, config MiddlewareChainConfig) http.Handler {
	// Apply middleware in reverse order of execution
	// The last middleware applied runs first

	// Apply permissions middleware (will run third)
	if config.EnablePermissions {
		handler = permissions.PermissionsMiddleware(handler)
	}

	// Apply auth middleware (will run second)
	if config.EnableAuth {
		handler = AuthMiddleware(handler)
	}

	// Apply trace context middleware LAST so it runs FIRST
	// This ensures trace headers are extracted before any other middleware
	if config.EnableTracing {
		handler = herosentry.TraceContextMiddleware(handler)
	}

	return handler
}

// DefaultMiddlewareChain applies the standard middleware chain with all features enabled
func DefaultMiddlewareChain(handler http.Handler) http.Handler {
	return ApplyMiddlewareChain(handler, MiddlewareChainConfig{
		EnablePermissions: true,
		EnableAuth:        true,
		EnableTracing:     true,
	})
}
