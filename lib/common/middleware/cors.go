// Package middleware provides HTTP middleware for the Hero Core services.
package middleware

import (
	"github.com/rs/cors"
)

// NewCORSHandler creates a CORS handler for local development.
// In production, CORS is handled by the API Gateway.
func NewCORSHandler() *cors.Cors {
	return cors.New(cors.Options{
		AllowedOrigins:   []string{"http://localhost:3000"},
		AllowedMethods:   []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowedHeaders:   []string{"*"},
		ExposedHeaders:   []string{"*"},
		AllowCredentials: true,
		MaxAge:           300,
	})
}
