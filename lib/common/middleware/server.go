// Package middleware provides HTTP middleware for the Hero Core services.
package middleware

import (
	"crypto/tls"
	"fmt"
	"log"
	"net/http"
	"time"

	"common/utils"

	"golang.org/x/net/http2"
	"golang.org/x/net/http2/h2c"
)

// ServerConfig contains configuration for the HTTP server
type ServerConfig struct {
	// Addr is the address to listen on (e.g., ":8080")
	Addr string
	// Handler is the HTTP handler to serve
	Handler http.Handler
	// ReadTimeout is the maximum duration for reading the entire request
	ReadTimeout time.Duration
	// WriteTimeout is the maximum duration before timing out writes of the response
	WriteTimeout time.Duration
	// IdleTimeout is the maximum amount of time to wait for the next request
	IdleTimeout time.Duration
	// TLSConfig is the TLS configuration for HTTPS
	TLSConfig *tls.Config
}

// DefaultServerConfig returns the default server configuration
func DefaultServerConfig(addr string, handler http.Handler) ServerConfig {
	return ServerConfig{
		Addr:         addr,
		Handler:      handler,
		ReadTimeout:  15 * time.Second,
		WriteTimeout: 15 * time.Second,
		IdleTimeout:  60 * time.Second,
	}
}

// NewH2cServer creates a new HTTP server with the given configuration.
// The server supports both HTTP/1.1 and HTTP/2 (h2c - HTTP/2 without TLS).
func NewH2cServer(config ServerConfig) *http.Server {
	return &http.Server{
		Addr:         config.Addr,
		Handler:      h2c.NewHandler(config.Handler, &http2.Server{}),
		ReadTimeout:  config.ReadTimeout,
		WriteTimeout: config.WriteTimeout,
		IdleTimeout:  config.IdleTimeout,
		TLSConfig:    config.TLSConfig,
	}
}

// NewTLSServer creates a new HTTPS server with TLS configuration.
// This server supports HTTP/1.1 and HTTP/2 with TLS.
func NewTLSServer(config ServerConfig) *http.Server {
	if config.TLSConfig == nil {
		panic("TLSConfig is required for NewTLSServer")
	}

	// Configure the TLS config to support HTTP/2
	config.TLSConfig.NextProtos = append(config.TLSConfig.NextProtos, "h2")

	return &http.Server{
		Addr:         config.Addr,
		Handler:      config.Handler, // No h2c wrapper for TLS
		ReadTimeout:  config.ReadTimeout,
		WriteTimeout: config.WriteTimeout,
		IdleTimeout:  config.IdleTimeout,
		TLSConfig:    config.TLSConfig,
	}
}

// NewServer creates a server that automatically handles TLS configuration.
// TLS is enabled by default unless TLS_DISABLED=true is set.
// If TLS is enabled but no certificate is configured, the function will return an error.
func NewServer(handler http.Handler) (*http.Server, error) {
	port := getServerPort()
	config := DefaultServerConfig(port, handler)

	// Only configure TLS if it's enabled
	if utils.IsTLSEnabled() {
		tlsConfig, err := utils.GetTLSConfig()
		if err != nil {
			return nil, fmt.Errorf("failed to get TLS config: %w", err)
		}
		if tlsConfig == nil {
			return nil, fmt.Errorf("TLS is enabled but no certificate configuration found")
		}
		config.TLSConfig = tlsConfig
		return NewTLSServer(config), nil
	}

	return NewH2cServer(config), nil
}

// StartServer starts the server with appropriate TLS handling
func StartServer(server *http.Server) error {
	if server.TLSConfig != nil {
		log.Printf("Starting HTTPS server on %s", server.Addr)
		return server.ListenAndServeTLS("", "") // Certificates are already in TLSConfig
	} else {
		log.Printf("Starting HTTP server on %s", server.Addr)
		return server.ListenAndServe()
	}
}
