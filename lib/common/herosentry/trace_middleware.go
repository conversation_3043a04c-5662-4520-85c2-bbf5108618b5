// Package herosentry provides HTTP middleware for trace context propagation.
// This middleware should be the FIRST middleware in the chain to ensure
// trace headers are available to all subsequent middlewares and handlers.
package herosentry

import (
	"context"
	"net/http"

	"github.com/getsentry/sentry-go"
)

// TraceContextMiddleware extracts trace headers from incoming requests and
// adds them to the request context. This allows subsequent middlewares
// (like permissions) and handlers to be aware of distributed traces.
//
// This middleware should be applied FIRST in the middleware chain:
//
//	handler = herosentry.TraceContextMiddleware(handler)
//	handler = permissions.PermissionsMiddleware(handler)
//	handler = authentication.Middleware(handler)
//
// The middleware checks for the sentry-trace header and stores it in context
// so that StartSpan can properly continue distributed traces.
func TraceContextMiddleware(nextHandler http.Handler) http.Handler {
	return http.HandlerFunc(func(responseWriter http.ResponseWriter, httpRequest *http.Request) {
		requestContext := httpRequest.Context()

		// Extract trace header if present
		if traceHeader := httpRequest.Header.Get(sentry.SentryTraceHeader); traceHeader != "" {
			// Store trace header in context for StartSpan to use
			requestContext = context.WithValue(requestContext, SentryTraceHeaderKey, traceHeader)
		}

		// Also extract baggage header if present
		if baggageHeader := httpRequest.Header.Get(sentry.SentryBaggageHeader); baggageHeader != "" {
			requestContext = context.WithValue(requestContext, baggageHeaderKey, baggageHeader)
		}

		// Update request with new context
		httpRequest = httpRequest.WithContext(requestContext)

		// Continue with the request
		nextHandler.ServeHTTP(responseWriter, httpRequest)
	})
}
