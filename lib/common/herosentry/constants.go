// Package herosentry provides constants used throughout the Hero Sentry SDK.
// This file centralizes all constant values including context keys and
// configuration defaults for better maintainability.
package herosentry

// Context keys for passing data through context.
// These typed constants prevent key collisions and improve type safety.
const (
	// SentryTraceHeaderKey is the context key for storing sentry trace headers.
	// This key is used by interceptors to pass trace information through the context.
	// It is exported so that middleware can preserve trace headers across context updates.
	SentryTraceHeaderKey contextKey = "sentry-trace-header"

	// baggageHeaderKey is the context key for storing sentry baggage headers.
	// The baggage header contains additional context like dynamic sampling configuration.
	baggageHeaderKey contextKey = "sentry-baggage-header"

	// httpRequestContextKey is the context key for storing HTTP request objects.
	// This key is used to pass HTTP request data through context for capture functions.
	httpRequestContextKey contextKey = "http.request"

	// capturedErrorsContextKey is the context key for storing captured errors to prevent duplicates.
	// This key is used to track which errors have already been sent to Sentry within a single trace.
	// The map is initialized at request entry points (interceptors/middleware) and shared across
	// all layers, preventing the same error from being captured multiple times as it bubbles up.
	capturedErrorsContextKey contextKey = "herosentry.captured_errors"
)
