// Package herosentry provides span management for distributed tracing.
// This file contains the core span creation and management functions that
// automatically handle trace propagation and parent-child relationships.
//
// The span system supports three scenarios:
// 1. Creating new root transactions for incoming requests
// 2. Creating child spans within an existing transaction
// 3. Continuing distributed traces from other services
//
// All spans automatically capture rich context including user information,
// code location, and request metadata.
package herosentry

import (
	"context"
	"log"
	"net/http"
	"strings"

	cmncontext "common/context"

	"github.com/getsentry/sentry-go"
)

// isHexString checks if a string contains only hexadecimal characters (0-9, a-f, A-F)
func isHexString(s string) bool {
	if s == "" {
		return false
	}
	for _, c := range s {
		if !((c >= '0' && c <= '9') || (c >= 'a' && c <= 'f') || (c >= 'A' && c <= 'F')) {
			return false
		}
	}
	return true
}

// StartTransaction creates a new root transaction.
//
// IMPORTANT: This function is intended for internal use by middleware and interceptors only.
// Application code should use StartSpan() instead, which automatically handles transactions.
//
// This is useful for middleware that needs to ensure a transaction exists
// before RPC interceptors run. The transaction will be the root of all
// subsequent spans created in the request.
//
// Internal usage in middleware:
//
//	ctx, finish := herosentry.StartTransaction(r.Context(), "ServiceName.Method", "rpc.server")
//	defer finish()
//	r = r.WithContext(ctx)
//
// This ensures that any subsequent operations (like permission checks)
// have a parent transaction to attach to.
//
// The operationType parameter is optional. If provided, it explicitly sets the span.op value.
// Common values: "rpc.server", "rpc.client", "http.server", "db", "cache", etc.
func StartTransaction(requestContext context.Context, operationName string, operationType ...string) (context.Context, FinishFunc) {
	// Return no-op if Sentry is not initialized
	if !isInitialized() {
		return requestContext, func() {}
	}

	// Check if we already have a transaction
	if existingSpan := sentry.SpanFromContext(requestContext); existingSpan != nil {
		return requestContext, func() {}
	}

	// Create new transaction
	transaction := sentry.StartTransaction(requestContext, operationName)

	// Check if transaction was created successfully
	if transaction == nil {
		log.Printf("[herosentry] ERROR: Failed to create transaction for %s", operationName)
		return requestContext, func() {}
	}

	// Set operation type - use explicit type if provided, otherwise infer
	if len(operationType) > 0 && operationType[0] != "" {
		transaction.Op = operationType[0]
	} else {
		transaction.Op = inferOperationType(operationName)
	}
	transaction.Description = operationName

	// IMPORTANT: The transaction.Context() creates a new context that only has the span
	// We need to preserve all the values from the original context (like auth headers)
	//
	// The Sentry SDK doesn't provide a way to add a span to an existing context,
	// so we need to work around this limitation by:
	// 1. Getting the new context with the span
	// 2. Copying any important values from the original context
	transactionCtx := transaction.Context()

	// Copy important values from the original context to the transaction context
	// This is a workaround for the Sentry SDK limitation

	if authHeaders := requestContext.Value(cmncontext.OriginalAuthHeaderContextKey); authHeaders != nil {
		transactionCtx = context.WithValue(transactionCtx, cmncontext.OriginalAuthHeaderContextKey, authHeaders)
	} else {
	}

	if orgID := requestContext.Value(cmncontext.OrgIdContextKey); orgID != nil {
		transactionCtx = context.WithValue(transactionCtx, cmncontext.OrgIdContextKey, orgID)
	}

	if username := requestContext.Value(cmncontext.UsernameContextKey); username != nil {
		transactionCtx = context.WithValue(transactionCtx, cmncontext.UsernameContextKey, username)
	}

	if ip := requestContext.Value(cmncontext.IPAddressContextKey); ip != nil {
		transactionCtx = context.WithValue(transactionCtx, cmncontext.IPAddressContextKey, ip)
	}

	// Initialize error deduplication map for this transaction
	// This is for manually created transactions (not common in production)
	// Most transactions are created by interceptors which handle this
	transactionCtx = initializeCapturedErrorsMap(transactionCtx)

	// Return context with transaction and finish function
	return transactionCtx, transaction.Finish
}

// StartSpan starts a new span for operation tracking.
// It automatically determines whether to create a child span (if one exists in context)
// or start a new transaction. For distributed systems, it continues traces from
// incoming requests when trace headers are present.
//
// The function returns an updated context containing the new span and a FinishFunc
// that must be called to complete the span. Always use defer with the FinishFunc.
//
// Parameters:
//   - requestContext: The current context, potentially containing a parent span
//   - operationName: A descriptive name for the operation (e.g., "database.query", "http.request")
//
// Returns:
//   - context.Context: Updated context containing the new span
//   - Span: The created span for adding tags and data
//   - FinishFunc: Function to call when the operation completes
//
// Example usage:
//
//	ctx, span, finish := herosentry.StartSpan(ctx, "order.process")
//	defer finish()
//	span.SetTag("order.id", orderID)
//	span.SetData("order.items", orderItems)
//	// ... perform order processing ...
//
// Span hierarchy example:
//
//	// Root transaction (from HTTP request)
//	ctx, span, finish := StartSpan(ctx, "OrderAPI.CreateOrder")
//	defer finish()
//	span.SetTag("order.type", "express")
//
//	// Child span for validation
//	ctx2, span2, finish2 := StartSpan(ctx, "order.validate")
//	defer finish2()
//	span2.SetTag("validation.result", "passed")
//	// ... validation logic ...
//
//	// Another child span for database operation
//	ctx3, span3, finish3 := StartSpan(ctx, "database.insert")
//	defer finish3()
//	span3.SetTag("db.table", "orders")
//	// ... database logic ...
//
// The function automatically:
// - Captures code location (file, line, function)
// - Extracts user context (username, org ID, IP)
// - Adds environment and service tags
// - Handles distributed trace propagation
func StartSpan(requestContext context.Context, operationName string) (context.Context, Span, FinishFunc) {
	// Return no-op if Sentry is not initialized
	if !isInitialized() {
		return requestContext, &internalSpan{}, func() {}
	}

	// Check if we're already in a span/transaction
	parentSpan := sentry.SpanFromContext(requestContext)

	var sentrySpan *sentry.Span
	var spanType string

	if parentSpan != nil {
		// Case 1: We're inside an existing span - create a child
		// This maintains the parent-child relationship in the trace tree
		sentrySpan = parentSpan.StartChild(operationName)
		spanType = "child_span"
		// Set operation type based on operation name pattern
		sentrySpan.Op = inferOperationType(operationName)
		// Set the description to the operation name
		sentrySpan.Description = operationName
	} else {
		// Case 2: No parent span - check for distributed trace
		traceHeader := extractTraceHeader(requestContext)

		if traceHeader != "" {
			// Case 2a: Continue from distributed trace
			// This happens when receiving a request from another service

			// Parse and validate the trace header format
			parts := strings.Split(traceHeader, "-")
			if len(parts) != 3 {
				log.Printf("[herosentry] WARNING: Invalid trace header format (expected 3 parts, got %d): %s",
					len(parts), traceHeader)
				// Fall through to create new transaction
			} else {
				// Validate trace header components
				traceID := parts[0]
				spanID := parts[1]
				sampled := parts[2]

				// Validate trace ID (32 hex chars)
				if len(traceID) != 32 || !isHexString(traceID) {
					log.Printf("[herosentry] WARNING: Invalid trace ID format (expected 32 hex chars): %s", traceID)
					// Fall through to create new transaction
				} else if len(spanID) != 16 || !isHexString(spanID) {
					// Validate span ID (16 hex chars)
					log.Printf("[herosentry] WARNING: Invalid span ID format (expected 16 hex chars): %s", spanID)
					// Fall through to create new transaction
				} else if sampled != "0" && sampled != "1" {
					// Validate sampled flag
					log.Printf("[herosentry] WARNING: Invalid sampled flag (expected 0 or 1): %s", sampled)
					// Fall through to create new transaction
				} else {
					// Valid trace header - check sampling decision

					// IMPORTANT: If the parent trace was not sampled (sampled=0),
					// we should NOT create a transaction to avoid orphan traces
					if sampled == "0" {
						// Parent trace was not sampled - return no-op span
						// This ensures we don't create orphan traces in Sentry
						return requestContext, &internalSpan{}, func() {}
					}

					// Parent trace was sampled (sampled=1) - continue with distributed trace
					// Use ContinueFromTrace with the validated trace header
					sentrySpan = sentry.StartTransaction(
						requestContext,
						operationName,
						sentry.ContinueFromTrace(traceHeader),
					)

					// Check if transaction was created successfully
					if sentrySpan == nil {
						log.Printf("[herosentry] ERROR: Failed to create continued transaction for %s", operationName)
						// Fall through to create new transaction
					} else {
						spanType = "continued_transaction"

						// Verify trace continuity
						actualTraceID := sentrySpan.TraceID.String()
						if traceID != actualTraceID {
							log.Printf("[herosentry] WARNING: Trace ID mismatch! Expected: %s, Got: %s", traceID, actualTraceID)
						}
					}
				}
			}
		}

		// If we didn't create a continued transaction (due to invalid header or error), create a new one
		if sentrySpan == nil {
			// Case 2b: No existing span or trace - start new transaction
			// This is the root of a new trace tree
			sentrySpan = sentry.StartTransaction(requestContext, operationName)
			spanType = "new_transaction"

			// Check if transaction was created successfully
			if sentrySpan == nil {
				log.Printf("[herosentry] ERROR: Failed to create new transaction for %s", operationName)
				return requestContext, &internalSpan{}, func() {}
			}
		}
		// Set operation type for transactions
		sentrySpan.Op = inferOperationType(operationName)
		// Set the description to the operation name
		sentrySpan.Description = operationName
	}

	// Create our internal span wrapper that adds Hero-specific functionality
	internalSpanWrapper := &internalSpan{
		Span: sentrySpan,
	}

	// Capture all context automatically
	// Skip 3 stack frames: captureRuntimeInfo, enrichSpanWithContext, and StartSpan
	enrichSpanWithContext(internalSpanWrapper, requestContext, 3)

	// Add database connection pool statistics
	enrichSpanWithDBPoolStats(internalSpanWrapper, requestContext)

	// Add span type for debugging and understanding trace structure
	internalSpanWrapper.SetTag("span.type", spanType)

	// Update context with new span for propagation to child operations
	newContext := sentrySpan.Context()

	// Initialize error deduplication map for new transactions
	// (only for root transactions, not child spans)
	// This handles edge cases where StartSpan creates a transaction directly
	// without going through interceptors (e.g., background jobs, CLI tools)
	if spanType == "new_transaction" || spanType == "continued_transaction" {
		if getCapturedErrorsMap(newContext) == nil {
			newContext = initializeCapturedErrorsMap(newContext)
		}
	}

	// Return updated context, span, and finish function
	return newContext, internalSpanWrapper, internalSpanWrapper.finish
}

// CurrentSpan returns the current span from context to add additional data.
// Use this function when you need to enrich an existing span with more information
// without creating a new child span.
//
// Parameters:
//   - requestContext: The context potentially containing a span
//
// Returns:
//   - Span: The current span or a no-op span if none exists
//
// Example usage:
//
//	// Inside a function that already has a span
//	if span := herosentry.CurrentSpan(ctx); span != nil {
//	    // Add custom tags
//	    span.SetTag("cache.hit", "true")
//	    span.SetTag("cache.key", cacheKey)
//
//	    // Add complex data
//	    span.SetData("cache.metadata", map[string]interface{}{
//	        "size": 1024,
//	        "ttl": 3600,
//	        "compression": "gzip",
//	    })
//
//	    // Add user information if not already captured
//	    span.SetUser(userID, userEmail)
//	}
//
// Common patterns:
//
//	// Pattern 1: Conditional data based on results
//	result, err := doOperation()
//	if span := herosentry.CurrentSpan(ctx); span != nil {
//	    if err != nil {
//	        span.SetTag("error", "true")
//	        span.SetData("error.message", err.Error())
//	    } else {
//	        span.SetTag("result.status", "success")
//	        span.SetData("result.count", len(result))
//	    }
//	}
//
//	// Pattern 2: Adding SQL query information
//	if span := herosentry.CurrentSpan(ctx); span != nil {
//	    span.SetTag("db.system", "postgresql")
//	    span.SetTag("db.operation", "SELECT")
//	    span.SetData("db.statement", query)
//	}
func CurrentSpan(requestContext context.Context) Span {
	// Return no-op span if Sentry is not initialized
	if !isInitialized() {
		return &internalSpan{}
	}

	// Extract the current span from context
	sentrySpan := sentry.SpanFromContext(requestContext)
	if sentrySpan == nil {
		// No active span in context, return no-op span
		// This allows safe usage without nil checks
		return &internalSpan{}
	}

	// Wrap the Sentry span in our interface
	return &internalSpan{
		Span: sentrySpan,
	}
}

// inferOperationType infers the operation type based on the operation name
// This helps Sentry categorize spans correctly for better organization
func inferOperationType(operationName string) string {
	lowerName := strings.ToLower(operationName)

	// Database operations
	if strings.Contains(lowerName, "database") || strings.Contains(lowerName, "db") ||
		strings.Contains(lowerName, "sql") || strings.Contains(lowerName, "query") {
		return "db"
	}

	// UseCase operations (business logic layer)
	if strings.Contains(lowerName, "usecase") {
		return "usecase"
	}

	// Repository operations (data access layer)
	if strings.Contains(lowerName, "repository") || strings.Contains(lowerName, "repo") {
		return "repository"
	}

	// HTTP operations
	if strings.Contains(lowerName, "http") || strings.Contains(lowerName, "api") ||
		strings.Contains(lowerName, "rest") {
		return "http"
	}

	// RPC operations - only match explicit rpc/grpc, not "service"
	// Server vs client distinction is handled by interceptors
	if strings.Contains(lowerName, "rpc") || strings.Contains(lowerName, "grpc") {
		return "rpc"
	}

	// Cache operations
	if strings.Contains(lowerName, "cache") || strings.Contains(lowerName, "redis") ||
		strings.Contains(lowerName, "memcache") {
		return "cache"
	}

	// Queue operations
	if strings.Contains(lowerName, "queue") || strings.Contains(lowerName, "message") ||
		strings.Contains(lowerName, "publish") || strings.Contains(lowerName, "consume") {
		return "queue"
	}

	// Default to function
	return "function"
}

// extractTraceHeader extracts distributed trace header from context.
// This header is used to continue traces across service boundaries.
//
// The function checks two sources:
// 1. Context value set by RPC interceptors
// 2. HTTP request headers (for HTTP endpoints)
//
// Parameters:
//   - requestContext: The context to search for trace headers
//
// Returns:
//   - string: The sentry-trace header value or empty string if not found
//
// Example trace header format:
//
//	"d4cda95b652f4a1592b449d5929fda1b-6e0c63257de34c92-1"
//	Where:
//	- First part: 32-char trace ID
//	- Second part: 16-char parent span ID
//	- Third part: Sampling decision (1=sampled, 0=not sampled)
//
// This function is called internally by StartSpan to maintain trace continuity.
func extractTraceHeader(requestContext context.Context) string {
	// Method 1: Check if trace header was stored in context by RPC interceptor
	// This is the primary method for RPC services
	if traceHeader, hasHeader := requestContext.Value(SentryTraceHeaderKey).(string); hasHeader {
		return traceHeader
	}

	// Method 2: Check HTTP request headers directly
	// This handles HTTP endpoints and webhooks
	if httpRequest, isHTTPRequest := requestContext.Value(httpRequestContextKey).(*http.Request); isHTTPRequest {
		header := httpRequest.Header.Get(sentry.SentryTraceHeader)
		return header
	}

	// No trace header found
	return ""
}
