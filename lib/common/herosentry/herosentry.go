// Package herosentry provides the main initialization and configuration for the Hero Sentry SDK.
// This file contains the core initialization function that sets up Sentry with
// environment-aware defaults and automatic configuration.
//
// The SDK is designed to be initialized once at application startup with minimal
// configuration required. It automatically detects the environment and adjusts
// settings accordingly.
//
// Key features:
// - Automatic environment detection from ENVIRONMENT variable
// - Smart sampling rates (100% in dev, 10% in production)
// - Privacy-first configuration (no PII by default)
// - Service name tagging for all events
// - Optional version tracking for releases
package herosentry

import (
	"context"
	"database/sql"
	"fmt"
	"net/http"
	"os"
	"strings"
	"sync"
	"time"

	"github.com/getsentry/sentry-go"
)

var (
	// serviceName stores the service identifier provided during initialization.
	// This is added as a tag to all events and spans for filtering in Sentry.
	serviceName string

	// initialized tracks whether the SDK has been successfully initialized.
	// Prevents double initialization and enables no-op behavior when not initialized.
	initialized bool

	// errorCaptureConfig stores the configuration for which error types to capture.
	// This is set during Init() and used by CaptureException and captureToSentry.
	errorCaptureConfig *ErrorCaptureConfig

	// globalStateMutex protects concurrent access to serviceName, initialized, and errorCaptureConfig
	// Uses RWMutex to allow multiple concurrent readers for better performance
	globalStateMutex sync.RWMutex
)

// ErrorCaptureConfig holds the runtime configuration for error capture behavior
type ErrorCaptureConfig struct {
	CaptureNotFoundErrors     bool
	CaptureValidationErrors   bool
	CaptureConflictErrors     bool
	CaptureUnauthorizedErrors bool
	CaptureForbiddenErrors    bool
	CaptureInternalErrors     bool
	CaptureExternalErrors     bool
	CaptureDatabaseErrors     bool
}

// Config allows customization of herosentry initialization.
// All fields are optional - if not set, defaults will be used.
type Config struct {
	// DataSourceName overrides the SENTRY_DSN environment variable
	DataSourceName string

	// Environment overrides the ENVIRONMENT env var (e.g., "production", "staging")
	Environment string

	// DevelopmentSampleRate sets the sampling rate for development environment (0.0-1.0)
	// Default: 1.0 (100% sampling)
	DevelopmentSampleRate *float64

	// ProductionSampleRate sets the sampling rate for all non-development environments (0.0-1.0)
	// Default: 0.1 (10% sampling)
	ProductionSampleRate *float64

	// Release version for tracking deployments
	// Default: APP_VERSION env var
	Release string

	// SendDefaultPII controls whether to send personally identifiable information
	// Default: false (privacy-first)
	SendDefaultPII bool

	// BeforeSend allows custom event processing
	// Default: adds service tag
	BeforeSend func(event *sentry.Event, hint *sentry.EventHint) *sentry.Event

	// CustomSamplingRules allows defining operation-specific sampling rates
	// Key is the operation pattern (e.g., "List*", "Get*", "BatchCheck*")
	// Value is the sampling rate (0.0-1.0)
	// Default: nil (uses global sampling rate)
	CustomSamplingRules map[string]float64

	// AlwaysSampleErrors ensures errors are always sampled regardless of trace sampling
	// Default: true
	AlwaysSampleErrors *bool

	// Error capture configuration - controls which error types are sent to Sentry
	// By default, ALL errors are captured (backward compatible)
	// Set to false to filter out specific error types

	// CaptureNotFoundErrors controls whether to capture 404/not found errors
	// Default: true (captures not found errors)
	CaptureNotFoundErrors *bool

	// CaptureValidationErrors controls whether to capture validation/bad request errors
	// Default: true (captures validation errors)
	CaptureValidationErrors *bool

	// CaptureConflictErrors controls whether to capture conflict/already exists errors
	// Default: true (captures conflict errors)
	CaptureConflictErrors *bool

	// CaptureUnauthorizedErrors controls whether to capture 401 unauthorized errors
	// Default: true (captures unauthorized errors)
	CaptureUnauthorizedErrors *bool

	// CaptureForbiddenErrors controls whether to capture 403 forbidden/permission denied errors
	// Default: true (captures forbidden errors)
	CaptureForbiddenErrors *bool

	// CaptureInternalErrors controls whether to capture internal system errors
	// Default: true (always capture critical internal errors)
	CaptureInternalErrors *bool

	// CaptureExternalErrors controls whether to capture external service failures
	// Default: true (always capture third-party service errors)
	CaptureExternalErrors *bool

	// CaptureDatabaseErrors controls whether to capture database operation failures
	// Default: true (always capture database errors)
	CaptureDatabaseErrors *bool
}

// Init initializes Sentry for the service with automatic configuration.
// This function should be called once at application startup, typically in main().
//
// The function automatically:
// - Detects environment from ENVIRONMENT env var (defaults to "development")
// - Sets appropriate sampling rates (100% dev, 10% production)
// - Configures privacy settings (no PII by default)
// - Adds service name as a tag to all events
// - Configures release tracking if APP_VERSION is set
//
// Parameters:
//   - service: The name of the service (e.g., "workflow-service", "asset-service")
//   - optionalConfig: Optional configuration to override defaults (variadic)
//
// Returns:
//   - error: An error if initialization fails or if already initialized
//
// Environment variables:
//   - ENVIRONMENT: The deployment environment (prod-1/demo-1/development)
//   - SENTRY_DSN: The Sentry project DSN (required unless DISABLE_SENTRY is set)
//   - APP_VERSION: Optional version string for release tracking
//   - DISABLE_SENTRY: Set to "true" to disable Sentry (useful for local development)
//
// Example usage:
//
//	// Basic usage with defaults
//	func main() {
//	    if err := herosentry.Init("workflow-service"); err != nil {
//	        log.Printf("Failed to initialize Sentry: %v", err)
//	    }
//	    defer herosentry.Flush()
//	}
//
//	// Advanced usage with custom config
//	func main() {
//	    devRate := 0.5
//	    prodRate := 0.05
//	    if err := herosentry.Init("workflow-service", herosentry.Config{
//	        Environment: "staging",
//	        DevelopmentSampleRate: &devRate,  // 50% in development
//	        ProductionSampleRate: &prodRate,   // 5% in production/staging
//	        Release: "v2.0.0-beta",
//	    }); err != nil {
//	        log.Printf("Failed to initialize Sentry: %v", err)
//	    }
//	    defer herosentry.Flush()
//	}
//
// The function is idempotent in terms of effects but returns an error
// on subsequent calls to prevent accidental re-initialization.
func Init(service string, optionalConfig ...Config) error {
	// Prevent double initialization and store service name
	globalStateMutex.Lock()
	if initialized {
		globalStateMutex.Unlock()
		return fmt.Errorf("herosentry already initialized")
	}
	// Keep the lock until we finish initialization
	serviceName = service

	// Extract config if provided
	var configuration Config
	if len(optionalConfig) > 0 {
		configuration = optionalConfig[0]
	}

	// Auto-detect environment from config or ENVIRONMENT variable
	environment := configuration.Environment
	if environment == "" {
		environment = os.Getenv("ENVIRONMENT")
		if environment == "" {
			environment = "development"
		}
	}

	// Determine sampling rate based on environment
	var sampleRate float64
	if environment == "development" {
		// Use development sample rate
		if configuration.DevelopmentSampleRate != nil {
			sampleRate = *configuration.DevelopmentSampleRate
		} else {
			sampleRate = 1.0 // Default 100% for development
		}
	} else {
		// Use production sample rate for all non-development environments
		if configuration.ProductionSampleRate != nil {
			sampleRate = *configuration.ProductionSampleRate
		} else {
			sampleRate = 0.1 // Default 10% for production
		}
	}

	// Check if Sentry is explicitly disabled
	if os.Getenv("DISABLE_SENTRY") == "true" {
		fmt.Printf("INFO: DISABLE_SENTRY is set. Herosentry will operate in no-op mode.\n")
		// Don't mark as initialized - keep everything as no-op
		globalStateMutex.Unlock()
		return nil
	} else {
		fmt.Printf("INFO: DISABLE_SENTRY is not set or is set to false. Herosentry will operate in normal mode.\n")
	}

	// Configure Sentry with Hero-specific defaults
	dataSourceName := configuration.DataSourceName
	if dataSourceName == "" {
		dataSourceName = os.Getenv("SENTRY_DSN")
	}

	// Check if DSN is provided
	if dataSourceName == "" {
		// Log warning but don't fail - allow service to run without Sentry
		fmt.Printf("WARNING: SENTRY_DSN not set. Herosentry will operate in no-op mode.\n")
		// Don't mark as initialized - keep everything as no-op
		globalStateMutex.Unlock()
		return nil
	}

	// Check if AlwaysSampleErrors is set, default to true
	alwaysSampleErrors := true
	if configuration.AlwaysSampleErrors != nil {
		alwaysSampleErrors = *configuration.AlwaysSampleErrors
	}

	// Store custom sampling rules for use in TracesSampler
	customSamplingRules := configuration.CustomSamplingRules
	if customSamplingRules == nil {
		// Set default sampling rules
		customSamplingRules = map[string]float64{
			// I am not turning on any defaultsampling rules for now.
			// But this is just example of how to set it.
			// "List*":            0.01, // 1% for list operations
			// "Get*":             0.1,  // 10% for get operations
			// "*Permission*":     0.05, // 5% for permission checks
			// "CheckPermission":  0.05, // 5% for explicit permission checks
			// "BatchCheck*":      0.01, // 1% for batch operations
			// "Create*":          0.5,  // 50% for create operations
			// "Update*":          0.5,  // 50% for update operations
			// "Delete*":          0.5,  // 50% for delete operations
			// "Health*":          0.0,  // 0% for health checks
			// "*Health":          0.0,  // 0% for health checks
		}
	}

	options := sentry.ClientOptions{
		// DSN from config or environment
		Dsn: dataSourceName,

		// Environment tag for filtering in Sentry UI
		Environment: environment,

		// Server name helps identify the specific service
		ServerName: service,

		// Enable distributed tracing across services
		EnableTracing: true,

		// Dynamic sampling rate based on environment
		TracesSampleRate: sampleRate,

		// Privacy settings from config
		SendDefaultPII: configuration.SendDefaultPII,

		// Custom TracesSampler for operation-based sampling
		TracesSampler: func(ctx sentry.SamplingContext) float64 {
			// Always sample if there's a parent span (maintain distributed trace)
			if ctx.Parent != nil && ctx.Parent.Sampled.Bool() {
				return 1.0
			}

			// Extract operation name from span
			spanName := ctx.Span.Name
			if spanName == "" && ctx.Span.Op != "" {
				spanName = ctx.Span.Op
			}

			// Check if this is an error transaction
			if alwaysSampleErrors && ctx.Span.Status == sentry.SpanStatusInternalError {
				return 1.0
			}

			// Use custom sampling rules
			return getSamplingRate(spanName, customSamplingRules, sampleRate)
		},
	}

	// Set BeforeSend hook
	if configuration.BeforeSend != nil {
		// Use custom BeforeSend but still add service tag
		options.BeforeSend = func(event *sentry.Event, hint *sentry.EventHint) *sentry.Event {
			// Ensure service tag is always added
			if event.Tags == nil {
				event.Tags = make(map[string]string)
			}
			event.Tags["service"] = service

			// Call custom BeforeSend
			return configuration.BeforeSend(event, hint)
		}
	} else {
		// Default BeforeSend hook
		options.BeforeSend = func(event *sentry.Event, hint *sentry.EventHint) *sentry.Event {
			if event.Tags == nil {
				event.Tags = make(map[string]string)
			}
			event.Tags["service"] = service
			return event
		}
	}

	// Optional: Track releases for better issue tracking
	if configuration.Release != "" {
		options.Release = configuration.Release
	} else if version := os.Getenv("APP_VERSION"); version != "" {
		options.Release = version
	}

	// Initialize Sentry with our configuration while holding the lock
	// This prevents race conditions during initialization
	if err := sentry.Init(options); err != nil {
		globalStateMutex.Unlock()
		return fmt.Errorf("failed to initialize sentry: %w", err)
	}

	// Initialize error capture configuration ONLY after successful Sentry init
	// This ensures config is only set when Sentry is actually running
	errorCaptureConfig = &ErrorCaptureConfig{
		CaptureNotFoundErrors:     true, // Default: capture
		CaptureValidationErrors:   true, // Default: capture
		CaptureConflictErrors:     true, // Default: capture
		CaptureUnauthorizedErrors: true, // Default: capture
		CaptureForbiddenErrors:    true, // Default: capture
		CaptureInternalErrors:     true, // Default: capture (critical errors)
		CaptureExternalErrors:     true, // Default: capture (third-party failures)
		CaptureDatabaseErrors:     true, // Default: capture (database issues)
	}

	// Override defaults if explicitly configured
	if configuration.CaptureNotFoundErrors != nil {
		errorCaptureConfig.CaptureNotFoundErrors = *configuration.CaptureNotFoundErrors
	}
	if configuration.CaptureValidationErrors != nil {
		errorCaptureConfig.CaptureValidationErrors = *configuration.CaptureValidationErrors
	}
	if configuration.CaptureConflictErrors != nil {
		errorCaptureConfig.CaptureConflictErrors = *configuration.CaptureConflictErrors
	}
	if configuration.CaptureUnauthorizedErrors != nil {
		errorCaptureConfig.CaptureUnauthorizedErrors = *configuration.CaptureUnauthorizedErrors
	}
	if configuration.CaptureForbiddenErrors != nil {
		errorCaptureConfig.CaptureForbiddenErrors = *configuration.CaptureForbiddenErrors
	}
	if configuration.CaptureInternalErrors != nil {
		errorCaptureConfig.CaptureInternalErrors = *configuration.CaptureInternalErrors
	}
	if configuration.CaptureExternalErrors != nil {
		errorCaptureConfig.CaptureExternalErrors = *configuration.CaptureExternalErrors
	}
	if configuration.CaptureDatabaseErrors != nil {
		errorCaptureConfig.CaptureDatabaseErrors = *configuration.CaptureDatabaseErrors
	}

	// Mark as initialized before releasing the lock
	initialized = true
	globalStateMutex.Unlock()
	return nil
}

// getSamplingRate determines the sampling rate for a given operation
// based on custom rules or defaults
func getSamplingRate(operation string, customRules map[string]float64, defaultRate float64) float64 {
	// If no custom rules, use default
	if customRules == nil {
		return defaultRate
	}

	// Check for exact match first
	if rate, ok := customRules[operation]; ok {
		return rate
	}

	// Check for pattern matches
	for pattern, rate := range customRules {
		if matchesPattern(operation, pattern) {
			return rate
		}
	}

	// Fall back to default
	return defaultRate
}

// matchesPattern checks if an operation matches a pattern
// Supports simple wildcard patterns like "List*", "*Permission*", etc.
func matchesPattern(operation, pattern string) bool {
	// Simple wildcard matching
	if strings.HasPrefix(pattern, "*") && strings.HasSuffix(pattern, "*") {
		// *middle* pattern
		middle := pattern[1 : len(pattern)-1]
		return strings.Contains(operation, middle)
	} else if strings.HasPrefix(pattern, "*") {
		// *suffix pattern
		suffix := pattern[1:]
		return strings.HasSuffix(operation, suffix)
	} else if strings.HasSuffix(pattern, "*") {
		// prefix* pattern
		prefix := pattern[:len(pattern)-1]
		return strings.HasPrefix(operation, prefix)
	}
	// Exact match
	return operation == pattern
}

// Flush waits for all events to be sent to Sentry before the program exits.
// This ensures that error reports and spans are not lost during shutdown.
//
// Always call this function with defer in your main function to ensure
// all telemetry is sent even if the program exits unexpectedly.
//
// The function waits up to 2 seconds for pending events to be sent.
// If not all events are sent within this time, it returns and some events
// may be lost. This timeout prevents the shutdown from hanging indefinitely.
//
// This function is safe to call even if Sentry was not initialized.
//
// Example usage:
//
//	func main() {
//	    if err := herosentry.Init("my-service"); err != nil {
//	        log.Printf("Sentry init failed: %v", err)
//	    }
//	    defer herosentry.Flush() // Always flush before exit
//
//	    // Run service...
//	}
//
// For graceful shutdown:
//
//	func main() {
//	    // ... initialization ...
//
//	    // Handle shutdown signals
//	    sigChan := make(chan os.Signal, 1)
//	    signal.Notify(sigChan, os.Interrupt, syscall.SIGTERM)
//
//	    go func() {
//	        <-sigChan
//	        log.Println("Shutting down...")
//	        herosentry.Flush() // Ensure events are sent
//	        os.Exit(0)
//	    }()
//
//	    // Run service...
//	}
func Flush() {
	// Only flush if initialized
	globalStateMutex.RLock()
	shouldFlush := initialized
	globalStateMutex.RUnlock()

	if shouldFlush {
		// Wait up to 2 seconds for events to be sent
		sentry.Flush(2 * time.Second)
	}
}

// getServiceName returns the configured service name.
// This is an internal helper used by other parts of the SDK
// to tag events with the service name.
//
// Returns:
//   - string: The service name provided during Init(), or empty if not initialized
//
// This function is safe to call before initialization.
func getServiceName() string {
	globalStateMutex.RLock()
	defer globalStateMutex.RUnlock()
	return serviceName
}

// isInitialized returns whether herosentry has been initialized.
// This is an internal helper used to enable no-op behavior
// when Sentry is not configured.
//
// Returns:
//   - bool: true if Init() was called successfully, false otherwise
//
// When false, all SDK operations become no-ops to allow the
// application to run without Sentry (e.g., in development or tests).
func isInitialized() bool {
	globalStateMutex.RLock()
	defer globalStateMutex.RUnlock()
	return initialized
}

// getErrorCaptureConfig returns the current error capture configuration.
// This is an internal helper used by shouldCaptureError.
//
// Returns:
//   - *ErrorCaptureConfig: The config or nil if not initialized
//
// This function is safe to call before initialization.
func getErrorCaptureConfig() *ErrorCaptureConfig {
	globalStateMutex.RLock()
	defer globalStateMutex.RUnlock()
	return errorCaptureConfig
}

// dbPoolContextKey is the context key for storing database connection pool
type dbPoolContextKey struct{}

// SetDBPool stores a database connection pool in the context for monitoring
func SetDBPool(ctx context.Context, db *sql.DB) context.Context {
	return context.WithValue(ctx, dbPoolContextKey{}, db)
}

// DBPoolMiddleware creates HTTP middleware that injects database pool into request context
// This allows herosentry to automatically capture database pool statistics in traces
//
// Usage in main.go:
//
//	mux := http.NewServeMux()
//	// Wrap mux with DB pool middleware
//	muxWithDB := herosentry.DBPoolMiddleware(postGresDB)(mux)
//	// Then use muxWithDB in your server setup
func DBPoolMiddleware(db *sql.DB) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Add database pool to request context
			ctx := SetDBPool(r.Context(), db)
			// Continue with updated context
			next.ServeHTTP(w, r.WithContext(ctx))
		})
	}
}

// enrichSpanWithDBPoolStats adds database connection pool statistics to a Sentry span.
//
// IMPORTANT: These metrics are PER-SERVICE connection pools, NOT database-wide metrics.
// Each service (perms, workflow, communications, etc.) maintains its own independent
// connection pool to the database. The metrics shown here are specific to the individual
// service's pool, not the total connections across all services on the database server.
//
// For example:
//   - Perms service: might show 75/80 connections in use
//   - Workflow service: might show 20/100 connections in use
//   - Communications service: might show 45/50 connections in use
//   - Database server total: would be 75+20+45 = 140 connections (plus other services)
//
// Metrics captured (all are per-service):
//
// db.pool.open_connections (stats.OpenConnections):
//   - Total number of open connections from THIS SERVICE to the database (both idle and in-use)
//   - This is the current size of THIS SERVICE's connection pool
//   - Should be ≤ SetMaxOpenConns() value configured for this service
//
// db.pool.in_use (stats.InUse):
//   - Number of connections from THIS SERVICE currently being used for active queries/transactions
//   - These connections are checked out from THIS SERVICE's pool and actively executing SQL
//   - High values indicate THIS SERVICE is putting heavy load on the database
//
// db.pool.idle (stats.Idle):
//   - Number of connections from THIS SERVICE sitting idle in the pool, ready to be used
//   - These are open connections from THIS SERVICE not currently executing any queries
//   - Idle = OpenConnections - InUse (for this service only)
//   - Should be ≤ SetMaxIdleConns() value for this service
//
// db.pool.wait_count (stats.WaitCount):
//   - Total number of times THIS SERVICE had to wait for a connection
//   - Increments when all of THIS SERVICE's connections are in use and a new request comes in
//   - High values indicate THIS SERVICE's pool is too small for its workload
//
// db.pool.wait_duration_ms (stats.WaitDuration.Milliseconds()):
//   - Total time THIS SERVICE spent waiting for connections (cumulative across all waits)
//   - In milliseconds
//   - High values indicate connection starvation in THIS SERVICE
//   - Average wait time for this service = WaitDuration / WaitCount
//
// db.pool.max_idle_closed (stats.MaxIdleClosed):
//   - Count of THIS SERVICE's connections closed because they exceeded SetMaxIdleConns()
//   - When THIS SERVICE's pool has too many idle connections, extras are closed
//   - High values might indicate THIS SERVICE's MaxIdleConns is set too low
//
// db.pool.max_lifetime_closed (stats.MaxLifetimeClosed):
//   - Count of THIS SERVICE's connections closed because they exceeded SetConnMaxLifetime()
//   - Connections from THIS SERVICE are recycled after their max lifetime to prevent stale connections
//   - Normal to see this increment over time
//   - High values might indicate THIS SERVICE's ConnMaxLifetime is set too short
//
// Example interpretation for a single service (e.g., workflow-service):
//
//	OpenConnections: 50   // THIS SERVICE has 50 total connections to the database
//	InUse: 45            // THIS SERVICE has 45 connections actively running queries
//	Idle: 5              // THIS SERVICE has 5 connections waiting for work
//	WaitCount: 1000      // THIS SERVICE had to wait 1000 times for a connection
//	WaitDuration: 5000ms // THIS SERVICE spent total 5 seconds waiting (avg 5ms per wait)
//	MaxIdleClosed: 10    // THIS SERVICE closed 10 connections for being idle
//	MaxLifetimeClosed: 100 // THIS SERVICE closed 100 connections for age
//
// This would indicate for THIS SPECIFIC SERVICE:
//   - This service's pool is nearly saturated (90% utilization)
//   - This service is experiencing some connection waiting
//   - This service's pool size might need to be increased
//   - This service's connection recycling is working normally
//
// Note: Other services (perms, orgs, etc.) have their own separate pools with
// their own metrics. To see total database load, you'd need to sum across all services.
func enrichSpanWithDBPoolStats(span Span, ctx context.Context) {
	if dbValue := ctx.Value(dbPoolContextKey{}); dbValue != nil {
		if db, ok := dbValue.(*sql.DB); ok && db != nil {
			stats := db.Stats()

			// Add pool stats as span data for visibility in Sentry
			span.SetData("db.pool.open_connections", stats.OpenConnections)
			span.SetData("db.pool.in_use", stats.InUse)
			span.SetData("db.pool.idle", stats.Idle)
			span.SetData("db.pool.wait_count", stats.WaitCount)
			span.SetData("db.pool.wait_duration_ms", stats.WaitDuration.Milliseconds())
			span.SetData("db.pool.max_idle_closed", stats.MaxIdleClosed)
			span.SetData("db.pool.max_lifetime_closed", stats.MaxLifetimeClosed)

			// Add tags for high-level monitoring
			span.SetTag("db.pool.connections", fmt.Sprintf("%d", stats.OpenConnections))
			if stats.OpenConnections > 0 {
				span.SetTag("db.pool.utilization", fmt.Sprintf("%.2f%%", float64(stats.InUse)/float64(stats.OpenConnections)*100))
			}
		}
	}
}
