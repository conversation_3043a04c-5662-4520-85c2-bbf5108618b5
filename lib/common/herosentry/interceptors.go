// Package herosentry provides RPC and HTTP interceptors for automatic distributed tracing.
// This file contains interceptors that automatically handle trace creation, propagation,
// and context enrichment for all incoming and outgoing RPC calls and HTTP requests.
//
// The interceptors handle the complexity of distributed tracing so that service code
// doesn't need to manually manage trace headers or span creation for cross-service calls.
package herosentry

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"

	"connectrpc.com/connect"
	"github.com/getsentry/sentry-go"
)

// RPCServiceInterceptor creates a Connect RPC interceptor for incoming requests.
// This interceptor automatically handles distributed tracing across services.
//
// The interceptor handles three scenarios:
//
// 1. **Existing span in context** (e.g., from permissions middleware):
//   - Returns immediately, letting the existing span continue
//   - This prevents duplicate transactions when middleware already started tracing
//
// 2. **Trace header present** (distributed trace from another service):
//   - Creates a CHILD SPAN (not a new transaction) to continue the trace
//   - This ensures the service call appears as part of the distributed trace
//   - Example: PermissionService receiving a call shows as a child of the caller
//
// 3. **No existing span or trace header** (new request):
//   - Creates a new root transaction
//   - This starts a fresh trace for direct API calls
//
// Usage:
//
//	mux.Handle(orderPath,
//	  connect.NewHandler(
//	    orderService,
//	    connect.WithInterceptors(herosentry.RPCServiceInterceptor()),
//	  ),
//	)
//
// Trace examples:
//
//	With permissions middleware:
//	OrderService.ListOrders (root from middleware)
//	├── calling PermissionService.CheckPermission
//	├── PermissionService.CheckPermission (child span, not new transaction!)
//	│   └── PermsUseCase.CheckPermission
//	└── OrderUseCase.ListOrders
//
//	Without permissions middleware:
//	OrderService.CreateOrder (root from interceptor)
//	└── OrderUseCase.CreateOrder
//
//	Cross-service call:
//	WorkflowService.ProcessOrder (root)
//	├── calling OrderService.UpdateStatus
//	└── OrderService.UpdateStatus (child span via trace header)
//	    └── OrderUseCase.UpdateStatus
func RPCServiceInterceptor() connect.Interceptor {
	return connect.UnaryInterceptorFunc(
		func(next connect.UnaryFunc) connect.UnaryFunc {
			return func(requestContext context.Context, request connect.AnyRequest) (connect.AnyResponse, error) {
				if !isInitialized() {
					return next(requestContext, request)
				}

				// Extract service and method names from procedure
				// Example: "/hero.orders.v2.OrderService/CreateOrder"
				procedure := request.Spec().Procedure
				serviceName := extractServiceName(procedure)
				methodName := extractMethodName(procedure)
				operationName := fmt.Sprintf("%s.%s", serviceName, methodName)

				// IMPORTANT: Check if there's already a span in context
				// This happens when middleware (like permissions) already started tracing
				existingSpan := sentry.SpanFromContext(requestContext)
				if existingSpan != nil {
					// We already have an active span/transaction from upstream middleware
					// DO NOT create a new transaction - this would cause duplicate traces!
					// Simply pass through and let the existing trace continue
					// The UseCase and Repository layers will create child spans under this

					// Initialize error deduplication map if not already present
					// This ensures errors captured in usecase/repository layers aren't
					// duplicated when they bubble up to the connect handler layer
					if getCapturedErrorsMap(requestContext) == nil {
						requestContext = initializeCapturedErrorsMap(requestContext)
					}

					// Add database connection pool statistics to existing span
					// Create a temporary wrapper to add the pool stats
					tempWrapper := &internalSpan{Span: existingSpan}
					enrichSpanWithDBPoolStats(tempWrapper, requestContext)

					// But we still need to handle panics!
					defer func() {
						if recovered := recover(); recovered != nil {
							// Tag the existing span with error info
							existingSpan.SetTag("error", "true")
							existingSpan.SetTag("error.type", "panic")
							existingSpan.SetTag("http.status_code", "500") // Panic = 500
							// Use our improved CaptureException with better formatting
							CaptureException(requestContext, fmt.Errorf("panic: %v", recovered), fmt.Sprintf("Panic in %s", operationName))
							// Ensure the panic is sent to Sentry before re-panicking
							sentry.Flush(2 * time.Second)
							panic(recovered) // Re-panic after capture
						}
					}()

					// Execute handler and capture response
					response, err := next(requestContext, request)

					// Set HTTP status code based on error
					if err != nil {
						if connectErr := new(connect.Error); errors.As(err, &connectErr) {
							existingSpan.SetTag("http.status_code", connectCodeToHTTPStatus(connectErr.Code()))
						} else {
							existingSpan.SetTag("http.status_code", "500")
						}
					} else {
						existingSpan.SetTag("http.status_code", "200")
					}

					return response, err
				}

				// Check for distributed trace headers
				var traceHeader string
				var baggageHeader string
				if request.Header() != nil {
					traceHeader = request.Header().Get(sentry.SentryTraceHeader)
					baggageHeader = request.Header().Get(sentry.SentryBaggageHeader)
				}

				if traceHeader != "" {
					// DISTRIBUTED TRACING: We received a trace header from another service
					// Store the trace header in context for StartSpan to use
					requestContext = context.WithValue(requestContext, SentryTraceHeaderKey, traceHeader)

					// Also store the baggage header if present
					if baggageHeader != "" {
						requestContext = context.WithValue(requestContext, baggageHeaderKey, baggageHeader)
					}

					// Initialize error deduplication map for this distributed trace
					// Even though this request came from another service, we still want to
					// prevent duplicates within THIS service's processing
					requestContext = initializeCapturedErrorsMap(requestContext)

					// Handle panics even in distributed trace path
					defer func() {
						if recovered := recover(); recovered != nil {
							// Use our improved CaptureException with better formatting
							CaptureException(requestContext, fmt.Errorf("panic: %v", recovered), fmt.Sprintf("Panic in %s", operationName))
							// Ensure the panic is sent to Sentry before re-panicking
							sentry.Flush(2 * time.Second)
							panic(recovered) // Re-panic after capture
						}
					}()

					// Pass through without creating a span - let the usecase create the first span
					// This avoids creating a separate transaction
					return next(requestContext, request)
				}

				// NEW TRACE: No trace header means this is a fresh request
				// This could be:
				// - A direct API call from a client
				// - A scheduled job or background task
				// - A service that doesn't have tracing enabled
				//
				// Create a new root transaction for this request

				sentrySpan := sentry.StartTransaction(requestContext, operationName)
				defer sentrySpan.Finish()

				// Set operation type for RPC
				sentrySpan.Op = "rpc.server"
				// Set the description to the operation name
				sentrySpan.Description = operationName

				// Create internal span wrapper and enrich with context
				internalSpanWrapper := &internalSpan{Span: sentrySpan}
				enrichSpanWithContext(internalSpanWrapper, requestContext, 3)

				// Set RPC-specific tags
				internalSpanWrapper.SetTag("rpc.service", serviceName)
				internalSpanWrapper.SetTag("rpc.method", methodName)
				internalSpanWrapper.SetTag("rpc.type", "server")
				internalSpanWrapper.SetTag("span.kind", "server")

				// Add database connection pool statistics
				enrichSpanWithDBPoolStats(internalSpanWrapper, requestContext)

				// Update context with span
				requestContext = sentrySpan.Context()

				// Initialize error deduplication map for this request
				// This map will be shared across all layers (usecase, repository, etc.)
				// to track which errors have been sent to Sentry, preventing duplicates
				requestContext = initializeCapturedErrorsMap(requestContext)

				// Handle panics
				defer func() {
					if recovered := recover(); recovered != nil {
						internalSpanWrapper.SetTag("error", "true")
						internalSpanWrapper.SetTag("error.type", "panic")
						// Use our improved CaptureException with better formatting
						CaptureException(requestContext, fmt.Errorf("panic: %v", recovered), fmt.Sprintf("Panic in %s", operationName))
						// Ensure the panic is sent to Sentry before re-panicking
						sentry.Flush(2 * time.Second)
						panic(recovered) // Re-panic after capture
					}
				}()

				// Execute the RPC handler
				response, err := next(requestContext, request)

				// Handle errors
				if err != nil {
					internalSpanWrapper.SetTag("error", "true")
					internalSpanWrapper.SetTag("rpc.status", "error")
					internalSpanWrapper.SetData("error.message", err.Error())

					// Capture Connect error details if available
					if connectErr := new(connect.Error); errors.As(err, &connectErr) {
						internalSpanWrapper.SetTag("rpc.code", connectErr.Code().String())
						internalSpanWrapper.SetData("error.details", connectErr.Details())
						// Add HTTP status code equivalent for consistent filtering in Sentry
						internalSpanWrapper.SetTag("http.status_code", connectCodeToHTTPStatus(connectErr.Code()))
					} else {
						// Unknown error type, default to 500
						internalSpanWrapper.SetTag("http.status_code", "500")
					}
				} else {
					internalSpanWrapper.SetTag("rpc.status", "success")
					// Add HTTP 200 for successful RPC calls
					internalSpanWrapper.SetTag("http.status_code", "200")
				}

				return response, err
			}
		},
	)
}

// connectCodeToHTTPStatus maps Connect/gRPC error codes to HTTP status codes
// This allows consistent filtering by http.status_code across both HTTP and RPC traces
func connectCodeToHTTPStatus(code connect.Code) string {
	switch code {
	case connect.CodeCanceled:
		return "499" // Client Closed Request
	case connect.CodeUnknown:
		return "500"
	case connect.CodeInvalidArgument:
		return "400" // Bad Request
	case connect.CodeDeadlineExceeded:
		return "504" // Gateway Timeout
	case connect.CodeNotFound:
		return "404" // Not Found
	case connect.CodeAlreadyExists:
		return "409" // Conflict
	case connect.CodePermissionDenied:
		return "403" // Forbidden
	case connect.CodeResourceExhausted:
		return "429" // Too Many Requests
	case connect.CodeFailedPrecondition:
		return "412" // Precondition Failed
	case connect.CodeAborted:
		return "409" // Conflict
	case connect.CodeOutOfRange:
		return "400" // Bad Request
	case connect.CodeUnimplemented:
		return "501" // Not Implemented
	case connect.CodeInternal:
		return "500" // Internal Server Error
	case connect.CodeUnavailable:
		return "503" // Service Unavailable
	case connect.CodeDataLoss:
		return "500" // Internal Server Error
	case connect.CodeUnauthenticated:
		return "401" // Unauthorized
	default:
		return "500" // Default to Internal Server Error for unknown codes
	}
}

// RPCClientInterceptor creates a Connect RPC interceptor for outgoing requests.
// This interceptor automatically:
//   - Creates child spans for outgoing RPC calls
//   - Propagates trace context via headers
//   - Tracks external service performance
//   - Captures failures and latency
//
// Usage:
//
//	assetClient := asset.NewClient(
//	  httpClient,
//	  url,
//	  connect.WithInterceptors(herosentry.RPCClientInterceptor()),
//	)
//
// For an outgoing call to AssetService.GetAsset:
//   - Creates child span "calling AssetService.GetAsset"
//   - Adds header: sentry-trace: {trace-id}-{span-id}-{sampled}
//   - Tracks: target service, method, duration, success/failure
func RPCClientInterceptor() connect.Interceptor {
	return connect.UnaryInterceptorFunc(
		func(next connect.UnaryFunc) connect.UnaryFunc {
			return func(requestContext context.Context, request connect.AnyRequest) (connect.AnyResponse, error) {
				if !isInitialized() {
					return next(requestContext, request)
				}

				// Extract service and method names
				procedure := request.Spec().Procedure
				serviceName := extractServiceName(procedure)
				methodName := extractMethodName(procedure)
				operationName := fmt.Sprintf("calling %s.%s", serviceName, methodName)

				// Start span for outgoing call
				requestContext, _, finishSpan := StartSpan(requestContext, operationName)
				defer finishSpan()

				// Get current span to add tags and set operation type
				if currentSpan := CurrentSpan(requestContext); currentSpan != nil {
					// Explicitly set operation type for outgoing RPC
					if sentrySpan := sentry.SpanFromContext(requestContext); sentrySpan != nil {
						sentrySpan.Op = "rpc.client"
					}

					currentSpan.SetTag("rpc.service", serviceName)
					currentSpan.SetTag("rpc.method", methodName)
					currentSpan.SetTag("rpc.type", "client")
					currentSpan.SetTag("span.kind", "client")
					currentSpan.SetTag("rpc.procedure", procedure)

					// Add database connection pool statistics
					enrichSpanWithDBPoolStats(currentSpan, requestContext)
				}

				// Propagate trace context via headers
				if sentrySpan := sentry.SpanFromContext(requestContext); sentrySpan != nil {
					traceHeader := createTraceHeader(sentrySpan)
					if traceHeader != "" && request.Header() != nil {
						request.Header().Set(sentry.SentryTraceHeader, traceHeader)

						// Also set the baggage header for proper distributed tracing
						// The baggage header contains additional context like dynamic sampling
						if baggageHeader := sentrySpan.ToBaggage(); baggageHeader != "" {
							request.Header().Set(sentry.SentryBaggageHeader, baggageHeader)
						}
					}
				}

				// Make the RPC call
				response, err := next(requestContext, request)

				// Handle response
				if currentSpan := CurrentSpan(requestContext); currentSpan != nil {
					if err != nil {
						currentSpan.SetTag("error", "true")
						currentSpan.SetTag("rpc.status", "error")
						currentSpan.SetData("error.message", err.Error())

						// Add HTTP status code for consistent filtering
						if connectErr := new(connect.Error); errors.As(err, &connectErr) {
							currentSpan.SetTag("rpc.code", connectErr.Code().String())
							currentSpan.SetTag("http.status_code", connectCodeToHTTPStatus(connectErr.Code()))
						} else {
							currentSpan.SetTag("http.status_code", "500")
						}
					} else {
						currentSpan.SetTag("rpc.status", "success")
						currentSpan.SetTag("http.status_code", "200")
					}
				}

				return response, err
			}
		},
	)
}

// HTTPMiddleware creates an HTTP middleware for non-RPC endpoints.
// This middleware automatically:
//   - Creates transactions for incoming HTTP requests
//   - Captures route, method, status codes
//   - Handles panics with proper error reporting
//   - Times request duration
//
// Usage:
//
//	http.Handle("/webhook/twilio", herosentry.HTTPMiddleware(webhookHandler))
//
// For an incoming POST to /webhook/twilio:
//   - Creates transaction "POST /webhook/twilio"
//   - Captures: method, path, status, duration, client IP
//   - Reports panics and errors to Sentry
func HTTPMiddleware(handler http.Handler) http.Handler {
	return http.HandlerFunc(func(responseWriter http.ResponseWriter, httpRequest *http.Request) {
		if !isInitialized() {
			handler.ServeHTTP(responseWriter, httpRequest)
			return
		}

		// Create operation name from method and path
		operationName := fmt.Sprintf("%s %s", httpRequest.Method, httpRequest.URL.Path)

		// Start transaction for HTTP request
		sentrySpan := sentry.StartTransaction(httpRequest.Context(), operationName)
		defer sentrySpan.Finish()

		// Set operation type for HTTP
		sentrySpan.Op = "http.server"
		// Set the description to the operation name
		sentrySpan.Description = operationName

		// Create internal span wrapper and enrich with context
		internalSpanWrapper := &internalSpan{Span: sentrySpan}
		requestContext := sentrySpan.Context()

		// Initialize error deduplication map for this HTTP request
		// Important for webhooks that might capture errors at multiple levels
		requestContext = initializeCapturedErrorsMap(requestContext)

		// Store HTTP request in context for capture functions
		requestContext = context.WithValue(requestContext, httpRequestContextKey, httpRequest)
		enrichSpanWithContext(internalSpanWrapper, requestContext, 3)

		// Set HTTP-specific tags
		internalSpanWrapper.SetTag("http.method", httpRequest.Method)
		internalSpanWrapper.SetTag("http.path", httpRequest.URL.Path)
		internalSpanWrapper.SetTag("http.url", httpRequest.URL.String())
		internalSpanWrapper.SetTag("span.kind", "server")

		// Add database connection pool statistics
		enrichSpanWithDBPoolStats(internalSpanWrapper, requestContext)

		// Create response writer wrapper to capture status code
		wrappedWriter := &responseWriterWrapper{
			ResponseWriter: responseWriter,
			statusCode:     200, // Default if not explicitly set
		}

		// Handle panics
		defer func() {
			if recovered := recover(); recovered != nil {
				internalSpanWrapper.SetTag("error", "true")
				internalSpanWrapper.SetTag("error.type", "panic")
				internalSpanWrapper.SetTag("http.status_code", "500")
				// Use our improved CaptureException with better formatting
				CaptureException(requestContext, fmt.Errorf("panic: %v", recovered), fmt.Sprintf("HTTP Panic in %s", operationName))
				// Ensure the panic is sent to Sentry before re-panicking
				sentry.Flush(2 * time.Second)

				// Only write error response if not already written
				// This prevents "http: multiple response.WriteHeader calls" panic
				// if the handler already started writing a response before panicking
				if !wrappedWriter.written {
					// Use wrappedWriter to ensure status code is captured by our tracking
					wrappedWriter.WriteHeader(http.StatusInternalServerError)
					wrappedWriter.Write([]byte("Internal Server Error"))
				}

				// Re-panic to maintain proper error propagation
				// Without this, panics would be silently swallowed, breaking debugging
				// and preventing upstream handlers from detecting the failure
				panic(recovered)
			}
		}()

		// Update request with new context
		httpRequest = httpRequest.WithContext(requestContext)

		// Call the actual handler
		handler.ServeHTTP(wrappedWriter, httpRequest)

		// Record final status
		internalSpanWrapper.SetTag("http.status_code", fmt.Sprintf("%d", wrappedWriter.statusCode))
		if wrappedWriter.statusCode >= 400 {
			internalSpanWrapper.SetTag("error", "true")
		}
	})
}

// responseWriterWrapper captures the HTTP status code
type responseWriterWrapper struct {
	http.ResponseWriter
	statusCode int
	written    bool
}

func (wrapper *responseWriterWrapper) WriteHeader(statusCode int) {
	if !wrapper.written {
		wrapper.statusCode = statusCode
		wrapper.written = true
	}
	wrapper.ResponseWriter.WriteHeader(statusCode)
}

func (wrapper *responseWriterWrapper) Write(data []byte) (int, error) {
	if !wrapper.written {
		wrapper.written = true
	}
	return wrapper.ResponseWriter.Write(data)
}

// extractServiceName extracts the service name from a Connect RPC procedure.
//
// Example Input: "/hero.orders.v2.OrderService/CreateOrder"
// Example Output: "OrderService"
func extractServiceName(procedure string) string {
	// Remove leading slash and split by remaining slash
	procedure = strings.TrimPrefix(procedure, "/")
	parts := strings.Split(procedure, "/")
	if len(parts) < 2 {
		return "unknown"
	}

	// Extract service name from full package path
	// "hero.orders.v2.OrderService" -> "OrderService"
	servicePath := parts[0]
	pathComponents := strings.Split(servicePath, ".")
	if len(pathComponents) > 0 {
		return pathComponents[len(pathComponents)-1]
	}

	return "unknown"
}

// extractMethodName extracts the method name from a Connect RPC procedure.
//
// Example Input: "/hero.orders.v2.OrderService/CreateOrder"
// Example Output: "CreateOrder"
func extractMethodName(procedure string) string {
	// Remove leading slash and split
	procedure = strings.TrimPrefix(procedure, "/")
	parts := strings.Split(procedure, "/")
	if len(parts) >= 2 {
		return parts[1]
	}
	return "unknown"
}
