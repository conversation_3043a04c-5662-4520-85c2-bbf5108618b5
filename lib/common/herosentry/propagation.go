// Package herosentry provides distributed tracing propagation functionality.
// This file handles the parsing and creation of Sentry trace headers that enable
// distributed tracing across microservices in the Hero Core system.
//
// Distributed tracing allows tracking a single request as it flows through multiple
// services, maintaining the same trace ID while creating parent-child relationships
// between spans across service boundaries.
//
// The trace header format follows Sentry's standard: {trace_id}-{span_id}-{sampled}
//
// Note: The utility functions parseTraceHeader, injectTraceContext, and extractTraceContext
// are provided for manual trace propagation scenarios where the automatic interceptors
// are not suitable (e.g., custom HTTP clients, message queues, or other protocols).
// The interceptors handle trace propagation automatically for most use cases.
package herosentry

import (
	"context"
	"fmt"
	"strings"

	"github.com/getsentry/sentry-go"
)

// parseTraceHeader parses a sentry-trace header value into its components.
// The header format is: {trace_id}-{parent_span_id}-{sampled}
//
// This is a utility function for manual trace header parsing. In most cases,
// the SDK's interceptors handle this automatically using Sentry's built-in
// ContinueFromHeaders function.
//
// Use cases:
//   - Debugging trace headers
//   - Custom protocol implementations
//   - Testing trace propagation
//
// Parameters:
//   - headerValue: The raw sentry-trace header value
//
// Returns:
//   - traceID: 32-character hex string identifying the trace
//   - parentSpanID: 16-character hex string identifying the parent span
//   - sampled: Whether this trace is being sampled (true if "1", false otherwise)
//
// Example Input:
//
//	"d4cda95b652f4a1592b449d5929fda1b-6e0c63257de34c92-1"
//
// Example Output:
//
//	traceID: "d4cda95b652f4a1592b449d5929fda1b"
//	parentSpanID: "6e0c63257de34c92"
//	sampled: true
func parseTraceHeader(headerValue string) (traceID string, parentSpanID string, sampled bool) {
	headerParts := strings.Split(headerValue, "-")
	if len(headerParts) >= 2 {
		traceID = headerParts[0]
		parentSpanID = headerParts[1]
		if len(headerParts) >= 3 && headerParts[2] == "1" {
			sampled = true
		}
	}
	return traceID, parentSpanID, sampled
}

// createTraceHeader creates a sentry-trace header value from a span.
// This header is used to propagate trace context to downstream services.
//
// Parameters:
//   - sentrySpan: The current Sentry span to create header from
//
// Returns:
//   - A formatted sentry-trace header string, or empty string if span is nil
//
// Example Input:
//
//	A span with:
//	- TraceID: d4cda95b652f4a1592b449d5929fda1b
//	- SpanID: 6e0c63257de34c92
//	- Sampled: true
//
// Example Output:
//
//	"d4cda95b652f4a1592b449d5929fda1b-6e0c63257de34c92-1"
func createTraceHeader(sentrySpan *sentry.Span) string {
	if sentrySpan == nil {
		return ""
	}

	traceID := sentrySpan.TraceID.String()
	spanID := sentrySpan.SpanID.String()
	sampledFlag := "0"
	if sentrySpan.Sampled.Bool() {
		sampledFlag = "1"
	}

	return fmt.Sprintf("%s-%s-%s", traceID, spanID, sampledFlag)
}

// injectTraceContext adds Sentry trace headers to outgoing HTTP requests.
// This enables distributed tracing by propagating the trace context to downstream services.
//
// This is a utility function for manual trace propagation. The RPC client interceptor
// handles this automatically for Connect RPC calls. Use this function when:
//   - Working with custom HTTP clients
//   - Integrating with non-RPC protocols (webhooks, REST APIs)
//   - Propagating traces through message queues
//
// Parameters:
//   - requestContext: Context containing the current span
//   - headers: HTTP headers map to inject the trace header into
//
// Example Usage:
//
//	// For a custom HTTP client
//	headers := make(map[string][]string)
//	injectTraceContext(ctx, headers)
//	req.Header = headers
//	// headers now contains: {"sentry-trace": ["d4cda95b652f4a1592b449d5929fda1b-6e0c63257de34c92-1"]}
//
// The injected header allows the receiving service to continue the same trace,
// creating a parent-child relationship between spans across service boundaries.
func injectTraceContext(requestContext context.Context, headers map[string][]string) {
	// Validate headers map is not nil to prevent panic
	if headers == nil {
		return
	}

	sentrySpan := sentry.SpanFromContext(requestContext)
	if sentrySpan == nil {
		return
	}

	traceHeader := createTraceHeader(sentrySpan)
	if traceHeader != "" {
		headers[sentry.SentryTraceHeader] = []string{traceHeader}
	}
}

// extractTraceContext extracts trace context from incoming HTTP headers.
// This is used by receiving services to continue a distributed trace.
//
// This is a utility function for manual trace extraction. The RPC service interceptor
// handles this automatically for Connect RPC calls. Use this function when:
//   - Building custom HTTP servers
//   - Processing webhook requests
//   - Extracting traces from message queue headers
//
// Note: This returns a new context with background context as parent. You may want
// to use context.WithValue on your existing context instead.
//
// Parameters:
//   - headers: HTTP headers map containing potential trace headers
//
// Returns:
//   - A context with the trace header stored for later use
//
// Example Input:
//
//	headers = map[string][]string{
//	  "sentry-trace": {"d4cda95b652f4a1592b449d5929fda1b-6e0c63257de34c92-1"},
//	  "content-type": {"application/json"},
//	}
//
// Example Output:
//
//	A context containing the trace header value, which can be retrieved using:
//	ctx.Value(SentryTraceHeaderKey).(string) // "d4cda95b652f4a1592b449d5929fda1b-6e0c63257de34c92-1"
//
// This context is later used by StartSpan to continue the distributed trace.
func extractTraceContext(headers map[string][]string) context.Context {
	traceHeaderValues, hasTraceHeader := headers[sentry.SentryTraceHeader]
	if !hasTraceHeader || len(traceHeaderValues) == 0 {
		return context.Background()
	}

	// Store the trace header in context for later use
	return context.WithValue(context.Background(), SentryTraceHeaderKey, traceHeaderValues[0])
}
