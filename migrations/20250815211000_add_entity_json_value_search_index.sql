-- +goose Up
-- +goose StatementBegin
/*──────────────────────────────────────────────────────────────────────────────
  Entity JSON value-only search index
  - Adds a GIN index over jsonb_to_tsvector on entities.data to support
    fast value-only text search using full-text queries.
  - This indexes only JSON string values (not keys) via the '"["string"]"' option.
  - To leverage this index, query with:
      jsonb_to_tsvector('simple', e.data, '["string"]') @@ plainto_tsquery($1)
  - Also drops the old gin_entities_data index which is no longer used for
    free-text search.
──────────────────────────────────────────────────────────────────────────────*/

-- Drop the old general-purpose GIN index on the data column
DROP INDEX IF EXISTS gin_entities_data;

-- Create the new, more specific index for value-only text search
CREATE INDEX IF NOT EXISTS gin_entities_data_strings_tsv
  ON entities
  USING GIN (jsonb_to_tsvector('simple', data, '["string"]'));

-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP INDEX IF EXISTS gin_entities_data_strings_tsv;

-- Recreate the old GIN index on the data column
CREATE INDEX gin_entities_data ON entities USING gin (data);
-- +goose StatementEnd


