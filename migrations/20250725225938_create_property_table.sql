-- +goose Up
-- +goose StatementBegin

-- ===================================================================
-- Table: properties
-- ===================================================================

CREATE TABLE properties (
    id TEXT PRIMARY KEY,
    org_id INTEGER NOT NULL REFERENCES orgs(id) ON DELETE CASCADE,
    property_number TEXT, -- Property/Evidence number for tracking
    is_evidence BOOLEAN DEFAULT FALSE,
    retention_period TEXT,
    property_status INT NOT NULL,
    disposal_type INT,
    notes TEXT,
    current_custodian TEXT,
    current_location TEXT,
    custody_chain JSONB DEFAULT '[]'::jsonb,
    property_schema JSONB DEFAULT '{}'::jsonb, -- Now contains property_type and other details
    create_time TIMESTAMPTZ DEFAULT NOW(),
    update_time TIMESTAMPTZ DEFAULT NOW(),
    created_by TEXT,
    updated_by TEXT,
    version INT NOT NULL DEFAULT 1,
    status INT DEFAULT 1, -- 1 = ACTIVE
    resource_type TEXT NOT NULL DEFAULT 'PROPERTY'
);

ALTER TABLE properties ENABLE ROW LEVEL SECURITY;
CREATE POLICY "properties_organization_access" ON properties
    USING (org_id::text = current_setting('app.org_id'))
    WITH CHECK (org_id::text = current_setting('app.org_id'));

CREATE POLICY "properties_meta_org_access" ON properties
    USING (current_setting('app.org_id') = '-1')
    WITH CHECK (current_setting('app.org_id') = '-1');

-- Performance improvements for properties:
CREATE INDEX idx_properties_org_id ON properties (org_id);
CREATE INDEX idx_properties_org_create ON properties (org_id, create_time DESC);
CREATE INDEX idx_properties_status ON properties (status);
CREATE INDEX idx_properties_property_number ON properties (property_number);
CREATE INDEX idx_properties_property_status ON properties (property_status);
CREATE INDEX idx_properties_current_custodian ON properties (current_custodian);
-- Index for property_type in JSON schema using btree for exact matches
CREATE INDEX idx_properties_schema_property_type ON properties ((property_schema->>'property_type'));

-- ===================================================================
-- Table: property_versions
-- ===================================================================

CREATE TABLE property_versions (
    property_id TEXT NOT NULL REFERENCES properties(id) ON DELETE CASCADE,
    version INT NOT NULL,
    property_snapshot JSONB,
    modified_by TEXT REFERENCES assets(id) ON DELETE SET NULL,
    modified_time TIMESTAMPTZ,
    change_comment TEXT,
    org_id INTEGER NOT NULL REFERENCES orgs(id) ON DELETE CASCADE,
    PRIMARY KEY (property_id, version)
);

ALTER TABLE property_versions ENABLE ROW LEVEL SECURITY;
CREATE POLICY "property_versions_organization_access" ON property_versions
    USING (org_id::text = current_setting('app.org_id'))
    WITH CHECK (org_id::text = current_setting('app.org_id'));

CREATE POLICY "property_versions_meta_org_access" ON property_versions
    USING (current_setting('app.org_id') = '-1')
    WITH CHECK (current_setting('app.org_id') = '-1');

-- Performance improvements for property_versions:
CREATE INDEX idx_property_versions_org_id ON property_versions (org_id);

-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin

-- Drop the properties tables
DROP TABLE IF EXISTS property_versions;
DROP TABLE IF EXISTS properties;

-- +goose StatementEnd
