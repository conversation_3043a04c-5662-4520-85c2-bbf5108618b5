-- +goose Up
-- +goose StatementBegin
-- Create separate table for org call forwarding config
CREATE TABLE IF NOT EXISTS org_call_forwarding_config (
    org_id INTEGER PRIMARY KEY REFERENCES orgs(id) ON DELETE CASCADE,
    is_call_forwarding_enabled BOOLEAN NOT NULL DEFAULT false,
    forward_to_number TEXT,
    assigned_asset_id TEXT NULL REFERENCES assets(id) ON DELETE SET NULL,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Indexes
CREATE INDEX IF NOT EXISTS idx_org_cfc_assigned_asset_id ON org_call_forwarding_config(assigned_asset_id);

-- RLS mirroring orgs by org context
ALTER TABLE org_call_forwarding_config ENABLE ROW LEVEL SECURITY;
CREATE POLICY org_cfc_organization_access ON org_call_forwarding_config
    USING (org_id::text = current_setting('app.org_id'))
    WITH CHECK (org_id::text = current_setting('app.org_id'));

CREATE POLICY org_cfc_meta_org_access ON org_call_forwarding_config
    USING (current_setting('app.org_id') = '-1')
    WITH CHECK (current_setting('app.org_id') = '-1');

-- Backfill from orgs
INSERT INTO org_call_forwarding_config (org_id, is_call_forwarding_enabled, forward_to_number, assigned_asset_id, updated_at)
SELECT id, is_call_forwarding_enabled, forward_to_number, NULL, now()
FROM orgs
ON CONFLICT (org_id) DO NOTHING;

-- Drop forwarded_from_number from orgs if exists (no longer used)
ALTER TABLE orgs DROP COLUMN IF EXISTS forwarded_from_number;

-- Drop duplicate columns from orgs table since we're using the new dedicated table
ALTER TABLE orgs DROP COLUMN IF EXISTS is_call_forwarding_enabled;
ALTER TABLE orgs DROP COLUMN IF EXISTS forward_to_number;
ALTER TABLE orgs DROP COLUMN IF EXISTS call_forwarding_type;
ALTER TABLE orgs DROP COLUMN IF EXISTS sip_uri;
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
-- Restore columns to orgs table
ALTER TABLE orgs ADD COLUMN IF NOT EXISTS forwarded_from_number TEXT;
ALTER TABLE orgs ADD COLUMN IF NOT EXISTS is_call_forwarding_enabled BOOLEAN DEFAULT false;
ALTER TABLE orgs ADD COLUMN IF NOT EXISTS forward_to_number TEXT;
ALTER TABLE orgs ADD COLUMN IF NOT EXISTS call_forwarding_type TEXT;
ALTER TABLE orgs ADD COLUMN IF NOT EXISTS sip_uri TEXT;

DROP POLICY IF EXISTS org_cfc_meta_org_access ON org_call_forwarding_config;
DROP POLICY IF EXISTS org_cfc_organization_access ON org_call_forwarding_config;
ALTER TABLE org_call_forwarding_config DISABLE ROW LEVEL SECURITY;

DROP INDEX IF EXISTS idx_org_cfc_assigned_asset_id;
DROP TABLE IF EXISTS org_call_forwarding_config;
-- +goose StatementEnd


