-- +goose Up
-- +goose StatementBegin
-- Add new timestamps to track logon/logoff semantics explicitly
ALTER TABLE assets
  ADD COLUMN IF NOT EXISTS active_since_time TIMESTAMPTZ NULL,
  ADD COLUMN IF NOT EXISTS last_active_time TIMESTAMPTZ NULL;

-- Indexes for efficient filtering/sorting
CREATE INDEX IF NOT EXISTS assets_active_since_time_idx ON assets (active_since_time);
CREATE INDEX IF NOT EXISTS assets_last_active_time_idx ON assets (last_active_time);

-- NOTE: Business logic for populating these columns is handled in the
-- application data layer (postgres_assets_repo.go). This migration intentionally
-- avoids creating DB triggers so migrations remain schema/backfill only.

-- Backfill: set last_active_time for currently OFFLINE units
UPDATE assets SET last_active_time = status_changed_time
WHERE status = 2 AND last_active_time IS NULL;

-- Note: We intentionally do not backfill active_since_time for non-AVAILABLE states
-- to avoid implying incorrect logon time without history.
-- For currently AVAILABLE units, we can seed with status_changed_time as best-effort.
UPDATE assets SET active_since_time = status_changed_time
WHERE status = 1 AND active_since_time IS NULL;

-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
-- No trigger/function were created in Up; nothing to drop here.

DROP INDEX IF EXISTS assets_active_since_time_idx;
DROP INDEX IF EXISTS assets_last_active_time_idx;

ALTER TABLE assets
  DROP COLUMN IF EXISTS active_since_time,
  DROP COLUMN IF EXISTS last_active_time;
-- +goose StatementEnd


