-- +goose Up
-- Add forward_to_number column to orgs table
ALTER TABLE orgs ADD COLUMN forward_to_number TEXT;

-- Create call_forward_events table for tracking and analytics
-- This table serves as an event log for all call forwarding attempts
-- Supports basic analytics on forwarding success/failure rates by organization
CREATE TABLE call_forward_events (
    id SERIAL PRIMARY KEY,
    call_sid TEXT NOT NULL,                                    -- Parent/original Call SID (Twilio) for linking
    org_id INTEGER NOT NULL REFERENCES orgs(id),              -- Organization this forwarding event belongs to
    from_number TEXT NOT NULL,                                 -- Originating caller number
    to_number TEXT NOT NULL,                                   -- Target number where call was forwarded
    status TEXT NOT NULL DEFAULT 'initiated',                  -- Normalized state: initiated|pending|answered|completed|failed|missed

    -- Correlation to child leg and situation
    child_call_sid TEXT,                                       -- Child CallSid created by Twilio <Dial>
    situation_id UUID,                                        -- Situation created for the forwarded call

    -- Per-phase timestamps derived from child-leg status callbacks
    initiated_at TIMESTAMPTZ,                                  -- When child leg was initiated
    ringing_at TIMESTAMPTZ,                                    -- When child leg started ringing
    answered_at TIMESTAMPTZ,                                   -- When mobile answered (CallStatus=in-progress)
    completed_at TIMESTAMPTZ,                                  -- When child leg completed

    -- Durations and connection outcome
    ring_to_answer_sec INTEGER,                                 -- answered_at - ringing_at (time-to-answer)
    answer_to_complete_sec INTEGER,                             -- completed_at - answered_at (talk time)
    bridged BOOLEAN,                                            -- DialBridged=true/false from forward-action

    -- Failure diagnostics (optional)
    failure_reason TEXT,                                        -- busy|no-answer|failed|canceled (friendly)
    sip_response_code INTEGER,                                  -- Final SIP response when present
    stir_status TEXT,                                           -- STIR/SHAKEN attestation (A/B/C) when present

    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(), -- Record creation timestamp
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()           -- Last update timestamp
);

-- Enable Row Level Security and add org-based access policy
ALTER TABLE call_forward_events ENABLE ROW LEVEL SECURITY;
CREATE POLICY call_forward_events_organization_access ON call_forward_events
    USING (org_id::text = current_setting('app.org_id'))
    WITH CHECK (org_id::text = current_setting('app.org_id'));

CREATE POLICY call_forward_events_meta_org_access ON call_forward_events
    USING (current_setting('app.org_id') = '-1')
    WITH CHECK (current_setting('app.org_id') = '-1');

-- Performance indexes for common query patterns
CREATE INDEX idx_call_forward_events_org_id ON call_forward_events(org_id);           -- Organization-based filtering
CREATE INDEX idx_call_forward_events_created_at ON call_forward_events(created_at);   -- Time-based analytics queries
CREATE UNIQUE INDEX idx_call_forward_events_call_sid ON call_forward_events(call_sid); -- One row per parent/original CallSid
CREATE INDEX idx_call_forward_events_child_call_sid ON call_forward_events(child_call_sid);
CREATE INDEX idx_call_forward_events_status ON call_forward_events(status);
CREATE INDEX idx_call_forward_events_situation ON call_forward_events(situation_id);

-- +goose Down
-- ==============================================================================

-- Remove indexes first
DROP INDEX IF EXISTS idx_call_forward_events_created_at;
DROP INDEX IF EXISTS idx_call_forward_events_org_id;
DROP INDEX IF EXISTS idx_call_forward_events_call_sid;
DROP INDEX IF EXISTS idx_call_forward_events_child_call_sid;
DROP INDEX IF EXISTS idx_call_forward_events_status;
DROP INDEX IF EXISTS idx_call_forward_events_situation;

-- Remove RLS policies first
DROP POLICY IF EXISTS call_forward_events_organization_access ON call_forward_events;
DROP POLICY IF EXISTS call_forward_events_meta_org_access ON call_forward_events;

-- Drop call_forward_events table
DROP TABLE IF EXISTS call_forward_events;

-- Remove forward_to_number column from orgs table
ALTER TABLE orgs DROP COLUMN IF EXISTS forward_to_number;