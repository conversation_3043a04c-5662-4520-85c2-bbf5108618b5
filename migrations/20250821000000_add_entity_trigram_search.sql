-- +goose Up
-- +goose StatementBegin
/*──────────────────────────────────────────────────────────────────────────────
  Entity JSON trigram-based partial matching search
  - Migrates from full-text search to trigram-based search
  - Creates a function to extract JSON values for searching
  - Supports partial string matching using trigram similarity
  - Indexes all data types (strings, numbers, booleans) as text
  - Enables ILIKE queries with good performance
──────────────────────────────────────────────────────────────────────────────*/

-- Drop the previous full-text search index if it exists
DROP INDEX IF EXISTS gin_entities_data_strings_tsv;

-- Enable trigram extension for partial matching
CREATE EXTENSION IF NOT EXISTS pg_trgm;

-- Create a function to recursively extract ALL JSON values (excluding keys)
-- This function traverses the entire JSON structure and extracts every value
-- from nested objects and arrays, no matter how deep
-- Also saves normalized phone numbers (digits only) for better search
CREATE OR REPLACE FUNCTION jsonb_values_as_text(data jsonb)
RETURNS text
LANGUAGE plpgsql
IMMUTABLE
PARALLEL SAFE
AS $$
DECLARE
  accumulated_values text[];          -- Array to accumulate all extracted values
  accumulated_values_text text;      -- Final concatenated text result
  extracted_value_text text;         -- Temporarily holds a single extracted value
  normalized_text text;               -- Normalized version (digits only for phone-like values)
  digit_count int;                    -- Count of digits in the value
  current_json_value jsonb;          -- Current JSON value being processed
  current_object_key text;           -- Current key when iterating through object
  current_array_element jsonb;       -- Current element when iterating through array
  recursive_extraction_result text;  -- Result from recursive calls
BEGIN
  -- Handle NULL input gracefully
  IF data IS NULL THEN
    RETURN NULL;
  END IF;
  
  -- Initialize the accumulator as empty array
  accumulated_values := ARRAY[]::text[];
  
  -- CASE 1: Input is a JSON OBJECT
  -- We iterate through all key-value pairs but ONLY extract values, never keys
  IF jsonb_typeof(data) = 'object' THEN
    
    -- Loop through each key-value pair in the object
    -- jsonb_each returns (key, value) pairs - we ignore keys completely
    FOR current_object_key, current_json_value IN SELECT * FROM jsonb_each(data)
    LOOP
      
      -- NESTED STRUCTURE: If the value is another object or array, recurse into it
      IF jsonb_typeof(current_json_value) IN ('object', 'array') THEN
        -- Recursively extract values from the nested structure
        recursive_extraction_result := jsonb_values_as_text(current_json_value);
        
        -- Add results from recursion to our array
        IF recursive_extraction_result IS NOT NULL AND recursive_extraction_result != '' THEN
          accumulated_values := accumulated_values || string_to_array(recursive_extraction_result, ' ');
        END IF;
        
      -- PRIMITIVE VALUE: String, number, or boolean - these are the actual values we want
      ELSIF jsonb_typeof(current_json_value) IN ('string', 'number', 'boolean') THEN
        -- Convert JSONB to text and remove surrounding quotes
        -- For example: "Anirudha" becomes Anirudha, 123 stays as 123
        extracted_value_text := trim(both '"' from current_json_value::text);
        
        -- Only add non-empty values (skip empty strings and null values)
        IF extracted_value_text != '' AND extracted_value_text != 'null' THEN
          accumulated_values := array_append(accumulated_values, extracted_value_text);
          
          -- If it looks like it might be a phone number (has digits and common phone chars)
          -- save a normalized version with just digits
          IF extracted_value_text ~ '\d' AND extracted_value_text ~ '[\(\)\-\.\s\+]' THEN
            normalized_text := regexp_replace(extracted_value_text, '[^0-9]', '', 'g');
            -- Count the digits
            digit_count := length(normalized_text);
            -- If it has a reasonable number of digits for a phone (7-15 digits), save the normalized version
            IF digit_count >= 7 AND digit_count <= 15 THEN
              accumulated_values := array_append(accumulated_values, normalized_text);
            END IF;
          END IF;
        END IF;
      END IF;
      -- Note: We completely ignore the current_object_key - this ensures keys are never searchable
    END LOOP;
    
  -- CASE 2: Input is a JSON ARRAY
  -- We iterate through all elements in the array
  ELSIF jsonb_typeof(data) = 'array' THEN
    
    -- Loop through each element in the array
    FOR current_array_element IN SELECT * FROM jsonb_array_elements(data)
    LOOP
      
      -- NESTED STRUCTURE: If the element is an object or another array, recurse into it
      IF jsonb_typeof(current_array_element) IN ('object', 'array') THEN
        -- Recursively extract values from the nested structure
        recursive_extraction_result := jsonb_values_as_text(current_array_element);
        
        -- Add results from recursion to our array
        IF recursive_extraction_result IS NOT NULL AND recursive_extraction_result != '' THEN
          accumulated_values := accumulated_values || string_to_array(recursive_extraction_result, ' ');
        END IF;
        
      -- PRIMITIVE VALUE: String, number, or boolean in the array
      ELSIF jsonb_typeof(current_array_element) IN ('string', 'number', 'boolean') THEN
        -- Convert JSONB to text and remove surrounding quotes
        extracted_value_text := trim(both '"' from current_array_element::text);
        
        -- Only add non-empty values
        IF extracted_value_text != '' AND extracted_value_text != 'null' THEN
          accumulated_values := array_append(accumulated_values, extracted_value_text);
          
          -- If it looks like it might be a phone number, save normalized version
          IF extracted_value_text ~ '\d' AND extracted_value_text ~ '[\(\)\-\.\s\+]' THEN
            normalized_text := regexp_replace(extracted_value_text, '[^0-9]', '', 'g');
            digit_count := length(normalized_text);
            IF digit_count >= 7 AND digit_count <= 15 THEN
              accumulated_values := array_append(accumulated_values, normalized_text);
            END IF;
          END IF;
        END IF;
      END IF;
    END LOOP;
    
  -- CASE 3: Input is a PRIMITIVE value at root level (string, number, or boolean)
  -- This handles cases where the entire JSON is just a single value
  ELSIF jsonb_typeof(data) IN ('string', 'number', 'boolean') THEN
    -- Convert to text and remove quotes
    extracted_value_text := trim(both '"' from data::text);
    
    -- Only use if non-empty
    IF extracted_value_text != '' AND extracted_value_text != 'null' THEN
      accumulated_values := array_append(accumulated_values, extracted_value_text);
      
      -- If it looks like it might be a phone number, save normalized version
      IF extracted_value_text ~ '\d' AND extracted_value_text ~ '[\(\)\-\.\s\+]' THEN
        normalized_text := regexp_replace(extracted_value_text, '[^0-9]', '', 'g');
        digit_count := length(normalized_text);
        IF digit_count >= 7 AND digit_count <= 15 THEN
          accumulated_values := array_append(accumulated_values, normalized_text);
        END IF;
      END IF;
    END IF;
  END IF;
  
  -- POST-PROCESSING: Join all values with spaces
  -- We keep duplicates to avoid any sorting that breaks up multi-word values
  RETURN array_to_string(accumulated_values, ' ');
END;
$$;

-- Add generated column containing only JSON values (no keys)
ALTER TABLE entities 
ADD COLUMN IF NOT EXISTS data_values text GENERATED ALWAYS AS (
  jsonb_values_as_text(data)
) STORED;

-- Create trigram index for fast partial matching on values only
CREATE INDEX IF NOT EXISTS gin_entities_data_values
  ON entities
  USING GIN (data_values gin_trgm_ops);

-- Also create a standard B-tree index for exact matches if needed
CREATE INDEX IF NOT EXISTS idx_entities_data_values_text
  ON entities
  USING btree (data_values text_pattern_ops);

-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
-- Drop the new indexes
DROP INDEX IF EXISTS idx_entities_data_values_text;
DROP INDEX IF EXISTS gin_entities_data_values;

-- Drop the generated column
ALTER TABLE entities DROP COLUMN IF EXISTS data_values;

-- Drop the function
DROP FUNCTION IF EXISTS jsonb_values_as_text(jsonb);

-- Recreate the previous full-text search index
CREATE INDEX gin_entities_data_strings_tsv
  ON entities
  USING GIN (jsonb_to_tsvector('simple', data, '["string"]'));
-- +goose StatementEnd