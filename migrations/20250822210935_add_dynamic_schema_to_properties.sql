-- +goose Up
-- +goose StatementBegin

-- Add dynamic schema support and NIBRS property type to properties table  
-- Removes legacy property_schema column since there are no real customers yet
-- Also adds NIBRS property type support and removes deprecated status column

ALTER TABLE properties 
ADD COLUMN schema_id TEXT REFERENCES entity_schemas(id) ON DELETE SET NULL,
ADD COLUMN schema_version INT,
ADD COLUMN details JSONB;

-- Add NIBRS property type support
ALTER TABLE properties 
ADD COLUMN nibrs_property_type INT; -- NIBRS property type - nullable

-- Drop the old status column since it's no longer in the protobuf
ALTER TABLE properties DROP COLUMN IF EXISTS status;

-- Drop legacy property_schema column since there are no real customers yet
ALTER TABLE properties DROP COLUMN IF EXISTS property_schema;

-- Add efficient indexes for dynamic schema queries
CREATE INDEX idx_properties_schema_id ON properties (schema_id) WHERE schema_id IS NOT NULL;
CREATE INDEX gin_properties_details ON properties USING gin (details) WHERE details IS NOT NULL;

-- Add index for NIBRS property type
CREATE INDEX idx_properties_nibrs_property_type ON properties (nibrs_property_type) WHERE nibrs_property_type IS NOT NULL;

-- Remove old status index since column is dropped
DROP INDEX IF EXISTS idx_properties_status;

-- Add comments for documentation
COMMENT ON COLUMN properties.schema_id IS 'Dynamic schema ID (reuses entity_schemas)';
COMMENT ON COLUMN properties.schema_version IS 'Schema version used when property was created';
COMMENT ON COLUMN properties.details IS 'Dynamic form data (replaces legacy property_schema)';
COMMENT ON COLUMN properties.nibrs_property_type IS 'NIBRS property type (nullable)';

-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin

-- Re-add the status column 
ALTER TABLE properties ADD COLUMN status INT DEFAULT 1;

-- Re-add legacy property_schema column
ALTER TABLE properties ADD COLUMN property_schema JSONB;

-- Remove the NIBRS property type column
ALTER TABLE properties DROP COLUMN IF EXISTS nibrs_property_type;

-- Re-create status index
CREATE INDEX idx_properties_status ON properties (status);

-- Drop the NIBRS index
DROP INDEX IF EXISTS idx_properties_nibrs_property_type;

-- Drop dynamic schema indexes
DROP INDEX IF EXISTS gin_properties_details;
DROP INDEX IF EXISTS idx_properties_schema_id;

-- Drop dynamic schema columns
ALTER TABLE properties 
DROP COLUMN IF EXISTS details,
DROP COLUMN IF EXISTS schema_version,
DROP COLUMN IF EXISTS schema_id;

-- +goose StatementEnd