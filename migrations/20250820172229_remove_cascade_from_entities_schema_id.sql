-- +goose Up
-- +goose StatementBegin
-- Remove ON DELETE CASCADE from schema_id foreign key in entities table
-- Note: The foreign key was created inline in the original migration, so PostgreSQL
-- auto-generated a constraint name (typically 'entities_schema_id_fkey')
-- You can verify the actual constraint name by running:
-- SELECT conname FROM pg_constraint WHERE conrelid = 'entities'::regclass AND contype = 'f';

-- First, drop the existing foreign key constraint (column and data remain intact)
-- If the constraint name is different in your database, update it here
ALTER TABLE entities DROP CONSTRAINT IF EXISTS entities_schema_id_fkey;

-- Then, add the foreign key back without ON DELETE CASCADE
-- This prevents cascade deletion while keeping the column and data
ALTER TABLE entities 
ADD CONSTRAINT entities_schema_id_fkey 
FOREIGN KEY (schema_id) 
REFERENCES entity_schemas(id);
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
-- Restore the foreign key with ON DELETE CASCADE to match original migration

-- Drop the foreign key constraint
ALTER TABLE entities DROP CONSTRAINT IF EXISTS entities_schema_id_fkey;

-- Add the foreign key back with ON DELETE CASCADE (as it was originally)
ALTER TABLE entities 
ADD CONSTRAINT entities_schema_id_fkey 
FOREIGN KEY (schema_id) 
REFERENCES entity_schemas(id) ON DELETE CASCADE;
-- +goose StatementEnd