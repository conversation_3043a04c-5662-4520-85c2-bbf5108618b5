-- +goose Up
-- +goose StatementBegin

-- Add case_id column to orders table for MANAGE_CASE order type
ALTER TABLE orders 
    ADD COLUMN IF NOT EXISTS case_id TEXT
    REFERENCES cases(id) ON DELETE CASCADE DEFERRABLE INITIALLY DEFERRED;

-- Add index for efficient case order lookups
CREATE INDEX IF NOT EXISTS idx_orders_case_id ON orders(case_id) WHERE case_id IS NOT NULL;

-- Add index for case_id with status for filtered queries
CREATE INDEX IF NOT EXISTS idx_orders_case_id_status ON orders(case_id, status) WHERE case_id IS NOT NULL;

-- Add comment on the column
COMMENT ON COLUMN orders.case_id IS 'Reference to the case for MANAGE_CASE order types';

-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin

-- Drop indexes
DROP INDEX IF EXISTS idx_orders_case_id_status;
DROP INDEX IF EXISTS idx_orders_case_id;

-- Drop the column
ALTER TABLE orders DROP COLUMN IF EXISTS case_id;

-- +goose StatementEnd