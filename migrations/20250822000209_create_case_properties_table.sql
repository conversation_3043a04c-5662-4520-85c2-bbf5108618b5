-- +goose Up
-- +goose StatementBegin

-- ===================================================================
-- Table: case_properties
-- ===================================================================

CREATE TABLE IF NOT EXISTS case_properties (
    case_id       TEXT NOT NULL REFERENCES cases(id) ON DELETE CASCADE,
    property_id   TEXT NOT NULL,                       -- hero.property.v1.Property.id
    display_name  TEXT,                                -- Optional cached label
    relation_type TEXT,                                -- Caller-defined relation
    org_id        INTEGER NOT NULL REFERENCES orgs(id) ON DELETE CASCADE,
    PRIMARY KEY (case_id, property_id)
);

COMMENT ON TABLE case_properties IS
'Links property-service properties to cases (separate from case_entities).';

-- Enable Row Level Security
ALTER TABLE case_properties ENABLE ROW LEVEL SECURITY;
CREATE POLICY "case_properties_organization_access" ON case_properties
    USING (org_id = ANY(string_to_array(coalesce(current_setting('app.allowed_org_ids',true),''),',')::integer[]))
    WITH CHECK (org_id = ANY(string_to_array(coalesce(current_setting('app.allowed_org_ids',true),''),',')::integer[]));

-- Performance improvements
CREATE INDEX IF NOT EXISTS idx_case_properties_property_id ON case_properties(property_id);
CREATE INDEX IF NOT EXISTS idx_case_properties_case_id ON case_properties(case_id);

-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE IF EXISTS case_properties;
-- +goose StatementEnd
