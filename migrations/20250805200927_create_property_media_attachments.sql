-- +goose Up
-- +goose StatementBegin
/*───────────────────────────────────────────────────────────────────────────────
 Property File Attachments Table
 ───────────────────────────────────────────────────────────────────────────────
 ⬢  Stores file attachment references for properties
 ⬢  Links to filerepository service via file_id 
 ⬢  Follows same RLS and indexing patterns as case_update_file_attachments
 ⬢  Supports ordering and categorization of attachments
 ───────────────────────────────────────────────────────────────────────────────*/
/*==============================================================================
 PROPERTY FILE ATTACHMENTS TABLE
 ==============================================================================*/
CREATE TABLE property_file_attachments (
    id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
    property_id TEXT NOT NULL REFERENCES properties(id) ON DELETE CASCADE,
    file_id TEXT NOT NULL,
    caption TEXT,
    display_name TEXT,
    display_order INTEGER DEFAULT 0,
    file_category TEXT,
    metadata JSONB,
    org_id INTEGER NOT NULL REFERENCES orgs(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON TABLE property_file_attachments IS 'File attachment references for properties, linking to filerepository service.';
COMMENT ON COLUMN property_file_attachments.file_id IS 'References FileMetadata.id from filerepository service - soft FK.';
COMMENT ON COLUMN property_file_attachments.file_category IS 'Categories: evidence_photo, evidence_video, evidence_audio, evidence_document, property_photo, property_video, other.';

-- RLS Policy
ALTER TABLE property_file_attachments ENABLE ROW LEVEL SECURITY;
CREATE POLICY property_file_attachments_organization_access ON property_file_attachments USING (
    org_id = ANY(
        string_to_array(
            coalesce(current_setting('app.allowed_org_ids', true), ''),
            ','
        )::integer []
    )
) WITH CHECK (
    org_id = ANY(
        string_to_array(
            coalesce(current_setting('app.allowed_org_ids', true), ''),
            ','
        )::integer []
    )
);

-- Indexes for performance
CREATE INDEX idx_property_file_attachments_property_id ON property_file_attachments(property_id);
CREATE INDEX idx_property_file_attachments_file_id ON property_file_attachments(file_id);
CREATE INDEX idx_property_file_attachments_org_id ON property_file_attachments(org_id);

-- Composite index for efficient batch loading
-- This index optimizes queries that load file attachments for multiple properties
-- using "WHERE property_id = ANY($1) ORDER BY property_id, display_order, id"
CREATE INDEX idx_property_file_attachments_batch_loading ON property_file_attachments(property_id, display_order, id);
COMMENT ON INDEX idx_property_file_attachments_batch_loading IS 'Composite index optimized for batch loading file attachments across multiple properties. Supports efficient queries with property_id = ANY() and maintains sort order by display_order, id.';

-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP INDEX IF EXISTS idx_property_file_attachments_batch_loading;
DROP INDEX IF EXISTS idx_property_file_attachments_org_id;
DROP INDEX IF EXISTS idx_property_file_attachments_file_id;
DROP INDEX IF EXISTS idx_property_file_attachments_property_id;
DROP TABLE IF EXISTS property_file_attachments;
-- +goose StatementEnd