-- +goose Up
-- +goose StatementBegin
-- Add assigned_asset_id to call_forward_events and index/foreign key
ALTER TABLE call_forward_events ADD COLUMN IF NOT EXISTS assigned_asset_id TEXT NULL;

-- Create a foreign key to assets(id); allow NULL and set NULL on delete
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'call_forward_events_assigned_asset_fk'
          AND table_name = 'call_forward_events'
    ) THEN
        ALTER TABLE call_forward_events
        ADD CONSTRAINT call_forward_events_assigned_asset_fk
        FOREIGN KEY (assigned_asset_id) REFERENCES assets(id) ON DELETE SET NULL;
    END IF;
END $$;

-- Index for lookup/analytics by assigned asset id
CREATE INDEX IF NOT EXISTS idx_call_forward_events_assigned_asset_id ON call_forward_events(assigned_asset_id);
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
-- Drop index and column (drops FK implicitly)
DROP INDEX IF EXISTS idx_call_forward_events_assigned_asset_id;
ALTER TABLE call_forward_events DROP COLUMN IF EXISTS assigned_asset_id;
-- +goose StatementEnd


