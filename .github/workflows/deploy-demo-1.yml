name: Deploy Demo-1

on:
  push:
    branches:
      - main
    paths:
      - 'services/**'
      - 'infra/**'
      - 'lib/**'
      - 'apps/**'
      - 'migrations/**'
      - '.github/workflows/**'

# Add concurrency control to queue deployments
concurrency:
  group: demo-deployment
  cancel-in-progress: false  # Don't cancel running deployments, queue them instead

permissions:
  id-token: write
  contents: write

jobs:
  build-images:
    uses: ./.github/workflows/build-and-publish-services.yml

  deploy-demo-1:
    needs: build-images
    uses: ./.github/workflows/deploy-environment.yml
    with:
      env_name: demo-1
      aws_account: ************
      aws_region: us-west-2
      cloudfront_distribution_id: E121HXCBRCHQXV