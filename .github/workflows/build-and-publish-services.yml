name: Build and Publish Images

on:
  workflow_call:
    inputs:
      suffix:
        description: "Optional release suffix (e.g. rc, staging, hotfix)"
        required: false
        type: string

permissions:
  id-token: write
  contents: write

jobs:
  build-and-push:
    name: <PERSON>uild and Push Docker Images
    runs-on:
      group: large_runners
    timeout-minutes: 60

    steps:
      - name: Notify build start
        run: |
          echo "::notice::🚀 Starting image build for commit ${{ github.sha }}"

      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Configure AWS credentials for shared account
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::************:role/GitHubActionsDeployRole
          aws-region: us-west-2

      - name: Log in to Amazon ECR
        uses: aws-actions/amazon-ecr-login@v1
        env:
          AWS_USE_FIPS_ENDPOINT: "true"

      - name: Build and push Docker images
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          AWS_USE_FIPS_ENDPOINT: "true"
          SUFFIX: ${{ inputs.suffix }}
        run: |
          set -euo pipefail

          ARGS=(--auto-skip "sensors")

          if [[ -n "${SUFFIX:-}" ]]; then
            ARGS+=(--suffix "${SUFFIX}")
          fi

          ./infra/scripts/build-and-publish-services.sh "${ARGS[@]}"

      - name: Notify build success
        run: |
          echo "::notice::✅ Image build and publish completed for commit ${{ github.sha }}"


