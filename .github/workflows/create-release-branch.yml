name: Create {tag}/release from tag

on:
  workflow_dispatch:
    inputs:
      base_tag:
        description: "Tag to base from (must match vX, e.g. v697). Leave empty to use highest available tag."
        required: false
        type: string
      dry_run:
        description: "Do everything except pushing the branch"
        required: false
        default: false
        type: boolean

permissions:
  contents: write


jobs:
  cut-release-branch:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repo
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          fetch-tags: true

      - name: Configure git author
        run: |
          git config user.name  "${{ github.actor }}"
          git config user.email "${{ github.actor }}@users.noreply.github.com"

      - name: Validate tag and prepare branch
        id: prep
        shell: bash
        run: |
          set -euo pipefail

          # If base_tag is not provided, find the highest tag matching vX format
          if [[ -z "${{ inputs.base_tag }}" ]]; then
            echo "No base_tag provided, finding highest tag matching vX format..."
            TAG=$(git tag --list "v*" | grep -E "^v[0-9]+$" | sort -V | tail -n 1)
            if [[ -z "$TAG" ]]; then
              echo "Error: No tags found matching vX format (e.g., v1, v2, v10, etc.)." >&2
              exit 1
            fi
            echo "Using highest tag: $TAG"
          else
            TAG="${{ inputs.base_tag }}"
          fi

          # Validate format vX (X is digits)
          if [[ ! "$TAG" =~ ^v[0-9]+$ ]]; then
            echo "Error: base_tag must match ^v[0-9]+$ (got '$TAG')." >&2
            exit 1
          fi

          # Ensure tag exists
          if ! git show-ref --tags --quiet --verify "refs/tags/$TAG"; then
            echo "Error: tag '$TAG' does not exist in this repository." >&2
            exit 1
          fi

          NEW_BRANCH="${TAG}/release"

          # Ensure the {tag}/release branch does not already exist remotely
          if git ls-remote --heads origin "$NEW_BRANCH" | grep -q .; then
            echo "Error: branch '$NEW_BRANCH' already exists on origin. Only one release branch per tag is allowed." >&2
            exit 1
          fi

          echo "branch=${NEW_BRANCH}" >> "$GITHUB_OUTPUT"
          echo "tag=${TAG}" >> "$GITHUB_OUTPUT"

          # Create branch at the tag (locally)
          git checkout --detach "$TAG"
          git checkout -b "$NEW_BRANCH"

      - name: Push branch to origin
        if: ${{ inputs.dry_run == false }}
        run: |
          set -euo pipefail
          git push -u origin "${{ steps.prep.outputs.branch }}"

      - name: Summary
        run: |
          echo "### Release branch" >> "$GITHUB_STEP_SUMMARY"
          echo "- Base tag: \`${{ steps.prep.outputs.tag }}\`" >> "$GITHUB_STEP_SUMMARY"
          echo "- New branch: \`${{ steps.prep.outputs.branch }}\`" >> "$GITHUB_STEP_SUMMARY"
          if [ "${{ inputs.dry_run }}" = "true" ]; then
            echo "> Dry run: branch was **not** pushed." >> "$GITHUB_STEP_SUMMARY"
          fi
