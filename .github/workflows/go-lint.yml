name: Lint

on:
  push:
    branches:
      - main
  pull_request:

jobs:
  lint-and-format:
    name: Format and Lint Go Code
    runs-on:
      group: large_runners

    steps:
    # Step 1: Checkout code
    - name: Checkout code
      uses: actions/checkout@v3

    # Step 2: Set up Go
    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: 1.24  # Use the desired Go version
  
    # Step 2.5: Install system dependencies required for CGO
    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y libglib2.0-dev \
          libgstreamer1.0-dev \
          libgstreamer-plugins-base1.0-dev \
          pkg-config

    # Step 3: Install linters and formatters
    - name: Install tools
      run: |
        go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest

    # Step 6: Run golangci-lint
    - name: Run golangci-lint
      run: |
        echo "Running golangci-lint..."
        export CGO_ENABLED=1
        for dir in services/*/; do
          if [ -d "$dir" ]; then
            echo "Linting $dir..."
            cd "$dir"
            golangci-lint run ./...
            cd ../../
          fi
        done

    # Optional: Fail if git status is dirty
    - name: Ensure no uncommitted changes
      run: |
        if [ -n "$(git status --porcelain)" ]; then
          echo "Uncommitted changes detected after formatting. Please commit changes."
          exit 1
        fi
