name: Deploy Staging-1 (TCU env)

on:
  push:
    branches:
      - 'v*/release'
    paths:
      - 'services/**'
      - 'infra/**'
      - 'lib/**'
      - 'apps/**'
      - 'migrations/**'
      - '.github/workflows/**'

# Add concurrency control to queue deployments
concurrency:
  group: staging-deployment
  cancel-in-progress: false  # Don't cancel running deployments, queue them instead

permissions:
  id-token: write
  contents: write

jobs:
  build-images:
    uses: ./.github/workflows/build-and-publish-services.yml
    with:
      suffix: rc

  # point this to staging env when ready 
  deploy-demo-1:
    needs: build-images
    uses: ./.github/workflows/deploy-environment.yml
    with:
      env_name: prod-1
      aws_account: ************
      aws_region: us-east-2
      cloudfront_distribution_id: E1KUCLS5WX3KR9