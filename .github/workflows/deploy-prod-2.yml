name: Deploy Prod-2 (Cisco env)

on:
  release:
    types: [published]

# Add concurrency control to queue deployments
concurrency:
  group: prod-2-deployment
  cancel-in-progress: false

permissions:
  id-token: write
  contents: write

jobs:
  deploy-prod-1:
    uses: ./.github/workflows/deploy-environment.yml
    with:
      env_name: prod-2
      aws_account: ************
      aws_region: us-east-2
      cloudfront_distribution_id: E9DSKIHABF1FO
      tag: ${{ github.event.release.tag_name }}

