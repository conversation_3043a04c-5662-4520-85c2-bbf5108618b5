name: Test

on:
  push:
    branches:
      - main
  pull_request:

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: 1.24 # Set your desired Go version

    - name: Find and test all services
      run: |
        for service in services/*; do
          if [ -f "$service/go.mod" ]; then
            echo "Running tests in $service..."
            cd "$service"
            go mod tidy
            
            # Skip workflow/test directory
            if [[ "$service" == "services/workflow" || "$service" == "services/sensors" || "$service" == "services/featureflags" ]]; then
              echo "Skipping test directory in $service service..."
              # Find all directories containing test files (excluding /test/)
              TEST_DIRS=$(find . -path '*/\.*' -prune -o -name '*_test.go' -print | grep -v "/test/" | xargs -n1 dirname | sort -u || true)
              if [ -n "$TEST_DIRS" ]; then
                for dir in $TEST_DIRS; do
                  echo "Testing in directory: $dir"
                  go test -v "$dir"
                done
              else
                echo "No test files found outside of /test/ directory"
              fi
            else
              go test ./... -v
            fi
            
            cd - # Return to the root directory
          fi
        done
