name: Slack Prod Deploy Notify

on:
  workflow_call:
    inputs:
      status:
        description: "Deployment status (e.g. success, failure, cancelled)"
        required: true
        type: string
      environment:
        description: "Environment deployed to (e.g. prod-1, prod-2)"
        required: true
        type: string
      version:
        description: "Version deployed (e.g. 1.2.3)"
        required: false
        type: string
      commit_sha:
        description: "Commit SHA deployed"
        required: true
        type: string
      run_url:
        description: "URL to the workflow run"
        required: true
        type: string

permissions:
  contents: read
  pull-requests: read

jobs:
  slack-prod-notify:
    runs-on: ubuntu-latest
    steps:
      - name: Get PR info for deployed commit
        id: pr
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          PR_JSON=$(curl -s -H "Authorization: Bearer $GITHUB_TOKEN" \
            "https://api.github.com/repos/${{ github.repository }}/commits/${{ inputs.commit_sha }}/pulls" \
            -H "Accept: application/vnd.github.groot-preview+json")
          PR_TITLE=$(echo "$PR_JSON" | jq -r '.[0].title // empty')
          PR_NUMBER=$(echo "$PR_JSON" | jq -r '.[0].number // empty')
          PR_URL=$(echo "$PR_JSON" | jq -r '.[0].html_url // empty')
          echo "pr_title=$PR_TITLE" >> $GITHUB_OUTPUT
          echo "pr_number=$PR_NUMBER" >> $GITHUB_OUTPUT
          echo "pr_url=$PR_URL" >> $GITHUB_OUTPUT

      - name: Checkout code
        uses: actions/checkout@v3
        with:
          ref: ${{ inputs.commit_sha }}

      - name: Get commit message
        id: commit
        run: |
          COMMIT_MSG=$(git log -1 --pretty=%B)
          echo "commit_msg<<EOF" >> $GITHUB_OUTPUT
          echo "$COMMIT_MSG" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT

      - name: Send prod deploy Slack message
        env:
          REPO: ${{ github.repository }}
          ACTOR: ${{ github.actor }}
          RUN_URL: ${{ inputs.run_url }}
          PR_TITLE: ${{ steps.pr.outputs.pr_title }}
          PR_NUMBER: ${{ steps.pr.outputs.pr_number }}
          PR_URL: ${{ steps.pr.outputs.pr_url }}
          COMMIT_MSG: ${{ steps.commit.outputs.commit_msg }}
          CONCLUSION: ${{ inputs.status }}
          ENVIRONMENT: ${{ inputs.environment }}
          VERSION: ${{ inputs.version }}
          SLACK_DEPLOY_WEBHOOK: ${{ secrets.SLACK_DEPLOY_WEBHOOK }}
        run: |
          set -e
          
          # Set status-specific variables
          if [ "$CONCLUSION" = "success" ]; then
            EMOJI=":rocket:"
            STATUS_TEXT="Deployment to ${ENVIRONMENT} Succeeded!"
          elif [ "$CONCLUSION" = "failure" ]; then
            EMOJI=":x:"
            STATUS_TEXT="Deployment to ${ENVIRONMENT} Failed!"
          elif [ "$CONCLUSION" = "cancelled" ]; then
            EMOJI=":large_yellow_circle:"
            STATUS_TEXT="Deployment to ${ENVIRONMENT} was cancelled."
          else
            EMOJI=":question:"
            STATUS_TEXT="Deployment to ${ENVIRONMENT} has an unknown status: ${CONCLUSION}."
          fi
          
          # Add version if available
          VERSION_TEXT=""
          if [ -n "$VERSION" ]; then
            VERSION_TEXT="Version: \`${VERSION}\`"
          fi
          
          if [ -n "$PR_TITLE" ] && [ -n "$PR_NUMBER" ] && [ -n "$PR_URL" ]; then
            TEXT=$(cat <<EOF
          ${EMOJI} *${STATUS_TEXT}*

          Repo: \`${REPO}\`
          By: \`${ACTOR}\`
          ${VERSION_TEXT}
          PR: <${PR_URL}|#${PR_NUMBER} - ${PR_TITLE}>
          See run: <${RUN_URL}|View Workflow Run>
          EOF
          )
          else
            TEXT=$(cat <<EOF
          ${EMOJI} *${STATUS_TEXT}*

          Repo: \`${REPO}\`
          By: \`${ACTOR}\`
          ${VERSION_TEXT}
          Commit: \`${COMMIT_MSG}\`
          See run: <${RUN_URL}|View Workflow Run>
          EOF
          )
          fi

          ESCAPED_TEXT=$(echo "$TEXT" | tr -d '\000' | jq -Rs .)
          PAYLOAD="{\"text\": ${ESCAPED_TEXT}, \"mrkdwn\": true}"

          RESPONSE=$(curl -s -o response.txt -w "%{http_code}" \
            -H 'Content-type: application/json' \
            --data "$PAYLOAD" "$SLACK_DEPLOY_WEBHOOK")

          if [ "$RESPONSE" -ne 200 ]; then
            echo "Slack notification failed (HTTP $RESPONSE)"
            cat response.txt
            exit 1
          fi