import * as cdk from 'aws-cdk-lib';
import * as secretsmanager from 'aws-cdk-lib/aws-secretsmanager';
import * as sns from 'aws-cdk-lib/aws-sns';
import * as snsSubscriptions from 'aws-cdk-lib/aws-sns-subscriptions';
import { Construct } from 'constructs';

export interface PagerDutyAlertsStackProps extends cdk.StackProps {
    domainTag?: string; // optional topic suffix, defaults to 'shared'
}

export class PagerDutyAlertsStack extends cdk.Stack {
    public readonly criticalTopic: sns.ITopic;
    public readonly highTopic: sns.ITopic;
    public readonly infoTopic: sns.ITopic;

    constructor(scope: Construct, id: string, props: PagerDutyAlertsStackProps) {
        super(scope, id, props);

        const domainTag = (props.domainTag ?? 'shared').replace(/\./g, '-');

        const criticalTopic = new sns.Topic(this, 'PagerDutyCriticalTopic', {
            topicName: `pagerduty-critical-alerts-${domainTag}`,
            displayName: 'PagerDuty Critical Alerts',
        });

        const highTopic = new sns.Topic(this, 'PagerDutyHighTopic', {
            topicName: `pagerduty-high-alerts-${domainTag}`,
            displayName: 'PagerDuty High Alerts',
        });

        const infoTopic = new sns.Topic(this, 'PagerDutyInfoTopic', {
            topicName: `pagerduty-info-alerts-${domainTag}`,
            displayName: 'PagerDuty Informational Alerts',
        });

        const pagerDutyIntegrationKeySecret = secretsmanager.Secret.fromSecretNameV2(
            this,
            'PagerDutyIntegrationKeySecret',
            'shared/team-eng-alarms/pagerduty-integration-key'
        );

        const integrationKeyRef = new cdk.CfnDynamicReference(
            cdk.CfnDynamicReferenceService.SECRETS_MANAGER,
            `${pagerDutyIntegrationKeySecret.secretArn}:SecretString`
        );

        const pagerDutyUrl = cdk.Fn.sub(
            'https://events.pagerduty.com/integration/${IntegrationKey}/enqueue',
            { IntegrationKey: integrationKeyRef.toString() }
        );

        [criticalTopic, highTopic, infoTopic].forEach((topic) => {
            topic.addSubscription(
                new snsSubscriptions.UrlSubscription(pagerDutyUrl, {
                    protocol: sns.SubscriptionProtocol.HTTPS,
                    rawMessageDelivery: false,
                })
            );
        });

        this.criticalTopic = criticalTopic;
        this.highTopic = highTopic;
        this.infoTopic = infoTopic;
    }
}


