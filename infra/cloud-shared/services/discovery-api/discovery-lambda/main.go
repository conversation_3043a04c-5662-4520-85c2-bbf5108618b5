package main

import (
	"context"
	"encoding/json"
	"log"
	"strings"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
)

type DiscoveryRequest struct {
	Email string `json:"email"`
}

type DiscoveryResponse struct {
	UserPoolURL string `json:"userPoolUrl"`
	ClientID    string `json:"clientId"`
}

type ErrorResponse struct {
	Error string `json:"error"`
}

type EnvironmentConfig struct {
	UserPoolURL string
	ClientID    string
}

// Domain to environment mapping
var domainConfigs = map[string]EnvironmentConfig{
	"tcu.edu": {
		UserPoolURL: "https://auth.tcu.gethero.com",
		ClientID:    "99e7542llrhiom2d573vc4ghr", // Client ID for InternalClient in the TCU cognito stack
	},
	"gethero.com": {
		UserPoolURL: "https://auth.demo-1.gethero.com",
		ClientID:    "3fv7mur82c02q30o7oflj0u5ub", // Client ID for InternalClient in the demo-1 cognito stack
	},
	"cisco.edu": {
		UserPoolURL: "https://auth.cisco.gethero.com",
		ClientID:    "1ncco499918qlmipmsh838tind", // Client ID for InternalClient in the cisco.edu cognito stack
	},
}

// No API key validation needed for API Gateway

func extractDomain(email string) string {
	parts := strings.Split(email, "@")
	if len(parts) != 2 {
		return ""
	}
	return strings.ToLower(parts[1])
}

func createResponse(statusCode int, body interface{}) (events.APIGatewayV2HTTPResponse, error) {
	bodyBytes, err := json.Marshal(body)
	if err != nil {
		return events.APIGatewayV2HTTPResponse{
			StatusCode: 500,
			Headers: map[string]string{
				"Content-Type": "application/json",
			},
			Body: `{"error": "Internal server error"}`,
		}, nil
	}

	return events.APIGatewayV2HTTPResponse{
		StatusCode: statusCode,
		Headers: map[string]string{
			"Content-Type": "application/json",
		},
		Body: string(bodyBytes),
	}, nil
}

func handler(ctx context.Context, event events.APIGatewayV2HTTPRequest) (events.APIGatewayV2HTTPResponse, error) {
	log.Printf("Received request: %+v", event)

	// Only allow POST method
	if event.RequestContext.HTTP.Method != "POST" {
		return createResponse(405, ErrorResponse{Error: "Method not allowed"})
	}

	// Parse request body
	var req DiscoveryRequest
	if err := json.Unmarshal([]byte(event.Body), &req); err != nil {
		log.Printf("Failed to parse request body: %v", err)
		return createResponse(400, ErrorResponse{Error: "Invalid request body"})
	}

	// Validate email format
	if req.Email == "" {
		return createResponse(400, ErrorResponse{Error: "Email is required"})
	}

	// Extract domain from email
	domain := extractDomain(req.Email)
	if domain == "" {
		return createResponse(400, ErrorResponse{Error: "Invalid email format"})
	}

	// Look up configuration for domain
	config, exists := domainConfigs[domain]
	if !exists {
		log.Printf("No configuration found for domain: %s", domain)
		return createResponse(404, ErrorResponse{Error: "No configuration found for the requested domain"})
	}

	// Return the configuration
	response := DiscoveryResponse(config)

	log.Printf("Returning config for domain %s: %+v", domain, response)
	return createResponse(200, response)
}

func main() {
	lambda.Start(handler)
}
