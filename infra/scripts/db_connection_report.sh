#!/bin/bash

# Exit on error
set -e

# Check if required arguments are provided
if [ "$#" -lt 1 ]; then
    echo "Usage: $0 <admin-secret-arn> [output-format]"
    echo "Example: $0 arn:aws:secretsmanager:us-west-2:123456789012:secret:myapp/db-admin-creds [json|csv|table]"
    echo "Default output format is table"
    exit 1
fi

ADMIN_SECRET_NAME=$1
OUTPUT_FORMAT=${2:-table}

# Extract region from ARN
AWS_REGION=$(echo "$ADMIN_SECRET_NAME" | cut -d: -f4)

# Get the admin credentials from AWS Secrets Manager
echo "Retrieving admin credentials from AWS Secrets Manager..."
ADMIN_SECRET_VALUE=$(aws secretsmanager get-secret-value \
    --secret-id "$ADMIN_SECRET_NAME" \
    --region "$AWS_REGION" \
    --query 'SecretString' \
    --output text)

# Extract admin credentials from the secret
ADMIN_USERNAME=$(echo "$ADMIN_SECRET_VALUE" | jq -r '.username')
ADMIN_PASSWORD=$(echo "$ADMIN_SECRET_VALUE" | jq -r '.password')
DB_HOST=$(echo "$ADMIN_SECRET_VALUE" | jq -r '.host')
DB_PORT=$(echo "$ADMIN_SECRET_VALUE" | jq -r '.port')
DB_NAME=$(echo "$ADMIN_SECRET_VALUE" | jq -r '.dbname')

if [ -z "$ADMIN_USERNAME" ] || [ -z "$ADMIN_PASSWORD" ] || [ -z "$DB_HOST" ] || [ -z "$DB_PORT" ]; then
    echo "Error: Could not extract all required admin credentials (username, password, host, port) from secret"
    exit 1
fi

# Construct connection string
DB_CONNECTION_STRING="postgresql://${ADMIN_USERNAME}:${ADMIN_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}"

# Create temporary SQL file for the connection report
echo "Creating database connection report..."

cat > db_connection_report.sql << 'EOF'
-- Database Connection and Load Report
-- Generated on: $(date)

-- 1. Connection Counts by User
\echo '=== CONNECTION COUNTS BY USER ==='
SELECT 
    usename as username,
    COUNT(*) as total_connections,
    COUNT(CASE WHEN state = 'active' THEN 1 END) as active_connections,
    COUNT(CASE WHEN state = 'idle' THEN 1 END) as idle_connections,
    COUNT(CASE WHEN state = 'idle in transaction' THEN 1 END) as idle_in_transaction,
    COUNT(CASE WHEN state = 'disabled' THEN 1 END) as disabled_connections
FROM pg_stat_activity 
GROUP BY usename
ORDER BY total_connections DESC;

-- 2. Current Active Queries by User
\echo '=== CURRENT ACTIVE QUERIES BY USER ==='
SELECT 
    usename as username,
    application_name,
    client_addr,
    state,
    query_start,
    now() - query_start as duration,
    LEFT(query, 100) as query_preview
FROM pg_stat_activity 
WHERE state = 'active'
ORDER BY duration DESC;

-- 3. Long Running Queries (> 1 minute)
\echo '=== LONG RUNNING QUERIES (> 1 minute) ==='
SELECT 
    usename as username,
    application_name,
    client_addr,
    now() - query_start as duration,
    LEFT(query, 150) as query_preview
FROM pg_stat_activity 
WHERE state = 'active' 
    AND now() - query_start > interval '1 minute'
ORDER BY duration DESC;

-- 4. Load Metrics by User (queries per second approximation)
\echo '=== LOAD METRICS BY USER ==='
SELECT 
    usename as username,
    COUNT(*) as current_connections,
    COUNT(CASE WHEN state = 'active' THEN 1 END) as active_queries,
    ROUND(
        COUNT(CASE WHEN state = 'active' THEN 1 END) * 100.0 / 
        NULLIF(COUNT(*), 0), 2
    ) as active_percentage,
    MAX(CASE WHEN state = 'active' THEN now() - query_start END) as longest_active_query
FROM pg_stat_activity 
GROUP BY usename
ORDER BY active_queries DESC;

-- 5. Connection Usage Summary
\echo '=== CONNECTION USAGE SUMMARY ==='
SELECT 
    'Total Connections' as metric,
    COUNT(*) as value
FROM pg_stat_activity
UNION ALL
SELECT 
    'Active Connections',
    COUNT(*)
FROM pg_stat_activity
WHERE state = 'active'
UNION ALL
SELECT 
    'Idle Connections',
    COUNT(*)
FROM pg_stat_activity
WHERE state = 'idle'
UNION ALL
SELECT 
    'Max Connections',
    setting::int
FROM pg_settings
WHERE name = 'max_connections'
UNION ALL
SELECT 
    'Connection Usage %',
    ROUND(100.0 * COUNT(*) / (SELECT setting::int FROM pg_settings WHERE name = 'max_connections'), 2)
FROM pg_stat_activity;

-- 6. Database Load by Application
\echo '=== DATABASE LOAD BY APPLICATION ==='
SELECT 
    COALESCE(application_name, 'unknown') as application,
    COUNT(*) as connections,
    COUNT(CASE WHEN state = 'active' THEN 1 END) as active_queries,
    COUNT(DISTINCT usename) as unique_users,
    COUNT(DISTINCT client_addr) as unique_clients
FROM pg_stat_activity 
GROUP BY application_name
ORDER BY active_queries DESC;

-- 7. Recent Query Activity (last 10 minutes)
\echo '=== RECENT QUERY ACTIVITY ==='
SELECT 
    usename as username,
    application_name,
    COUNT(*) as queries_in_period,
    AVG(EXTRACT(EPOCH FROM (now() - query_start))) as avg_duration_seconds,
    MAX(EXTRACT(EPOCH FROM (now() - query_start))) as max_duration_seconds
FROM pg_stat_activity 
WHERE query_start > now() - interval '10 minutes'
    AND state = 'active'
GROUP BY usename, application_name
ORDER BY queries_in_period DESC;

-- 8. Lock Contention by User
\echo '=== LOCK CONTENTION BY USER ==='
SELECT 
    a.usename as username,
    a.application_name,
    COUNT(l.pid) as locks_held,
    COUNT(CASE WHEN l.granted = false THEN 1 END) as locks_waiting,
    MAX(CASE WHEN l.granted = false THEN now() - a.query_start END) as longest_wait
FROM pg_locks l
JOIN pg_stat_activity a ON l.pid = a.pid
WHERE l.pid != pg_backend_pid()
GROUP BY a.usename, a.application_name
HAVING COUNT(l.pid) > 0
ORDER BY locks_waiting DESC;
EOF

# Execute the SQL file and format output
echo "Executing database connection report..."
if [ "$OUTPUT_FORMAT" = "json" ]; then
    # JSON format
    psql "${DB_CONNECTION_STRING}" -f db_connection_report.sql -t -A -F ',' | \
    awk -F',' '{
        if ($1 ~ /^===/) {
            section = substr($1, 4, length($1)-6);
            print "{\"section\": \"" section "\", \"data\": []}";
        } else if (NF > 1) {
            # Convert CSV to JSON array
            printf "{\"section\": \"" section "\", \"data\": [";
            for (i=1; i<=NF; i++) {
                if (i > 1) printf ",";
                printf "\"" $i "\"";
            }
            printf "]}\n";
        }
    }' | jq -s 'group_by(.section) | map({section: .[0].section, data: map(.data)})'
elif [ "$OUTPUT_FORMAT" = "csv" ]; then
    # CSV format
    psql "${DB_CONNECTION_STRING}" -f db_connection_report.sql -t -A -F ','
else
    # Default table format
    psql "${DB_CONNECTION_STRING}" -f db_connection_report.sql
fi

# Clean up
rm db_connection_report.sql

echo "Database connection report completed!" 