#!/bin/bash

# Exit on error
set -e

# Check if required arguments are provided
if [ "$#" -lt 1 ]; then
    echo "Usage: $0 <admin-secret-arn> [output-format]"
    echo "Example: $0 arn:aws:secretsmanager:us-west-2:123456789012:secret:myapp/db-admin-creds [json|csv|table]"
    echo "Default output format is table"
    exit 1
fi

ADMIN_SECRET_NAME=$1
OUTPUT_FORMAT=${2:-table}

# Extract region from ARN
AWS_REGION=$(echo "$ADMIN_SECRET_NAME" | cut -d: -f4)

# Get the admin credentials from AWS Secrets Manager
echo "Retrieving admin credentials from AWS Secrets Manager..."
ADMIN_SECRET_VALUE=$(aws secretsmanager get-secret-value \
    --secret-id "$ADMIN_SECRET_NAME" \
    --region "$AWS_REGION" \
    --query 'SecretString' \
    --output text)

# Extract admin credentials from the secret
ADMIN_USERNAME=$(echo "$ADMIN_SECRET_VALUE" | jq -r '.username')
ADMIN_PASSWORD=$(echo "$ADMIN_SECRET_VALUE" | jq -r '.password')
DB_HOST=$(echo "$ADMIN_SECRET_VALUE" | jq -r '.host')
DB_PORT=$(echo "$ADMIN_SECRET_VALUE" | jq -r '.port')
DB_NAME=$(echo "$ADMIN_SECRET_VALUE" | jq -r '.dbname')

if [ -z "$ADMIN_USERNAME" ] || [ -z "$ADMIN_PASSWORD" ] || [ -z "$DB_HOST" ] || [ -z "$DB_PORT" ]; then
    echo "Error: Could not extract all required admin credentials (username, password, host, port) from secret"
    exit 1
fi

# Construct connection string
DB_CONNECTION_STRING="postgresql://${ADMIN_USERNAME}:${ADMIN_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}"

# Create temporary SQL file for the status report
echo "Creating database status report..."

cat > db_status_report.sql << 'EOF'
-- Database Status Report
-- Generated on: $(date)

-- 1. Current Connections by User
\echo '=== CURRENT CONNECTIONS BY USER ==='
SELECT 
    usename as username,
    application_name,
    client_addr,
    state,
    query_start,
    now() - query_start as duration
FROM pg_stat_activity 
WHERE state IS NOT NULL
ORDER BY usename, duration DESC;

-- 2. Connection Count Summary
\echo '=== CONNECTION COUNT SUMMARY ==='
SELECT 
    usename as username,
    COUNT(*) as connection_count,
    COUNT(CASE WHEN state = 'active' THEN 1 END) as active_connections,
    COUNT(CASE WHEN state = 'idle' THEN 1 END) as idle_connections,
    COUNT(CASE WHEN state = 'idle in transaction' THEN 1 END) as idle_in_transaction
FROM pg_stat_activity 
GROUP BY usename
ORDER BY connection_count DESC;

-- 3. Database Size Information
\echo '=== DATABASE SIZE INFORMATION ==='
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
    pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
FROM pg_tables 
WHERE schemaname NOT IN ('information_schema', 'pg_catalog')
ORDER BY size_bytes DESC;

-- 4. Table Statistics
\echo '=== TABLE STATISTICS ==='
SELECT 
    schemaname,
    tablename,
    n_tup_ins as inserts,
    n_tup_upd as updates,
    n_tup_del as deletes,
    n_live_tup as live_tuples,
    n_dead_tup as dead_tuples,
    last_vacuum,
    last_autovacuum,
    last_analyze,
    last_autoanalyze
FROM pg_stat_user_tables
ORDER BY n_live_tup DESC;

-- 5. Index Usage Statistics
\echo '=== INDEX USAGE STATISTICS ==='
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan as index_scans,
    idx_tup_read as tuples_read,
    idx_tup_fetch as tuples_fetched
FROM pg_stat_user_indexes
ORDER BY idx_scan DESC;

-- 6. Long Running Queries
\echo '=== LONG RUNNING QUERIES (> 5 minutes) ==='
SELECT 
    pid,
    usename as username,
    application_name,
    client_addr,
    state,
    query_start,
    now() - query_start as duration,
    LEFT(query, 100) as query_preview
FROM pg_stat_activity 
WHERE state = 'active' 
    AND now() - query_start > interval '5 minutes'
ORDER BY duration DESC;

-- 7. Lock Information
\echo '=== LOCK INFORMATION ==='
SELECT 
    l.pid,
    l.mode,
    l.granted,
    a.usename as username,
    a.application_name,
    a.client_addr,
    a.state,
    a.query_start,
    now() - a.query_start as duration
FROM pg_locks l
JOIN pg_stat_activity a ON l.pid = a.pid
WHERE l.pid != pg_backend_pid()
ORDER BY duration DESC;

-- 8. Cache Hit Ratios
\echo '=== CACHE HIT RATIOS ==='
SELECT 
    schemaname,
    tablename,
    heap_blks_read,
    heap_blks_hit,
    CASE 
        WHEN heap_blks_hit + heap_blks_read = 0 THEN 0
        ELSE ROUND(100.0 * heap_blks_hit / (heap_blks_hit + heap_blks_read), 2)
    END as cache_hit_ratio
FROM pg_statio_user_tables
ORDER BY cache_hit_ratio ASC;

-- 9. Database Configuration
\echo '=== DATABASE CONFIGURATION ==='
SELECT 
    name,
    setting,
    unit,
    context,
    category
FROM pg_settings 
WHERE name IN (
    'max_connections',
    'shared_buffers',
    'effective_cache_size',
    'work_mem',
    'maintenance_work_mem',
    'checkpoint_completion_target',
    'wal_buffers',
    'default_statistics_target',
    'random_page_cost',
    'effective_io_concurrency'
)
ORDER BY category, name;

-- 10. WAL Statistics
\echo '=== WAL STATISTICS ==='
SELECT 
    name,
    setting,
    unit
FROM pg_settings 
WHERE name LIKE 'wal_%' 
    AND name IN (
        'wal_level',
        'wal_buffers',
        'wal_writer_delay',
        'commit_delay',
        'commit_siblings'
    )
ORDER BY name;

-- 11. Background Writer Statistics
\echo '=== BACKGROUND WRITER STATISTICS ==='
SELECT 
    checkpoints_timed,
    checkpoints_req,
    checkpoint_write_time,
    checkpoint_sync_time,
    buffers_checkpoint,
    buffers_clean,
    maxwritten_clean,
    buffers_backend,
    buffers_backend_fsync,
    buffers_alloc
FROM pg_stat_bgwriter;

-- 12. System Resource Usage
\echo '=== SYSTEM RESOURCE USAGE ==='
SELECT 
    'Total Connections' as metric,
    COUNT(*) as value
FROM pg_stat_activity
UNION ALL
SELECT 
    'Active Connections',
    COUNT(*)
FROM pg_stat_activity
WHERE state = 'active'
UNION ALL
SELECT 
    'Idle Connections',
    COUNT(*)
FROM pg_stat_activity
WHERE state = 'idle'
UNION ALL
SELECT 
    'Idle in Transaction',
    COUNT(*)
FROM pg_stat_activity
WHERE state = 'idle in transaction'
UNION ALL
SELECT 
    'Max Connections',
    setting::int
FROM pg_settings
WHERE name = 'max_connections'
UNION ALL
SELECT 
    'Connection Usage %',
    ROUND(100.0 * COUNT(*) / (SELECT setting::int FROM pg_settings WHERE name = 'max_connections'), 2)
FROM pg_stat_activity;
EOF

# Execute the SQL file and format output
echo "Executing database status report..."
if [ "$OUTPUT_FORMAT" = "json" ]; then
    # JSON format
    psql "${DB_CONNECTION_STRING}" -f db_status_report.sql -t -A -F ',' | \
    awk -F',' '{
        if ($1 ~ /^===/) {
            section = substr($1, 4, length($1)-6);
            print "{\"section\": \"" section "\", \"data\": []}";
        } else if (NF > 1) {
            # Convert CSV to JSON array
            printf "{\"section\": \"" section "\", \"data\": [";
            for (i=1; i<=NF; i++) {
                if (i > 1) printf ",";
                printf "\"" $i "\"";
            }
            printf "]}\n";
        }
    }' | jq -s 'group_by(.section) | map({section: .[0].section, data: map(.data)})'
elif [ "$OUTPUT_FORMAT" = "csv" ]; then
    # CSV format
    psql "${DB_CONNECTION_STRING}" -f db_status_report.sql -t -A -F ','
else
    # Default table format
    psql "${DB_CONNECTION_STRING}" -f db_status_report.sql
fi

# Clean up
rm db_status_report.sql

echo "Database status report completed!" 