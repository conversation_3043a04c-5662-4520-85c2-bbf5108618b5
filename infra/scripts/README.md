# Database Management Scripts

This directory contains scripts for managing PostgreSQL database connections and generating status reports.

## Scripts

### 1. `create_postgres_role.sh`
Creates a new PostgreSQL role with specified permissions.

**Usage:**
```bash
./create_postgres_role.sh <admin-secret-arn> <role-secret-arn>
```

**Example:**
```bash
./create_postgres_role.sh arn:aws:secretsmanager:us-west-2:123456789012:secret:myapp/db-admin-creds arn:aws:secretsmanager:us-west-2:123456789012:secret:myapp/db-role-creds
```

**Requirements:**
- AWS CLI configured with appropriate permissions
- `jq` installed for JSON parsing
- `psql` installed for PostgreSQL client
- Template file `create-db-role.sql.template` in the same directory

### 2. `db_status_report.sh`
Generates a comprehensive database status report including:
- Current connections by user
- Connection count summary
- Database size information
- Table statistics
- Index usage statistics
- Long running queries
- Lock information
- Cache hit ratios
- Database configuration
- WAL statistics
- Background writer statistics
- System resource usage

**Usage:**
```bash
./db_status_report.sh <admin-secret-arn> [output-format]
```

**Output Formats:**
- `table` (default): Human-readable table format
- `json`: JSON format for programmatic processing
- `csv`: CSV format for spreadsheet import

**Example:**
```bash
# Default table format
./db_status_report.sh arn:aws:secretsmanager:us-west-2:123456789012:secret:myapp/db-admin-creds

# JSON format
./db_status_report.sh arn:aws:secretsmanager:us-west-2:123456789012:secret:myapp/db-admin-creds json

# CSV format
./db_status_report.sh arn:aws:secretsmanager:us-west-2:123456789012:secret:myapp/db-admin-creds csv
```

### 3. `db_connection_report.sh`
Generates a focused report on database connections and load metrics:
- Connection counts by user
- Current active queries by user
- Long running queries (> 1 minute)
- Load metrics by user
- Connection usage summary
- Database load by application
- Recent query activity (last 10 minutes)
- Lock contention by user

**Usage:**
```bash
./db_connection_report.sh <admin-secret-arn> [output-format]
```

**Example:**
```bash
# Default table format
./db_connection_report.sh arn:aws:secretsmanager:us-west-2:123456789012:secret:myapp/db-admin-creds

# JSON format for monitoring integration
./db_connection_report.sh arn:aws:secretsmanager:us-west-2:123456789012:secret:myapp/db-admin-creds json
```

## Prerequisites

1. **AWS CLI**: Must be configured with appropriate permissions to access Secrets Manager
2. **PostgreSQL Client**: `psql` must be installed and available in PATH
3. **jq**: JSON processor for parsing AWS Secrets Manager responses
4. **Bash**: Scripts require bash shell

## Installation

```bash
# Make scripts executable
chmod +x *.sh

# Install dependencies (Ubuntu/Debian)
sudo apt-get install postgresql-client jq

# Install dependencies (macOS)
brew install postgresql jq
```

## AWS Secrets Manager Format

The scripts expect secrets in AWS Secrets Manager with the following JSON format:

```json
{
  "username": "db_user",
  "password": "db_password",
  "host": "db-host.example.com",
  "port": "5432",
  "dbname": "database_name"
}
```

## Security Considerations

- Scripts use admin credentials to access the database
- Ensure proper IAM permissions are configured
- Consider using temporary credentials for production use
- Monitor and audit script usage in production environments

## Troubleshooting

### Common Issues

1. **Permission Denied**: Ensure AWS CLI has proper permissions for Secrets Manager
2. **Connection Refused**: Verify database host and port are correct
3. **Authentication Failed**: Check username/password in the secret
4. **psql not found**: Install PostgreSQL client tools

### Debug Mode

Add `set -x` at the beginning of scripts to enable debug output:

```bash
#!/bin/bash
set -x  # Add this line for debug output
set -e
```

## Integration with Monitoring

The JSON output format can be easily integrated with monitoring systems:

```bash
# Example: Send to monitoring system
./db_connection_report.sh $ADMIN_SECRET_ARN json | curl -X POST -H "Content-Type: application/json" -d @- https://monitoring.example.com/api/metrics
```

## Scheduled Reports

Example cron job to generate daily reports:

```bash
# Add to crontab
0 9 * * * /path/to/infra/scripts/db_connection_report.sh $ADMIN_SECRET_ARN json > /var/log/db-report-$(date +\%Y\%m\%d).json
``` 