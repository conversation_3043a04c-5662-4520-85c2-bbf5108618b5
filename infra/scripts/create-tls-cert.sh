#!/bin/bash

# Script to create a self-signed certificate for ALB-to-Fargate encryption and store it in AWS Secrets Manager

set -e

# Configuration
CERT_DIR="/tmp/server-tls-cert"
CERT_NAME="server-tls-cert"
SECRET_NAME="server-tls-cert"
CERT_VALIDITY_DAYS=36500

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}Creating server certificate for re-encryption...${NC}"

# Create temporary directory
mkdir -p "$CERT_DIR"
cd "$CERT_DIR"

# Generate private key
echo -e "${YELLOW}Generating private key...${NC}"
openssl genrsa -out private.key 2048

# Generate certificate signing request
echo -e "${YELLOW}Generating certificate signing request...${NC}"
openssl req -new -key private.key -out cert.csr -subj "/C=US/ST=CA/L=San Francisco/O=Hero Core/CN=server-tls-cert"

# Generate self-signed certificate
echo -e "${YELLOW}Generating self-signed certificate...${NC}"
openssl x509 -req -in cert.csr -signkey private.key -out cert.pem -days "$CERT_VALIDITY_DAYS"

# Create combined certificate file (cert + key)
echo -e "${YELLOW}Creating combined certificate file...${NC}"
cat cert.pem private.key > combined.pem

# Verify certificate
echo -e "${YELLOW}Verifying certificate...${NC}"
openssl x509 -in cert.pem -text -noout | head -20

# Store in AWS Secrets Manager
echo -e "${YELLOW}Storing certificate in AWS Secrets Manager...${NC}"

# Create JSON structure for the secret
cat > secret.json << EOF
{
  "certificate": "$(base64 < cert.pem | tr -d '\n')",
  "private_key": "$(base64 < private.key | tr -d '\n')"
}
EOF

# Store in AWS Secrets Manager
echo -e "${YELLOW}Checking if secret already exists...${NC}"
if aws secretsmanager describe-secret --secret-id "$SECRET_NAME" --region "$(aws configure get region)" >/dev/null 2>&1; then
    echo -e "${YELLOW}Secret already exists, updating...${NC}"
    aws secretsmanager update-secret \
      --secret-id "$SECRET_NAME" \
      --secret-string file://secret.json \
      --region "$(aws configure get region)"
else
    echo -e "${YELLOW}Creating new secret...${NC}"
    aws secretsmanager create-secret \
      --name "$SECRET_NAME" \
      --description "Nginx sidecar certificate for re-encryption from ALB to Fargate tasks" \
      --secret-string file://secret.json \
      --region "$(aws configure get region)"
fi

echo -e "${GREEN}Certificate stored in AWS Secrets Manager as: $SECRET_NAME${NC}"

# Display certificate information
echo -e "${GREEN}Certificate Details:${NC}"
echo "Secret Name: $SECRET_NAME"
echo "Certificate Validity: $CERT_VALIDITY_DAYS days"
echo "Certificate Subject: /C=US/ST=CA/L=San Francisco/O=Hero Core/CN=server-tls-cert"
echo ""
echo -e "${YELLOW}Certificate fingerprint:${NC}"
openssl x509 -in cert.pem -fingerprint -noout

# Clean up temporary files
echo -e "${YELLOW}Cleaning up temporary files...${NC}"
rm -rf "$CERT_DIR"

echo -e "${GREEN}Certificate creation complete!${NC}"
echo -e "${GREEN}You can now run 'cdk deploy' to deploy the infrastructure with nginx sidecar.${NC}" 