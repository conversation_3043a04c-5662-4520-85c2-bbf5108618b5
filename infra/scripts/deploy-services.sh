#!/usr/bin/env bash
set -euo pipefail

# Resolve to the infra directory regardless of current working directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
INFRA_DIR="$(cd "$SCRIPT_DIR/.." && pwd)"
cd "$INFRA_DIR"

# Determine target CDK environment:
# Priority: first argument > CDK_ENV env var > default "demo-1"
TARGET_ENV="${1:-${CDK_ENV:-demo-1}}"

# Get image tag from second argument or environment variable
IMAGE_TAG="${2:-${IMAGE_TAG:-}}"

echo "Deploying CDK stacks to environment: $TARGET_ENV"
if [[ -n "$IMAGE_TAG" ]]; then
  echo "Using image tag: $IMAGE_TAG"
fi

# Build CDK command with optional image tag context
CDK_CMD="cdk deploy --concurrency 10 --require-approval never --exclusively"
if [[ -n "$IMAGE_TAG" ]]; then
  CDK_CMD="$CDK_CMD -c imageTag=$IMAGE_TAG"
fi

CDK_ENV="$TARGET_ENV" $CDK_CMD \
S3Stack \
WorkflowFargateServiceStack \
CommunicationsFargateServiceStack \
SensorsFargateServiceStack \
CommandFargateServiceStack \
OrgsFargateServiceStack \
PermsFargateServiceStack \
FileRepositoryFargateServiceStack \
FeatureFlagsFargateServiceStack \
LambdasStack \
CommandUIStack