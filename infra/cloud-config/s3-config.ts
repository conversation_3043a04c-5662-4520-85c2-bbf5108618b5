import * as cdk from 'aws-cdk-lib';
import * as s3 from 'aws-cdk-lib/aws-s3';

export interface S3BucketConfig {
    bucketName: string;
    description: string;
    bucketKeyEnabled?: boolean;
    versioned?: boolean;
    blockPublicAccess?: s3.BlockPublicAccess;
    cors?: s3.CorsRule[];
    lifecycleRules?: s3.LifecycleRule[];
    removalPolicy?: cdk.RemovalPolicy;
    autoDeleteObjects?: boolean;
    tags?: { [key: string]: string };
} 