import { EnvConfig } from '../../config';
import { defaultConfig } from '../../default/config';
import { mergeDeep } from '../../merge';

const prodOverrides: Partial<EnvConfig> = {
    vpc: {
        cidr: '10.0.0.0/16',
    },
    deploys: {
        tagPrefix: 'v*',
    },
    environment: {
        accountId: '************',
        envName: 'prod-1',
        domain: 'tcu.gethero.com',
        primaryRegion: 'us-east-2',
    },
    sharedSecrets: {
        tlsCert: {
            arn: 'arn:aws:secretsmanager:us-east-2:************:secret:server-tls-cert-VLWaVW',
        },
    },
    oktaProviders: [
        {
            name: 'Okta',
            metadataUrl: 'https://gethero.okta.com/app/exk1x0kxj98mJXo9V1d8/sso/saml/metadata',
            issuer: 'http://www.okta.com/exk1x0kxj98mJXo9V1d8',
            orgId: '1'
        },
        {
            name: 'OktaSandbox',
            metadataUrl: 'https://gethero.okta.com/app/exk1wzdmtadxej3up1d8/sso/saml/metadata',
            issuer: 'http://www.okta.com/exk1wzdmtadxej3up1d8',
            orgId: '2'
        }
    ],
    db: {
        hero: {
            instanceClass: 'm7g',
            instanceSize: 'large',
            multiAz: true,
        },
        perms: {
            instanceClass: 'm7g',
            instanceSize: 'large',
            multiAz: true,
        },
        auditlog: {
            instanceClass: 'm7g',
            instanceSize: 'large',
            multiAz: true,
        },
    },
    servers: {
        perms: {
            secrets: [
                {
                    secretName: "bots/bot-post-confirmation-lambda-secret",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:bots/bot-post-confirmation-lambda-secret-2Kggrj",
                    envVarName: "BOT_POST_CONFIRMATION_LAMBDA_SECRET",
                },
                {
                    secretName: "bots/bot-pre-signup-lambda-secret",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:bots/bot-pre-signup-lambda-secret-wAUITJ",
                    envVarName: "BOT_PRE_SIGNUP_LAMBDA_SECRET",
                },
                {
                    secretName: "bots/bot-basic-auth-lambda-secret",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:bots/bot-basic-auth-lambda-secret/Communications-V6jMyH",
                    envVarName: "BOT_BASIC_AUTH_LAMBDA_SECRET",
                },
                {
                    secretName: "bots/bot-camera-listener-lambda-secret",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:bots/bot-camera-listener-lambda-secret-andSdC",
                    envVarName: "BOT_CAMERA_LISTENER_LAMBDA_SECRET",
                },
            ]
        },
        communications: {
            secrets: [
                {
                    secretName: "agora/chatOrgName",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:agora/chatOrgName-gMkJ80",
                    envVarName: "AGORA_CHAT_ORG_NAME",
                },
                {
                    secretName: "agora/chatAppName",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:agora/chatAppName-WEvCRT",
                    envVarName: "AGORA_CHAT_APP_NAME",
                },
                {
                    secretName: "agora/chatHostURL",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:agora/chatHostURL-CVBvtd",
                    envVarName: "AGORA_CHAT_HOST_URL",
                },
                {
                    secretName: "agora/chatAppId",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:agora/chatAppId-drLrSY",
                    envVarName: "AGORA_CHAT_APP_ID",
                },
                {
                    secretName: "agora/appCertificate",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:agora/appCertificate-amoYOS",
                    envVarName: "AGORA_APP_CERTIFICATE",
                },
                {
                    secretName: "agora/appId",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:agora/appId-5XShTE",
                    envVarName: "AGORA_APP_ID",
                },
                {
                    secretName: "twilio/account_sid",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:twilio/account_sid-RSUsKN",
                    envVarName: "TWILIO_ACCOUNT_SID",
                },
                {
                    secretName: "twilio/auth_token",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:twilio/auth_token-sw0JpC",
                    envVarName: "TWILIO_AUTH_TOKEN",
                },
                {
                    secretName: "twilio/api_key_sid",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:twilio/api_key_sid-ug6H0q",
                    envVarName: "TWILIO_API_KEY_SID",
                },
                {
                    secretName: "twilio/api_key_secret",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:twilio/api_key_secret-wOqXLb",
                    envVarName: "TWILIO_API_KEY_SECRET",
                },
                {
                    secretName: "zello/admin_user",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:zello/admin_user-cjaFzC",
                    envVarName: "ZELLO_ADMIN_USER",
                },
                {
                    secretName: "zello/admin_user_password",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:zello/admin_user_password-ETsDin",
                    envVarName: "ZELLO_ADMIN_USER_PASSWORD",
                },
                {
                    secretName: "zello/api_key",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:zello/api_key-8DWxEe",
                    envVarName: "ZELLO_API_KEY",
                },
            ],
        },
        workflow: {
            secrets: [
                {
                    secretName: "bots/bot-post-confirmation-lambda-secret",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:bots/bot-post-confirmation-lambda-secret-2Kggrj",
                    envVarName: "BOT_POST_CONFIRMATION_LAMBDA_SECRET",
                },
                {
                    secretName: "bots/bot-camera-listener-lambda-secret",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:bots/bot-camera-listener-lambda-secret-andSdC",
                    envVarName: "BOT_CAMERA_LISTENER_LAMBDA_SECRET",
                },
                {
                    secretName: "zello/admin_user",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:zello/admin_user-cjaFzC",
                    envVarName: "ZELLO_ADMIN_USER",
                },
                {
                    secretName: "zello/admin_user_password",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:zello/admin_user_password-ETsDin",
                    envVarName: "ZELLO_ADMIN_USER_PASSWORD",
                },
                {
                    secretName: "zello/api_key",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:zello/api_key-8DWxEe",
                    envVarName: "ZELLO_API_KEY",
                },
            ]
        },
        orgs: {
            secrets: [
                {
                    secretName: "bots/bot-post-confirmation-lambda-secret",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:bots/bot-post-confirmation-lambda-secret-2Kggrj",
                    envVarName: "BOT_POST_CONFIRMATION_LAMBDA_SECRET",
                },
                {
                    secretName: "bots/bot-pre-signup-lambda-secret",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:bots/bot-pre-signup-lambda-secret-wAUITJ",
                    envVarName: "BOT_PRE_SIGNUP_LAMBDA_SECRET",
                },
                {
                    secretName: "bots/bot-basic-auth-lambda-secret",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:bots/bot-basic-auth-lambda-secret/Communications-V6jMyH",
                    envVarName: "BOT_BASIC_AUTH_LAMBDA_SECRET",
                },
                {
                    secretName: "zello/admin_user",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:zello/admin_user-cjaFzC",
                    envVarName: "ZELLO_ADMIN_USER",
                },
                {
                    secretName: "zello/admin_user_password",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:zello/admin_user_password-ETsDin",
                    envVarName: "ZELLO_ADMIN_USER_PASSWORD",
                },
                {
                    secretName: "zello/api_key",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:zello/api_key-8DWxEe",
                    envVarName: "ZELLO_API_KEY",
                },
                {
                    secretName: "twilio/account_sid",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:twilio/account_sid-RSUsKN",
                    envVarName: "TWILIO_ACCOUNT_SID",
                },
                {
                    secretName: "twilio/auth_token",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:twilio/auth_token-sw0JpC",
                    envVarName: "TWILIO_AUTH_TOKEN",
                },
                {
                    secretName: "twilio/api_key_sid",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:twilio/api_key_sid-ug6H0q",
                    envVarName: "TWILIO_API_KEY_SID",
                },
                {
                    secretName: "twilio/api_key_secret",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:twilio/api_key_secret-wOqXLb",
                    envVarName: "TWILIO_API_KEY_SECRET",
                },
            ]
        }
    },
    twilioAccountSid: 'AC5d248524a0eab576e9b56d2f98bf34d0',
    tags: {
        env: 'prod',
    },
};

export default mergeDeep(defaultConfig, prodOverrides);