import { EnvConfig } from '../../config';
import { defaultConfig } from '../../default/config';
import { mergeDeep } from '../../merge';

const prodOverrides: Partial<EnvConfig> = {
    vpc: {
        cidr: '10.2.0.0/16',
    },
    deploys: {
        tagPrefix: 'v*',
    },
    environment: {
        accountId: '************',
        envName: 'prod-2',
        domain: 'cisco.gethero.com',
        primaryRegion: 'us-east-2',
    },
    sharedSecrets: {
        tlsCert: {
            arn: 'arn:aws:secretsmanager:us-east-2:************:secret:server-tls-cert-tPlNtx',
        },
    },
    oktaProviders: [
        {
            name: 'Org1',
            metadataUrl: 'https://gethero.okta.com/app/exk1xkl3ymdPuVPbc1d8/sso/saml/metadata',
            issuer: 'http://www.okta.com/exk1xkl3ymdPuVPbc1d8',
            orgId: '1'
        },
        {
            name: 'Org2',
            metadataUrl: 'https://gethero.okta.com/app/exk1xklax0nPCu6jV1d8/sso/saml/metadata',
            issuer: 'http://www.okta.com/exk1xklax0nPCu6jV1d8',
            orgId: '2'
        }
    ],
    db: {
        hero: {
            instanceClass: 'm7g',
            instanceSize: 'large',
            multiAz: true,
        },
        perms: {
            instanceClass: 'm7g',
            instanceSize: 'large',
            multiAz: true,
        },
        auditlog: {
            instanceClass: 'm7g',
            instanceSize: 'large',
            multiAz: true,
        },
    },
    servers: {
        perms: {
            secrets: [
                {
                    secretName: "bots/bot-post-confirmation-lambda-secret",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:bots/bot-post-confirmation-lambda-secret-WU1m2X",
                    envVarName: "BOT_POST_CONFIRMATION_LAMBDA_SECRET",
                },
                {
                    secretName: "bots/bot-pre-signup-lambda-secret",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:bots/bot-pre-signup-lambda-secret-F7BFQ4",
                    envVarName: "BOT_PRE_SIGNUP_LAMBDA_SECRET",
                },
                {
                    secretName: "bots/bot-basic-auth-lambda-secret",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:bots/bot-basic-auth-lambda-secret/Communications-a0cuwl",
                    envVarName: "BOT_BASIC_AUTH_LAMBDA_SECRET",
                },
                {
                    secretName: "bots/bot-camera-listener-lambda-secret",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:bots/bot-camera-listener-lambda-secret-k2S8m1",
                    envVarName: "BOT_CAMERA_LISTENER_LAMBDA_SECRET",
                },
            ]
        },
        communications: {
            secrets: [
                {
                    secretName: "agora/chatOrgName",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:agora/chatOrgName-THy2fA",
                    envVarName: "AGORA_CHAT_ORG_NAME",
                },
                {
                    secretName: "agora/chatAppName",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:agora/chatAppName-GadqTX",
                    envVarName: "AGORA_CHAT_APP_NAME",
                },
                {
                    secretName: "agora/chatHostURL",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:agora/chatHostURL-jSjZng",
                    envVarName: "AGORA_CHAT_HOST_URL",
                },
                {
                    secretName: "agora/chatAppId",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:agora/chatAppId-IVMnt8",
                    envVarName: "AGORA_CHAT_APP_ID",
                },
                {
                    secretName: "agora/appCertificate",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:agora/appCertificate-dnX5bu",
                    envVarName: "AGORA_APP_CERTIFICATE",
                },
                {
                    secretName: "agora/appId",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:agora/appId-t2RMb8",
                    envVarName: "AGORA_APP_ID",
                },
                {
                    secretName: "twilio/account_sid",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:twilio/account_sid-ScpQNt",
                    envVarName: "TWILIO_ACCOUNT_SID",
                },
                {
                    secretName: "twilio/auth_token",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:twilio/auth_token-SBXDdZ",
                    envVarName: "TWILIO_AUTH_TOKEN",
                },
                {
                    secretName: "twilio/api_key_sid",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:twilio/api_key_sid-5ByZwD",
                    envVarName: "TWILIO_API_KEY_SID",
                },
                {
                    secretName: "twilio/api_key_secret",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:twilio/api_key_secret-YwHbRg",
                    envVarName: "TWILIO_API_KEY_SECRET",
                },
                {
                    secretName: "zello/admin_user",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:zello/admin_user-lsP8oq",
                    envVarName: "ZELLO_ADMIN_USER",
                },
                {
                    secretName: "zello/admin_user_password",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:zello/admin_user_password-mZ6rbx",
                    envVarName: "ZELLO_ADMIN_USER_PASSWORD",
                },
                {
                    secretName: "zello/api_key",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:zello/api_key-Jaubkv",
                    envVarName: "ZELLO_API_KEY",
                },
            ],
        },
        workflow: {
            secrets: [
                {
                    secretName: "bots/bot-post-confirmation-lambda-secret",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:bots/bot-post-confirmation-lambda-secret-WU1m2X",
                    envVarName: "BOT_POST_CONFIRMATION_LAMBDA_SECRET",
                },
                {
                    secretName: "bots/bot-camera-listener-lambda-secret",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:bots/bot-camera-listener-lambda-secret-k2S8m1",
                    envVarName: "BOT_CAMERA_LISTENER_LAMBDA_SECRET",
                },
                {
                    secretName: "zello/admin_user",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:zello/admin_user-lsP8oq",
                    envVarName: "ZELLO_ADMIN_USER",
                },
                {
                    secretName: "zello/admin_user_password",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:zello/admin_user_password-mZ6rbx",
                    envVarName: "ZELLO_ADMIN_USER_PASSWORD",
                },
                {
                    secretName: "zello/api_key",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:zello/api_key-Jaubkv",
                    envVarName: "ZELLO_API_KEY",
                },
            ]
        },
        orgs: {
            secrets: [
                {
                    secretName: "bots/bot-post-confirmation-lambda-secret",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:bots/bot-post-confirmation-lambda-secret-WU1m2X",
                    envVarName: "BOT_POST_CONFIRMATION_LAMBDA_SECRET",
                },
                {
                    secretName: "bots/bot-pre-signup-lambda-secret",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:bots/bot-pre-signup-lambda-secret-F7BFQ4",
                    envVarName: "BOT_PRE_SIGNUP_LAMBDA_SECRET",
                },
                {
                    secretName: "bots/bot-basic-auth-lambda-secret",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:bots/bot-basic-auth-lambda-secret/Communications-a0cuwl",
                    envVarName: "BOT_BASIC_AUTH_LAMBDA_SECRET",
                },
                {
                    secretName: "zello/admin_user",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:zello/admin_user-lsP8oq",
                    envVarName: "ZELLO_ADMIN_USER",
                },
                {
                    secretName: "zello/admin_user_password",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:zello/admin_user_password-mZ6rbx",
                    envVarName: "ZELLO_ADMIN_USER_PASSWORD",
                },
                {
                    secretName: "zello/api_key",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:zello/api_key-Jaubkv",
                    envVarName: "ZELLO_API_KEY",
                },
                {
                    secretName: "twilio/account_sid",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:twilio/account_sid-ScpQNt",
                    envVarName: "TWILIO_ACCOUNT_SID",
                },
                {
                    secretName: "twilio/auth_token",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:twilio/auth_token-SBXDdZ",
                    envVarName: "TWILIO_AUTH_TOKEN",
                },
                {
                    secretName: "twilio/api_key_sid",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:twilio/api_key_sid-5ByZwD",
                    envVarName: "TWILIO_API_KEY_SID",
                },
                {
                    secretName: "twilio/api_key_secret",
                    arn: "arn:aws:secretsmanager:us-east-2:************:secret:twilio/api_key_secret-YwHbRg",
                    envVarName: "TWILIO_API_KEY_SECRET",
                },
            ]
        }
    },
    twilioAccountSid: 'AC7711601709547e49bea37ddddb4e18d6',
    tags: {
        env: 'prod-2',
    },
};

export default mergeDeep(defaultConfig, prodOverrides);