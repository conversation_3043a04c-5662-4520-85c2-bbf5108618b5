import { ServerConfig } from '../../server-config';

export const workflowServerConfig: ServerConfig = {
    serviceName: "Workflow",
    enableDbAccess: true,
    taskRolePolicies: [
        {
            actions: ["kms:Encrypt", "kms:Decrypt", "kms:ReEncrypt*", "kms:GenerateDataKey*", "kms:DescribeKey"],
            resources: ["${KMS_KEY_ARN}"]
        },
        {
            actions: [
                "s3:GetObject", "s3:PutObject", "s3:DeleteObject", "s3:ListBucket", "s3:GetObjectVersion",
                "s3:PutObjectAcl", "s3:GetObjectAcl", "s3:GetBucketLocation", "s3:GetBucketVersioning"
            ],
            resources: ["arn:aws:s3:::*", "arn:aws:s3:::*/*"]
        },
        {
            actions: ["kinesis:PutRecord", "kinesis:PutRecords"],
            resources: ["${KINESIS_STREAM_ARN}"]
        },
    ],
    secrets: [
        {
            secretName: "bots/bot-post-confirmation-lambda-secret",
            envVarName: "BOT_POST_CONFIRMATION_LAMBDA_SECRET",
        },
        {
            secretName: "bots/bot-camera-listener-lambda-secret",
            envVarName: "BOT_CAMERA_LISTENER_LAMBDA_SECRET",
        },
        {
            secretName: "zello/admin_user",
            envVarName: "ZELLO_ADMIN_USER",
        },
        {
            secretName: "zello/admin_user_password",
            envVarName: "ZELLO_ADMIN_USER_PASSWORD",
        },
        {
            secretName: "zello/api_key",
            envVarName: "ZELLO_API_KEY",
        },
    ],
    env: {
        "REPO_TYPE": "postgres",
        "KINESIS_STREAM_NAME": "kinesis-stream",
    }
}; 