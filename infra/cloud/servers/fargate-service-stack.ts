import * as cdk from 'aws-cdk-lib';
import * as apigw from 'aws-cdk-lib/aws-apigateway';
import * as apigwv2 from 'aws-cdk-lib/aws-apigatewayv2';
import { HttpIamAuthorizer, HttpUserPoolAuthorizer } from 'aws-cdk-lib/aws-apigatewayv2-authorizers';
import { HttpAlbIntegration, HttpLambdaIntegration } from 'aws-cdk-lib/aws-apigatewayv2-integrations';
import * as cloudwatch from 'aws-cdk-lib/aws-cloudwatch';
import * as actions from 'aws-cdk-lib/aws-cloudwatch-actions';
import { Peer, Port, SecurityGroup } from 'aws-cdk-lib/aws-ec2';
import * as ecr from 'aws-cdk-lib/aws-ecr';
import * as ecs from 'aws-cdk-lib/aws-ecs';
import * as ecs_patterns from 'aws-cdk-lib/aws-ecs-patterns';
import { ApplicationProtocol, ApplicationProtocolVersion, ApplicationTargetGroup, ListenerCondition, Protocol, SslPolicy, TargetType } from 'aws-cdk-lib/aws-elasticloadbalancingv2';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import { CfnRecordSet } from 'aws-cdk-lib/aws-route53';
import * as sns from 'aws-cdk-lib/aws-sns';
import * as wafv2 from 'aws-cdk-lib/aws-wafv2';
import { Construct } from 'constructs';
import * as path from 'path';
import { EnvConfig } from '../../cloud-config/config';
import { ServerConfig } from '../../cloud-config/server-config';
import { GlobalCertsStack } from '../shared/global-certs-stack';
import { LocalCertsStack } from '../shared/local-certs-stack';
import { SharedStacks } from '../shared/shared-config';


export interface FargateServiceStackProps extends cdk.StackProps {
  serverConfig: ServerConfig;
  sharedStacks: SharedStacks;
  sharedEnvVars: Record<string, string>;
  imageTag: string;
  config: EnvConfig;
  globalCertsStack: GlobalCertsStack;
  localCertsStack: LocalCertsStack;
  webAcl: wafv2.CfnWebACL;
  pagerDutyTopics?: {
    critical: sns.ITopic;
    high: sns.ITopic;
    info: sns.ITopic;
  };
}

export class FargateServiceStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props: FargateServiceStackProps) {
    const { serverConfig, sharedStacks, sharedEnvVars, imageTag, config, globalCertsStack, localCertsStack, webAcl } = props;
    const stackId = `${id}FargateServiceStack`;
    super(scope, stackId, props);

    if (props.sharedStacks.passwordStack) {
      this.addDependency(props.sharedStacks.passwordStack);
    }

    const repositoryName = `${id.toLowerCase()}-service`;

    const repository = config.sharedEnvironment.accountId
      ? ecr.Repository.fromRepositoryArn(this, `${id}ServiceRepo`, `arn:aws:ecr:${config.sharedEnvironment.ecrRegion}:${config.sharedEnvironment.accountId}:repository/${repositoryName}`)
      : ecr.Repository.fromRepositoryName(this, `${id}ServiceRepo`, repositoryName);

    const hostedZone = globalCertsStack.hostedZone;
    const rootDomain = config.environment.domain;

    // Domain name for the Cognito-authorized API
    const CognitoApiDomainName = `${id.toLocaleLowerCase()}.api.${rootDomain}`;
    // Domain name for the IAM-authorized API
    const IAMApiDomainName = `${id.toLocaleLowerCase()}.iam.api.${rootDomain}`;
    // Domain name for the Basic Auth API
    const BasicAuthApiDomainName = `${id.toLocaleLowerCase()}.basic.api.${rootDomain}`;
    // Domain name for the load balancer (accessible from the VPC)
    const lbDomainName = `${id.toLocaleLowerCase()}.lb.api.${rootDomain}`;

    // Create certificates for the domains
    const wildcardCertificate = localCertsStack.wildcardApiCert;


    const logGroup = new ecs.AwsLogDriver({
      streamPrefix: `${id}-logs`,
      logGroup: new cdk.aws_logs.LogGroup(this, `${id}LogGroup`, {
        logGroupName: `/ecs/${id}Service`,
        retention: cdk.aws_logs.RetentionDays.ONE_WEEK, // Customize the retention period
        removalPolicy: cdk.RemovalPolicy.DESTROY, // Adjust as needed
      }),
    });


    // build the secrets object
    const secretsObj: { [key: string]: ecs.Secret } = {};
    const secretConstructs: cdk.aws_secretsmanager.ISecret[] = [];
    if (serverConfig.secrets) {
      serverConfig.secrets.forEach(secret => {
        const secretConstruct = secret.arn
          ? cdk.aws_secretsmanager.Secret.fromSecretAttributes(this, `${id}${secret.envVarName}Secret`, {
            secretCompleteArn: secret.arn,
          })
          : cdk.aws_secretsmanager.Secret.fromSecretNameV2(this, `${id}${secret.envVarName}Secret`, secret.secretName);

        secretConstructs.push(secretConstruct);
        secretsObj[secret.envVarName] = ecs.Secret.fromSecretsManager(secretConstruct);
      });
    }

    // Add TLS certificate secret
    const certificateSecret = cdk.aws_secretsmanager.Secret.fromSecretAttributes(
      this,
      `${id}CertificateSecret`,
      {
        secretCompleteArn: config.sharedSecrets.tlsCert.arn,
      }
    );
    secretsObj['TLS_CERTIFICATE_SECRET'] = ecs.Secret.fromSecretsManager(certificateSecret);

    if (serverConfig.enableDbAccess) {
      const dbStack = sharedStacks.dbStack;
      secretsObj["DB_CREDENTIALS"] = ecs.Secret.fromSecretsManager(dbStack.dbSecret);

      // Create a unique database user secret for this service
      const dbInstance = dbStack.dbInstance;
      const serviceName = serverConfig.serviceName.toLowerCase();
      const userSecret = new cdk.aws_secretsmanager.Secret(this, `${id}DbUserSecret`, {
        secretName: `${serviceName}-db-user-secret`,
        generateSecretString: {
          secretStringTemplate: cdk.Stack.of(this).toJsonString({
            username: serviceName,
            host: dbInstance.instanceEndpoint.hostname,
            port: dbInstance.instanceEndpoint.port,
            dbname: 'hero',  // Using default database name
          }),
          generateStringKey: 'password',
          excludePunctuation: true,
          includeSpace: false,
          passwordLength: 16,
        },
      });

      secretsObj["DB_USER_SECRET"] = ecs.Secret.fromSecretsManager(userSecret);
    }

    const key = new cdk.aws_kms.Key(this, `${id}Key`, {
      alias: `${id}-key`,
      description: `KMS key for ${id} service`,
      enableKeyRotation: true,
      removalPolicy: cdk.RemovalPolicy.DESTROY,
    });

    // Create a Security Group for the Load Balancers
    const LBsecurityGroup = new SecurityGroup(this, `${id}NlbSecurityGroup`, {
      vpc: sharedStacks.vpc.vpc,
      description: `Security group for ${id} LBs`,
      allowAllOutbound: true,
    });

    // Allow inbound traffic on ports 80 and 443 to the LB security group
    LBsecurityGroup.addIngressRule(
      Peer.anyIpv4(),
      Port.tcp(80),
      'Allow inbound HTTP traffic from anywhere'
    );
    LBsecurityGroup.addIngressRule(
      Peer.anyIpv4(),
      Port.tcp(443),
      'Allow inbound HTTPS traffic from anywhere'
    );

    // Create a Security Group for the Service
    const serviceSecurityGroup = new SecurityGroup(this, `${id}ServiceSecurityGroup`, {
      vpc: sharedStacks.vpc.vpc,
      description: `Security group for ${id} Service`,
      allowAllOutbound: true,
    });

    // Allow inbound traffic on port 8080 from the LB security groups
    serviceSecurityGroup.addIngressRule(
      Peer.securityGroupId(LBsecurityGroup.securityGroupId),
      Port.tcp(443),
      'Allow inbound traffic on port 443 from the LB security group'
    );


    // merge shared environment variables with service-specific environment variables
    const envVars = { ...sharedEnvVars, ...serverConfig.env };
    envVars[`KMS_KEY_ARN`] = key.keyArn;

    const envVarReplacements: { [key: string]: string } = {
      '${ROOT_DOMAIN}': rootDomain,
      '${ENVIRONMENT}': config.environment.envName,
    };

    for (const key in envVars) {
      let value = envVars[key];
      for (const placeholder in envVarReplacements) {
        if (value.includes(placeholder)) {
          value = value.replaceAll(placeholder, envVarReplacements[placeholder]);
        }
      }
      envVars[key] = value;
    }

    const containerImage = serverConfig.dockerImageName
      ? ecs.ContainerImage.fromRegistry(serverConfig.dockerImageName)
      : ecs.ContainerImage.fromEcrRepository(repository, imageTag)

    // Create a Fargate service with a load balancer
    const fargateService = new ecs_patterns.ApplicationLoadBalancedFargateService(this, `${id}Service`, {
      cluster: sharedStacks.vpc.cluster,
      cpu: serverConfig.cpu || 512,
      memoryLimitMiB: serverConfig.memoryLimitMiB || 1024,
      desiredCount: serverConfig.desiredCount || 1,
      maxHealthyPercent: 200,
      minHealthyPercent: 50,
      circuitBreaker: { rollback: true },
      healthCheckGracePeriod: cdk.Duration.seconds(5),
      taskImageOptions: {
        image: containerImage,
        containerPort: 443,
        environment: envVars,
        logDriver: logGroup,
        secrets: secretsObj,
      },
      domainZone: hostedZone,
      domainName: lbDomainName,
      protocol: ApplicationProtocol.HTTPS,
      protocolVersion: ApplicationProtocolVersion.HTTP1,
      targetProtocol: ApplicationProtocol.HTTPS,
      redirectHTTP: true,
      publicLoadBalancer: false,
      securityGroups: [serviceSecurityGroup],
      sslPolicy: SslPolicy.FIPS_TLS13_12_RES,
      runtimePlatform: {
        operatingSystemFamily: ecs.OperatingSystemFamily.LINUX,
        cpuArchitecture: ecs.CpuArchitecture.X86_64,
      },
    });

    // Associate WAF with the Application Load Balancer
    new wafv2.CfnWebACLAssociation(this, `${id}AlbWafAssociation`, {
      resourceArn: fargateService.loadBalancer.loadBalancerArn,
      webAclArn: webAcl.attrArn,
    });

    if (serverConfig.taskRolePolicies) {
      const replacements: { [key: string]: string } = {
        '${KMS_KEY_ARN}': key.keyArn,
        '${COGNITO_USER_POOL_ARN}': sharedStacks.cognitoStack.userPool.userPoolArn,
        '${KINESIS_STREAM_ARN}': sharedStacks.auditLogKinesisStack.stream.streamArn,
      };

      serverConfig.taskRolePolicies.forEach((policy) => {
        const resources = policy.resources.map(resource => {
          let replacedResource = resource;
          for (const placeholder in replacements) {
            if (resource.includes(placeholder)) {
              replacedResource = replacedResource.replaceAll(placeholder, replacements[placeholder]);
            }
          }
          return replacedResource;
        });

        fargateService.taskDefinition.addToTaskRolePolicy(new cdk.aws_iam.PolicyStatement({
          effect: policy.effect ? policy.effect as cdk.aws_iam.Effect : cdk.aws_iam.Effect.ALLOW,
          actions: policy.actions,
          resources: resources,
        }));
      });
    }

    fargateService.targetGroup.configureHealthCheck({
      path: '/health',
      protocol: Protocol.HTTPS,
    });

    const grpcTargetGroup = new ApplicationTargetGroup(this, `${id}GrpcTargetGroup`, {
      vpc: sharedStacks.vpc.vpc,
      targetType: TargetType.IP,
      protocol: ApplicationProtocol.HTTPS,
      port: 443,
      protocolVersion: ApplicationProtocolVersion.GRPC,
      healthCheck: {
        path: '/grpc.health.v1.Health/Check',
        healthyGrpcCodes: '0',
      },
    });

    fargateService.service.attachToApplicationTargetGroup(grpcTargetGroup);

    const scalableTarget = fargateService.service.autoScaleTaskCount({
      minCapacity: serverConfig.minCapacity || 1,
      maxCapacity: serverConfig.maxCapacity || 20,
    });

    scalableTarget.scaleOnCpuUtilization('CpuScaling', {
      targetUtilizationPercent: 70,
    });

    scalableTarget.scaleOnMemoryUtilization('MemoryScaling', {
      targetUtilizationPercent: 70,
    });

    fargateService.listener.addTargetGroups(`${id}GrpcRule`, {
      priority: 10,
      conditions: [
        ListenerCondition.httpHeader('content-type', [
          'application/grpc',
          'application/grpc+proto',
          'application/grpc+json',
        ]),
      ],
      targetGroups: [grpcTargetGroup],
    });

    fargateService.listener.addTargetGroups(`${id}GrpcRuleSpillover`, {
      priority: 9,
      conditions: [
        ListenerCondition.httpHeader('content-type', [
          'application/grpc-web',
          'application/grpc-web+proto',
          'application/grpc-web+json',
        ]),
      ],
      targetGroups: [grpcTargetGroup],
    });

    // Basic performance/usage CloudWatch alarms wired to PagerDuty topics
    const alarmPeriod = cdk.Duration.minutes(5);

    const cpuAlarm = new cloudwatch.Alarm(this, `${id}ServiceCpuHigh`, {
      alarmName: `${serverConfig.serviceName}-cpu-utilization-high`,
      alarmDescription: `High CPU utilization on ${serverConfig.serviceName} ECS service`,
      metric: fargateService.service.metricCpuUtilization({
        period: alarmPeriod,
        statistic: 'Average',
      }),
      threshold: 80,
      evaluationPeriods: 3,
      datapointsToAlarm: 3,
      comparisonOperator: cloudwatch.ComparisonOperator.GREATER_THAN_OR_EQUAL_TO_THRESHOLD,
      treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
    });

    const memoryAlarm = new cloudwatch.Alarm(this, `${id}ServiceMemoryHigh`, {
      alarmName: `${serverConfig.serviceName}-memory-utilization-high`,
      alarmDescription: `High memory utilization on ${serverConfig.serviceName} ECS service`,
      metric: fargateService.service.metricMemoryUtilization({
        period: alarmPeriod,
        statistic: 'Average',
      }),
      threshold: 80,
      evaluationPeriods: 3,
      datapointsToAlarm: 3,
      comparisonOperator: cloudwatch.ComparisonOperator.GREATER_THAN_OR_EQUAL_TO_THRESHOLD,
      treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
    });

    const responseTimeAlarm = new cloudwatch.Alarm(this, `${id}AlbResponseTimeHigh`, {
      alarmName: `${serverConfig.serviceName}-alb-target-response-time-high`,
      alarmDescription: `High target response time on ${serverConfig.serviceName} ALB`,
      metric: fargateService.targetGroup.metrics.targetResponseTime({
        period: alarmPeriod,
        statistic: 'Average',
      }),
      threshold: 1, // seconds
      evaluationPeriods: 3,
      datapointsToAlarm: 3,
      comparisonOperator: cloudwatch.ComparisonOperator.GREATER_THAN_OR_EQUAL_TO_THRESHOLD,
      treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
    });

    const target5xxAlarm = new cloudwatch.Alarm(this, `${id}AlbTarget5xxHigh`, {
      alarmName: `${serverConfig.serviceName}-alb-target-5xx-errors`,
      alarmDescription: `High number of 5xx errors from targets behind ${serverConfig.serviceName} ALB`,
      metric: fargateService.targetGroup.metrics.httpCodeTarget(
        cdk.aws_elasticloadbalancingv2.HttpCodeTarget.TARGET_5XX_COUNT,
        { period: alarmPeriod, statistic: 'Sum' }
      ),
      threshold: 10,
      evaluationPeriods: 2,
      datapointsToAlarm: 2,
      comparisonOperator: cloudwatch.ComparisonOperator.GREATER_THAN_OR_EQUAL_TO_THRESHOLD,
      treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
    });

    const elb5xxAlarm = new cloudwatch.Alarm(this, `${id}AlbElb5xxHigh`, {
      alarmName: `${serverConfig.serviceName}-alb-elb-5xx-errors`,
      alarmDescription: `High number of 5xx errors reported by the ALB for ${serverConfig.serviceName}`,
      metric: fargateService.loadBalancer.metrics.httpCodeElb(
        cdk.aws_elasticloadbalancingv2.HttpCodeElb.ELB_5XX_COUNT,
        { period: alarmPeriod, statistic: 'Sum' }
      ),
      threshold: 5,
      evaluationPeriods: 2,
      datapointsToAlarm: 2,
      comparisonOperator: cloudwatch.ComparisonOperator.GREATER_THAN_OR_EQUAL_TO_THRESHOLD,
      treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
    });

    const unhealthyHostsAlarm = new cloudwatch.Alarm(this, `${id}AlbUnhealthyHosts`, {
      alarmName: `${serverConfig.serviceName}-alb-unhealthy-hosts`,
      alarmDescription: `Detected unhealthy targets behind ${serverConfig.serviceName} ALB`,
      metric: fargateService.targetGroup.metrics.unhealthyHostCount({
        period: alarmPeriod,
        statistic: 'Average',
      }),
      threshold: 0,
      evaluationPeriods: 1,
      datapointsToAlarm: 1,
      comparisonOperator: cloudwatch.ComparisonOperator.GREATER_THAN_THRESHOLD,
      treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
    });

    // Wire alarms to PagerDuty topics when provided
    if (props.pagerDutyTopics) {
      const critical = new actions.SnsAction(props.pagerDutyTopics.critical);
      const high = new actions.SnsAction(props.pagerDutyTopics.high);
      const info = new actions.SnsAction(props.pagerDutyTopics.info);

      [cpuAlarm, memoryAlarm, responseTimeAlarm, target5xxAlarm, elb5xxAlarm].forEach((alarm) => {
        alarm.addAlarmAction(high);
        alarm.addOkAction(high);
      });

      unhealthyHostsAlarm.addAlarmAction(critical);
      unhealthyHostsAlarm.addOkAction(critical);
    }

    const apiGwCustomDomain = new apigwv2.DomainName(this, `${id}DomainMapping`, {
      domainName: CognitoApiDomainName,
      certificate: wildcardCertificate,
    })

    const IAMapiGwCustomDomain = new apigwv2.DomainName(this, `${id}IAMDomainMapping`, {
      domainName: IAMApiDomainName,
      certificate: wildcardCertificate,
    })


    const vpcLinkSecurityGroup = new SecurityGroup(this, `${id}VpcLinkSG`, {
      vpc: sharedStacks.vpc.vpc,
      description: 'Security group for VPC Link',
      allowAllOutbound: true, // Adjust based on security needs
    });

    // Allow traffic from API Gateway or other sources
    vpcLinkSecurityGroup.addIngressRule(
      Peer.anyIpv4(),
      Port.tcp(443), // Assuming HTTPS traffic
      'Allow HTTPS traffic'
    );

    // Create a VPC Link to allow API Gateway to access the Fargate service
    const vpcLink = new apigwv2.VpcLink(this, `${id}VpcLink`, {
      vpc: sharedStacks.vpc.vpc,
      securityGroups: [vpcLinkSecurityGroup], // Attach Security Group
    });

    // Create an API Gateway proxy with a cognito authorizer
    const userPool = sharedStacks.cognitoStack.userPool;
    const api = new apigwv2.HttpApi(this, `${id}HttpProxyPrivateApi`, {
      defaultDomainMapping: {
        domainName: apiGwCustomDomain,
      },
      defaultAuthorizer: new HttpUserPoolAuthorizer(`${id}CognitoAuthorizor`, userPool, {
        userPoolClients: [sharedStacks.cognitoStack.memberClient, sharedStacks.cognitoStack.internalClient, sharedStacks.cognitoStack.adminClient],
      }),
      defaultIntegration: new HttpAlbIntegration(`${id}DefaultIntegration`, fargateService.listener, {
        secureServerName: lbDomainName,
        vpcLink: vpcLink,
      }),
      corsPreflight: {
        allowHeaders: ['Authorization', 'Content-Type'],
        allowMethods: [apigwv2.CorsHttpMethod.GET, apigwv2.CorsHttpMethod.POST, apigwv2.CorsHttpMethod.PUT, apigwv2.CorsHttpMethod.DELETE],
        allowOrigins: [`https://command.${rootDomain}`, `https://${rootDomain}`],
        maxAge: cdk.Duration.days(10),
      },
    });

    const apiLogGroup = new cdk.aws_logs.LogGroup(this, `${id}HttpProxyPrivateApiLogGroup`, {
      logGroupName: `/aws/apigateway/${id}-http-proxy-private-api`,
      retention: cdk.aws_logs.RetentionDays.ONE_WEEK,
      removalPolicy: cdk.RemovalPolicy.DESTROY,
    });

    const cfnStage = api.defaultStage!.node.defaultChild as apigwv2.CfnStage;
    cfnStage.accessLogSettings = {
      destinationArn: apiLogGroup.logGroupArn,
      format: JSON.stringify({
        caller: '$context.identity.caller',
        httpMethod: '$context.httpMethod',
        ip: '$context.identity.sourceIp',
        protocol: '$context.protocol',
        requestTime: '$context.requestTime',
        resourcePath: '$context.path',
        responseLength: '$context.responseLength',
        status: '$context.status',
        user: '$context.identity.user',
        requestId: '$context.requestId',
      }),
    };

    // Add an OPTIONS /{proxy+} route to support unauthorized OPTIONS requests
    // https://docs.aws.amazon.com/apigateway/latest/developerguide/http-api-cors.html
    const optionsLambda = new lambda.Function(this, 'OptionsLambda', {
      runtime: lambda.Runtime.NODEJS_18_X,
      handler: 'index.handler',
      code: lambda.Code.fromInline(`
        exports.handler = async (event) => {
          return {
            statusCode: 204,
            headers: {
              "Access-Control-Allow-Origin": "https://command.${rootDomain}, https://${rootDomain}",
              "Access-Control-Allow-Methods": "OPTIONS, GET, POST, PUT, DELETE",
              "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            body: ""
          };
        };
      `),
    });

    // Integration for OPTIONS requests
    const optionsIntegration = new HttpLambdaIntegration(
      'OptionsIntegration',
      optionsLambda
    );

    // Define OPTIONS /{proxy+} route with no authorization

    // necessary to enable cross-origin requests
    api.addRoutes({
      path: '/{proxy+}',
      methods: [apigwv2.HttpMethod.OPTIONS],
      integration: optionsIntegration,
      authorizer: new apigwv2.HttpNoneAuthorizer(),
    });

    // Create an API Gateway proxy with an IAM authorizer
    const IAMapi = new apigwv2.HttpApi(this, `${id}IAMHttpProxyPrivateApi`, {
      defaultDomainMapping: {
        domainName: IAMapiGwCustomDomain,
      },
      defaultAuthorizer: new HttpIamAuthorizer(),
      defaultIntegration: new HttpAlbIntegration(`${id}IAMDefaultIntegration`, fargateService.listener, {
        secureServerName: lbDomainName,
        vpcLink: vpcLink,
        parameterMapping: new apigwv2.ParameterMapping()
          .overwriteHeader('authorization2', apigwv2.MappingValue.contextVariable('identity.user'))
      }),
    });

    const iamApiLogGroup = new cdk.aws_logs.LogGroup(this, `${id}IAMHttpProxyPrivateApiLogGroup`, {
      logGroupName: `/aws/apigateway/${id}-iam-http-proxy-private-api`,
      retention: cdk.aws_logs.RetentionDays.ONE_WEEK,
      removalPolicy: cdk.RemovalPolicy.DESTROY,
    });

    const iamCfnStage = IAMapi.defaultStage!.node.defaultChild as apigwv2.CfnStage;
    iamCfnStage.accessLogSettings = {
      destinationArn: iamApiLogGroup.logGroupArn,
      format: JSON.stringify({
        caller: '$context.identity.caller',
        httpMethod: '$context.httpMethod',
        ip: '$context.identity.sourceIp',
        protocol: '$context.protocol',
        requestTime: '$context.requestTime',
        resourcePath: '$context.path',
        responseLength: '$context.responseLength',
        status: '$context.status',
        user: '$context.identity.user',
        requestId: '$context.requestId',
      }),
    };


    // Create DNS record to point to the API Gateway domain name
    new CfnRecordSet(this, `${id}ApiGatewayDNSRecord`, {
      hostedZoneId: hostedZone.hostedZoneId,
      name: CognitoApiDomainName,
      type: 'A',
      aliasTarget: {
        dnsName: apiGwCustomDomain.regionalDomainName,
        hostedZoneId: apiGwCustomDomain.regionalHostedZoneId,
      },
    });

    // Create DNS record to point to the IAM API Gateway domain name
    new CfnRecordSet(this, `${id}IAMApiGatewayDNSRecord`, {
      hostedZoneId: hostedZone.hostedZoneId,
      name: IAMApiDomainName,
      type: 'A',
      aliasTarget: {
        dnsName: IAMapiGwCustomDomain.regionalDomainName,
        hostedZoneId: IAMapiGwCustomDomain.regionalHostedZoneId,
      },
    });

    new cdk.CfnOutput(this, `${id}ApiGatewayURL`, {
      value: api.url!,
    });

    new cdk.CfnOutput(this, `${id}IAMApiGatewayURL`, {
      value: IAMapi.url!,
    });

    new cdk.CfnOutput(this, `${id}LoadBalancerDNS`, {
      value: fargateService.loadBalancer.loadBalancerDnsName,
    });

    if (serverConfig.enableBasicAuth) {
      const basicAuthSecret = new cdk.aws_secretsmanager.Secret(this, 'BasicAuthLambdaSecret', {
        secretName: `bots/bot-basic-auth-lambda-secret/${id}`,
        generateSecretString: {
          passwordLength: 30,
          excludePunctuation: true,
        },
      });

      // NOTE: This lambda is hard-coded for the communications service twilio webhooks usecase
      // Refactor as needed when second usecase is added
      const basicAuthApi = new apigw.RestApi(this, `${id}BasicAuthRestApi`, {
        cloudWatchRole: true,
        deployOptions: {
          stageName: 'prod',
          loggingLevel: apigw.MethodLoggingLevel.INFO,
          accessLogDestination: new apigw.LogGroupLogDestination(
            new cdk.aws_logs.LogGroup(this, `${id}BasicAuthApiLogGroup`, {
              logGroupName: `/aws/apigateway/${id}-basic-auth-api`,
              retention: cdk.aws_logs.RetentionDays.ONE_WEEK,
              removalPolicy: cdk.RemovalPolicy.DESTROY,
            })
          ),
          accessLogFormat: apigw.AccessLogFormat.jsonWithStandardFields({
            caller: true,
            httpMethod: true,
            ip: true,
            protocol: true,
            requestTime: true,
            resourcePath: true,
            responseLength: true,
            status: true,
            user: true
          }),
          // Create and use a CloudWatch logs role
        },
        // Add domain name configuration
        domainName: {
          domainName: BasicAuthApiDomainName,
          certificate: wildcardCertificate,
          endpointType: apigw.EndpointType.REGIONAL,
          securityPolicy: apigw.SecurityPolicy.TLS_1_2
        }
      });

      const basicAuthLambda = new lambda.Function(this, `${id}BasicAuthLambda`, {
        runtime: lambda.Runtime.PROVIDED_AL2023,
        handler: 'main',
        timeout: cdk.Duration.seconds(10),
        vpc: sharedStacks.vpc.vpc,
        code: lambda.Code.fromAsset(path.join(__dirname, './lambda/bootstrap.zip')),
        environment: {
          PARAMETERS_SECRETS_EXTENSION_LOG_LEVEL: 'debug',
          PARAMETERS_SECRETS_EXTENSION_CACHE_SIZE: '10',
          SECRETS_MANAGER_TTL: '300',
          ORGS_SERVICE_URL: `https://orgs.lb.api.${rootDomain}`,
          ALLOWED_RESOURCES: basicAuthApi.arnForExecuteApi('POST', '/*', 'prod'),
          BOT_SECRET_ARN: basicAuthSecret.secretArn,
          AWS_USE_FIPS_ENDPOINT: 'true',
        },
        paramsAndSecrets: lambda.ParamsAndSecretsLayerVersion.fromVersion(
          lambda.ParamsAndSecretsVersions.V1_0_103,
          {
            cacheSize: 10,
            logLevel: lambda.ParamsAndSecretsLogLevel.DEBUG,
          }
        ),
      });

      basicAuthSecret.grantRead(basicAuthLambda);

      // Create a custom authorizer for the RestApi using the above Lambda function
      const restApiAuthorizer = new apigw.RequestAuthorizer(this, `${id}RestApiAuthorizer`, {
        handler: basicAuthLambda,
        identitySources: ['method.request.header.Authorization'],
        resultsCacheTtl: cdk.Duration.seconds(0),
      });

      new wafv2.CfnWebACLAssociation(this, `${id}BasicAuthApiGatewayWafAssociation`, {
        resourceArn: basicAuthApi.deploymentStage.stageArn,
        webAclArn: webAcl.attrArn,
      });

      const nlb = new cdk.aws_elasticloadbalancingv2.NetworkLoadBalancer(this, `${id}BasicAuthNLB`, {
        vpc: sharedStacks.vpc.vpc,
        internetFacing: false,
        crossZoneEnabled: true,
        securityGroups: [LBsecurityGroup]
      });

      // Add a listener to the NLB
      const nlbListener = nlb.addListener(`${id}NLBListener`, {
        port: 443,
        protocol: cdk.aws_elasticloadbalancingv2.Protocol.TLS,
        certificates: [wildcardCertificate],
        sslPolicy: SslPolicy.FIPS_TLS13_12_RES,
      });

      // Add the Fargate service as a target to the NLB
      nlbListener.addTargets(`${id}NLBTargets`, {
        port: 443,
        targets: [fargateService.service],
        preserveClientIp: true,
        protocol: cdk.aws_elasticloadbalancingv2.Protocol.TLS,
      });

      const nlbDomainName = `${id.toLocaleLowerCase()}-network.lb.api.${rootDomain}`;
      // Create DNS record for the NLB
      new CfnRecordSet(this, `${id}NlbDnsRecord`, {
        hostedZoneId: hostedZone.hostedZoneId,
        name: nlbDomainName,
        type: 'A',
        aliasTarget: {
          dnsName: nlb.loadBalancerDnsName,
          hostedZoneId: nlb.loadBalancerCanonicalHostedZoneId,
        },
      });

      // Create a VPC Link for the REST API that points to the NLB
      const restApiVpcLink = new apigw.VpcLink(this, `${id}RestApiVpcLink`, {
        targets: [nlb],
        description: `VPC Link for ${id} Basic Auth API`,
      });

      // Create specific endpoint integrations
      const callStatusIntegration = new apigw.Integration({
        type: apigw.IntegrationType.HTTP_PROXY,
        integrationHttpMethod: 'POST',
        uri: "https://" + nlbDomainName + "/hero.communications.v1.TwilioWebhookService/callstatus",
        options: {
          connectionType: apigw.ConnectionType.VPC_LINK,
          vpcLink: restApiVpcLink,
          requestParameters: {
            'integration.request.header.X-Org-Id': 'context.authorizer.org_id',
            'integration.request.header.X-Username': 'context.authorizer.username'
          }
        }
      });

      const voiceIntegration = new apigw.Integration({
        type: apigw.IntegrationType.HTTP_PROXY,
        integrationHttpMethod: 'POST',
        uri: "https://" + nlbDomainName + "/hero.communications.v1.TwilioWebhookService/voice",
        options: {
          connectionType: apigw.ConnectionType.VPC_LINK,
          vpcLink: restApiVpcLink,
          requestParameters: {
            'integration.request.header.X-Org-Id': 'context.authorizer.org_id',
            'integration.request.header.X-Username': 'context.authorizer.username'
          }
        }
      });

      // Create specific endpoint integrations
      const connectAgentIntegration = new apigw.Integration({
        type: apigw.IntegrationType.HTTP_PROXY,
        integrationHttpMethod: 'POST',
        uri: "https://" + nlbDomainName + "/hero.communications.v1.TwilioWebhookService/twiml/connectAgent",
        options: {
          connectionType: apigw.ConnectionType.VPC_LINK,
          vpcLink: restApiVpcLink,
          requestParameters: {
            'integration.request.header.X-Org-Id': 'context.authorizer.org_id',
            'integration.request.header.X-Username': 'context.authorizer.username'
          }
        }
      });

      // Create specific endpoint integrations
      const agentDialStatusIntegration = new apigw.Integration({
        type: apigw.IntegrationType.HTTP_PROXY,
        integrationHttpMethod: 'POST',
        uri: "https://" + nlbDomainName + "/hero.communications.v1.TwilioWebhookService/twilio/agent-dial-status",
        options: {
          connectionType: apigw.ConnectionType.VPC_LINK,
          vpcLink: restApiVpcLink,
          requestParameters: {
            'integration.request.header.X-Org-Id': 'context.authorizer.org_id',
            'integration.request.header.X-Username': 'context.authorizer.username'
          }
        }
      });

      const waitHoldIntegration = new apigw.Integration({
        type: apigw.IntegrationType.HTTP_PROXY,
        integrationHttpMethod: 'POST',
        uri: "https://" + nlbDomainName + "/hero.communications.v1.TwilioWebhookService/waithold",
        options: {
          connectionType: apigw.ConnectionType.VPC_LINK,
          vpcLink: restApiVpcLink,
          requestParameters: {
            'integration.request.header.X-Org-Id': 'context.authorizer.org_id',
            'integration.request.header.X-Username': 'context.authorizer.username'
          }
        }
      });

      const methodOptions = {
        authorizer: restApiAuthorizer,
        authorizationType: apigw.AuthorizationType.CUSTOM,
        requestParameters: {
          'method.request.header.Authorization': true
        }
      };

      // Add specific endpoints
      const communicationsResource = basicAuthApi.root.addResource('hero.communications.v1.TwilioWebhookService');
      const callStatusResource = communicationsResource.addResource('callstatus');
      callStatusResource.addMethod('POST', callStatusIntegration, methodOptions);

      const voiceResource = communicationsResource.addResource('voice');
      voiceResource.addMethod('POST', voiceIntegration, methodOptions);

      const twimlResource = communicationsResource.addResource('twiml');
      const connectAgentResource = twimlResource.addResource('connectAgent');
      connectAgentResource.addMethod('POST', connectAgentIntegration, methodOptions);

      // Forward call action webhook integration
      const forwardActionIntegration = new apigw.Integration({
        type: apigw.IntegrationType.HTTP_PROXY,
        integrationHttpMethod: 'POST',
        uri: "https://" + nlbDomainName + "/hero.communications.v1.TwilioWebhookService/twiml/forward-action",
        options: {
          connectionType: apigw.ConnectionType.VPC_LINK,
          vpcLink: restApiVpcLink,
          requestParameters: {
            'integration.request.header.X-Org-Id': 'context.authorizer.org_id',
            'integration.request.header.X-Username': 'context.authorizer.username'
          }
        }
      });

      const twilioResource = communicationsResource.addResource('twilio');
      const agentDialStatusResource = twilioResource.addResource('agent-dial-status');
      agentDialStatusResource.addMethod('POST', agentDialStatusIntegration, methodOptions);

      const waitHoldResource = communicationsResource.addResource('waithold');
      waitHoldResource.addMethod('POST', waitHoldIntegration, methodOptions);

      const forwardActionResource = twimlResource.addResource('forward-action');
      forwardActionResource.addMethod('POST', forwardActionIntegration, methodOptions);

      // Add a gateway response for unauthorized (Twilio requires this)
      basicAuthApi.addGatewayResponse('MyGatewayResponse', {
        type: apigw.ResponseType.UNAUTHORIZED,
        statusCode: '401',
        responseHeaders: {
          'www-authenticate': `'Basic realm="Default"'`
        }
      });

      // Create DNS record for the Basic Auth API Gateway
      new CfnRecordSet(this, `${id}BasicAuthApiGatewayDNSRecord`, {
        hostedZoneId: hostedZone.hostedZoneId,
        name: BasicAuthApiDomainName,
        type: 'A',
        aliasTarget: {
          dnsName: basicAuthApi.domainName!.domainNameAliasDomainName,
          hostedZoneId: basicAuthApi.domainName!.domainNameAliasHostedZoneId,
        },
      });

      new cdk.CfnOutput(this, `${id}ApiEndpoint`, {
        value: basicAuthApi.url,
      });

      // Output the NLB DNS name for reference
      new cdk.CfnOutput(this, `${id}NlbDnsName`, {
        value: nlb.loadBalancerDnsName,
      });
    }
  }
}
