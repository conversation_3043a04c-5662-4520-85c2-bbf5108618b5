import * as cdk from 'aws-cdk-lib';
import * as sns from 'aws-cdk-lib/aws-sns';
import * as wafv2 from 'aws-cdk-lib/aws-wafv2';
import { execSync } from 'child_process';
import { Construct } from 'constructs';
import { EnvConfig } from '../../cloud-config/config';
import { ServerConfig } from '../../cloud-config/server-config';
import { SharedStacks } from '../shared/shared-config';
import { FargateServiceStack } from './fargate-service-stack';


function defineServers(
    scope: Construct,
    sharedStacks: SharedStacks,
    config: EnvConfig,
    env: cdk.StackProps['env'],
    webAcl: wafv2.CfnWebACL,
    pagerDutyTopics?: { critical: sns.ITopic; high: sns.ITopic; info: sns.ITopic }
): void {
    const getLatestGitTag = (): string => {
        const tag = execSync(`git tag -l 'v*' | sort -V | tail -n 1`).toString().trim();
        return tag;
    };

    // Allow overriding image tag via CDK context
    const contextImageTag = scope.node.tryGetContext('imageTag');
    const imageTag = contextImageTag || getLatestGitTag();

    if (contextImageTag) {
        console.log('Using context image tag:', contextImageTag);
    } else {
        console.log('Using latest git tag:', imageTag);
    }

    // Generate all server domain names
    const envVars = Object.values(config.servers).reduce((acc, serverConfig_) => {
        const serverConfig = serverConfig_ as ServerConfig;
        const domainName = `https://${serverConfig.serviceName.toLocaleLowerCase()}.lb.api.${config.environment.domain}`;
        acc[`${serverConfig.serviceName.toUpperCase()}_SERVICE_URL`] = domainName;
        return acc;
    }, {} as Record<string, string>);

    if (!env || !env.region) {
        throw new Error('CDK environment or region is not defined.');
    }

    envVars['COGNITO_USER_POOL_ID'] = sharedStacks.cognitoStack.userPool.userPoolId;
    envVars['AWS_REGION'] = env.region;
    envVars['SENTRY_DSN'] = config.sentryDsn;
    envVars['ENVIRONMENT'] = config.environment.envName;
    envVars['AWS_USE_FIPS_ENDPOINT'] = 'true';

    for (const serverConfig_ of Object.values(config.servers)) {
        const serverConfig = serverConfig_ as ServerConfig;
        new FargateServiceStack(
            scope,
            serverConfig.serviceName,
            {
                serverConfig,
                sharedStacks,
                sharedEnvVars: envVars,
                imageTag,
                config,
                env: env,
                crossRegionReferences: true,
                globalCertsStack: sharedStacks.certStack,
                localCertsStack: sharedStacks.localCertStack,
                webAcl: webAcl,
                pagerDutyTopics,
            }
        );
    }
}

export { defineServers };
