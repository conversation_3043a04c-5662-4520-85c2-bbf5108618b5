import * as cdk from 'aws-cdk-lib';
import * as kms from 'aws-cdk-lib/aws-kms';
import * as s3 from 'aws-cdk-lib/aws-s3';
import { Construct } from 'constructs';
import { EnvConfig } from '../../cloud-config/config';
import { S3BucketConfig } from '../../cloud-config/s3-config';

export interface S3StackProps extends cdk.StackProps {
  config: EnvConfig;
  kmsKey: kms.IKey;
}

export class S3Stack extends cdk.Stack {
  public readonly buckets: { [key: string]: s3.Bucket } = {};
  private readonly kmsKey: kms.IKey;

  constructor(scope: Construct, id: string, props: S3StackProps) {
    super(scope, id, props);

    this.kmsKey = props.kmsKey;

    // Get enabled buckets from configuration
    const bucketsConfig = props.config.s3buckets;
    const envName = props.config.environment.envName;

    // Create each configured bucket
    Object.entries(bucketsConfig).forEach(([bucketKey, config]) => {
      this.buckets[bucketKey] = this.createBucket(bucketKey, config, envName);
    });
  }

  private createBucket(bucketKey: string, config: S3BucketConfig, envName: string): s3.Bucket {
    const bucket = new s3.Bucket(this, `${bucketKey}Bucket`, {
      bucketName: `${envName}-${config.bucketName}`,

      // Encryption configuration
      encryption: s3.BucketEncryption.KMS,
      encryptionKey: this.kmsKey,
      bucketKeyEnabled: config.bucketKeyEnabled ?? true,

      // Versioning
      versioned: config.versioned ?? false,

      // Public access block
      blockPublicAccess: config.blockPublicAccess || s3.BlockPublicAccess.BLOCK_ALL,

      // CORS configuration
      cors: config.cors || [],

      // Lifecycle rules
      lifecycleRules: config.lifecycleRules || [],

      // Removal policy
      removalPolicy: config.removalPolicy || cdk.RemovalPolicy.RETAIN,
      autoDeleteObjects: config.autoDeleteObjects ?? false,
    });

    bucket.addToResourcePolicy(
      new cdk.aws_iam.PolicyStatement({
        sid: 'DenyInsecureTransport',
        effect: cdk.aws_iam.Effect.DENY,
        principals: [new cdk.aws_iam.AnyPrincipal()],
        actions: ['s3:*'],
        resources: [bucket.bucketArn, bucket.arnForObjects('*')],
        conditions: {
          Bool: {
            'aws:SecureTransport': 'false',
          },
        },
      })
    );

    // Add tags if specified
    if (config.tags) {
      Object.entries(config.tags).forEach(([key, value]) => {
        cdk.Tags.of(bucket).add(key, value);
      });
    }

    // Create outputs for each bucket
    new cdk.CfnOutput(this, `${bucketKey}BucketName`, {
      value: bucket.bucketName,
      description: `${config.description} - Bucket Name`,
    });

    new cdk.CfnOutput(this, `${bucketKey}BucketArn`, {
      value: bucket.bucketArn,
      description: `${config.description} - Bucket ARN`,
    });

    new cdk.CfnOutput(this, `${bucketKey}BucketDomainName`, {
      value: bucket.bucketDomainName,
      description: `${config.description} - Bucket Domain Name`,
    });

    return bucket;
  }

  // Helper method to get a specific bucket
  public getBucket(bucketKey: string): s3.Bucket | undefined {
    return this.buckets[bucketKey];
  }

  // Helper method to get the main file repository bucket
  public getFileRepositoryBucket(): s3.Bucket | undefined {
    return this.getBucket('fileRepository');
  }

  // Helper method to get the camera stream storage bucket
  public getCameraStreamStorageBucket(): s3.Bucket | undefined {
    return this.getBucket('cameraStreamStorage');
  }
} 