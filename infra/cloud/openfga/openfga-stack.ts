import * as cdk from 'aws-cdk-lib';
import { Peer, Port, SecurityGroup } from 'aws-cdk-lib/aws-ec2';
import * as ecs from 'aws-cdk-lib/aws-ecs';
import * as ecs_patterns from 'aws-cdk-lib/aws-ecs-patterns';
import { ApplicationProtocol, ApplicationProtocolVersion, ApplicationTargetGroup, ListenerCondition, TargetType } from 'aws-cdk-lib/aws-elasticloadbalancingv2';
import { Construct } from 'constructs';
import { EnvConfig } from '../../cloud-config/config';
import { SharedStacks } from '../shared/shared-config';

export interface OpenFgaFargateServiceStackProps extends cdk.StackProps {
  sharedStacks: SharedStacks;
  config: EnvConfig;
  dbUrlSecretName: string;
}

export class OpenFgaFargateServiceStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props: OpenFgaFargateServiceStackProps) {
    super(scope, id, props);

    const { sharedStacks, config, dbUrlSecretName } = props;

    const hostedZone = sharedStacks.certStack.hostedZone;


    // Domain name for the load balancer (accessible from the VPC)
    const lbDomainName = `openfga.lb.api.${config.environment.domain}`;


    const logGroup = new ecs.AwsLogDriver({
      streamPrefix: `${id}-logs`,
      logGroup: new cdk.aws_logs.LogGroup(this, `${id}LogGroup`, {
        logGroupName: `/ecs/${id}Service`,
        retention: cdk.aws_logs.RetentionDays.ONE_WEEK, // Customize the retention period
        removalPolicy: cdk.RemovalPolicy.DESTROY, // Adjust as needed
      }),
    });


    // build the secrets object
    const secretsObj: { [key: string]: ecs.Secret } = {};
    secretsObj['OPENFGA_DATASTORE_URI'] = ecs.Secret.fromSecretsManager(
      cdk.aws_secretsmanager.Secret.fromSecretNameV2(this, `${id}PermsDbUriSecret`, dbUrlSecretName)
    );


    // Create a Security Group for the Load Balancers
    const LBsecurityGroup = new SecurityGroup(this, `${id}NlbSecurityGroup`, {
      vpc: sharedStacks.vpc.vpc,
      description: `Security group for ${id} LBs`,
      allowAllOutbound: true,
    });

    // Allow inbound traffic on ports 80 and 443 only from within the VPC
    LBsecurityGroup.addIngressRule(
      Peer.ipv4(sharedStacks.vpc.vpc.vpcCidrBlock),
      Port.tcp(80),
      'Allow inbound HTTP traffic from within VPC'
    );
    LBsecurityGroup.addIngressRule(
      Peer.ipv4(sharedStacks.vpc.vpc.vpcCidrBlock),
      Port.tcp(443),
      'Allow inbound HTTPS traffic from within VPC'
    );

    // Create a Security Group for the Service
    const serviceSecurityGroup = new SecurityGroup(this, `${id}ServiceSecurityGroup`, {
      vpc: sharedStacks.vpc.vpc,
      description: `Security group for ${id} Service`,
      allowAllOutbound: true,
    });

    // // Allow inbound traffic on port 8080 from the LB security groups
    serviceSecurityGroup.addIngressRule(
      Peer.securityGroupId(LBsecurityGroup.securityGroupId),
      Port.tcp(8080),
      'Allow inbound traffic on port 8080 from the LB security group'
    );
    serviceSecurityGroup.addIngressRule(
      Peer.securityGroupId(LBsecurityGroup.securityGroupId),
      Port.tcp(8081),
      'Allow inbound traffic on port 8081 from the LB security group'
    );


    const envVars = {
      OPENFGA_DATASTORE_ENGINE: 'postgres',
      OPENFGA_LOG_FORMAT: 'json',
      // When enabled, cache controller will verify whether check subproblem cache and check iterator cache needs to be invalidated when there is a check or list objects API request.The invalidation determination is based on whether there are recent write or deletes for the store.This feature allows a larger check- query - cache - ttl and check-iterator - cache - ttl at the expense of additional datastore queries for recent writes and deletes.
      OPENFGA_CACHE_CONTROLLER_ENABLED: 'true',
      // Enables in-memory caching of Check subproblems.For example, if you have a relation define viewer: owner or editor, and the query is Check(user: anne, viewer, doc: 1), we'll evaluate the owner relation and the editor relation and cache both results: (user:anne, viewer, doc:1) -> allowed=true and (user:anne, owner, doc:1) -> allowed=true.
      OPENFGA_CHECK_QUERY_CACHE_ENABLED: 'true',
      OPENFGA_CHECK_QUERY_CACHE_TTL: '300s',
      // Enables in-memory caching of database iterators. Each iterator is the result of a database query, for example, usersets related to a specific object, or objects related to a specific user, up to a certain number of tuples per iterator
      OPENFGA_CHECK_ITERATOR_CACHE_ENABLED: 'true',
      OPENFGA_CHECK_ITERATOR_CACHE_TTL: '300s',
      // Enable caching of datastore iterators for ListObjects. The key is a string representing a database query, and the value is a list of tuples. Each iterator is the result of a database query, for example usersets related to a specific object, or objects related to a specific user, up to a certain number of tuples per iterator. If the request's consistency is HIGHER_CONSISTENCY, this cache is not used.
      OPENFGA_LIST_OBJECTS_ITERATOR_CACHE_ENABLED: 'true',
      OPENFGA_LIST_OBJECTS_ITERATOR_CACHE_TTL: '300s',
      AWS_USE_FIPS_ENDPOINT: 'true',
    };

    // Create a Fargate service with a load balancer
    const fargateService = new ecs_patterns.ApplicationLoadBalancedFargateService(this, `${id}Service`, {
      cluster: sharedStacks.vpc.cluster,
      cpu: config.openfga.cpu,
      memoryLimitMiB: config.openfga.memoryLimitMiB,
      desiredCount: config.openfga.desiredCount,
      maxHealthyPercent: 200,
      minHealthyPercent: 50,
      circuitBreaker: { rollback: true },
      healthCheckGracePeriod: cdk.Duration.seconds(5),
      taskImageOptions: {
        containerName: 'openfga',
        image: ecs.ContainerImage.fromRegistry(config.openfga.dockerImage),
        containerPort: 8080,
        environment: envVars,
        logDriver: logGroup,
        secrets: secretsObj,
        command: ['run'],
      },
      certificate: sharedStacks.localCertStack.wildcardApiCert,
      domainZone: hostedZone,
      domainName: lbDomainName,
      protocol: ApplicationProtocol.HTTPS,
      protocolVersion: ApplicationProtocolVersion.HTTP1,
      publicLoadBalancer: false,
      securityGroups: [serviceSecurityGroup],
      runtimePlatform: {
        operatingSystemFamily: ecs.OperatingSystemFamily.LINUX,
        cpuArchitecture: ecs.CpuArchitecture.X86_64,
      },
    });

    fargateService.taskDefinition.findContainer('openfga')?.addPortMappings({
      containerPort: 8081,
      protocol: ecs.Protocol.TCP,
    });


    fargateService.targetGroup.configureHealthCheck({
      path: '/healthz',
    });

    const grpcTargetGroup = new ApplicationTargetGroup(this, `${id}GrpcTargetGroup`, {
      vpc: sharedStacks.vpc.vpc,
      targetType: TargetType.IP,
      protocol: ApplicationProtocol.HTTP,
      port: 8081,
      targets: [fargateService.service.loadBalancerTarget({
        containerName: 'openfga',
        containerPort: 8081,
      })],
      protocolVersion: ApplicationProtocolVersion.GRPC,
      healthCheck: {
        path: '/grpc.health.v1.Health/Check',
        healthyGrpcCodes: '0',
      },
    });

    fargateService.listener.addTargetGroups(`${id}GrpcRule`, {
      priority: 10,
      conditions: [
        ListenerCondition.httpHeader('content-type', [
          'application/grpc',
          'application/grpc+proto',
          'application/grpc+json',
        ]),
      ],
      targetGroups: [grpcTargetGroup],
    });

    fargateService.listener.addTargetGroups(`${id}GrpcRuleSpillover`, {
      priority: 9,
      conditions: [
        ListenerCondition.httpHeader('content-type', [
          'application/grpc-web',
          'application/grpc-web+proto',
          'application/grpc-web+json',
        ]),
      ],
      targetGroups: [grpcTargetGroup],
    });

    const scalableTarget = fargateService.service.autoScaleTaskCount({
      minCapacity: config.openfga.minCapacity,
      maxCapacity: config.openfga.maxCapacity,
    });

    scalableTarget.scaleOnCpuUtilization('CpuScaling', {
      targetUtilizationPercent: 70,
    });

    scalableTarget.scaleOnMemoryUtilization('MemoryScaling', {
      targetUtilizationPercent: 70,
    });


    new cdk.CfnOutput(this, `${id}LoadBalancerDNS`, {
      value: fargateService.loadBalancer.loadBalancerDnsName,
    });

  }
}
