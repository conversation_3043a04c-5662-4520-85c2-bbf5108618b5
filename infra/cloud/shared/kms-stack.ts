import * as cdk from 'aws-cdk-lib';
import * as kms from 'aws-cdk-lib/aws-kms';
import { Construct } from 'constructs';

export class KmsStack extends cdk.Stack {
    public readonly dbKey: kms.Key;
    public readonly s3Key: kms.Key;

    constructor(scope: Construct, id: string, props?: cdk.StackProps) {
        super(scope, id, props);

        this.dbKey = new kms.Key(this, 'DbEncryptionKey', {
            description: 'KMS key for RDS database encryption',
            enableKeyRotation: true,
            alias: 'alias/db-encryption-key',
        });

        this.s3Key = new kms.Key(this, 'S3EncryptionKey', {
            description: 'KMS key for S3 bucket encryption',
            enableKeyRotation: true,
            alias: 'alias/s3-encryption-key',
            removalPolicy: cdk.RemovalPolicy.RETAIN,
        });
    }
} 