import * as cdk from 'aws-cdk-lib';
import * as acm from 'aws-cdk-lib/aws-certificatemanager';
import { PublicHostedZone } from 'aws-cdk-lib/aws-route53';
import { Construct } from 'constructs';
import { EnvConfig } from '../../cloud-config/config';

export interface LocalCertsStackProps extends cdk.StackProps {
    config: EnvConfig;
    hostedZone: PublicHostedZone;
}

export class LocalCertsStack extends cdk.Stack {
    public readonly wildcardApiCert: acm.Certificate;

    constructor(scope: Construct, id: string, props: LocalCertsStackProps) {
        super(scope, id, props);

        const { config, hostedZone } = props;
        const rootDomain = config.environment.domain;

        this.wildcardApiCert = new acm.Certificate(this, 'WildcardApiCertificate', {
            domainName: `*.api.${rootDomain}`,
            subjectAlternativeNames: [
                `*.iam.api.${rootDomain}`,
                `*.basic.api.${rootDomain}`,
                `*.lb.api.${rootDomain}`,
            ],
            validation: acm.CertificateValidation.fromDns(hostedZone),
        });
    }
} 