import * as cdk from 'aws-cdk-lib';
import { Construct } from "constructs";
import * as kinesis from "aws-cdk-lib/aws-kinesis";
import * as lambda from 'aws-cdk-lib/aws-lambda';
import { VPCStack } from '../vpc-stack';
import { PostgresRdsStack } from '../db-stack';
import * as path from 'path';


export interface AuditLogKinesisStackProps extends cdk.StackProps {

    vpcStack: VPCStack;
    auditLogDbStack: PostgresRdsStack;
}


export class AuditLogKinesisStack extends cdk.Stack {
    public readonly stream: kinesis.Stream;

    constructor(scope: Construct, id: string, props: AuditLogKinesisStackProps) {
        super(scope, id, props);

        this.stream = new kinesis.Stream(this, "MyStream", {
            streamName: "kinesis-stream",
            shardCount: 1,
            removalPolicy: cdk.RemovalPolicy.RETAIN, // for dev/test, can use DESTROY, need to change to RETAIN for prod 
        });

        const auditlogLambda = new lambda.Function(this, 'auditlogLambda', {
            functionName: 'auditlogLambda',
            runtime: lambda.Runtime.PROVIDED_AL2023,
            handler: 'main',
            timeout: cdk.Duration.seconds(10),
            vpc: props.vpcStack.vpc,
            code: lambda.Code.fromAsset(path.join(__dirname, './lambdas/bootstrap.zip')), // Path to Go Lambda zip 
            environment: {
                PARAMETERS_SECRETS_EXTENSION_LOG_LEVEL: 'debug',
                PARAMETERS_SECRETS_EXTENSION_CACHE_SIZE: '10',
                SECRETS_MANAGER_TTL: '300',
                DB_SECRET_ARN: props.auditLogDbStack.userSecret.secretArn

            },
            paramsAndSecrets: lambda.ParamsAndSecretsLayerVersion.fromVersion(
                lambda.ParamsAndSecretsVersions.V1_0_103,
                {
                    cacheSize: 10,
                    logLevel: lambda.ParamsAndSecretsLogLevel.DEBUG,
                }
            ),
        });
    }
}



