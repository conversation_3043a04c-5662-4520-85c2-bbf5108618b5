import * as cdk from 'aws-cdk-lib';
import * as guardduty from 'aws-cdk-lib/aws-guardduty';
import * as securityhub from 'aws-cdk-lib/aws-securityhub';
import { Construct } from 'constructs';
import { EnvConfig } from '../../cloud-config/config';

export interface SecurityStackProps extends cdk.StackProps {
    config: EnvConfig;
}

export class SecurityStack extends cdk.Stack {
    public readonly securityHub: securityhub.CfnHub;
    public readonly guardDutyDetector: guardduty.CfnDetector;

    constructor(scope: Construct, id: string, props: SecurityStackProps) {
        super(scope, id, props);

        // Security Hub Configuration
        this.securityHub = new securityhub.CfnHub(this, 'SecurityHub', {
            enableDefaultStandards: true
        });

        // GuardDuty Detector - Enhanced for Hero infrastructure
        this.guardDutyDetector = new guardduty.CfnDetector(this, 'GuardDutyDetector', {
            enable: true,
            findingPublishingFrequency: 'FIFTEEN_MINUTES', // Real-time threat detection
        });

    }
} 