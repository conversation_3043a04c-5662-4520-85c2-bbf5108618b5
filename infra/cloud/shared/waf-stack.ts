
import * as cdk from 'aws-cdk-lib';
import * as wafv2 from 'aws-cdk-lib/aws-wafv2';
import { Construct } from 'constructs';

export interface WafStackProps extends cdk.StackProps {
    scope: 'REGIONAL' | 'CLOUDFRONT';
}

export class WafStack extends cdk.Stack {
    public readonly webAcl: wafv2.CfnWebACL;

    constructor(scope: Construct, id: string, props: WafStackProps) {
        super(scope, id, props);

        const rules: wafv2.CfnWebACL.RuleProperty[] = [
            {
                name: 'AWS-AWSManagedRulesCommonRuleSet',
                priority: 1,
                statement: {
                    managedRuleGroupStatement: {
                        vendorName: 'AWS',
                        name: 'AWSManagedRulesCommonRuleSet',
                    },
                },
                overrideAction: {
                    none: {},
                },
                visibilityConfig: {
                    sampledRequestsEnabled: true,
                    cloudWatchMetricsEnabled: true,
                    metricName: 'AWS-AWSManagedRulesCommonRuleSet',
                },
            },
            {
                name: 'AWS-AWSManagedRulesAmazonIpReputationList',
                priority: 2,
                statement: {
                    managedRuleGroupStatement: {
                        vendorName: 'AWS',
                        name: 'AWSManagedRulesAmazonIpReputationList',
                    },
                },
                overrideAction: {
                    none: {},
                },
                visibilityConfig: {
                    sampledRequestsEnabled: true,
                    cloudWatchMetricsEnabled: true,
                    metricName: 'AWS-AWSManagedRulesAmazonIpReputationList',
                },
            },
            {
                name: 'AWS-AWSManagedRulesKnownBadInputsRuleSet',
                priority: 3,
                statement: {
                    managedRuleGroupStatement: {
                        vendorName: 'AWS',
                        name: 'AWSManagedRulesKnownBadInputsRuleSet',
                    },
                },
                overrideAction: {
                    none: {},
                },
                visibilityConfig: {
                    sampledRequestsEnabled: true,
                    cloudWatchMetricsEnabled: true,
                    metricName: 'AWS-AWSManagedRulesKnownBadInputsRuleSet',
                },
            },
        ];

        this.webAcl = new wafv2.CfnWebACL(this, 'WebAcl', {
            defaultAction: { allow: {} },
            scope: props.scope,
            visibilityConfig: {
                sampledRequestsEnabled: true,
                cloudWatchMetricsEnabled: true,
                metricName: 'web-acl',
            },
            name: `hero-${props.scope.toLowerCase()}-web-acl`,
            rules: rules,
        });
    }
} 