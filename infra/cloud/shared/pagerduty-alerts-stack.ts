import * as cdk from 'aws-cdk-lib';
import * as secretsmanager from 'aws-cdk-lib/aws-secretsmanager';
import * as sns from 'aws-cdk-lib/aws-sns';
import * as snsSubscriptions from 'aws-cdk-lib/aws-sns-subscriptions';
import { Construct } from 'constructs';
import { EnvConfig } from '../../cloud-config/config';

export interface PagerDutyAlertsStackProps extends cdk.StackProps {
    config?: EnvConfig;
    domainTag?: string; // Optional explicit topic suffix when config is not available
}

export class PagerDutyAlertsStack extends cdk.Stack {
    public readonly criticalTopic: sns.ITopic;
    public readonly highTopic: sns.ITopic;
    public readonly infoTopic: sns.ITopic;

    constructor(scope: Construct, id: string, props: PagerDutyAlertsStackProps) {
        super(scope, id, props);

        const { config } = props;
        const computedDomain = config?.environment?.domain || config?.environment?.envName || props.domainTag || 'shared';
        const domainTag = computedDomain.replace(/\./g, '-');

        // SNS Topics by severity
        const criticalTopic = new sns.Topic(this, 'PagerDutyCriticalTopic', {
            topicName: `pagerduty-critical-alerts-${domainTag}`,
            displayName: 'PagerDuty Critical Alerts',
        });
        const highTopic = new sns.Topic(this, 'PagerDutyHighTopic', {
            topicName: `pagerduty-high-alerts-${domainTag}`,
            displayName: 'PagerDuty High Alerts',
        });
        const infoTopic = new sns.Topic(this, 'PagerDutyInfoTopic', {
            topicName: `pagerduty-info-alerts-${domainTag}`,
            displayName: 'PagerDuty Informational Alerts',
        });

        // PagerDuty Integration Key sourced from Secrets Manager
        const pagerDutyIntegrationKeySecret = secretsmanager.Secret.fromSecretNameV2(
            this,
            'PagerDutyIntegrationKeySecret',
            'shared/team-eng-alarms/pagerduty-integration-key'
        );

        // Build PagerDuty URL using dynamic reference to avoid exposing key
        const integrationKeyRef = new cdk.CfnDynamicReference(
            cdk.CfnDynamicReferenceService.SECRETS_MANAGER,
            `${pagerDutyIntegrationKeySecret.secretArn}:SecretString`
        );

        const pagerDutyUrl = cdk.Fn.sub(
            'https://events.pagerduty.com/integration/${IntegrationKey}/enqueue',
            {
                IntegrationKey: integrationKeyRef.toString(),
            }
        );

        // Subscribe all topics to PagerDuty HTTPS endpoint
        [criticalTopic, highTopic, infoTopic].forEach((topic) => {
            topic.addSubscription(
                new snsSubscriptions.UrlSubscription(pagerDutyUrl, {
                    protocol: sns.SubscriptionProtocol.HTTPS,
                    rawMessageDelivery: false,
                })
            );
        });

        // Exports for other stacks to import
        new cdk.CfnOutput(this, 'PagerDutyAlertsTopicCriticalArn', {
            value: criticalTopic.topicArn,
            description: 'SNS Topic ARN for critical severity alerts integrated with PagerDuty',
            exportName: 'PagerDutyAlertsTopicCriticalArn',
        });
        new cdk.CfnOutput(this, 'PagerDutyAlertsTopicHighArn', {
            value: highTopic.topicArn,
            description: 'SNS Topic ARN for high severity alerts integrated with PagerDuty',
            exportName: 'PagerDutyAlertsTopicHighArn',
        });
        new cdk.CfnOutput(this, 'PagerDutyAlertsTopicInfoArn', {
            value: infoTopic.topicArn,
            description: 'SNS Topic ARN for informational alerts integrated with PagerDuty',
            exportName: 'PagerDutyAlertsTopicInfoArn',
        });

        this.criticalTopic = criticalTopic;
        this.highTopic = highTopic;
        this.infoTopic = infoTopic;
    }
}


