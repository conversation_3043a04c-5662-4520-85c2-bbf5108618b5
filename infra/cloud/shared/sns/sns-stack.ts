import * as cdk from 'aws-cdk-lib';
import * as sns from 'aws-cdk-lib/aws-sns';
import { Construct } from 'constructs';
import { EnvConfig } from '../../../cloud-config/config';

export class SnsStack extends cdk.Stack {
    public readonly topics: { [key: string]: sns.ITopic } = {};

    constructor(scope: Construct, id: string, config: EnvConfig, props?: cdk.StackProps) {
        super(scope, id, props);

        for (const [topicKey, topicConfig] of Object.entries(config.snsTopics)) {
            const topic = new sns.Topic(this, topicConfig.topicName, {
                topicName: topicConfig.topicName,
                displayName: topicConfig.displayName
            });
            this.topics[topicKey] = topic;
        }
    }
} 