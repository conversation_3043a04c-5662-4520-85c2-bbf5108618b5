import * as cdk from 'aws-cdk-lib';
import * as acm from 'aws-cdk-lib/aws-certificatemanager';
import { Role } from 'aws-cdk-lib/aws-iam';
import { CrossAccountZoneDelegationRecord, PublicHostedZone } from 'aws-cdk-lib/aws-route53';
import { Construct } from 'constructs';
import { EnvConfig } from '../../cloud-config/config';

export interface GlobalCertsStackProps extends cdk.StackProps {
    config: EnvConfig;
}

export class GlobalCertsStack extends cdk.Stack {
    public readonly authCert: acm.Certificate;
    public readonly commandCert: acm.Certificate;
    public readonly hostedZone: PublicHostedZone;

    constructor(scope: Construct, id: string, props: GlobalCertsStackProps) {
        super(scope, id, props);

        const { config } = props;
        const rootDomain = config.environment.domain;
        const hostedZone = new PublicHostedZone(this, 'HostedZone', {
            zoneName: rootDomain,
        });
        this.hostedZone = hostedZone;

        this.authCert = new acm.Certificate(this, 'AuthCertificate', {
            domainName: `auth.${rootDomain}`,
            validation: acm.CertificateValidation.fromDns(hostedZone),
        });

        this.commandCert = new acm.Certificate(this, 'CommandCertificate', {
            domainName: `command.${rootDomain}`,
            subjectAlternativeNames: [rootDomain, `www.${rootDomain}`],
            validation: acm.CertificateValidation.fromDns(hostedZone),
        });

        // this must match the role name in the shared account, defined in the root hosted zone stack
        const hostedZoneDelegationRoleName = config.environment.envName + '-HostedZoneDelegationRole';

        const delegationRoleArn = cdk.Stack.of(this).formatArn({
            account: config.sharedEnvironment.accountId,
            region: '',
            resource: 'role',
            resourceName: hostedZoneDelegationRoleName,
            service: 'iam',
        });

        // Get the role by ARN
        const delegationRole = Role.fromRoleArn(this, 'DelegationRole', delegationRoleArn);

        // create a cross account hosted zone delegation record (NS)
        new CrossAccountZoneDelegationRecord(this, 'DelegationRecord', {
            delegationRole,
            delegatedZone: hostedZone,
            parentHostedZoneName: 'gethero.com',
        });
    }
}