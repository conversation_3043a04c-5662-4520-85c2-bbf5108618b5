// lib/react-frontend-cloudfront-stack.ts
import * as cdk from 'aws-cdk-lib';
import { DockerImage } from 'aws-cdk-lib';
import * as cloudfront from 'aws-cdk-lib/aws-cloudfront';
import * as origins from 'aws-cdk-lib/aws-cloudfront-origins';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as logs from 'aws-cdk-lib/aws-logs';
import * as route53 from 'aws-cdk-lib/aws-route53';
import * as route53Targets from 'aws-cdk-lib/aws-route53-targets';
import * as s3 from 'aws-cdk-lib/aws-s3';
import * as s3deploy from 'aws-cdk-lib/aws-s3-deployment';
import * as secretsmanager from 'aws-cdk-lib/aws-secretsmanager';
import * as ssm from 'aws-cdk-lib/aws-ssm';
import * as wafv2 from 'aws-cdk-lib/aws-wafv2';
import { Construct } from 'constructs';
import * as fs from 'fs';
import * as path from 'path';
import { EnvConfig } from '../../cloud-config/config';
import { SharedStacks } from '../shared/shared-config';

export interface ReactFrontendCloudFrontStackProps extends cdk.StackProps {
  sharedStacks: SharedStacks;
  config: EnvConfig;
  webAcl: wafv2.CfnWebACL;
}

export class ReactFrontendCloudFrontStack extends cdk.Stack {
  public readonly distribution: cloudfront.Distribution;

  constructor(scope: Construct, id: string, props: ReactFrontendCloudFrontStackProps) {
    super(scope, id, props);

    const { sharedStacks, config, webAcl } = props;

    const websiteBucket = new s3.Bucket(this, 'ReactWebsiteBucket', {
      removalPolicy: cdk.RemovalPolicy.DESTROY,
      autoDeleteObjects: true,
    });

    const sentryAuthToken = secretsmanager.Secret.fromSecretNameV2(
      this,
      'SentryAuthToken',
      `cad/sentry_auth_token`
    );

    const buildEnvironment: Record<string, string> = {
      SENTRY_AUTH_TOKEN: sentryAuthToken.secretValue.toString(),
      // these MUST match the names created in the SSMParametersStack
      // these MUST already be deployed
      NEXT_PUBLIC_COGNITO_USER_POOL_ID: ssm.StringParameter.valueFromLookup(this, `/hero/${config.environment.envName}/cognito/user-pool-id`),
      NEXT_PUBLIC_COGNITO_CLIENT_ID: ssm.StringParameter.valueFromLookup(this, `/hero/${config.environment.envName}/cognito/internal-user-pool-client-id`),
      NEXT_PUBLIC_COGNITO_IDENTITY_POOL_ID: ssm.StringParameter.valueFromLookup(this, `/hero/${config.environment.envName}/cognito/internal-identity-pool-id`),
    };

    // This is where it dynamically creates and injects the environment variables for the build in Production
    // For dev it reads from .env.development file in the root of the hero-command-and-control-web folder
    for (const serverName in config.servers) {
      if (Object.prototype.hasOwnProperty.call(config.servers, serverName)) {
        const varName = `NEXT_PUBLIC_${serverName.toUpperCase()}_SERVICE_URL`;
        const url = `https://${serverName}.api.${config.environment.domain}`;
        buildEnvironment[varName] = url;
      }
    }

    // run `npm run build` before this
    new s3deploy.BucketDeployment(this, 'DeployNextJsApp', {
      sources: [s3deploy.Source.asset(path.join(__dirname, '../../../'), {
        bundling: {
          image: DockerImage.fromRegistry('node:22'),
          user: 'root',
          command: [
            'bash', '-c', [
              'npm install',
              'npm run build --workspace=hero-command-and-control-web',
              'cp -r apps/hero-command-and-control-web/out/. /asset-output'
            ].join(' && ')
          ],
          environment: buildEnvironment,
        }
      })],
      destinationBucket: websiteBucket,
    });

    const logGroup = new logs.LogGroup(this, 'CloudfrontAuthorizerLogGroup', {
      logGroupName: '/aws/lambda/CommandUI-CloudfrontAuthorizer',
      removalPolicy: cdk.RemovalPolicy.DESTROY,
    });

    const lambdaTemplate = fs.readFileSync(path.join(__dirname, 'authorizationLambda/dist/index.js'), 'utf-8');

    // This is a hack to get the cognito details into the lambda code, 
    // required since lambda@edge does not support environment variables
    // This hack works since the lambda is simple enough to be inlined
    const lambdaCode = lambdaTemplate
      .replace('%%REGION%%', cdk.Stack.of(sharedStacks.cognitoStack).region)
      .replace('%%USER_POOL_ID%%', sharedStacks.cognitoStack.userPool.userPoolId)
      .replace('%%USER_POOL_APP_ID%%', sharedStacks.cognitoStack.internalClient.userPoolClientId)
      .replace('%%USER_POOL_DOMAIN%%', `auth.${config.environment.domain}`)
      .replace('%%COMMAND_DOMAIN%%', `${config.environment.domain}`);

    // build the lambda before running this
    // WARNING: last time we tried to update this we were unable to get CDK
    // to detect and deploy the changes and had to do it manually by
    // 1. manually updating the lambda code
    // 2. deploying a new lambda version
    // 3. pointing the cloudfront distribution to the new lambda version
    const authFunction = new cloudfront.experimental.EdgeFunction(this, 'CloudfrontAuthorizer', {
      runtime: lambda.Runtime.NODEJS_LATEST,
      handler: 'index.handler',
      code: lambda.Code.fromInline(lambdaCode),
      logGroup: logGroup,
    });

    this.distribution = new cloudfront.Distribution(this, 'ReactDistribution', {
      defaultBehavior: {
        origin: origins.S3BucketOrigin.withOriginAccessControl(websiteBucket),
        // Attach the auth function to run on every viewer request.
        edgeLambdas: [{
          functionVersion: authFunction.currentVersion,
          eventType: cloudfront.LambdaEdgeEventType.VIEWER_REQUEST,
        }],
        viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
      },
      webAclId: webAcl.attrArn,
      enableLogging: true,
      logBucket: new s3.Bucket(this, 'CloudfrontLogsBucket', {
        removalPolicy: cdk.RemovalPolicy.DESTROY,
        autoDeleteObjects: true,
        accessControl: s3.BucketAccessControl.LOG_DELIVERY_WRITE,
      }),
      domainNames: [`command.${config.environment.domain}`, config.environment.domain],
      certificate: sharedStacks.certStack.commandCert,
      // Optional: handle 403 errors (e.g. for SPA routing)
      errorResponses: [{
        httpStatus: 403,
        responseHttpStatus: 200,
        responsePagePath: '/index.html',
        ttl: cdk.Duration.minutes(30),
      }],
      minimumProtocolVersion: cloudfront.SecurityPolicyProtocol.TLS_V1_2_2021,
    });

    const hostedZone = sharedStacks.certStack.hostedZone;
    new route53.ARecord(this, 'CommandARecord', {
      zone: hostedZone,
      target: route53.RecordTarget.fromAlias(new route53Targets.CloudFrontTarget(this.distribution)),
      recordName: 'command',
    });

    new route53.ARecord(this, 'ApexARecord', {
      zone: hostedZone,
      target: route53.RecordTarget.fromAlias(new route53Targets.CloudFrontTarget(this.distribution)),
    });

    new cdk.CfnOutput(this, 'CloudFrontDomain', {
      value: this.distribution.domainName,
    });
  }
}
