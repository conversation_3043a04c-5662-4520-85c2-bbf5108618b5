import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { EnvConfig } from '../cloud-config/config';
import { ReactFrontendCloudFrontStack } from './apps/react-frontend-stack';
import { LambdasStack } from './lambdas/lambdas-stack';
import { MigrationRunnerStack } from './lambdas/migration-runner-stack';
import { OpenFgaFargateServiceStack } from './openfga/openfga-stack';
import { S3Stack } from './s3bucket/s3-stack';
import { defineServers } from './servers/servers';
import { AuditLogKinesisStack } from './shared/auditLog/auditlog-stack';
import { BastionStack } from './shared/bastion-stack';
import { CognitoStack } from './shared/cognito-stack/cognito-stack';
import { PostgresRdsStack } from './shared/db-stack';
import { GithubActionsRoleStack } from './shared/github-actions-role-stack';
import { GlobalCertsStack } from './shared/global-certs-stack';
import { GreengrassSetupStack } from './shared/greengrass-stack';
import { KmsStack } from './shared/kms-stack';
import { LocalCertsStack } from './shared/local-certs-stack';
import { PagerDutyAlertsStack } from './shared/pagerduty-alerts-stack';
import { PasswordStack } from './shared/password-stack';
import { SecurityStack } from './shared/security-stack';
import { SharedStacks } from './shared/shared-config';
import { SnsStack } from './shared/sns/sns-stack';
import { SSMParametersStack } from './shared/ssm-parameters-stack';
import { VPCStack } from './shared/vpc-stack';
import { WafStack } from './shared/waf-stack';

export interface EntryStackProps extends cdk.StackProps {
  config: EnvConfig;
}

export class EntryStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props: EntryStackProps) {
    super(scope, id, props);

    const { config } = props;

    const wafCloudFrontStack = new WafStack(scope, 'WafCloudFrontStack', {
      scope: 'CLOUDFRONT',
      env: { account: props.env?.account, region: 'us-east-1' }, // WAF for CloudFront must be in us-east-1
      crossRegionReferences: true,
    });

    const wafRegionalStack = new WafStack(scope, 'WafRegionalStack', {
      scope: 'REGIONAL',
      env: props.env,
      crossRegionReferences: true,
    });

    const vpcStack = new VPCStack(scope, 'VPCStack', {
      cidr: config.vpc?.cidr,
      env: props.env,
      crossRegionReferences: true
    });

    const certStack = new GlobalCertsStack(scope, 'GlobalCertsStack', {
      config,
      env: { account: props.env?.account, region: 'us-east-1' }, // Certificate must be in us-east-1 for CloudFront
      crossRegionReferences: true
    });

    const kmsStack = new KmsStack(scope, 'KmsStack', {
      env: props.env,
      crossRegionReferences: true
    });

    const localCertStack = new LocalCertsStack(scope, 'LocalCertsStack', {
      config,
      hostedZone: certStack.hostedZone,
      env: props.env,
      crossRegionReferences: true,
    });

    // Centralized PagerDuty + SNS Alerts Stack
    const pagerDutyAlertsStack = new PagerDutyAlertsStack(scope, 'PagerDutyAlertsStack', {
      config,
      env: props.env,
      crossRegionReferences: true,
    });

    const pagerDutyTopics = {
      critical: pagerDutyAlertsStack.criticalTopic,
      high: pagerDutyAlertsStack.highTopic,
      info: pagerDutyAlertsStack.infoTopic,
    }

    const cognitoStack = new CognitoStack(scope, 'CognitoStack', {
      cert: certStack.authCert,
      vpcStack: vpcStack,
      config: config,
      hostedZone: certStack.hostedZone,
      pagerDutyTopicHigh: pagerDutyAlertsStack.highTopic,
      crossRegionReferences: true,
      env: props.env,
    });

    // Create SSM Parameters stack in the global region
    // Used to pass the cognito information into the Command UI stack build
    const ssmParametersStack = new SSMParametersStack(scope, 'SSMParametersStack', {
      config,
      userPoolId: cognitoStack.userPool.userPoolId,
      internalUserPoolClientId: cognitoStack.internalClient.userPoolClientId,
      internalIdentityPoolId: cognitoStack.internalIdentityPoolId,
      env: { account: props.env?.account, region: 'us-east-1' },
      crossRegionReferences: true
    });

    const s3Stack = new S3Stack(scope, 'S3Stack', { config, env: props.env, crossRegionReferences: true, kmsKey: kmsStack.s3Key });



    const dbStack = new PostgresRdsStack(scope, 'PostgresRdsStack', {
      dbName: 'hero',
      username: 'server',
      vpc: vpcStack.vpc,
      config: config,
      dbKey: kmsStack.dbKey,
      env: props.env,
      crossRegionReferences: true,
      pagerDutyTopics
    });
    const permsDbStack = new PostgresRdsStack(scope, 'PermsPostgresRdsStack', {
      dbName: 'perms',
      username: 'perms',
      vpc: vpcStack.vpc,
      config: config,
      dbKey: kmsStack.dbKey,
      env: props.env,
      crossRegionReferences: true,
      pagerDutyTopics
    });
    const auditLogDbStack = new PostgresRdsStack(scope, 'AuditLogPostgresRdsStack', {
      dbName: 'auditlog',
      username: 'audit',
      vpc: vpcStack.vpc,
      config: config,
      dbKey: kmsStack.dbKey,
      env: props.env,
      crossRegionReferences: true,
      pagerDutyTopics
    });

    const snsStack = new SnsStack(scope, 'SnsStack', config, { env: props.env, crossRegionReferences: true });

    const auditLogKinesisStack = new AuditLogKinesisStack(scope, 'AuditLogKinesisStack', {
      auditLogDbStack: auditLogDbStack,
      vpcStack: vpcStack,
      env: props.env,
      crossRegionReferences: true
    });

    auditLogKinesisStack.addDependency(auditLogDbStack);

    const passwordStack = new PasswordStack(scope, 'PasswordStack', {
      config,
      env: props.env,
      crossRegionReferences: true
    });

    const securityStack = new SecurityStack(scope, 'SecurityStack', {
      config,
      env: props.env,
      crossRegionReferences: true
    });

    const sharedStacks: SharedStacks = {
      vpc: vpcStack,
      s3Stack: s3Stack,
      cognitoStack: cognitoStack,
      certStack: certStack,
      localCertStack: localCertStack,
      dbStack: dbStack,
      permsDbStack: permsDbStack,
      snsStack: snsStack,
      passwordStack: passwordStack,
      kmsStack: kmsStack,
      securityStack: securityStack,
      auditLogKinesisStack: auditLogKinesisStack,
    };

    new BastionStack(scope, 'BastionStack', {
      sharedStacks: sharedStacks,
      config: config,
      env: props.env,
      crossRegionReferences: true
    });

    new GreengrassSetupStack(scope, 'GreengrassStack', { env: props.env, crossRegionReferences: true });

    // NOTE - we deploy this stack to the global region (us-east-1)
    // to ensure that the edge function it contains is deployed as part of the same stack,
    // to avoid cross-region references.
    // This is deemed okay since the primary resources in this stack (cloudfront and lambda@edge) are already global 
    const commandUIStack = new ReactFrontendCloudFrontStack(scope, 'CommandUIStack', {
      sharedStacks: sharedStacks,
      config: config,
      webAcl: wafCloudFrontStack.webAcl,
      env: { account: props.env?.account, region: 'us-east-1' },
      crossRegionReferences: true
    });
    commandUIStack.addDependency(ssmParametersStack);

    new GithubActionsRoleStack(scope, 'GitHubActionsRoleStack', {
      config,
      distributionId: commandUIStack.distribution.distributionId,
      githubOrg: 'herosafety',
      githubRepo: 'hero-core',
      githubBranch: config.deploys.githubBranch,
      tagPrefix: config.deploys.tagPrefix,
      env: { account: props.env?.account, region: 'us-east-1' },
      crossRegionReferences: true,
    });

    const lambdasStack = new LambdasStack(scope, 'LambdasStack', {
      sharedStacks,
      config,
      env: props.env,
      crossRegionReferences: true
    });

    const migrationRunnerStack = new MigrationRunnerStack(scope, 'MigrationRunnerStack', {
      sharedStacks,
      config,
      env: props.env,
      crossRegionReferences: true
    });
    lambdasStack.addDependency(migrationRunnerStack);

    new OpenFgaFargateServiceStack(scope, 'OpenFgaStack', {
      sharedStacks: sharedStacks,
      config: config,
      dbUrlSecretName: 'PostgrespermsDBUrl',
      env: props.env,
      crossRegionReferences: true
    });

    defineServers(scope, sharedStacks, config, props.env, wafRegionalStack.webAcl, {
      critical: pagerDutyAlertsStack.criticalTopic,
      high: pagerDutyAlertsStack.highTopic,
      info: pagerDutyAlertsStack.infoTopic,
    });
  }
}
