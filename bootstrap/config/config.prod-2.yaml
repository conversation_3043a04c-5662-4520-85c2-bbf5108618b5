# Production endpoints configuration
region: us-east-2
auth:
  client_id: "6ek6qg2dgmb2gis17lgl1jk93j"
  admin_secret_arn: "arn:aws:secretsmanager:us-east-2:234824776122:secret:root-admin-password-wFGaSA"
services:
  orgs:
    host: orgs.api.cisco.gethero.com
    port: 443
  assets:
    host: workflow.api.cisco.gethero.com
    port: 443
  situations:
    host: workflow.api.cisco.gethero.com
    port: 443
  communications:
    host: communications.api.cisco.gethero.com
    port: 443
  sensors:
    host: sensors.api.cisco.gethero.com
    port: 443
  command:
    host: command.api.cisco.gethero.com
    port: 443
  permissions:
    host: perms.api.cisco.gethero.com
    port: 443 
  filerepository:
    host: filerepository.api.cisco.gethero.com
    port: 443