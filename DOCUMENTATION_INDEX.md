# HERO Core Documentation Index

## 📚 Overview
This index provides a comprehensive guide to all documentation files in the HERO monorepo. The documentation is organized by component type and functionality.

---

## 🏠 Root Documentation

- [**README.md**](./README.md) - **Main project setup guide** covering monorepo structure, Docker/Go/Node prerequisites, AWS SSO authentication setup, and quick start instructions for both backend services and frontend apps
- [**DEPLOYMENT.md**](./.github/DEPLOYMENT.md) - **Automated deployment pipeline** explaining GitHub Actions workflows, deployment queuing with concurrency control, 60-minute timeout settings, and Slack notifications

---

## 🎯 Applications Documentation

### Frontend Web Applications

#### Hero Command and Control Web
- [**README.md**](./apps/hero-command-and-control-web/README.md) - **Basic Next.js setup** with `npm run dev` for development server, Storybook on port 6006, and standard Next.js deployment instructions
- **Context Documentation:**
  - [Auth Context](./apps/hero-command-and-control-web/src/app/contexts/Auth/README.md) - **Web app authentication architecture** - CloudFront Edge Lambda integration, token refresh without page reloads, cross-user data protection via React Query cache management, and production vs development auth flows
  - [Call Context](./apps/hero-command-and-control-web/src/app/contexts/Call/README.md) - **Twilio Voice SDK integration** for telephony operations - token management, device handling, call queue operations (accept/hold/resume/end/mute), and local development setup with ngrok
  - [Call Observability](./apps/hero-command-and-control-web/src/app/contexts/Call/OBSERVABILITY.md) - **Sentry monitoring setup for calls** - metrics for call operations, device health, state consistency, performance thresholds, alert configurations, and dashboard queries
- **Component Documentation:**
  - [Entity Cards](./apps/hero-command-and-control-web/src/app/entity/entityComponents/cards/README.md) - **Property entity display components** - chain of custody tracking card and media file management table with view/download capabilities
  - [Property Components](./apps/hero-command-and-control-web/src/app/property/components/README.md) - **Chain of custody update UI** - button dropdown and modal form for recording property custody events (collected/transfer/release/dispose)

#### Hero Admin Web
- [**README.md**](./apps/hero-admin-web/README.md) - **Basic Next.js setup** with standard create-next-app template, development server instructions, and Vercel deployment guidelines

### Mobile Applications

#### Hero Responder App
- [**README.md**](./apps/hero-responder-app/README.md) - **Basic React Native setup** with standard React Native CLI instructions for iOS/Android development
- [**AUTHENTICATION.md**](./apps/hero-responder-app/AUTHENTICATION.md) - **Multi-tenant OAuth 2.0 authentication** with dynamic environment discovery, PKCE implementation, ephemeral token management, provider rekeying, and Axios authentication integration
- [Expo Configuration](./apps/hero-responder-app/.expo/README.md) - **Expo folder explanation** - why .expo folder exists, what files it contains (devices.json, settings.json), and why not to commit it

#### Hero Member App
- [**README.md**](./apps/hero-member-app/README.md) - **Basic Expo app setup** with `npx expo start`, file-based routing, and standard Expo development instructions

### General Application Documentation
- [**FRONTEND_OBSERVABILITY_GUIDE.md**](./apps/FRONTEND_OBSERVABILITY_GUIDE.md) - **Comprehensive Sentry integration guide** with 5-minute setup, business metrics hooks, structured error handling, environment-aware sampling, and common query patterns

---

## ⚙️ Services Documentation

### Core Services
- [**Services Overview**](./services/README.md) - **Complete Go microservices guide** including GVM installation, step-by-step service creation, Docker configuration, deployment scripts, and common ConnectRPC patterns
- [**Command Service**](./services/command/README.md) - **Gateway to physical assets** - tracks status and desired state, communicates with field assets (very brief 3-line description)
- [**Communications Service**](./services/communications/README.md) - **Real-time communication platform** with Twilio voice calls/queuing, Push-to-Talk via Zello, legacy Agora support, and local webhook development with ngrok
  - [Communications Observability](./services/communications/OBSERVABILITY.md) - **Backend Sentry instrumentation guide** - 5 HTTP webhooks, 7 RPC operations, 4 database operations, distributed tracing, error tracking, and useful Sentry queries
- [**Feature Flags Service**](./services/featureflags/README.md) - **Comprehensive feature flag system** with organization/asset-level targeting, bulk operations, evaluation logic, complete API documentation with examples, and E2E testing setup
- [**File Repository Service**](./services/filerepository/README.md) - **2300+ line exhaustive documentation** covering presigned S3 URLs, circuit breaker patterns, metadata management, search capabilities, lifecycle management, complete troubleshooting guide, and emergency procedures
- [**Organizations Service**](./services/orgs/README.md) - **Multi-tenant organization management** with Twilio integration, call forwarding configuration, API user management, Zello channels, and pre-registration user mapping system
- [**Permissions Service**](./services/perms/README.md) - **OpenFGA-based RBAC system** with middleware permission checks, post-hoc filtering for lists, object-level overrides, contextual tuples, and detailed architecture for extending to new object types
- [**Sensors Service**](./services/sensors/README.md) - **Camera management service** with RTSP/WebRTC streaming via AWS KVS, GStreamer configuration, stream orchestration, and database schema for camera configurations

### Workflow Service
- [**Workflow Overview**](./services/workflow/README.md) - **Core operational orchestration** - integrates Assets, Situations, Orders, Reports, Entity, and Common modules with automated side effects, state machines, and transactional integrity
- **Internal Modules:**
  - [Assets](./services/workflow/internal/assets/README.md) - **Operational entity management** - tracks team members, dispatchers, responders, cameras, and bots with status, location, and contact info for real-time resource coordination
  - [Cases](./services/workflow/internal/cases/README.md) - **Investigation and incident tracking** - structured case management with asset coordination, audit trails, cross-entity relationships, and comprehensive test suite
  - [Entity](./services/workflow/internal/entity/README.md) - **Dynamic object management with JSON schemas** - flexible data structures for people/vehicles/properties with versioning, auditing, and advanced search
  - [Orders](./services/workflow/internal/orders/README.md) - **Task and instruction management** - coordinates operational responses with status tracking, asset assignments, permission controls, and automated situation updates
  - [Property](./services/workflow/internal/property/README.md) - **Evidence and property management** - chain of custody tracking, evidence lifecycle, case associations, authentication/rate limiting, and legal compliance
  - [Reports](./services/workflow/internal/reports/README.md) - **Incident documentation system** - multi-section reports with narrative/entities/media, review workflow with preferred reviewers, versioning, comments, and object relations
  - [Situations](./services/workflow/internal/situations/README.md) - **Incident and event management** - comprehensive incident logging with 22+ situation types, status lifecycle, trigger sources, media attachments, and coordinated response tracking

### ETL (Extract, Transform, Load)
- [**ETL Overview**](./services/workflow/internal/etl/README.md) - **Law enforcement compliance ETL** transforming incident reports to NIBRS XML format for FBI/DOJ/State Police submissions, with template system and security features
- [**Extraction**](./services/workflow/internal/etl/extraction/README.md) - **Unified data extraction system** - high-performance parallel extraction of reports/assets/entities with hydration, type safety, and extensible architecture
- [**Transformation Overview**](./services/workflow/internal/etl/transformation/README.md) - **Configuration-driven transformation engine** - zero-code business logic philosophy, immutable data pipeline, mapping configurations, and law enforcement format conversion
  - [Core Transformation](./services/workflow/internal/etl/transformation/core/README.md) - **Mapping configuration guide** - transformation types (direct/filter/lookup), path syntax, filter operations, and no-code configuration recipes
  - [Test Documentation](./services/workflow/internal/etl/transformation/core/tests/README.md) - **Transformation test suite** - path resolver tests, transformation types, filter operations, and self-contained test examples
  - [Test Examples](./services/workflow/internal/etl/transformation/core/tests/examples.md) - ETL test examples (content to be verified)
  - [Formatters](./services/workflow/internal/etl/transformation/formatters/README.md) - **XML template formatting** - template syntax, dynamic data insertion, conditionals, loops, and NIBRS-compliant output generation

---

## 🏗️ Infrastructure Documentation

### Core Infrastructure
- [**Infrastructure Overview**](./infra/README.md) - **AWS CDK TypeScript project** - infrastructure as code with CloudFormation synthesis and deployment commands
- [**Cloud Configuration**](./infra/cloud-config/README.md) - **Layered environment configuration** with default configs inherited by environments, CPU/memory settings, IAM task role policies with dynamic ARN placeholders for KMS/Cognito
- [**Scripts**](./infra/scripts/README.md) - **Database management scripts** - PostgreSQL role creation, connection monitoring, and status reporting

### AWS Lambda Functions
- [**Camera Listener Lambda**](./infra/cloud/lambdas/camera-listener/README.md) - **AI camera event processing** - SNS-triggered Lambdas for video capture (S3), metadata storage (DB), and field reporting
- [**Discovery Lambda**](./infra/cloud-shared/services/discovery-api/discovery-lambda/README.md) - **Email-to-environment mapping** - API Gateway service for multi-tenant mobile authentication discovery

---

## 📦 Libraries Documentation

### Common Libraries
- [**Hero Sentry**](./lib/common/herosentry/README.md) - **Simplified Sentry SDK** - automatic context capture, distributed tracing, database monitoring, and microservice integration
- [**Middleware**](./lib/common/middleware/README.md) - **Authentication middleware** - role-based access control with Cognito JWT, IAM, basic auth, and service-to-service secrets

---

## 🔧 Bootstrap & Scripts

- [**Bootstrap**](./bootstrap/README.md) - **Modular bootstrap framework** with CLI tool, runner script, recipe system, Cognito configuration requirements, and step execution framework
- [**Scripts Overview**](./scripts/README.md) - **Utility scripts collection** - schema drift detection, webhook development, and various developer tools
- [**Schema Drift Detection**](./scripts/schema-drift/README.md) - **Production database analysis** - detects schema differences, RLS policies, missing migrations, and constraint mismatches
- [**Twilio Webhook Dev**](./scripts/twilio-webhook-dev/README.md) - **Automated webhook development** - ngrok tunnel setup, Twilio configuration, and local testing environment

---

## 🗄️ Database & Migrations

- [**Migrations Overview**](./migrations/README.md) - **Goose migration setup** with commands: `make db` (run migrations), `make db-new` (create new migration), `make db-down` (rollback), `make db-print` (print current schema)
- [**Current Database Schema**](./migrations/current-db-schema.md) - Current database schema snapshot (auto-generated file)

---

## 📖 Quick Links

### Getting Started
1. [Main README](./README.md) - **Start here** for monorepo overview and setup
2. [Services Setup](./services/README.md) - **Creating Go microservices** with ConnectRPC
3. [Frontend Observability](./apps/FRONTEND_OBSERVABILITY_GUIDE.md) - **Sentry setup** for React/Next.js apps

### Development Guides
- [Creating a Service](./services/README.md#how-to-create-a-service) - Step-by-step service creation
- [Deploying a Service](./services/README.md#how-to-deploy-a-service) - Service deployment process
- [Database Migrations](./migrations/README.md) - Using Goose for schema management
- [Bootstrap Framework](./bootstrap/README.md) - Environment initialization

### Application Development
- [Command & Control Web](./apps/hero-command-and-control-web/README.md) - Next.js dispatcher app
- [Responder App](./apps/hero-responder-app/README.md) - React Native mobile app
- [Admin Web](./apps/hero-admin-web/README.md) - Next.js admin interface

### Infrastructure & DevOps
- [Cloud Configuration](./infra/cloud-config/README.md) - Environment configuration layers
- [Deployment Pipeline](./.github/DEPLOYMENT.md) - GitHub Actions automation
- [Communications Setup](./services/communications/README.md) - Twilio webhook development

---

## 🔍 Documentation Categories

### By Component Type
- **Applications**: Next.js web apps, React Native/Expo mobile apps
- **Services**: Go microservices with ConnectRPC, protobuf interfaces, Docker containers
- **Infrastructure**: AWS services, Lambda functions, environment configurations
- **Libraries**: Shared middleware and utilities
- **Database**: PostgreSQL with Goose migrations

### By Functionality
- **Authentication**: [Mobile OAuth 2.0](./apps/hero-responder-app/AUTHENTICATION.md), [Bootstrap Cognito](./bootstrap/README.md)
- **Observability**: [Frontend Sentry Guide](./apps/FRONTEND_OBSERVABILITY_GUIDE.md)
- **Communications**: [Twilio/Zello Service](./services/communications/README.md)
- **Permissions**: [OpenFGA RBAC](./services/perms/README.md), [Feature Flags](./services/featureflags/README.md)
- **File Management**: [S3 File Repository](./services/filerepository/README.md)
- **Compliance**: [NIBRS ETL Pipeline](./services/workflow/internal/etl/README.md)
- **Deployment**: [GitHub Actions](./.github/DEPLOYMENT.md)

---

## 📝 Notes

- Documentation files are organized hierarchically following the project structure
- Each major component has its own README.md file
- Specialized documentation (OBSERVABILITY.md, AUTHENTICATION.md) provides deep-dives into specific topics
- The project follows a microservices architecture with separate documentation for each service
- Some documentation files marked as "content to be verified" need further inspection to provide accurate descriptions

---
