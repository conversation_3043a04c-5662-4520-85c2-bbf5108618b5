import {
    useQuery,
    useMutation,
    useQueryClient,
    UseQueryOptions,
    UseMutationOptions
} from "@tanstack/react-query";
import * as Sentry from "@sentry/nextjs";

import {
    PresignedUploadRequest,
    PresignedUploadResponse,
    PresignedDownloadRequest,
    PresignedDownloadResponse,
    GetFileMetadataRequest,
    GetFileMetadataResponse,
    SearchFilesRequest,
    SearchFilesResponse,
    ListFilesRequest,
    ListFilesResponse,
    UpdateFileMetadataRequest,
    UpdateFileMetadataResponse,
    ConfirmUploadRequest,
    ConfirmUploadResponse,
    CleanupPendingUploadsRequest,
    CleanupPendingUploadsResponse,
    DeleteFileRequest,
    DeleteFileResponse,
    UndeleteFileRequest,
    UndeleteFileResponse,
    PurgeFileRequest,
    PurgeFileResponse,
    GetAccessLogRequest,
    GetAccessLogResponse,
    RecordAccessRequest,
    RecordAccessResponse,
} from "proto/hero/filerepository/v1/filerepository_pb";

import {
    getPresignedUploadUrl,
    getPresignedDownloadUrl,
    getFileMetadata,
    updateFileMetadata,
    searchFiles,
    listFiles,
    confirmUpload,
    cleanupPendingUploads,
    deleteFile,
    undeleteFile,
    purgeFile,
    getAccessLog,
    recordAccess
} from "./endpoints";

// Constants for cache keys
const FILES_QUERY_KEY = "files";
const FILE_QUERY_KEY = "file";
const FILE_ACCESS_LOG_QUERY_KEY = "fileAccessLog";

/**
 * Hook for getting a presigned upload URL.
 * This creates a PENDING file metadata record and returns a URL for direct upload to cloud storage.
 */
export function useGetPresignedUploadUrl(
    options?: UseMutationOptions<PresignedUploadResponse, Error, PresignedUploadRequest>
) {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (request: PresignedUploadRequest) => getPresignedUploadUrl(request),
        onSuccess: () => {
            // Invalidate files list to show the new PENDING file
            queryClient.invalidateQueries({ queryKey: [FILES_QUERY_KEY] });
        },
        ...options,
    });
}

/**
 * Hook for getting a presigned download URL for an existing file.
 */
export function useGetPresignedDownloadUrl(
    options?: UseMutationOptions<PresignedDownloadResponse, Error, PresignedDownloadRequest>
) {
    return useMutation({
        mutationFn: (request: PresignedDownloadRequest) => getPresignedDownloadUrl(request),
        ...options,
    });
}

/**
 * Hook for fetching file metadata by ID.
 * Uses the fileId as part of the cache key.
 */
export function useFileMetadata(
    fileId: string,
    options?: Omit<UseQueryOptions<GetFileMetadataResponse, Error>, 'queryKey'>
) {
    return useQuery({
        queryKey: [FILE_QUERY_KEY, fileId, "metadata"],
        queryFn: () => getFileMetadata({ id: fileId } as GetFileMetadataRequest),
        enabled: !!fileId,
        staleTime: 5 * 60 * 1000, // 5 minutes
        retry: 2,
        ...options,
    });
}

/**
 * Hook for updating file metadata.
 * On success, invalidates both the files list and the specific file cache.
 */
export function useUpdateFileMetadata(
    options?: UseMutationOptions<UpdateFileMetadataResponse, Error, UpdateFileMetadataRequest>
) {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (request: UpdateFileMetadataRequest) => updateFileMetadata(request),
        onSuccess: (data: UpdateFileMetadataResponse) => {
            queryClient.invalidateQueries({ queryKey: [FILES_QUERY_KEY] });
            queryClient.invalidateQueries({ queryKey: [FILE_QUERY_KEY, data.metadata?.id] });
        },
        ...options,
    });
}

/**
 * Hook for searching files with advanced filtering and text search capabilities.
 * Uses the search parameters as part of the cache key for proper cache segmentation.
 */
export function useSearchFiles(
    params: SearchFilesRequest,
    options?: Omit<
        UseQueryOptions<SearchFilesResponse, Error, SearchFilesResponse>,
        "queryKey" | "queryFn"
    >
) {
    return useQuery({
        queryKey: [FILES_QUERY_KEY, "search", params],
        queryFn: () => searchFiles(params),
        retry: 2,
        ...options,
    });
}

/**
 * Hook for listing files with basic filtering and pagination.
 * The query key includes the request parameters for proper cache segmentation.
 */
export function useListFiles(
    params: ListFilesRequest,
    options?: Omit<
        UseQueryOptions<ListFilesResponse, Error, ListFilesResponse>,
        "queryKey" | "queryFn"
    >
) {
    return useQuery({
        queryKey: [FILES_QUERY_KEY, "list", params],
        queryFn: () => listFiles(params),
        retry: 2,
        ...options,
    });
}

/**
 * Hook for confirming an upload after the file has been uploaded to cloud storage.
 * This changes the file status from PENDING to ACTIVE.
 */
export function useConfirmUpload(
    options?: UseMutationOptions<ConfirmUploadResponse, Error, ConfirmUploadRequest>
) {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (request: ConfirmUploadRequest) => confirmUpload(request),
        onSuccess: (data: ConfirmUploadResponse) => {
            queryClient.invalidateQueries({ queryKey: [FILES_QUERY_KEY] });
            queryClient.invalidateQueries({ queryKey: [FILE_QUERY_KEY, data.metadata?.id] });
        },
        ...options,
    });
}

/**
 * Hook for cleaning up pending uploads older than a specified timestamp.
 * This is typically used for maintenance operations.
 */
export function useCleanupPendingUploads(
    options?: UseMutationOptions<CleanupPendingUploadsResponse, Error, CleanupPendingUploadsRequest>
) {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (request: CleanupPendingUploadsRequest) => cleanupPendingUploads(request),
        onSuccess: () => {
            // Invalidate files list to remove cleaned up files
            queryClient.invalidateQueries({ queryKey: [FILES_QUERY_KEY] });
        },
        ...options,
    });
}

/**
 * Hook for soft-deleting a file.
 * The file is marked as DELETED but not permanently removed.
 */
export function useDeleteFile(
    options?: UseMutationOptions<DeleteFileResponse, Error, DeleteFileRequest>
) {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (request: DeleteFileRequest) => deleteFile(request),
        onSuccess: (_, variables) => {
            queryClient.invalidateQueries({ queryKey: [FILES_QUERY_KEY] });
            queryClient.invalidateQueries({ queryKey: [FILE_QUERY_KEY, variables.id] });
        },
        ...options,
    });
}

/**
 * Hook for undeleting a previously soft-deleted file.
 * This restores the file status from DELETED back to ACTIVE.
 */
export function useUndeleteFile(
    options?: UseMutationOptions<UndeleteFileResponse, Error, UndeleteFileRequest>
) {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (request: UndeleteFileRequest) => undeleteFile(request),
        onSuccess: (data: UndeleteFileResponse) => {
            queryClient.invalidateQueries({ queryKey: [FILES_QUERY_KEY] });
            queryClient.invalidateQueries({ queryKey: [FILE_QUERY_KEY, data.metadata?.id] });
        },
        ...options,
    });
}

/**
 * Hook for permanently purging a file.
 * This removes the file metadata and triggers deletion from cloud storage.
 * This operation is irreversible.
 */
export function usePurgeFile(
    options?: UseMutationOptions<PurgeFileResponse, Error, PurgeFileRequest>
) {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (request: PurgeFileRequest) => purgeFile(request),
        onSuccess: (_, variables) => {
            queryClient.invalidateQueries({ queryKey: [FILES_QUERY_KEY] });
            queryClient.removeQueries({ queryKey: [FILE_QUERY_KEY, variables.id] });
            queryClient.removeQueries({ queryKey: [FILE_ACCESS_LOG_QUERY_KEY, variables.id] });
        },
        ...options,
    });
}

/**
 * Hook for fetching access logs for a specific file.
 * The query key includes the file ID and request parameters for proper cache segmentation.
 */
export function useGetAccessLog(
    params: GetAccessLogRequest,
    options?: Omit<
        UseQueryOptions<GetAccessLogResponse, Error, GetAccessLogResponse>,
        "queryKey" | "queryFn"
    >
) {
    return useQuery({
        queryKey: [FILE_ACCESS_LOG_QUERY_KEY, params.fileId, params],
        queryFn: () => getAccessLog(params),
        enabled: !!params.fileId,
        retry: 2,
        ...options,
    });
}

/**
 * Hook for recording a file access event.
 * This is used for audit trails and analytics.
 */
export function useRecordAccess(
    options?: UseMutationOptions<RecordAccessResponse, Error, RecordAccessRequest>
) {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (request: RecordAccessRequest) => recordAccess(request),
        onSuccess: (_, variables) => {
            // Invalidate access logs for this file
            queryClient.invalidateQueries({ 
                queryKey: [FILE_ACCESS_LOG_QUERY_KEY, variables.fileId] 
            });
            // Optionally invalidate file metadata if it tracks download counts
            queryClient.invalidateQueries({ 
                queryKey: [FILE_QUERY_KEY, variables.fileId] 
            });
        },
        ...options,
    });
}

/**
 * Composite hook for the complete file upload workflow.
 * This combines getting a presigned URL, uploading the file, and confirming the upload.
 */
export function useFileUpload() {
    const getPresignedUrl = useGetPresignedUploadUrl();
    const confirmUploadMutation = useConfirmUpload();

    const uploadFile = async (
        file: File,
        orgId?: number,
        extraMetadata?: any
    ): Promise<{ success: boolean; fileId?: string; error?: string }> => {
        try {
            // Validate file size on client side (250MB limit)
            const maxSizeBytes = 250 * 1024 * 1024; // 250MB
            if (file.size > maxSizeBytes) {
                const sizeError = new Error(`File size exceeds 250MB limit. File size: ${(file.size / 1024 / 1024).toFixed(2)}MB`);
                throw sizeError;
            }

            // Step 1: Get presigned URL
            const presignedRequest: PresignedUploadRequest = {
                fileName: file.name,
                fileType: file.type,
                // ownerId is automatically set by backend from authentication context
                orgId: orgId || 0, // Backend will use context org_id if not provided
                expiresIn: 900, // 15 minutes
                extraMetadata: extraMetadata,
            } as PresignedUploadRequest;

            const presignedResponse = await getPresignedUrl.mutateAsync(presignedRequest);

            if (!presignedResponse.presignedUrl || !presignedResponse.metadata) {
                const presignedError = new Error("Failed to get presigned URL");
                try {
                    Sentry.withScope((scope) => {
                        scope.setTag("error_key", "media-upload");
                        scope.setTag("upload_stage", "presigned_url");
                        scope.setTag("failure_reason", "no_presigned_url");
                        scope.setContext("file_info", {
                            fileName: file.name,
                            fileType: file.type,
                            fileSize: file.size
                        });
                        Sentry.captureException(presignedError);
                    });
                } catch {
                    console.error("Error capturing presigned URL error:", presignedError);
                }
                throw presignedError;
            }

            // Step 2: Upload file to cloud storage with required metadata headers
            // The presigned URL was generated with specific metadata headers that must be included
            const uploadHeaders: Record<string, string> = {
                'Content-Type': file.type,
                'x-amz-server-side-encryption': 'aws:kms',
            };

            // Add the same metadata headers that were used when generating the presigned URL
            if (presignedResponse.metadata) {
                uploadHeaders['x-amz-meta-file-id'] = presignedResponse.metadata.id;
                uploadHeaders['x-amz-meta-owner-id'] = presignedResponse.metadata.ownerId;
                uploadHeaders['x-amz-meta-created-at'] = presignedResponse.metadata.createdAt;
            }

            console.log('📤 Uploading with headers:', uploadHeaders);

            const uploadResponse = await fetch(presignedResponse.presignedUrl, {
                method: 'PUT',
                headers: uploadHeaders,
                body: file,
            });

            if (!uploadResponse.ok) {
                const uploadError = new Error(`Upload failed: ${uploadResponse.status} ${uploadResponse.statusText}`);
                try {
                    Sentry.withScope((scope) => {
                        scope.setTag("error_key", "media-upload");
                        scope.setTag("upload_stage", "s3_upload");
                        scope.setTag("failure_reason", "upload_failed");
                        scope.setTag("http_status", uploadResponse.status.toString());
                        scope.setContext("file_info", {
                            fileName: file.name,
                            fileType: file.type,
                            fileSize: file.size,
                        });
                        scope.setContext("upload_metadata", extraMetadata);
                        Sentry.captureException(uploadError);
                    });
                } catch {
                    console.error("Error capturing upload error:", uploadError);
                }
                throw uploadError;
            }

            // Step 3: Confirm upload
            const confirmRequest: ConfirmUploadRequest = {
                pendingId: presignedResponse.metadata.id,
                fileSize: file.size,
            } as ConfirmUploadRequest;

            await confirmUploadMutation.mutateAsync(confirmRequest);

            return {
                success: true,
                fileId: presignedResponse.metadata.id,
            };
        } catch (error) {
            // If it's not one of our already-captured errors, capture it here
            if (error instanceof Error && !error.message.includes('File size exceeds') && 
                !error.message.includes('Failed to get presigned URL') && 
                !error.message.includes('Upload failed:')) {
                try {
                    Sentry.withScope((scope) => {
                        scope.setTag("error_key", "media-upload");
                        scope.setTag("upload_stage", "unknown");
                        scope.setTag("failure_reason", "unexpected_error");
                        scope.setContext("file_info", {
                            fileName: file.name,
                            fileType: file.type,
                            fileSize: file.size
                        });
                        scope.setContext("upload_metadata", extraMetadata);
                        Sentry.captureException(error);
                    });
                } catch {
                    console.error("Error capturing unknown error:", error);
                }
            }
            
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error occurred',
            };
        }
    };

    return {
        uploadFile,
        isLoading: getPresignedUrl.isPending || confirmUploadMutation.isPending,
        error: getPresignedUrl.error || confirmUploadMutation.error,
    };
} 