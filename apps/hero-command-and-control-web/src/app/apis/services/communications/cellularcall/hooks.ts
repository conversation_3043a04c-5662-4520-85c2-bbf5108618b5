import { create } from "@bufbuild/protobuf";
import {
  useMutation,
  UseMutationOptions,
  useQuery,
  useQueryClient,
  UseQueryOptions,
} from "@tanstack/react-query";
import { useEffect } from "react";

import {
  ListCallsRequest,
  ListCallsResponse,
  UpdateCallerNameRequest,
  UpdateCallerNameRequestSchema,
  UpdateCallerNameResponse,
} from "proto/hero/communications/v1/conversation_pb";

import { formatPhoneNumberForDisplay } from "@/app/utils/caller-identification";
import {
  listCalls,
  updateCallerName
} from "./endpoints";

const CELLULAR_CALL_QUERY_KEY = "cellular_call";

/**
 * Hook to list calls with pagination and filtering.
 * @param params - ListCallsRequest with pagination and filter parameters.
 * @param options - Additional useQuery options.
 * @returns Query result with paginated call records.
 */
export function useListCalls(
  params: ListCallsRequest,
  options?: Omit<
    UseQueryOptions<ListCallsResponse, Error, ListCallsResponse>,
    "queryKey" | "queryFn"
  >
) {
  return useQuery({
    queryKey: [CELLULAR_CALL_QUERY_KEY, "listCalls", params],
    queryFn: () => listCalls(params),
    retry: 2,
    ...options,
  });
}

/**
 * Hook to update caller name for a call.
 * @param options - Additional useMutation options.
 * @returns Mutation function to update caller name.
 */
export function useUpdateCallerName(
  options?: UseMutationOptions<
    UpdateCallerNameResponse,
    Error,
    UpdateCallerNameRequest,
    unknown
  >
) {
  const queryClient = useQueryClient();
  const userOnSuccess = options?.onSuccess;
  return useMutation({
    mutationFn: (params: UpdateCallerNameRequest) => updateCallerName(params),
    ...options,
    onSuccess: (data, variables, context) => {
      // Refresh cellular call lists after caller-name changes
      queryClient.invalidateQueries({ queryKey: [CELLULAR_CALL_QUERY_KEY] });
      userOnSuccess?.(data, variables, context);
    },
  });
}

/**
 * Persist caller names to backend once identification resolves.
 */
export function usePersistCallerName(
  callSid: string | undefined,
  phoneNumber: string | undefined,
  identifiedName: string | undefined,
  isUnknown: boolean
) {
  const updateCallerNameMutation = useUpdateCallerName();

  useEffect(() => {
    if (!callSid || !phoneNumber || !identifiedName || isUnknown) return;
    if (sharedCallerNameUpdates.has(callSid)) return;

    const formattedPhone = formatPhoneNumberForDisplay(phoneNumber);
    if (identifiedName === formattedPhone) return;

    sharedCallerNameUpdates.add(callSid);
    updateCallerNameMutation.mutate(
      create(UpdateCallerNameRequestSchema, {
        callSid,
        callerName: identifiedName,
      }),
      {
        onSuccess: (res) => {
          if (!res.success) sharedCallerNameUpdates.delete(callSid);
        },
        onError: () => {
          sharedCallerNameUpdates.delete(callSid);
        },
      }
    );
  }, [callSid, phoneNumber, identifiedName, isUnknown, updateCallerNameMutation]);

  return {
    isUpdating: updateCallerNameMutation.isPending,
    error: updateCallerNameMutation.error,
  };
}

// Shared state across all instances to prevent duplicate updates
const sharedForwardedCallUpdates = new Set<string>();
// Shared state for regular caller-name persistence to avoid duplicates across components
const sharedCallerNameUpdates = new Set<string>();

/**
 * Persist caller names for forwarded calls using situation-provided callSid and phone number.
 * Mirrors usePersistCallerName behavior but is keyed off situation additionalInfoJson.
 */
export function usePersistForwardedCallerName(
  callSid: string | undefined,
  phoneNumber: string | undefined,
  identifiedName: string | undefined,
  isUnknown: boolean
) {
  const updateCallerNameMutation = useUpdateCallerName();

  useEffect(() => {
    if (!callSid || !phoneNumber || !identifiedName || isUnknown) return;
    if (sharedForwardedCallUpdates.has(callSid)) return;

    const formattedPhone = formatPhoneNumberForDisplay(phoneNumber);
    if (identifiedName === formattedPhone) return;

    sharedForwardedCallUpdates.add(callSid);
    updateCallerNameMutation.mutate(
      create(UpdateCallerNameRequestSchema, {
        callSid,
        callerName: identifiedName,
      }),
      {
        onSuccess: (res) => {
          if (!res.success) sharedForwardedCallUpdates.delete(callSid);
        },
        onError: () => {
          sharedForwardedCallUpdates.delete(callSid);
        },
      }
    );
  }, [callSid, phoneNumber, identifiedName, isUnknown, updateCallerNameMutation]);

  return {
    isUpdating: updateCallerNameMutation.isPending,
    error: updateCallerNameMutation.error,
  };
}