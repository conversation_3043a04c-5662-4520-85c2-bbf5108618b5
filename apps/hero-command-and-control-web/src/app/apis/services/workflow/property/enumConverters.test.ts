import {
    propertyStatusToString,
    propertyTypeToString,
    stringToPropertyStatus,
    stringToPropertyType,
} from './enumConverters';
import { PropertyStatus, PropertyType } from './types';

describe('PropertyType Converters', () => {
    describe('stringToPropertyType', () => {
        it('should convert a valid string to the corresponding PropertyType enum value', () => {
            expect(stringToPropertyType('PROPERTY_TYPE_FOUND')).toBe(PropertyType.PROPERTY_TYPE_FOUND);
            expect(stringToPropertyType('PROPERTY_TYPE_SEIZED')).toBe(PropertyType.PROPERTY_TYPE_SEIZED);
            expect(stringToPropertyType('PROPERTY_TYPE_STOLEN')).toBe(PropertyType.PROPERTY_TYPE_STOLEN);
        });

        it('should return PROPERTY_TYPE_UNSPECIFIED for strings without the proper prefix', () => {
            expect(stringToPropertyType('INVALID_FOUND')).toBe(PropertyType.PROPERTY_TYPE_UNSPECIFIED);
        });

        it('should return PROPERTY_TYPE_UNSPECIFIED for unknown enum keys', () => {
            expect(stringToPropertyType('PROPERTY_TYPE_UNKNOWN')).toBe(PropertyType.PROPERTY_TYPE_UNSPECIFIED);
        });
    });

    describe('propertyTypeToString', () => {
        it('should convert a PropertyType enum value to its string representation', () => {
            expect(propertyTypeToString(PropertyType.PROPERTY_TYPE_FOUND)).toBe('PROPERTY_TYPE_FOUND');
            expect(propertyTypeToString(PropertyType.PROPERTY_TYPE_SEIZED)).toBe('PROPERTY_TYPE_SEIZED');
        });
    });
});

describe('PropertyStatus Converters', () => {
    describe('stringToPropertyStatus', () => {
        it('should convert a valid string to the corresponding PropertyStatus enum value', () => {
            expect(stringToPropertyStatus('PROPERTY_STATUS_COLLECTED')).toBe(PropertyStatus.PROPERTY_STATUS_COLLECTED);
            expect(stringToPropertyStatus('PROPERTY_STATUS_IN_CUSTODY')).toBe(PropertyStatus.PROPERTY_STATUS_IN_CUSTODY);
        });

        it('should return PROPERTY_STATUS_UNSPECIFIED for strings without the proper prefix', () => {
            expect(stringToPropertyStatus('INVALID_COLLECTED')).toBe(PropertyStatus.PROPERTY_STATUS_UNSPECIFIED);
        });
    });

    describe('propertyStatusToString', () => {
        it('should convert a PropertyStatus enum value to its string representation', () => {
            expect(propertyStatusToString(PropertyStatus.PROPERTY_STATUS_COLLECTED)).toBe('PROPERTY_STATUS_COLLECTED');
            expect(propertyStatusToString(PropertyStatus.PROPERTY_STATUS_IN_CUSTODY)).toBe('PROPERTY_STATUS_IN_CUSTODY');
        });
    });
}); 