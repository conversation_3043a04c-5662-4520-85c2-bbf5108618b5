// Import protobuf types instead of duplicating them
import {
    CustodyActionType,
    NIBRSPropertyType,
    PropertyDisposalType,
    PropertyStatus
} from "proto/hero/property/v1/property_pb";

// Re-export protobuf types for backward compatibility
export {
    CustodyActionType, NIBRSPropertyType, PropertyDisposalType, PropertyStatus
};

// Use NIBRSPropertyType directly - no more aliases!

export enum SearchOrderBy {
    SEARCH_ORDER_BY_UNSPECIFIED = 0,
    SEARCH_ORDER_BY_RELEVANCE = 1,
    SEARCH_ORDER_BY_CREATED_AT = 2,
    SEARCH_ORDER_BY_UPDATED_AT = 3,
    SEARCH_ORDER_BY_STATUS = 4,
}

// Core Types
export interface CustodyEvent {
    timestamp: string;
    transferringUserId: string;
    transferringAgency: string;
    receivingUserId: string;
    receivingAgency: string;
    newLocation: string;
    actionType: CustodyActionType;
    notes: string;
    caseNumber: string;
    evidenceNumber: string;

    // Enhanced fields for detailed custody information
    performingOfficerId?: string;
    receivingEntityType?: string;
    receivingEntityName?: string;
    checkoutLength?: string;
    reason?: string;
    receivingAgencyType?: string;
    confirmationReceived?: boolean;
    collectionLocation?: string;
    disposalType?: string;
}

export interface PropertySchema {
    description: string;
    quantity: string;
    category: string;
    identifiers: string;
    owner: string;
    condition: string;
    serialNumber: string;
    value: string;
    propertyType: NIBRSPropertyType; // Moved from Property interface
}

export interface Property {
    id: string;
    orgId: number;
    propertyNumber?: string; // Added property/evidence number for tracking
    propertyStatus: PropertyStatus;
    nibrsPropertyType: NIBRSPropertyType; // NIBRS property type classification
    isEvidence: boolean;
    retentionPeriod?: string;
    disposalType: PropertyDisposalType;
    notes?: string;
    currentCustodian?: string;
    currentLocation?: string;
    custodyChain: CustodyEvent[];
    propertySchema?: PropertySchema; // Legacy property schema (fallback)

    // Dynamic schema support (new approach)
    schemaId?: string; // ID of the schema this property conforms to
    schemaVersion?: number; // Version of the schema used
    details?: { [key: string]: any }; // Dynamic payload validated/rendered by the resolved schema

    createTime: string;
    updateTime: string;
    createdBy?: string;
    updatedBy?: string;
    version: number;
    status: number;
    resourceType: string;
}

// Search Types
export interface DateRange {
    from: string;
    to: string;
}

export interface FieldQuery {
    field: string;
    query: string;
}

export interface HighlightResult {
    field: string;
    fragments: string[];
}

export interface SearchPropertiesRequest {
    query: string;
    fieldQueries?: FieldQuery[];
    dateRange?: DateRange;
    nibrsPropertyType?: NIBRSPropertyType;
    propertyStatus?: PropertyStatus;
    orderBy?: SearchOrderBy;
    pageSize?: number;
    pageToken?: string;
}

export interface SearchPropertiesResponse {
    properties: Property[];
    highlights: HighlightResult[];
    nextPageToken?: string;
    totalCount: number;
}

// Property File Attachment Types
export interface PropertyAttachmentMetadata {
    // Commonly used fields coming from filerepository metadata or UI context
    fileType?: string;
    originalFilename?: string;
    uploadContext?: string;
    attachmentId?: string;

    // Allow arbitrary JSON keys since backend uses google.protobuf.Struct
    [key: string]: unknown;
}

export interface PropertyFileReference {
    id: string;
    propertyId: string;
    fileId: string;
    caption?: string;
    displayName?: string;
    displayOrder?: number;
    fileCategory?: string;
    metadata?: PropertyAttachmentMetadata;
} 