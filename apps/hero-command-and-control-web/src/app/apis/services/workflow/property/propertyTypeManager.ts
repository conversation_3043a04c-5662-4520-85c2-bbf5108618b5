import { NIBRSPropertyType } from "proto/hero/property/v1/property_pb";
import { propertyTypeToString, stringToPropertyType } from './enumConverters';

/**
 * Centralized Property Type Management
 * 
 * This module provides a single source of truth for property type handling,
 * ensuring consistency between proto enums, UI display, and NIBRS mappings.
 */

// NIBRS Loss Type mappings to proper proto enum values
export type NIBRSLossType =
  | 'burned'
  | 'counterfeited_forged'
  | 'destroyed_damaged_vandalized'
  | 'recovered'
  | 'seized'
  | 'stolen'
  | 'unknown'
  | 'found';

/**
 * Maps NIBRS loss types to proto PropertyType enum values
 * This is the single source of truth for NIBRS -> PropertyType mapping
 */
export const NIBRS_TO_PROPERTY_TYPE_MAP: Record<NIBRSLossType, NIBRSPropertyType> = {
  'burned': NIBRSPropertyType.PROPERTY_TYPE_BURNED,
  'counterfeited_forged': NIBRSPropertyType.PROPERTY_TYPE_FORGED,
  'destroyed_damaged_vandalized': NIBRSPropertyType.PROPERTY_TYPE_DAMAGED,
  'recovered': NIBRSPropertyType.PROPERTY_TYPE_RECOVERED,
  'seized': NIBRSPropertyType.PROPERTY_TYPE_SEIZED,
  'stolen': NIBRSPropertyType.PROPERTY_TYPE_STOLEN,
  'unknown': NIBRSPropertyType.PROPERTY_TYPE_UNKNOWN,
  'found': NIBRSPropertyType.PROPERTY_TYPE_FOUND,
};

/**
 * Maps proto PropertyType enum values to human-readable display strings
 * This is for UI display purposes only
 */
export const PROPERTY_TYPE_DISPLAY_MAP: Record<NIBRSPropertyType, string> = {
  [NIBRSPropertyType.PROPERTY_TYPE_UNSPECIFIED]: 'Unspecified',
  [NIBRSPropertyType.PROPERTY_TYPE_NONE]: 'None',
  [NIBRSPropertyType.PROPERTY_TYPE_BURNED]: 'Burned',
  [NIBRSPropertyType.PROPERTY_TYPE_FORGED]: 'Forged',
  [NIBRSPropertyType.PROPERTY_TYPE_DAMAGED]: 'Damaged',
  [NIBRSPropertyType.PROPERTY_TYPE_RECOVERED]: 'Recovered',
  [NIBRSPropertyType.PROPERTY_TYPE_SEIZED]: 'Seized',
  [NIBRSPropertyType.PROPERTY_TYPE_STOLEN]: 'Stolen',
  [NIBRSPropertyType.PROPERTY_TYPE_UNKNOWN]: 'Unknown',
  [NIBRSPropertyType.PROPERTY_TYPE_FOUND]: 'Found',
};


// ============ Utility Functions ============

/**
 * Converts NIBRS loss type to proto PropertyType enum value
 */
export function nibrsToPropertyType(nibrsType: NIBRSLossType): NIBRSPropertyType {
  return NIBRS_TO_PROPERTY_TYPE_MAP[nibrsType] || NIBRSPropertyType.PROPERTY_TYPE_UNKNOWN;
}

/**
 * Converts proto PropertyType enum to human-readable display string
 */
export function propertyTypeToDisplayString(propertyType: NIBRSPropertyType): string {
  return PROPERTY_TYPE_DISPLAY_MAP[propertyType] || 'Unknown';
}

/**
 * Converts proto PropertyType enum to enum string (for API/storage)
 * Uses the existing enum converter
 */
export function propertyTypeToEnumString(propertyType: NIBRSPropertyType): string {
  return propertyTypeToString(propertyType);
}

/**
 * Converts enum string to proto PropertyType enum
 * Uses the existing enum converter
 */
export function enumStringToPropertyType(enumString: string): NIBRSPropertyType {
  return stringToPropertyType(enumString);
}

/**
 * Gets PropertyType for form dropdowns with both value and label
 * Returns proto enum values as values (not lowercase strings)
 */
export function getPropertyTypeDropdownOptions() {
  return [
    { value: propertyTypeToString(NIBRSPropertyType.PROPERTY_TYPE_FOUND), label: propertyTypeToDisplayString(NIBRSPropertyType.PROPERTY_TYPE_FOUND) },
    { value: propertyTypeToString(NIBRSPropertyType.PROPERTY_TYPE_SEIZED), label: propertyTypeToDisplayString(NIBRSPropertyType.PROPERTY_TYPE_SEIZED) },
    { value: propertyTypeToString(NIBRSPropertyType.PROPERTY_TYPE_STOLEN), label: propertyTypeToDisplayString(NIBRSPropertyType.PROPERTY_TYPE_STOLEN) },
    { value: propertyTypeToString(NIBRSPropertyType.PROPERTY_TYPE_RECOVERED), label: propertyTypeToDisplayString(NIBRSPropertyType.PROPERTY_TYPE_RECOVERED) },
    { value: propertyTypeToString(NIBRSPropertyType.PROPERTY_TYPE_BURNED), label: propertyTypeToDisplayString(NIBRSPropertyType.PROPERTY_TYPE_BURNED) },
    { value: propertyTypeToString(NIBRSPropertyType.PROPERTY_TYPE_FORGED), label: propertyTypeToDisplayString(NIBRSPropertyType.PROPERTY_TYPE_FORGED) },
    { value: propertyTypeToString(NIBRSPropertyType.PROPERTY_TYPE_DAMAGED), label: propertyTypeToDisplayString(NIBRSPropertyType.PROPERTY_TYPE_DAMAGED) },
    { value: propertyTypeToString(NIBRSPropertyType.PROPERTY_TYPE_UNKNOWN), label: propertyTypeToDisplayString(NIBRSPropertyType.PROPERTY_TYPE_UNKNOWN) },
  ];
}


/**
 * Validates if a property type value is a valid proto enum string
 */
export function isValidPropertyTypeEnumString(value: string): boolean {
  try {
    const type = stringToPropertyType(value);
    return type !== NIBRSPropertyType.PROPERTY_TYPE_UNSPECIFIED || value === 'PROPERTY_TYPE_UNSPECIFIED';
  } catch {
    return false;
  }
}

/**
 * Converts legacy lowercase property type strings to proper proto enum strings
 * Used for migration/compatibility
 */
export function migrateLegacyPropertyType(legacyType: string): string {
  const legacyMap: Record<string, NIBRSPropertyType> = {
    'found': NIBRSPropertyType.PROPERTY_TYPE_FOUND,
    'seized': NIBRSPropertyType.PROPERTY_TYPE_SEIZED,
    'stolen': NIBRSPropertyType.PROPERTY_TYPE_STOLEN,
    'recovered': NIBRSPropertyType.PROPERTY_TYPE_RECOVERED,
    'burned': NIBRSPropertyType.PROPERTY_TYPE_BURNED,
    'forged': NIBRSPropertyType.PROPERTY_TYPE_FORGED,
    'damaged': NIBRSPropertyType.PROPERTY_TYPE_DAMAGED,
    'unknown': NIBRSPropertyType.PROPERTY_TYPE_UNKNOWN,
    'none': NIBRSPropertyType.PROPERTY_TYPE_NONE,
  };

  const propertyType = legacyMap[legacyType.toLowerCase()];
  return propertyType ? propertyTypeToString(propertyType) : legacyType;
}