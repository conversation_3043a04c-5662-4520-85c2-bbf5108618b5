import { CustodyActionType, NIBRSPropertyDescription, NIBRSPropertyType, PropertyDisposalType, PropertyStatus } from "proto/hero/property/v1/property_pb";

// Function to convert a string (e.g. "PROPERTY_TYPE_FOUND")
// to the corresponding enum value (e.g. NIBRSPropertyType.PROPERTY_TYPE_FOUND).
export function stringToPropertyType(value: string): NIBRSPropertyType {
    // The enum keys are the full strings, so we can use the value directly
    if (value in NIBRSPropertyType) {
        return NIBRSPropertyType[value as keyof typeof NIBRSPropertyType];
    }
    return NIBRSPropertyType.PROPERTY_TYPE_UNSPECIFIED;
}

// Function to convert an enum value (e.g. NIBRSPropertyType.PROPERTY_TYPE_FOUND)
// back to its string representation (e.g. "PROPERTY_TYPE_FOUND").
export function propertyTypeToString(propertyType: NIBRSPropertyType): string {
    const key = NIBRSPropertyType[propertyType];
    return key || "PROPERTY_TYPE_UNSPECIFIED";
}

// For crazy reason when you use our hooks to get Property it gives you not the right Enum 
// It gives you the string representation of the enum
// But type is still NIBRSPropertyType
// You can just type cast it from NIBRSPropertyType
export function hookPropertyTypeToString(propertyType: NIBRSPropertyType): string {
    return String(propertyType);
}

// Function to convert a string (e.g. "PROPERTY_STATUS_COLLECTED" or "Collected")
// to the corresponding enum value (e.g. PropertyStatus.PROPERTY_STATUS_COLLECTED).
export function stringToPropertyStatus(value: string): PropertyStatus {
    // Direct mapping from backend protobuf enum string to frontend enum value
    switch (value) {
        case "PROPERTY_STATUS_UNSPECIFIED":
            return PropertyStatus.UNSPECIFIED;
        case "PROPERTY_STATUS_INTAKE_PENDING":
            return PropertyStatus.INTAKE_PENDING;
        case "PROPERTY_STATUS_COLLECTED":
            return PropertyStatus.COLLECTED;
        case "PROPERTY_STATUS_CHECKED_IN":
            return PropertyStatus.CHECKED_IN;
        case "PROPERTY_STATUS_CHECKED_OUT":
            return PropertyStatus.CHECKED_OUT;
        case "PROPERTY_STATUS_RECOVERED":
            return PropertyStatus.RECOVERED;
        case "PROPERTY_STATUS_FOUND":
            return PropertyStatus.FOUND;
        case "PROPERTY_STATUS_SAFEKEEPING":
            return PropertyStatus.SAFEKEEPING;
        case "PROPERTY_STATUS_AWAITING_DISPOSITION":
            return PropertyStatus.AWAITING_DISPOSITION;
        case "PROPERTY_STATUS_DISPOSED":
            return PropertyStatus.DISPOSED;
        case "PROPERTY_STATUS_MISSING":
            return PropertyStatus.MISSING;
        case "PROPERTY_STATUS_STOLEN":
            return PropertyStatus.STOLEN;

        // Fallback: try exact enum key match for shortened names
        default:
            if (value in PropertyStatus) {
                return PropertyStatus[value as keyof typeof PropertyStatus];
            }

            // Try human-readable strings as last resort
            switch (value.toLowerCase()) {
                case "intake pending":
                case "intake_pending":
                    return PropertyStatus.INTAKE_PENDING;
                case "collected":
                    return PropertyStatus.COLLECTED;
                case "checked in":
                case "checked_in":
                    return PropertyStatus.CHECKED_IN;
                case "checked out":
                case "checked_out":
                    return PropertyStatus.CHECKED_OUT;
                case "recovered":
                    return PropertyStatus.RECOVERED;
                case "found":
                    return PropertyStatus.FOUND;
                case "safekeeping":
                    return PropertyStatus.SAFEKEEPING;
                case "awaiting disposition":
                case "awaiting_disposition":
                    return PropertyStatus.AWAITING_DISPOSITION;
                case "disposed":
                    return PropertyStatus.DISPOSED;
                case "missing":
                    return PropertyStatus.MISSING;
                case "stolen":
                    return PropertyStatus.STOLEN;
                default:
                    return PropertyStatus.UNSPECIFIED;
            }
    }
}

// Function to convert an enum value (e.g. PropertyStatus.PROPERTY_STATUS_COLLECTED)
// back to its string representation (e.g. "PROPERTY_STATUS_COLLECTED").
export function propertyStatusToString(propertyStatus: PropertyStatus): string {
    const key = PropertyStatus[propertyStatus];
    return key || "PROPERTY_STATUS_UNSPECIFIED";
}

// For crazy reason when you use our hooks to get Property it gives you not the right Enum 
// It gives you the string representation of the enum
// But type is still PropertyStatus
// You can just type cast it from PropertyStatus
export function hookPropertyStatusToString(propertyStatus: PropertyStatus): string {
    const key = PropertyStatus[propertyStatus];
    return key || "PROPERTY_STATUS_UNSPECIFIED";
}

// Function to convert a string (e.g. "PROPERTY_DISPOSAL_TYPE_RELEASED")
// to the corresponding enum value (e.g. PropertyDisposalType.PROPERTY_DISPOSAL_TYPE_RELEASED).
export function stringToPropertyDisposalType(value: string): PropertyDisposalType {
    if (!value) return PropertyDisposalType.UNSPECIFIED;
    if (value in PropertyDisposalType) {
        const resolved: unknown = (PropertyDisposalType as any)[value];
        if (typeof resolved === 'number') return resolved as PropertyDisposalType;
        if (typeof resolved === 'string' && resolved in PropertyDisposalType) {
            const numeric = (PropertyDisposalType as any)[resolved];
            if (typeof numeric === 'number') return numeric as PropertyDisposalType;
        }
    }
    // Human strings fallback
    switch (value.toLowerCase()) {
        case 'released': return PropertyDisposalType.RELEASED;
        case 'destroyed': return PropertyDisposalType.DESTROYED;
        case 'auctioned': return PropertyDisposalType.AUCTIONED;
        case 'agency_retain': return PropertyDisposalType.AGENCY_RETAIN;
        case 'transferred': return PropertyDisposalType.TRANSFERRED;
        default: return PropertyDisposalType.UNSPECIFIED;
    }
}

// Function to convert an enum value (e.g. PropertyDisposalType.PROPERTY_DISPOSAL_TYPE_RELEASED)
// back to its string representation (e.g. "PROPERTY_DISPOSAL_TYPE_RELEASED").
export function propertyDisposalTypeToString(disposalType: PropertyDisposalType): string {
    const key = PropertyDisposalType[disposalType];
    return key || "PROPERTY_DISPOSAL_TYPE_UNSPECIFIED";
}

// For crazy reason when you use our hooks to get Property it gives you not the right Enum 
// It gives you the string representation of the enum
// But type is still PropertyDisposalType
// You can just type cast it from PropertyDisposalType
export function hookPropertyDisposalTypeToString(disposalType: PropertyDisposalType): string {
    return String(disposalType);
}

// Function to convert a string (e.g. "CUSTODY_ACTION_TYPE_COLLECTED")
// to the corresponding enum value (e.g. CustodyActionType.CUSTODY_ACTION_TYPE_COLLECTED).
export function stringToCustodyActionType(value: string): CustodyActionType {
    if (!value) {
        return CustodyActionType.UNSPECIFIED;
    }

    // 1) Direct mapping from backend protobuf enum strings
    switch (value) {
        case "CUSTODY_ACTION_TYPE_UNSPECIFIED":
            return CustodyActionType.UNSPECIFIED;
        case "CUSTODY_ACTION_TYPE_COLLECTED":
            return CustodyActionType.COLLECTED;
        case "CUSTODY_ACTION_TYPE_CHECKED_IN":
            return CustodyActionType.CHECKED_IN;
        case "CUSTODY_ACTION_TYPE_CHECKED_OUT":
            return CustodyActionType.CHECKED_OUT;
        case "CUSTODY_ACTION_TYPE_TRANSFERRED":
            return CustodyActionType.TRANSFERRED;
        case "CUSTODY_ACTION_TYPE_RELEASED":
            return CustodyActionType.RELEASED;
        case "CUSTODY_ACTION_TYPE_DISPOSED":
            return CustodyActionType.DISPOSED;
        case "CUSTODY_ACTION_TYPE_LOGGED":
            return CustodyActionType.LOGGED;
        case "CUSTODY_ACTION_TYPE_MOVED":
            return CustodyActionType.MOVED;
    }

    // 2) Exact short enum key (defensive)
    if (value in CustodyActionType) {
        const resolved: unknown = (CustodyActionType as any)[value];
        if (typeof resolved === 'number') return resolved as CustodyActionType;
        if (typeof resolved === 'string' && resolved in CustodyActionType) {
            const numeric = (CustodyActionType as any)[resolved];
            if (typeof numeric === 'number') return numeric as CustodyActionType;
        }
    }

    // 3) Numeric string (e.g., "2")
    const asNumber = Number(value);
    if (!Number.isNaN(asNumber)) {
        return asNumber as CustodyActionType;
    }

    return CustodyActionType.UNSPECIFIED;
}

// Function to convert an enum value (e.g. CustodyActionType.CUSTODY_ACTION_TYPE_COLLECTED)
// back to its string representation (e.g. "CUSTODY_ACTION_TYPE_COLLECTED").
export function custodyActionTypeToString(actionType: CustodyActionType): string {
    const key = CustodyActionType[actionType];
    return key || "CUSTODY_ACTION_TYPE_UNSPECIFIED";
}

// For crazy reason when you use our hooks to get Property it gives you not the right Enum 
// It gives you the string representation of the enum
// But type is still CustodyActionType
// You can just type cast it from CustodyActionType
export function hookCustodyActionTypeToString(actionType: CustodyActionType): string {
    return String(actionType);
}

// Function to convert a NIBRS property description enum value to a readable string
export function nibrsPropertyDescriptionToString(description: NIBRSPropertyDescription): string {
    switch (description) {
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_AIRCRAFT:
            return "Aircraft";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_ALCOHOL:
            return "Alcohol";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_AUTOMOBILES:
            return "Automobiles";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_BICYCLES:
            return "Bicycles";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_BUSES:
            return "Buses";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_CLOTHES_FURS:
            return "Clothes/Furs";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_COMPUTER_HARDWARE_SOFTWARE:
            return "Computer Hardware/Software";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_CONSUMABLE_GOODS:
            return "Consumable Goods";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_CREDIT_DEBIT_CARDS:
            return "Credit/Debit Cards";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_DRUGS_NARCOTICS:
            return "Drugs/Narcotics";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_DRUG_NARCOTIC_EQUIPMENT:
            return "Drug/Narcotic Equipment";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_FARM_EQUIPMENT:
            return "Farm Equipment";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_FIREARMS:
            return "Firearms";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_GAMBLING_EQUIPMENT:
            return "Gambling Equipment";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_HEAVY_CONSTRUCTION_INDUSTRIAL_EQUIPMENT:
            return "Heavy Construction/Industrial Equipment";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_HOUSEHOLD_GOODS:
            return "Household Goods";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_JEWELRY_PRECIOUS_METALS_GEMS:
            return "Jewelry/Precious Metals/Gems";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_LIVESTOCK:
            return "Livestock";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_MERCHANDISE:
            return "Merchandise";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_MONEY:
            return "Money";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_NEGOTIABLE_INSTRUMENTS:
            return "Negotiable Instruments";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_NONNEGOTIABLE_INSTRUMENTS:
            return "Nonnegotiable Instruments";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_OFFICE_TYPE_EQUIPMENT:
            return "Office Type Equipment";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_OTHER_MOTOR_VEHICLES:
            return "Other Motor Vehicles";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_PURSES_HANDBAGS_WALLETS:
            return "Purses/Handbags/Wallets";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_RADIOS_TVS_VCRS:
            return "Radios/TVs/VCRs";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_RECORDINGS_AUDIO_VISUAL:
            return "Recordings (Audio/Visual)";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_RECREATIONAL_VEHICLES:
            return "Recreational Vehicles";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_SINGLE_OCCUPANCY_DWELLINGS:
            return "Structures - Single Occupancy Dwellings";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_OTHER_DWELLINGS:
            return "Structures - Other Dwellings";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_OTHER_COMMERCIAL_BUSINESS:
            return "Structures - Other Commercial/Business";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_INDUSTRIAL_MANUFACTURING:
            return "Structures - Industrial/Manufacturing";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_PUBLIC_COMMUNITY:
            return "Structures - Public/Community";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_STORAGE:
            return "Structures - Storage";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_OTHER:
            return "Structures - Other";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_TOOLS:
            return "Tools";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_TRUCKS:
            return "Trucks";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_VEHICLE_PARTS_ACCESSORIES:
            return "Vehicle Parts/Accessories";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_WATERCRAFT:
            return "Watercraft";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_AIRCRAFT_PARTS_ACCESSORIES:
            return "Aircraft Parts/Accessories";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_ARTISTIC_SUPPLIES_ACCESSORIES:
            return "Artistic Supplies/Accessories";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_BUILDING_MATERIALS:
            return "Building Materials";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_CAMPING_HUNTING_FISHING_EQUIPMENT:
            return "Camping/Hunting/Fishing Equipment/Supplies";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_CHEMICALS:
            return "Chemicals";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_COLLECTIONS_COLLECTIBLES:
            return "Collections/Collectibles";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_CROPS:
            return "Crops";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_DOCUMENTS_PERSONAL_OR_BUSINESS:
            return "Documents/Personal or Business";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_EXPLOSIVES:
            return "Explosives";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_FIREARM_ACCESSORIES:
            return "Firearm Accessories";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_FUEL:
            return "Fuel";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_IDENTITY_DOCUMENTS:
            return "Identity Documents";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_IDENTITY_INTANGIBLE:
            return "Identity–Intangible";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_LAW_ENFORCEMENT_EQUIPMENT:
            return "Law Enforcement Equipment";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_LAWN_YARD_GARDEN_EQUIPMENT:
            return "Lawn/Yard/Garden Equipment";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_LOGGING_EQUIPMENT:
            return "Logging Equipment";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_MEDICAL_MEDICAL_LAB_EQUIPMENT:
            return "Medical/Medical Lab Equipment";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_METALS_NON_PRECIOUS:
            return "Metals, Non-Precious";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_MUSICAL_INSTRUMENTS:
            return "Musical Instruments";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_PETS:
            return "Pets";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_PHOTOGRAPHIC_OPTICAL_EQUIPMENT:
            return "Photographic/Optical Equipment";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_PORTABLE_ELECTRONIC_COMMUNICATIONS:
            return "Portable Electronic Communications";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_RECREATIONAL_SPORTS_EQUIPMENT:
            return "Recreational/Sports Equipment";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_OTHER:
            return "Other";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_TRAILERS:
            return "Trailers";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_WATERCRAFT_EQUIPMENT_PARTS_ACCESSORIES:
            return "Watercraft Equipment/Parts/Accessories";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_WEAPONS_OTHER:
            return "Weapons–Other";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_PENDING_INVENTORY:
            return "Pending Inventory";
        case NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_BLANK:
            return "( blank )";
        default:
            return "Unknown";
    }
}

// Function to convert a string to NIBRS property description enum value
export function stringToNIBRSPropertyDescription(value: string): NIBRSPropertyDescription {
    // The enum keys are the full strings, so we can use the value directly
    if (value in NIBRSPropertyDescription) {
        return NIBRSPropertyDescription[value as keyof typeof NIBRSPropertyDescription];
    }
    return NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_UNSPECIFIED;
} 