// hooks/useAssociatedAssetQuery.ts
import { AUTH_DEBUG_ENABLED, AUTH_LOG_PREFIX } from '@/app/config/auth.config';
import { useQuery } from '@tanstack/react-query';
import { fetchAuthSession, getCurrentUser } from 'aws-amplify/auth';
import {
    Asset,
    AssetType,
    CreateAssetRequest,
    GetAssetByCognitoSubRequest,
    GetAssetByCognitoSubResponse,
} from 'proto/hero/assets/v2/assets_pb';
// Token management is now handled by Amplify after sync from cookies
import { createAsset, getAssetByCognitoSub } from '../../services/workflow/assets/endpoints';

// Define a type for the associated asset (you can adjust this to match your actual asset shape)
export interface AssociatedAsset {
    id: string;
    name: string;
    type: AssetType;
    orgId: number;
    isInternal?: boolean;
    // Add other asset properties as needed
}

// Custom error class for missing asset
export class AssetNotFoundError extends Error {
    constructor(message: string) {
        super(message);
        this.name = 'AssetNotFoundError';
    }
}

const isDevelopment = process.env.NODE_ENV === 'development';

/**
 * fetchAssociatedAsset fetches the associated asset for the authenticated user.
 * Uses Amplify's session management which works uniformly after cookie sync.
 */
export const fetchAssociatedAsset = async (): Promise<AssociatedAsset> => {
    if (AUTH_DEBUG_ENABLED) {
        console.log(`${AUTH_LOG_PREFIX.DEBUG} Fetching associated asset`);
        console.log(`${AUTH_LOG_PREFIX.DEBUG} Starting associated asset fetch operation`);
    }

    let username: string | undefined;
    let sub: string | undefined;

    // Get user from Amplify session (works for both dev and prod after cookie sync)
    try {
        if (AUTH_DEBUG_ENABLED) {
            console.log(`${AUTH_LOG_PREFIX.DEBUG} Fetching auth session for associated asset`);
        }

        const session = await fetchAuthSession();
        if (session.tokens?.accessToken) {
            const currentUser = await getCurrentUser();
            username = currentUser.username;
            sub = currentUser.userId;

            if (AUTH_DEBUG_ENABLED) {
                console.log(`${AUTH_LOG_PREFIX.DEBUG} Authentication successful from Amplify session`);
                console.log(`${AUTH_LOG_PREFIX.DEBUG} User authenticated: ${username}, sub: ${sub ? 'present' : 'missing'}`);
            }
        }
    } catch (error) {
        console.error('Could not get auth session:', error);
        if (AUTH_DEBUG_ENABLED) {
            console.log(`${AUTH_LOG_PREFIX.DEBUG} Auth session fetch failed:`, error);
        }
        throw new Error('Authentication required');
    }

    if (!username) {
        console.error('No authenticated user found');
        if (AUTH_DEBUG_ENABLED) {
            console.log(`${AUTH_LOG_PREFIX.DEBUG} No username found in session`);
        }
        throw new Error('Authentication required');
    }

    if (AUTH_DEBUG_ENABLED) {
        console.log(`${AUTH_LOG_PREFIX.DEBUG} Attempting to get asset by cognito sub`);
        console.log(`${AUTH_LOG_PREFIX.DEBUG} Looking up asset for cognito sub`);
    }

    const request: GetAssetByCognitoSubRequest = { cognitoJwtSub: sub } as GetAssetByCognitoSubRequest;
    let response: GetAssetByCognitoSubResponse;

    try {
        response = await getAssetByCognitoSub(request);

        if (AUTH_DEBUG_ENABLED) {
            console.log(`${AUTH_LOG_PREFIX.DEBUG} Asset lookup completed`);
            console.log(`${AUTH_LOG_PREFIX.DEBUG} Asset lookup successful`);
        }
    } catch (error: any) {
        if (error?.message && error.message.includes('asset not found')) {
            if (AUTH_DEBUG_ENABLED) {
                console.log(`${AUTH_LOG_PREFIX.DEBUG} No existing asset found`);
                console.log(`${AUTH_LOG_PREFIX.DEBUG} Asset not found for user`);
            }

            // In development, create a new asset if not found
            if (isDevelopment) {
                if (AUTH_DEBUG_ENABLED) {
                    console.log(`${AUTH_LOG_PREFIX.DEBUG} Creating new associated asset (dev mode)`);
                    console.log(`${AUTH_LOG_PREFIX.DEBUG} Development mode: creating new associated asset`);
                }

                const createRequest: CreateAssetRequest = {
                    asset: {
                        name: "Mona Sax",
                        type: AssetType.DISPATCHER,
                        cognitoJwtSub: sub,
                        orgId: 1
                    } as Asset,
                } as CreateAssetRequest;

                try {
                    const createResponse = await createAsset(createRequest);
                    if (createResponse.asset) {
                        if (AUTH_DEBUG_ENABLED) {
                            console.log(`${AUTH_LOG_PREFIX.DEBUG} New asset created successfully`);
                            console.log(`${AUTH_LOG_PREFIX.DEBUG} Asset creation successful`);
                        }

                        return createResponse.asset;
                    }
                } catch (createError) {
                    console.error('Asset creation failed:', createError);

                    if (AUTH_DEBUG_ENABLED) {
                        console.log(`${AUTH_LOG_PREFIX.DEBUG} Asset creation error:`, createError);
                    }

                    throw new AssetNotFoundError('Asset creation failed - please contact administrator');
                }
            }

            // In production, asset must exist - throw specific error
            throw new AssetNotFoundError('No associated asset found for your account. Please contact your administrator to complete account setup.');
        }

        console.error('Asset lookup failed:', error);

        if (AUTH_DEBUG_ENABLED) {
            console.log(`${AUTH_LOG_PREFIX.DEBUG} Asset lookup error:`, error);
        }

        throw error;
    }

    if (response.asset) {
        if (AUTH_DEBUG_ENABLED) {
            console.log(`${AUTH_LOG_PREFIX.DEBUG} Asset found`);
            console.log(`${AUTH_LOG_PREFIX.DEBUG} Returning asset for user`);
        }

        return response.asset;
    }

    throw new AssetNotFoundError('Asset not found in response');
};

/**
 * useAssociatedAssetQuery is a React Query hook that runs fetchAssociatedAsset,
 * caching the result so that the associated asset is accessible across the app.
 */
export const useAssociatedAssetQuery = (options?: {
    enabled?: boolean;
    retry?: number;
    retryDelay?: number | ((attemptIndex: number) => number);
    userId?: string; // User identifier for cache key
}) => {
    // Include userId in cache key to prevent cross-user cache pollution
    // This ensures each user has their own cached asset data
    const cacheKey = options?.userId 
        ? ['associatedAsset', options.userId] 
        : ['associatedAsset', 'unknown'];
    
    return useQuery<AssociatedAsset, Error>({
        queryKey: cacheKey,
        queryFn: fetchAssociatedAsset,
        staleTime: 30 * 1000, // Cache for 30 seconds
        gcTime: 60 * 1000, // Keep in cache for 60 seconds
        retry: options?.retry ?? 3, // Retry up to 3 times by default
        retryDelay: options?.retryDelay ?? ((attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000)), // Exponential backoff
        enabled: options?.enabled !== false, // Only run when enabled
    });
};
