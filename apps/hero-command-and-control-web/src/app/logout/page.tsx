'use client';

import { LoadingScreen } from '@/app/components/LoadingScreen';
import { useAuth } from '@/app/contexts/Auth/AuthContext';
import { useQueryClient } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

const LogoutPage = () => {
  const router = useRouter();
  const queryClient = useQueryClient();
  const { localLogout } = useAuth();
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const doLogout = async () => {
      try {
        // Clear React Query cache BEFORE logout to prevent cross-user data leakage
        queryClient.clear();

        await localLogout();
        // Successfully logged out, redirect to home
        router.push('/');
      } catch (err) {
        // Log error silently and still redirect
        // We don't want to block the user from logging out
        setError('Logout completed with errors. You may need to clear your browser cookies.');

        // Still try to clear cache even on error
        try {
          queryClient.clear();
        } catch (cacheErr) {
          console.error('[Logout] Failed to clear React Query cache:', cacheErr);
        }

        // Still redirect after a short delay
        setTimeout(() => {
          router.push('/');
        }, 3000);
      }
    };

    doLogout();
  }, [localLogout, router, queryClient]);

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900">Logging out...</h2>
          <p className="mt-2 text-sm text-red-600">{error}</p>
          <p className="mt-1 text-xs text-gray-600">Redirecting to home page...</p>
        </div>
      </div>
    );
  }

  return <LoadingScreen message="Logging out..." timeoutMs={10000} />;
};

export default LogoutPage;