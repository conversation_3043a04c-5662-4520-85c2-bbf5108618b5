'use client'
import {
  useCreateRole,
  useDeleteRole,
  useListActionsByCategory,
  useListPermissionSets,
  useListRoles,
  useUpdateRole,
} from "@/app/apis/services/perms/hooks";
import { colors } from "@/design-system/tokens";
import AddIcon from "@mui/icons-material/Add";
import CloseIcon from '@mui/icons-material/Close';
import {
  Alert,
  Box,
  Button,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Typography,
} from "@mui/material";
import { useQueryClient } from "@tanstack/react-query";
import Link from 'next/link';
import {
  Action,
  Category,
  CreateRoleRequest,
  DeleteRoleRequest,
  ListActionsByCategoryRequest,
  ListPermissionSetsRequest,
  ListRolesRequest,
  PermissionCategory,
  PermissionSet,
  Role,
  UpdateRoleRequest,
} from "proto/hero/permissions/v1/permissions_pb";
import React, { useState } from "react";
import { CreateRoleForm } from './components/CreateRoleForm';
import { RoleCard } from "./components/RoleCard";

// Define a type for the Role data structure expected by the component
type RoleData = Role;

const AdminPage: React.FC = () => {
  const queryClient = useQueryClient();
  // State for dialogs and forms visibility
  const [isCreating, setIsCreating] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedRole, setSelectedRole] = useState<RoleData | null>(null);
  const [apiError, setApiError] = useState<string | null>(null);
  const [expandedRoles, setExpandedRoles] = useState<Record<string, boolean>>({});

  // State for inline permission editing
  const [editingRoleId, setEditingRoleId] = useState<string | null>(null);
  const [editedPermissions, setEditedPermissions] = useState<PermissionCategory[]>([]);
  const [editedSelectedPermissionSetIds, setEditedSelectedPermissionSetIds] = useState<string[]>([]);
  const [inlineSaveLoading, setInlineSaveLoading] = useState(false);

  // Fetch roles
  const {
    data: rolesData,
    isLoading: isLoadingRoles,
    error: listRolesError,
  } = useListRoles(
    { excludeInternal: true } as ListRolesRequest,
    { staleTime: 5 * 60 * 1000 }
  );

  // Fetch available categories
  const { data: categoriesData, isLoading: isLoadingCategories } = useListActionsByCategory(
    {} as ListActionsByCategoryRequest,
    { staleTime: Infinity }
  );
  const availableCategories: Category[] = categoriesData?.categories || [];

  // Fetch available permission sets
  const { data: permissionSetsData, isLoading: isLoadingPermissionSets, error: listPermissionSetsError } = useListPermissionSets(
    {} as ListPermissionSetsRequest,
    { staleTime: 5 * 60 * 1000 }
  );
  const availablePermissionSets: PermissionSet[] = permissionSetsData?.permissionSets || [];

  // Mutations
  const createRoleMutation = useCreateRole({
    onSuccess: () => {
      queryClient.refetchQueries({ queryKey: ['listRoles'] });
      setIsCreating(false);
      setApiError(null);
    },
    onError: (error: Error) => {
      console.error("Error creating role:", error);
      setApiError(`Failed to create role: ${error.message}`);
    },
  });

  const updateRoleMutation = useUpdateRole({
    onSuccess: () => {
      queryClient.refetchQueries({ queryKey: ['listRoles'] });
      setEditingRoleId(null);
      setEditedPermissions([]);
      setEditedSelectedPermissionSetIds([]);
      setSelectedRole(null);
      setApiError(null);
      setInlineSaveLoading(false);
    },
    onError: (error: Error) => {
      console.error("Error updating role:", error);
      setApiError(`Failed to update role: ${error.message}`);
      setInlineSaveLoading(false);
    },
  });

  const deleteRoleMutation = useDeleteRole({
    onSuccess: () => {
      queryClient.refetchQueries({ queryKey: ['listRoles'] });
      setDeleteDialogOpen(false);
      setSelectedRole(null);
      setApiError(null);
    },
    onError: (error: Error) => {
      console.error("Error deleting role:", error);
      setApiError(`Failed to delete role: ${error.message}`);
    },
  });

  // --- Handlers ---  

  const handleCreateOpen = () => {
    setApiError(null);
    setIsCreating(true);
    setEditingRoleId(null);
    setEditDialogOpen(false);
  };

  const handleCreateCancel = () => {
    setIsCreating(false);
    setApiError(null);
  };

  const _handleOpenRoleDetailsDialog = (role: RoleData) => {
    setApiError(null);
    setSelectedRole(role);
    setEditDialogOpen(true);
    setEditingRoleId(null);
    setIsCreating(false);
  };

  const handleEditClose = () => {
    setEditDialogOpen(false);
    setSelectedRole(null);
    setApiError(null);
  };

  const handleDeleteOpen = (role: RoleData) => {
    setApiError(null);
    setSelectedRole(role);
    setDeleteDialogOpen(true);
  };

  const handleDeleteClose = () => {
    setDeleteDialogOpen(false);
    setSelectedRole(null);
    setApiError(null);
  };

  const handleDeleteConfirm = () => {
    if (selectedRole?.id) {
      setApiError(null);
      deleteRoleMutation.mutate({ roleId: selectedRole.id } as DeleteRoleRequest);
    }
  };

  const handleTogglePermissionEdit = (role: RoleData) => {
    setApiError(null);
    if (editingRoleId === role.name) {
      setEditingRoleId(null);
      setEditedPermissions([]);
      setEditedSelectedPermissionSetIds([]);
    } else {
      setEditingRoleId(role.name);
      setExpandedRoles(prev => ({ ...prev, [role.name]: true }));

      const initialPermissions: PermissionCategory[] = availableCategories.map(availableCat => {
        const roleCategory = role.categories?.find(rc => rc.name === availableCat.name);
        const canDoAllForRole = roleCategory?.canDoAll ?? false;

        const actions: Action[] = availableCat.actions.map(actionName => {
          const roleAction = !canDoAllForRole ? roleCategory?.actions?.find(ra => ra.name === actionName) : null;
          const initialCanDoAction = canDoAllForRole || (roleAction?.canDoAction ?? false);

          return {
            name: actionName,
            canDoAction: initialCanDoAction,
          } as Action;
        });

        return {
          name: availableCat.name,
          canDoAll: canDoAllForRole,
          actions: actions,
        } as PermissionCategory;
      });

      setEditedPermissions(initialPermissions);

      // Initialize edited selected permission set IDs
      const currentRolePermissionSetIds = role.permissionSets?.map(ps => ps.id) || [];
      setEditedSelectedPermissionSetIds(currentRolePermissionSetIds);

      setEditDialogOpen(false);
      setIsCreating(false);
    }
  };

  const handlePermissionChange = (
    categoryIndex: number,
    actionIndex: number | 'all',
    value: boolean
  ) => {
    setEditedPermissions(currentCategories => {
      const updatedCategories = JSON.parse(JSON.stringify(currentCategories)) as PermissionCategory[];
      if (!updatedCategories[categoryIndex]) return updatedCategories;
      const category = updatedCategories[categoryIndex];

      if (actionIndex === 'all') {
        category.canDoAll = value;
        if (value) {
          category.actions?.forEach(a => a.canDoAction = true);
        }
      } else {
        if (category.actions && category.actions[actionIndex]) {
          const action = category.actions[actionIndex];
          action.canDoAction = value;
          if (!value) {
            category.canDoAll = false;
          } else {
            const allActionsTrue = category.actions.every(a => a.canDoAction);
            if (allActionsTrue) {
              category.canDoAll = true;
            }
          }
        }
      }
      return updatedCategories;
    });
    if (editingRoleId) {
      setInlineSaveLoading(false);
    }
  };

  const handleSelectedPermissionSetsChange = (selectedIds: string[]) => {
    setEditedSelectedPermissionSetIds(selectedIds);
    if (editingRoleId) {
      setInlineSaveLoading(false);
    }
  };

  const handleSavePermissions = (roleId: string, roleName: string) => {
    setApiError(null);
    setInlineSaveLoading(true);

    const categoriesForSave = editedPermissions.map(category => {
      if (category.canDoAll) { return { ...category, actions: [] }; }
      return category;
    });

    // Get the full PermissionSet objects for the selected IDs
    const selectedPermissionSetObjects = availablePermissionSets.filter(ps =>
      editedSelectedPermissionSetIds.includes(ps.id)
    );

    updateRoleMutation.mutate({
      role: {
        id: roleId,
        name: roleName,
        categories: categoriesForSave,
        permissionSets: selectedPermissionSetObjects,
      } as Role,
    } as UpdateRoleRequest);
  };

  const handleCancelPermissionEdit = () => {
    setEditingRoleId(null);
    setEditedPermissions([]);
    setEditedSelectedPermissionSetIds([]);
    setApiError(null);
    setInlineSaveLoading(false);
  };

  const handleToggleRoleExpansion = (roleName: string) => {
    setExpandedRoles(prev => ({ ...prev, [roleName]: !prev[roleName] }));
  };

  const handleSaveNewRole = (roleData: Role) => {
    setApiError(null);
    const createPayload = {
      role: roleData,
    } as CreateRoleRequest;
    createRoleMutation.mutate(createPayload);
  };

  const roles: RoleData[] = rolesData?.roles || [];
  const isMutating =
    createRoleMutation.isPending ||
    updateRoleMutation.isPending ||
    deleteRoleMutation.isPending ||
    isLoadingPermissionSets;

  const inlineCreateLoading = createRoleMutation.isPending;
  const dialogDeleteLoading = deleteRoleMutation.isPending && deleteDialogOpen;

  return (
    <Box sx={{ p: 3, width: "100%", display: 'flex', flexDirection: 'column', height: 'calc(100vh - 64px)' }}>
      {/* Header */}
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: 3,
        }}
      >
        <Typography variant="h4" sx={{ color: colors.grey[900] }}>
          Admin - Role Management
        </Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Link href="/admin/permissionset" passHref>
            <Button
              variant="outlined"
              disabled={isMutating || isCreating || editDialogOpen || deleteDialogOpen || !!editingRoleId}
            >
              Manage Permission Sets
            </Button>
          </Link>
          <Button
            variant="contained"
            startIcon={isCreating ? <CloseIcon /> : <AddIcon />}
            onClick={isCreating ? handleCreateCancel : handleCreateOpen}
            color={isCreating ? "inherit" : "primary"}
            disabled={editDialogOpen || !!editingRoleId}
          >
            {isCreating ? "Cancel Creation" : "Create New Role"}
          </Button>
        </Box>
      </Box>

      {/* Scrollable Content Area */}
      <Box sx={{ flexGrow: 1, overflowY: 'auto', pr: 1 }}>

        {/* Global Errors */}
        {listRolesError && (
          <Alert severity="error" sx={{ mb: 2 }}>
            Error loading roles: {listRolesError.message}
          </Alert>
        )}
        {listPermissionSetsError && (
          <Alert severity="error" sx={{ mb: 2 }}>
            Error loading permission sets: {listPermissionSetsError.message}
          </Alert>
        )}
        {/* Display API errors NOT specific to a dialog/inline form */}
        {apiError && !editDialogOpen && !deleteDialogOpen && !isCreating && !editingRoleId && (
          <Alert severity="error" sx={{ mb: 2 }} onClose={() => setApiError(null)}>
            {apiError}
          </Alert>
        )}

        {/* Inline Create Role Form */}
        {isCreating && (
          <CreateRoleForm
            availableCategories={availableCategories}
            onSave={handleSaveNewRole} // Use the dedicated create handler
            onCancel={handleCreateCancel}
            isLoading={inlineCreateLoading}
            isLoadingCategories={isLoadingCategories}
            availablePermissionSets={availablePermissionSets}
            isLoadingPermissionSets={isLoadingPermissionSets}
            apiError={apiError} // Pass down the API error state
            onClearApiError={() => setApiError(null)} // Allow form to clear the error
          />
        )}

        {/* Roles List/Loading Indicator */}
        {isLoadingRoles ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : roles.length === 0 && !isCreating ? (
          <Typography sx={{ textAlign: 'center', p: 3 }}>
            No roles found. Click &quot;Create New Role&quot; to add one.
          </Typography>
        ) : (
          // Map over roles and render RoleCard for each
          roles.map((role) => {
            const isEditingThisRolePermissions = editingRoleId === role.name;
            const isExpanded = expandedRoles[role.name] ?? false;

            return (
              <RoleCard
                key={role.name}
                role={role}
                isEditingPermissions={isEditingThisRolePermissions}
                isExpanded={isExpanded}
                editedPermissions={isEditingThisRolePermissions ? editedPermissions : []}
                availableCategories={availableCategories}
                onToggleExpansion={handleToggleRoleExpansion}
                onTogglePermissionEdit={handleTogglePermissionEdit}
                onOpenDeleteDialog={handleDeleteOpen}
                onPermissionChange={handlePermissionChange}
                onSavePermissions={handleSavePermissions}
                onCancelPermissionEdit={handleCancelPermissionEdit}
                isLoading={isEditingThisRolePermissions && inlineSaveLoading}
                isGloballyDisabled={isMutating || isCreating || editDialogOpen || isLoadingCategories || isLoadingPermissionSets}
                apiError={isEditingThisRolePermissions ? apiError : null}
                availablePermissionSets={availablePermissionSets}
                editedSelectedPermissionSetIds={isEditingThisRolePermissions ? editedSelectedPermissionSetIds : []}
                onSelectedPermissionSetsChange={handleSelectedPermissionSetsChange}
              />
            );
          })
        )}

      </Box> {/* Close Scrollable Content Area */}

      {/* Edit Role Dialog (Kept for potential future use, e.g., editing name/description) */}
      <Dialog
        open={editDialogOpen}
        onClose={handleEditClose}
        fullWidth
        maxWidth="sm"
      >
        <DialogTitle>
          Edit Role: {selectedRole?.name || ''}
        </DialogTitle>
        <DialogContent>
          {/* Show Edit API error within dialog */}
          {apiError && editDialogOpen && (
            <Alert severity="error" sx={{ mb: 2 }} onClose={() => setApiError(null)}>
              {apiError}
            </Alert>
          )}

          {/* Content currently removed as editing happens inline */}
          <Typography color="text.secondary" sx={{ mt: 2, textAlign: 'center', fontStyle: 'italic' }}>
            Role name and permissions are edited inline within each role card.
          </Typography>

        </DialogContent>
        <DialogActions sx={{ p: '16px 24px' }}>
          <Button onClick={handleEditClose}> Close </Button> {/* Changed from Cancel */}
          {/* Save button removed as editing happens inline */}
          {/* <Button
             variant="contained"
             onClick={handleSaveRoleDetails} // This needs to be adjusted if dialog handles saves again
             disabled={dialogEditLoading || !roleName || isLoadingCategories}
           >
             {dialogEditLoading ? <CircularProgress size={24} /> : "Save Changes"}
           </Button> */}
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title">Confirm Deletion</DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-description">
            Are you sure you want to delete the role &quot;{selectedRole?.name}&quot;?
            This action cannot be undone.
          </DialogContentText>
          {/* Show Delete API error within dialog */}
          {apiError && deleteDialogOpen && (
            <Alert severity="error" sx={{ mt: 2 }} onClose={() => setApiError(null)}>
              {apiError}
            </Alert>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteClose} disabled={dialogDeleteLoading}>Cancel</Button>
          <Button
            onClick={handleDeleteConfirm}
            color="error"
            variant="contained"
            disabled={dialogDeleteLoading}
            autoFocus
          >
            {dialogDeleteLoading ? <CircularProgress size={24} /> : "Delete"}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default AdminPage;