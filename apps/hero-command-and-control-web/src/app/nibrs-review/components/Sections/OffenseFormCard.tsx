import { Button } from "@/design-system/components/Button";
import { Checkbox } from "@/design-system/components/Checkbox";
import { Dropdown } from "@/design-system/components/Dropdown";
import { Radio } from "@/design-system/components/Radio";
import {
  DropdownOption,
  InputType,
  TextInput,
} from "@/design-system/components/TextInput";
import { Typography } from "@/design-system/components/Typography";
import { colors } from "@/design-system/tokens";
import AddIcon from "@mui/icons-material/Add";
import DeleteIcon from "@mui/icons-material/Delete";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { Box, Collapse, IconButton } from "@mui/material";
import React, { useEffect, useRef, useState } from "react";
import { OffenseData } from "./OffenseSearchModal";

// Import constants
import {
  ADDITIONAL_JUSTIFIABLE_HOMICIDE_CIRCUMSTANCES_OPTIONS,
  AGGRAVATED_ASSAULT_HOMICIDE_CIRCUMSTANCES_OPTIONS,
  BIAS_MOTIVATION_OPTIONS,
  CRIMINAL_ACTIVITY_AND_GANG_OPTIONS,
  METHOD_OF_ENTRY_OPTIONS,
  OFFENSE_WAS_OPTIONS,
  WEAPON_TYPE_OPTIONS,
} from "./constants";

interface OffenseFormData {
  offenseWas: string;
  cargoTheftRelated: boolean;
  offenseSpecialCircumstance: string;
  justifiableHomicideCircumstance: string;
  biasMotivation: string;
  numberOfPremisesEntered: string;
  methodOfEntry: string;
  offenderSuspectedOfUsing: {
    alcohol: boolean;
    computerEquipment: boolean;
    drugsNarcotics: boolean;
  };
  attributes: {
    domesticAbuse: boolean;
    gangRelated: boolean;
    gamblingRelated: boolean;
  };
  weapons: Array<{
    id: string;
    weaponType: string;
    isAutomatic: boolean;
  }>;
  criminalActivityAndGang: DropdownOption[];
}

interface OffenseFormCardProps {
  offense: OffenseData;
  offenseId: string;
  onSave: (offenseId: string, data: OffenseFormData) => void;
  onDelete: (offenseId: string) => void;
  onSaveStatusChange?: (status: {
    isSaving: boolean;
    hasUnsavedChanges: boolean;
    source: string;
  }) => void;
  readOnly?: boolean;
  initialData?: Partial<OffenseFormData>;
}

// Helper function to convert text to title case
const toTitleCase = (str: string): string => {
  return str
    .toLowerCase()
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
};

const OffenseFormCard: React.FC<OffenseFormCardProps> = ({
  offense,
  offenseId,
  onSave,
  onDelete,
  onSaveStatusChange,
  readOnly = false,
  initialData,
}) => {
  const [isExpanded, setIsExpanded] = useState(true);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  const [formData, setFormData] = useState<OffenseFormData>({
    offenseWas: "completed",
    cargoTheftRelated: false,
    offenseSpecialCircumstance: "",
    justifiableHomicideCircumstance: "",
    biasMotivation: "",
    numberOfPremisesEntered: "",
    methodOfEntry: "",
    offenderSuspectedOfUsing: {
      alcohol: false,
      computerEquipment: false,
      drugsNarcotics: false,
    },
    attributes: {
      domesticAbuse: false,
      gangRelated: false,
      gamblingRelated: false,
    },
    weapons: [],
    criminalActivityAndGang: [],
    ...initialData,
  });

  const currentFormDataRef = useRef(formData);

  // Update refs when state changes
  useEffect(() => {
    currentFormDataRef.current = formData;
  }, [formData]);

  // Use a ref to track previous status like IncidentDetailsCard
  const lastStatusRef = useRef<{
    isSaving: boolean;
    hasUnsavedChanges: boolean;
  } | null>(null);

  // Notify parent on save/dirty changes like IncidentDetailsCard
  useEffect(() => {
    if (!onSaveStatusChange) return;

    const currentStatus = { isSaving, hasUnsavedChanges };
    const lastStatus = lastStatusRef.current;
    const changed =
      !lastStatus ||
      lastStatus.isSaving !== currentStatus.isSaving ||
      lastStatus.hasUnsavedChanges !== currentStatus.hasUnsavedChanges;

    if (changed) {
      lastStatusRef.current = { ...currentStatus };
      onSaveStatusChange({ ...currentStatus, source: `offense_${offenseId}` });
    }
  }, [isSaving, hasUnsavedChanges, onSaveStatusChange, offenseId]);

  // Save offense immediately when created
  useEffect(() => {
    if (!initialData) {
      // This is a new offense, save immediately
      saveOffenseData();
    }
  }, []); // Only run on mount

  // Handler for criminal activity and gang multi-select
  const handleCriminalActivityAndGangChange = (newItems: DropdownOption[]) => {
    let processedItems = newItems;
    const noneUnknownOption = newItems.find(
      (item) => item.value === "noneUnknown"
    );
    const previousItems = formData.criminalActivityAndGang;
    const hadNoneUnknown = previousItems.find(
      (item) => item.value === "noneUnknown"
    );

    // If "None/Unknown" was just added
    if (noneUnknownOption && !hadNoneUnknown) {
      // Keep only "None/Unknown"
      processedItems = [noneUnknownOption];
    }
    // If "None/Unknown" was already selected and user added something else
    else if (hadNoneUnknown && newItems.length > previousItems.length) {
      // Remove "None/Unknown" and keep the new selection
      processedItems = newItems.filter((item) => item.value !== "noneUnknown");
    }

    // If "None/Unknown" is not selected, enforce maximum of 3 selections
    if (!processedItems.find((item) => item.value === "noneUnknown")) {
      processedItems = processedItems.slice(0, 3);
    }

    setFormData((prev) => {
      const updated = { ...prev, criminalActivityAndGang: processedItems };
      currentFormDataRef.current = updated;
      return updated;
    });
    setHasUnsavedChanges(true);
    // Save immediately on change
    setTimeout(() => saveOffenseData(), 0);
  };

  const saveOffenseData = () => {
    if (isSaving || readOnly) return;

    setIsSaving(true);
    setHasUnsavedChanges(false);

    const currentData = currentFormDataRef.current;
    onSave(offenseId, currentData);

    // Simulate save completion
    setTimeout(() => {
      setIsSaving(false);
    }, 300);
  };

  const handleBlur = () => {
    if (hasUnsavedChanges && !readOnly) {
      saveOffenseData();
    }
  };

  const handleFormChange = (field: string, value: any) => {
    setFormData((prev) => {
      const keys = field.split(".");
      if (keys.length === 1) {
        const updated = { ...prev, [field]: value };
        currentFormDataRef.current = updated;
        return updated;
      } else if (keys.length === 2) {
        const parentKey = keys[0] as keyof OffenseFormData;
        const parent = prev[parentKey];
        if (
          typeof parent === "object" &&
          parent !== null &&
          !Array.isArray(parent)
        ) {
          const updated = {
            ...prev,
            [parentKey]: {
              ...parent,
              [keys[1]]: value,
            },
          };
          currentFormDataRef.current = updated;
          return updated;
        }
      }
      return prev;
    });
    setHasUnsavedChanges(true);
  };

  const handleDropdownChange = (field: string) => (value: string | null) => {
    if (value !== null) {
      setFormData((prev) => {
        const updated = { ...prev, [field]: value };
        currentFormDataRef.current = updated;
        return updated;
      });
      setHasUnsavedChanges(true);
      // Save immediately on dropdown selection
      setTimeout(() => saveOffenseData(), 0);
    }
  };

  // Handle checkbox changes with proper signature
  const handleCheckboxChange =
    (field: string) => (e: React.ChangeEvent<HTMLInputElement>) => {
      const checked = e.target.checked;
      handleFormChange(field, checked);
      // Save immediately on checkbox change
      setTimeout(() => saveOffenseData(), 0);
    };

  // Handle radio changes for boolean values
  const handleRadioChange =
    (field: string) => (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      handleFormChange(field, value === "true");
      // Save immediately on radio change
      setTimeout(() => saveOffenseData(), 0);
    };

  // Handle weapon dropdown changes
  const handleWeaponDropdownChange =
    (weaponId: string, field: string) => (value: string | null) => {
      setFormData((prev) => {
        const updated = {
          ...prev,
          weapons: prev.weapons.map((weapon) =>
            weapon.id === weaponId
              ? { ...weapon, [field]: value || "" }
              : weapon
          ),
        };
        currentFormDataRef.current = updated;
        return updated;
      });
      setHasUnsavedChanges(true);
      // Save immediately on weapon dropdown change
      setTimeout(() => saveOffenseData(), 0);
    };

  const handleWeaponChange = (weaponId: string, field: string, value: any) => {
    setFormData((prev) => {
      const updated = {
        ...prev,
        weapons: prev.weapons.map((weapon) =>
          weapon.id === weaponId ? { ...weapon, [field]: value } : weapon
        ),
      };
      currentFormDataRef.current = updated;
      return updated;
    });
    setHasUnsavedChanges(true);
    // Save immediately on weapon change
    setTimeout(() => saveOffenseData(), 0);
  };

  const addWeapon = () => {
    const newWeaponId = `weapon_${Date.now()}`;
    setFormData((prev) => {
      const updated = {
        ...prev,
        weapons: [
          ...prev.weapons,
          { id: newWeaponId, weaponType: "", isAutomatic: false },
        ],
      };
      currentFormDataRef.current = updated;
      return updated;
    });
    setHasUnsavedChanges(true);
    setTimeout(() => saveOffenseData(), 0);
  };

  const removeWeapon = (weaponId: string) => {
    setFormData((prev) => {
      const updated = {
        ...prev,
        weapons: prev.weapons.filter((weapon) => weapon.id !== weaponId),
      };
      currentFormDataRef.current = updated;
      return updated;
    });
    setHasUnsavedChanges(true);
    setTimeout(() => saveOffenseData(), 0);
  };

  return (
    <Box
      sx={{
        display: "flex",
        width: "100%",
        flexDirection: "column",
        alignItems: "flex-start",
        borderRadius: "12px",
        border: `1px solid ${colors.grey[200]}`,
        background: "#FFF",
        overflow: "hidden",
        boxShadow: "0px 0px 16px 0px rgba(0, 0, 0, 0.04)",
        mb: 3,
      }}
    >
      {/* Header */}
      <Box
        sx={{
          display: "flex",
          width: "100%",
          justifyContent: "space-between",
          alignItems: "center",
          padding: "16px 24px",
          borderBottom: `1px solid ${colors.grey[200]}`,
          backgroundColor: colors.grey[50],
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
          <Typography style="h3" color={colors.grey[900]}>
            {offense.citation} {toTitleCase(offense.literal)}
          </Typography>
        </Box>

        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          {!readOnly && (
            <IconButton
              onClick={() => onDelete(offenseId)}
              size="small"
              sx={{
                color: colors.grey[600],
                "&:hover": {
                  bgcolor: colors.grey[100],
                },
              }}
            >
              <DeleteIcon />
            </IconButton>
          )}
          <IconButton
            onClick={() => setIsExpanded(!isExpanded)}
            size="small"
            sx={{
              color: colors.grey[600],
              "&:hover": {
                bgcolor: colors.grey[100],
              },
            }}
          >
            {isExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
          </IconButton>
        </Box>
      </Box>

      {/* Form Content */}
      <Collapse in={isExpanded} sx={{ width: "100%" }}>
        <Box sx={{ padding: "24px" }}>
          {/* Crime & Motivation Section */}
          <Box sx={{ mb: 4 }}>
            <Box sx={{ mb: 3 }}>
              <Typography style="caps1" color={colors.grey[700]}>
                Crime & Motivation
              </Typography>
            </Box>

            {/* NIBRS Offense Code (read-only) */}
            <Box sx={{ display: "flex", gap: 2, mb: 3 }}>
              <Box sx={{ width: "50%" }}>
                <TextInput
                  title="NIBRS Offense Code"
                  value={`${offense.citation} - ${toTitleCase(
                    offense.literal
                  )}`}
                  readOnly={true}
                />
              </Box>
              <Box sx={{ width: "50%" }}>
                <Dropdown
                  title="Offense was"
                  placeholder="Select status"
                  options={OFFENSE_WAS_OPTIONS}
                  value={formData.offenseWas}
                  onChange={handleDropdownChange("offenseWas")}
                  readOnly={readOnly}
                />
              </Box>
            </Box>

            {/* Cargo Theft Related */}
            <Box sx={{ mb: 3 }}>
              <Box sx={{ mb: 2 }}>
                <Typography style="body4" color={colors.grey[500]}>
                  Cargo Theft Related
                </Typography>
              </Box>
              <Box sx={{ display: "flex", gap: 3 }}>
                <Radio
                  name={`cargoTheft_${offenseId}`}
                  value="true"
                  checked={formData.cargoTheftRelated === true}
                  onChange={handleRadioChange("cargoTheftRelated")}
                  label="Yes"
                  readOnly={readOnly}
                />
                <Radio
                  name={`cargoTheft_${offenseId}`}
                  value="false"
                  checked={formData.cargoTheftRelated === false}
                  onChange={handleRadioChange("cargoTheftRelated")}
                  label="No"
                  readOnly={readOnly}
                />
              </Box>
            </Box>

            {/* Special Circumstances */}
            <Box sx={{ display: "flex", gap: 2, mb: 3 }}>
              <Box sx={{ width: "50%" }}>
                <Dropdown
                  title="Aggravated Assault/Homicide Circumstances"
                  placeholder="Select circumstance"
                  options={AGGRAVATED_ASSAULT_HOMICIDE_CIRCUMSTANCES_OPTIONS}
                  value={formData.offenseSpecialCircumstance}
                  onChange={handleDropdownChange("offenseSpecialCircumstance")}
                  readOnly={readOnly}
                  enableSearch
                />
              </Box>
              <Box sx={{ width: "50%" }}>
                <Dropdown
                  title="Additional Justifiable Homicide Circumstances"
                  placeholder="Select circumstance"
                  options={
                    ADDITIONAL_JUSTIFIABLE_HOMICIDE_CIRCUMSTANCES_OPTIONS
                  }
                  value={formData.justifiableHomicideCircumstance}
                  onChange={handleDropdownChange(
                    "justifiableHomicideCircumstance"
                  )}
                  readOnly={readOnly}
                  enableSearch
                />
              </Box>
            </Box>

            {/* Bias Motivation */}
            <Box sx={{ display: "flex", gap: 2, mb: 3 }}>
              <Box sx={{ width: "50%" }}>
                <Dropdown
                  title="Bias Motivation"
                  placeholder="Select bias motivation"
                  options={BIAS_MOTIVATION_OPTIONS}
                  value={formData.biasMotivation}
                  onChange={handleDropdownChange("biasMotivation")}
                  readOnly={readOnly}
                  enableSearch
                />
              </Box>
              <Box sx={{ width: "50%" }}>
                <Dropdown
                  title="Method Of Entry"
                  placeholder="Select Method"
                  options={METHOD_OF_ENTRY_OPTIONS}
                  value={formData.methodOfEntry}
                  onChange={handleDropdownChange("methodOfEntry")}
                  readOnly={readOnly}
                />
              </Box>
            </Box>

            {/* Number of Premises Entered */}
            <Box sx={{ mb: 3 }}>
              <TextInput
                title="Number of Premises Entered"
                placeholder="Enter number"
                value={formData.numberOfPremisesEntered}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  handleFormChange("numberOfPremisesEntered", e.target.value)
                }
                onBlur={handleBlur}
                readOnly={readOnly}
              />
            </Box>

            {/* Offender Suspected of Using */}
            <Box sx={{ mb: 3 }}>
              <Box sx={{ mb: 2 }}>
                <Typography style="body4" color={colors.grey[500]}>
                  Offender Suspected of Using
                </Typography>
              </Box>
              <Box sx={{ display: "flex", gap: 3, flexWrap: "wrap" }}>
                <Checkbox
                  label="Alcohol"
                  checked={formData.offenderSuspectedOfUsing.alcohol}
                  onChange={handleCheckboxChange(
                    "offenderSuspectedOfUsing.alcohol"
                  )}
                  readOnly={readOnly}
                />
                <Checkbox
                  label="Computer Equipment"
                  checked={formData.offenderSuspectedOfUsing.computerEquipment}
                  onChange={handleCheckboxChange(
                    "offenderSuspectedOfUsing.computerEquipment"
                  )}
                  readOnly={readOnly}
                />
                <Checkbox
                  label="Drugs/Narcotics"
                  checked={formData.offenderSuspectedOfUsing.drugsNarcotics}
                  onChange={handleCheckboxChange(
                    "offenderSuspectedOfUsing.drugsNarcotics"
                  )}
                  readOnly={readOnly}
                />
              </Box>
            </Box>

            {/* Attributes */}
            <Box sx={{ mb: 3 }}>
              <Box sx={{ mb: 2 }}>
                <Typography style="body4" color={colors.grey[500]}>
                  Attributes
                </Typography>
              </Box>
              <Box sx={{ display: "flex", gap: 3, flexWrap: "wrap" }}>
                <Checkbox
                  label="Domestic Abuse"
                  checked={formData.attributes.domesticAbuse}
                  onChange={handleCheckboxChange("attributes.domesticAbuse")}
                  readOnly={readOnly}
                />
                <Checkbox
                  label="Gang Related"
                  checked={formData.attributes.gangRelated}
                  onChange={handleCheckboxChange("attributes.gangRelated")}
                  readOnly={readOnly}
                />
                <Checkbox
                  label="Gambling Related"
                  checked={formData.attributes.gamblingRelated}
                  onChange={handleCheckboxChange("attributes.gamblingRelated")}
                  readOnly={readOnly}
                />
              </Box>
            </Box>
          </Box>

          {/* Weapons / Force Involved Section */}
          <Box sx={{ mb: 4 }}>
            <Box sx={{ mb: 3 }}>
              <Typography style="caps1" color={colors.grey[700]}>
                Weapons / Force Involved
              </Typography>
            </Box>
            <Box sx={{ mb: 3 }}>
              <Typography style="body4" color={colors.grey[500]}>
                Add up to 3
              </Typography>
            </Box>

            {formData.weapons.length > 0 && (
              <>
                {formData.weapons.map((weapon) => (
                  <Box key={weapon.id} sx={{ mb: 3 }}>
                    <Box sx={{ display: "flex", alignItems: "flex-end", gap: 2 }}>
                      <Box sx={{ flex: 1 }}>
                        <Dropdown
                          title="Weapon Type"
                          placeholder="Select weapon type"
                          options={WEAPON_TYPE_OPTIONS}
                          value={weapon.weaponType}
                          onChange={handleWeaponDropdownChange(
                            weapon.id,
                            "weaponType"
                          )}
                          readOnly={readOnly}
                          enableSearch
                        />
                      </Box>
                      {!readOnly && (
                        <Box sx={{ flexShrink: 0 }}>
                          <Button
                            label=""
                            rightIcon={<DeleteIcon />}
                            color="grey"
                            size="large"
                            prominence={false}
                            onClick={() => removeWeapon(weapon.id)}
                          />
                        </Box>
                      )}
                    </Box>
                    <Box sx={{ mt: 2 }}>
                      <Checkbox
                        label="Firearm is automatic"
                        checked={weapon.isAutomatic}
                        onChange={(e) => {
                          handleWeaponChange(
                            weapon.id,
                            "isAutomatic",
                            e.target.checked
                          );
                        }}
                        readOnly={readOnly}
                      />
                    </Box>
                  </Box>
                ))}
              </>
            )}

            {formData.weapons.length < 3 && !readOnly && (
              <Button
                label="Add Weapon"
                style="ghost"
                color="blue"
                size="small"
                leftIcon={<AddIcon />}
                onClick={addWeapon}
              />
            )}
          </Box>

          {/* Criminal Activity & Gang Information Section */}
          <Box sx={{ mb: 4 }}>
            <Box sx={{ mb: 3 }}>
              <Typography style="caps1" color={colors.grey[700]}>
                Type of Criminal Activity
              </Typography>
            </Box>
            <Box sx={{ mb: 3 }}>
              <Typography style="body4" color={colors.grey[500]}>
                Select up to 3
              </Typography>
            </Box>

            <TextInput
              type={InputType.Dropdown}
              isMultiSelect={true}
              options={CRIMINAL_ACTIVITY_AND_GANG_OPTIONS}
              selectedItems={formData.criminalActivityAndGang}
              onChangeSelectedItems={handleCriminalActivityAndGangChange}
              readOnly={readOnly}
              hideSelectedFromOptions={true}
            />
          </Box>
        </Box>
      </Collapse>
    </Box>
  );
};

export default OffenseFormCard;
