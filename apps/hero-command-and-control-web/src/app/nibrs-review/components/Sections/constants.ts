import { DropdownOption } from "@/design-system/components/Dropdown";

const PREFIX = "RELATION_TYPE_VICTIM_OFFENDER_";

// Dropdown options with constant value
export const RELATIONSHIP_OPTIONS: DropdownOption[] = [
  { value: "SE", label: "Victim Was Spouse" },
  { value: "CS", label: "Victim Was Common-Law Spouse" },
  { value: "PA", label: "Victim Was Parent" },
  { value: "SB", label: "Victim Was Sibling" },
  { value: "CH", label: "Victim Was Child" },
  { value: "GP", label: "Victim Was Grandparent" },
  { value: "GC", label: "Victim Was Grandchild" },
  { value: "IL", label: "Victim Was In-law" },
  { value: "SP", label: "Victim Was Step-parent" },
  { value: "SC", label: "Victim Was Step-child" },
  { value: "SS", label: "<PERSON>tim Was Step-sibling" },
  { value: "OF", label: "<PERSON><PERSON><PERSON> Was Other Family Member" },
  { value: "AQ", label: "Victim Was Acquaintance" },
  { value: "FR", label: "Victim Was Friend" },
  { value: "NE", label: "Victim Was Neighbor" },
  { value: "BE", label: "Victim Was Babysitter" },
  { value: "BG", label: "Victim was Boyfriend/Girlfriend" },
  { value: "CG", label: "Victim Was Child of Boyfriend/Girlfriend" },
  { value: "XR", label: "Victim Was Ex-Relationship" },
  { value: "XS", label: "Victim Was Ex-Spouse" },
  { value: "EE", label: "Victim Was Employee" },
  { value: "ER", label: "Victim Was Employer" },
  { value: "OK", label: "Victim Was Otherwise Known" },
  { value: "RU", label: "Relationship Unknown" },
  { value: "ST", label: "Victim Was Stranger" },
  { value: "VO", label: "Victim Was Offender" },
].map((o) => ({
  value: `${PREFIX}${o.value}`,
  label: `${o.value} - ${o.label}`,
}));

// Offense Form Field Options
export const OFFENSE_WAS_OPTIONS: DropdownOption[] = [
  { value: "completed", label: "Completed" },
  { value: "attempted", label: "Attempted" },
];

export const AGGRAVATED_ASSAULT_HOMICIDE_CIRCUMSTANCES_OPTIONS: DropdownOption[] =
  [
    { value: "argument", label: "Argument" },
    {
      value: "assaultOnLawEnforcement",
      label: "Assault on Law Enforcement Officer",
    },
    { value: "drugDealing", label: "Drug Dealing" },
    { value: "gangland", label: "Gangland (Organized Crime Involvement)" },
    { value: "juvenileGang", label: "Juvenile Gang" },
    { value: "domesticViolence", label: "Domestic Violence" },
    {
      value: "mercyKilling",
      label: "Mercy Killing (Not applicable to Aggravated Assault)",
    },
    { value: "otherFelony", label: "Other Felony Involved" },
    { value: "otherCircumstances", label: "Other Circumstances" },
    { value: "unknownCircumstances", label: "Unknown Circumstances" },
    { value: "childPlayingWeapon", label: "Child Playing With Weapon" },
    { value: "gunCleaningAccident", label: "Gun-Cleaning Accident" },
    { value: "huntingAccident", label: "Hunting Accident" },
    {
      value: "otherNegligentWeapon",
      label: "Other Negligent Weapon Handling",
    },
    { value: "otherNegligentKilling", label: "Other Negligent Killing" },
    {
      value: "criminalKilledByCitizen",
      label: "Criminal Killed by Private Citizen",
    },
    {
      value: "criminalKilledByPolice",
      label: "Criminal Killed by Police Officer",
    },
  ];

export const ADDITIONAL_JUSTIFIABLE_HOMICIDE_CIRCUMSTANCES_OPTIONS: DropdownOption[] =
  [
    {
      value: "criminalAttackedOfficer",
      label:
        "Criminal Attacked Police Officer and That Officer Killed Criminal",
    },
    {
      value: "criminalAttackedOfficerKilledByAnother",
      label:
        "Criminal Attacked Police Officer and Criminal Killed by Another Police Officer",
    },
    {
      value: "criminalAttackedCivilian",
      label: "Criminal Attacked a Civilian",
    },
    {
      value: "criminalAttemptedFlight",
      label: "Criminal Attempted Flight from a Crime",
    },
    {
      value: "criminalKilledInCommission",
      label: "Criminal Killed in Commission of a Crime",
    },
    { value: "criminalResistedArrest", label: "Criminal Resisted Arrest" },
    {
      value: "unableToDetermine",
      label: "Unable to Determine/Not Enough Information",
    },
  ];

export const BIAS_MOTIVATION_OPTIONS: DropdownOption[] = [
  { value: "antiWhite", label: "Anti-White" },
  { value: "antiBlack", label: "Anti-Black or African American" },
  {
    value: "antiAmericanIndian",
    label: "Anti-American Indian or Alaska Native",
  },
  { value: "antiAsian", label: "Anti-Asian" },
  { value: "antiMultipleRaces", label: "Anti-Multiple Races, Group" },
  {
    value: "antiNativeHawaiian",
    label: "Anti-Native Hawaiian or Other Pacific Islander",
  },
  { value: "antiArab", label: "Anti-Arab" },
  { value: "antiHispanic", label: "Anti-Hispanic or Latino" },
  { value: "antiOtherRace", label: "Anti-Other Race/Ethnicity/Ancestry" },
  { value: "antiJewish", label: "Anti-Jewish" },
  { value: "antiCatholic", label: "Anti-Catholic" },
  { value: "antiProtestant", label: "Anti-Protestant" },
  { value: "antiIslamic", label: "Anti-Islamic (Muslim)" },
  { value: "antiOtherReligion", label: "Anti-Other Religion" },
  { value: "antiMultipleReligions", label: "Anti-Multiple Religions, Group" },
  { value: "antiAtheism", label: "Anti-Atheism/Agnosticism" },
  { value: "antiChurchJesusChrist", label: "Anti-Church of Jesus Christ" },
  { value: "antiJehovahWitness", label: "Anti-Jehovah's Witness" },
  {
    value: "antiEasternOrthodox",
    label: "Anti-Eastern Orthodox (Russian, Greek, Other)",
  },
  { value: "antiOtherChristian", label: "Anti-Other Christian" },
  { value: "antiBuddhist", label: "Anti-Buddhist" },
  { value: "antiHindu", label: "Anti-Hindu" },
  { value: "antiSikh", label: "Anti-Sikh" },
  { value: "antiGay", label: "Anti-Gay" },
  { value: "antiLesbian", label: "Anti-Lesbian" },
  { value: "antiLgbt", label: "Anti-Lesbian, Gay, Bisexual, or Transgender" },
  { value: "antiHeterosexual", label: "Anti-Heterosexual" },
  { value: "antiBisexual", label: "Anti-Bisexual" },
  { value: "antiPhysicalDisability", label: "Anti-Physical Disability" },
  { value: "antiMentalDisability", label: "Anti-Mental Disability" },
  { value: "antiMale", label: "Anti-Male" },
  { value: "antiFemale", label: "Anti-Female" },
  { value: "antiTransgender", label: "Anti-Transgender" },
  { value: "antiGenderNonConforming", label: "Anti-Gender Non-Conforming" },
  { value: "none", label: "None" },
  { value: "unknown", label: "Unknown" },
];

export const METHOD_OF_ENTRY_OPTIONS: DropdownOption[] = [
  { value: "force", label: "Force" },
  { value: "noForce", label: "No Force" },
];

export const CRIMINAL_ACTIVITY_OPTIONS = [
  {
    value: "simpleGrossNeglect",
    label:
      "Simple/Gross Neglect (unintentionally, intentionally, or knowingly failing to provide food, water, shelter, veterinary care, hoarding, etc.)",
  },
  { value: "buyingReceiving", label: "Buying/Receiving" },
  {
    value: "cultivatingManufacturing",
    label:
      "Cultivating/Manufacturing/Publishing (i.e., production of any type)",
  },
  { value: "distributingSelling", label: "Distributing/Selling" },
  { value: "exploitingChildren", label: "Exploiting Children" },
  {
    value: "organizedAbuse",
    label: "Organized Abuse (Dog Fighting and Cock Fighting)",
  },
  {
    value: "intentionalAbuse",
    label:
      "Intentional Abuse and Torture (tormenting, mutilating, maiming, poisoning, or abandonment)",
  },
  { value: "operatingPromoting", label: "Operating/Promoting/Assisting" },
  { value: "possessingConcealing", label: "Possessing/Concealing" },
  { value: "animalSexualAbuse", label: "Animal Sexual Abuse (Bestiality)" },
  {
    value: "transportingTransmitting",
    label: "Transporting/Transmitting/Importing",
  },
  { value: "usingConsuming", label: "Using/Consuming" },
];

export const GANG_INFORMATION_OPTIONS: DropdownOption[] = [
  { value: "juvenileGang", label: "Juvenile Gang" },
  { value: "otherGang", label: "Other Gang" },
  { value: "noneUnknown", label: "None/Unknown" },
];

// Combined criminal activity and gang information options for multi-select
export const CRIMINAL_ACTIVITY_AND_GANG_OPTIONS: DropdownOption[] = [
  ...CRIMINAL_ACTIVITY_OPTIONS,
  ...GANG_INFORMATION_OPTIONS,
];

export const WEAPON_TYPE_OPTIONS: DropdownOption[] = [
  { value: "firearm", label: "Firearm" },
  { value: "handgun", label: "Handgun" },
  { value: "rifle", label: "Rifle" },
  { value: "shotgun", label: "Shotgun" },
  { value: "otherFirearm", label: "Other Firearm" },
  {
    value: "knifeCutting",
    label:
      "Knife/Cutting Instrument (knives, razors, hatchets, axes, cleavers, scissors, glass, broken bottles, ice picks, etc.)",
  },
  {
    value: "bluntObject",
    label:
      "Blunt Object (baseball bats, butt of handgun, clubs, bricks, jack handles, tire irons, bottles, etc.)",
  },
  { value: "motorVehicle", label: "Motor Vehicle/Vessel" },
  {
    value: "personalWeapons",
    label: "Personal Weapons (hands, fist, feet, arms, teeth, etc.)",
  },
  { value: "poison", label: "Poison" },
  { value: "explosives", label: "Explosives" },
  { value: "fireIncendiary", label: "Fire/Incendiary Device" },
  { value: "drugsNarcotics", label: "Drugs/Narcotics/Sleeping Pills" },
  { value: "asphyxiation", label: "Asphyxiation" },
  {
    value: "other",
    label:
      "Other (BB guns, pellet guns, tasers, pepper spray, stun guns, etc.)",
  },
  { value: "unknown", label: "Unknown" },
  { value: "none", label: "None (Mutually Exclusive)" },
];

export interface PersonData {
  id: string;
  name: string;
  sex: string;
  height: string;
  hair: string;
  weight: string;
  eye: string;
  dateOfBirth: string;
}

export interface VehicleData {
  id: string;
  vIN: string;
  year: string;
  make: string;
  model: string;
  color: string;
  ownerIfApplicable: string;
}

export interface PropertyData {
  id: string;
  serialNumber: string;
  propertyType: string;
  category: string;
  collectedValue: string;
  makeModelBrand: string;
  description: string;
}

// Arrest Type Options based on the image
export const ARREST_TYPE_OPTIONS: DropdownOption[] = [
  { value: "0", label: "0 - On-View Arrest (Without Warrant)" },
  { value: "1", label: "1 - On-View Arrest (With Warrant)" },
  { value: "2", label: "2 - Taken Into Custody Without Arrest" },
  { value: "3", label: "3 - Summoned/Cited" },
];

// Multi-Arrest Segment Indicator Options
export const MULTI_ARREST_SEGMENT_INDICATOR_OPTIONS: DropdownOption[] = [
  { value: "N", label: "N - Not Applicable" },
  { value: "C", label: "C - Count Arrestee" },
  { value: "M", label: "M - Multiple Arrestee Segments" },
];

// Resident Status Options
export const RESIDENT_STATUS_OPTIONS: DropdownOption[] = [
  { value: "R", label: "R - Resident" },
  { value: "N", label: "N - Non-Resident" },
  { value: "U", label: "U - Unknown" },
];

// Weapon Type Options (reused from offense constants)
export const ARREST_WEAPON_TYPE_OPTIONS: DropdownOption[] = [
  { value: "01", label: "01 - Unarmed" },
  { value: "11", label: "11 - Firearm" },
  { value: "12", label: "12 - Handgun" },
  { value: "13", label: "13 - Rifle" },
  { value: "14", label: "14 - Shotgun" },
  { value: "15", label: "15 - Other Firearm" },
  { value: "20", label: "20 - Knife/Cutting Instrument" },
  { value: "30", label: "30 - Blunt Object" },
  { value: "35", label: "35 - Motor Vehicle/Vessel" },
  { value: "40", label: "40 - Personal Weapons" },
  { value: "50", label: "50 - Poison" },
  { value: "60", label: "60 - Explosives" },
  { value: "65", label: "65 - Fire/Incendiary Device" },
  { value: "70", label: "70 - Drugs/Narcotics/Sleeping Pills" },
  { value: "85", label: "85 - Asphyxiation" },
  { value: "90", label: "90 - Other" },
  { value: "95", label: "95 - Unknown" },
  { value: "99", label: "99 - None" },
];
