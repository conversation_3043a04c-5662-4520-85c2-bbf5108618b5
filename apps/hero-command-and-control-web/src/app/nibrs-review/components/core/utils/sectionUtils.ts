import { EntityListContent, PropertyListContent, ReportSection, SectionType } from "proto/hero/reports/v2/reports_pb";
import { getEntityDisplayName } from "./utils";

// Extended types to handle actual API response structure
interface ExtendedReportSection extends ReportSection {
  propertyList?: PropertyListContent;
  entityList?: EntityListContent;
}

// Process report sections to extract section IDs and entity references
export const processReportSections = (
  sections: ReportSection[] | undefined,
  setSectionCallbacks: {
    setPeopleListSectionId: (id: string | null) => void;
    setVehicleListSectionId: (id: string | null) => void;
    setPropertyListSectionId: (id: string | null) => void;
    setOrganizationListSectionId: (id: string | null) => void;
    setOffenseListSectionId: (id: string | null) => void;
    setSectionIdToType: (mapping: Record<string, string>) => void;
  }
) => {
  if (!sections) return { entityIds: [] };

  const allEntityIds: string[] = [];
  // Create mapping for section IDs to section types
  const sectionMapping: Record<string, string> = {};

  // First pass: collect all sections and prioritize SECTION_TYPE_PROPERTY over SECTION_TYPE_ENTITY_LIST_PROPERTIES
  const propertySections: { id: string; type: string }[] = [];
  const otherSections: { section: any; type: string; entityRefs?: Array<{ id: string }> }[] = [];

  sections.forEach((section) => {
    const sectionType = section.type;
    let entityRefs: Array<{ id: string }> | undefined;

    // Store section ID to type mapping
    sectionMapping[section.id] = sectionType.toString();

    // Special handling for property sections - collect them separately
    if (sectionType === SectionType.PROPERTY || sectionType === SectionType.ENTITY_LIST_PROPERTIES) {
      propertySections.push({ id: section.id, type: sectionType.toString() });
      // Collect entity IDs from property sections
      if (sectionType === SectionType.ENTITY_LIST_PROPERTIES) {
        const extendedSection = section as ExtendedReportSection;
        entityRefs = extendedSection.content?.case === "entityList"
          ? extendedSection.content.value.entityRefs
          : extendedSection.entityList?.entityRefs;
        if (entityRefs && entityRefs.length > 0) {
          const ids = entityRefs.map((ref) => ref.id);
          allEntityIds.push(...ids);
        }
      } else if (sectionType === SectionType.PROPERTY) {
        const extendedSection = section as ExtendedReportSection;
        const propertyRefs = extendedSection.content?.case === "propertyList"
          ? extendedSection.content.value.propertyRefs
          : extendedSection.propertyList?.propertyRefs;
        if (propertyRefs && propertyRefs.length > 0) {
          const ids = propertyRefs.map((ref: any) => ref.id);
          allEntityIds.push(...ids);
        }
      }
    } else {
      // Handle other sections normally
      otherSections.push({ section, type: sectionType.toString(), entityRefs });
    }
  });

  // Process property sections with priority for SECTION_TYPE_PROPERTY
  const preferredPropertySection = propertySections.find(s => s.type === "SECTION_TYPE_PROPERTY") ||
    propertySections.find(s => s.type === "SECTION_TYPE_ENTITY_LIST_PROPERTIES");

  if (preferredPropertySection) {
    setSectionCallbacks.setPropertyListSectionId(preferredPropertySection.id);
  }

  // Process other sections
  otherSections.forEach(({ section, type }) => {
    let entityRefs: Array<{ id: string }> | undefined;

    switch (type) {
      case "SECTION_TYPE_ENTITY_LIST_PEOPLE": {
        setSectionCallbacks.setPeopleListSectionId(section.id);
        const peopleSection = section as ExtendedReportSection;
        entityRefs = peopleSection.content?.case === "entityList"
          ? peopleSection.content.value.entityRefs
          : peopleSection.entityList?.entityRefs;
        break;
      }
      case "SECTION_TYPE_ENTITY_LIST_VEHICLE": {
        setSectionCallbacks.setVehicleListSectionId(section.id);
        const vehicleSection = section as ExtendedReportSection;
        entityRefs = vehicleSection.content?.case === "entityList"
          ? vehicleSection.content.value.entityRefs
          : vehicleSection.entityList?.entityRefs;
        break;
      }
      case "SECTION_TYPE_ENTITY_LIST_ORGANIZATIONS": {
        setSectionCallbacks.setOrganizationListSectionId(section.id);
        const orgSection = section as ExtendedReportSection;
        entityRefs = orgSection.content?.case === "entityList"
          ? orgSection.content.value.entityRefs
          : orgSection.entityList?.entityRefs;
        break;
      }
      case "SECTION_TYPE_OFFENSE":
        setSectionCallbacks.setOffenseListSectionId(section.id);
        break;
    }

    // Collect entity IDs from entity list sections
    if (entityRefs && entityRefs.length > 0) {
      const ids = entityRefs.map((ref) => ref.id);
      allEntityIds.push(...ids);
    }
  });

  // Update section ID to type mapping
  setSectionCallbacks.setSectionIdToType(sectionMapping);

  // Return the unique entity IDs to fetch
  return {
    entityIds: [...new Set(allEntityIds)],
  };
};

// Helper: Update entity display name in section
export const updateEntityDisplayNameInSection = (
  entity: any,
  sectionId: string,
  reportId: string,
  reportSections: any,
  updateReportSectionMutation: any
) => {
  if (!reportId) return;
  const section = reportSections?.sections?.find(
    (s: any) => s.id === sectionId
  );
  const sectionTypeStr = section?.type as string;
  const isEntityListSection =
    sectionTypeStr === "SECTION_TYPE_ENTITY_LIST_PEOPLE" ||
    sectionTypeStr === "SECTION_TYPE_ENTITY_LIST_VEHICLE" ||
    sectionTypeStr === "SECTION_TYPE_ENTITY_LIST_PROPERTIES" ||
    sectionTypeStr === "SECTION_TYPE_ENTITY_LIST_ORGANIZATIONS";

  if (!section || !isEntityListSection) return;

  // Create a formatted display name for the entity
  const displayName = getEntityDisplayName(entity);

  const extendedSection = section as ExtendedReportSection;
  const existingRefs = extendedSection.content?.case === "entityList"
    ? extendedSection.content.value.entityRefs
    : extendedSection.entityList?.entityRefs;
  const entityRefs = Array.isArray(existingRefs) ? [...existingRefs] : [];

  const existingRefIndex = entityRefs.findIndex((ref) => ref.id === entity.id);

  if (existingRefIndex >= 0) {
    // Update existing reference with updated display name
    entityRefs[existingRefIndex] = {
      ...entityRefs[existingRefIndex],
      displayName,
    };

    // Create the update request
    const updateRequest = {
      reportId,
      section: extendedSection.content?.case === "entityList"
        ? {
          ...extendedSection,
          content: {
            case: "entityList" as const,
            value: {
              ...extendedSection.content.value,
              entityRefs,
            } as EntityListContent,
          },
        }
        : {
          ...extendedSection,
          entityList: {
            ...extendedSection.entityList,
            entityRefs,
          },
        },
    };
    updateReportSectionMutation.mutate(updateRequest);
  }
};

// Helper: Create or update entity list section in report
export const updateEntityListSection = (
  title: string,
  entity: any,
  existingSectionId: string | null,
  reportId: string,
  reportSections: any,
  updateReportSectionMutation: any,
  entities: {
    people: any[];
    vehicles: any[];
    properties: any[];
    organizations: any[];
  },
  allEntitiesOverride?: any[]
) => {
  if (!reportId) return;
  if (existingSectionId) {
    // Update existing section
    const section = reportSections?.sections?.find(
      (s: any) => s.id === existingSectionId
    );

    const sectionTypeStr = section?.type as string;
    const isEntityListSection =
      sectionTypeStr === "SECTION_TYPE_ENTITY_LIST_PEOPLE" ||
      sectionTypeStr === "SECTION_TYPE_ENTITY_LIST_VEHICLE" ||
      sectionTypeStr === "SECTION_TYPE_ENTITY_LIST_PROPERTIES" ||
      sectionTypeStr === "SECTION_TYPE_ENTITY_LIST_ORGANIZATIONS";

    if (section && isEntityListSection) {
      // Get all entities of this type from our local state or use the provided override
      let allEntities;
      // Define the type for entityRefs
      let entityRefs: Array<{
        id: string;
        type: string;
        version: number;
        displayName: string;
      }> = [];

      // Use override if provided, otherwise use the current state
      if (allEntitiesOverride) {
        allEntities = allEntitiesOverride;
      } else {
        // Build complete entity references based on our local state arrays
        switch (sectionTypeStr) {
          case "SECTION_TYPE_ENTITY_LIST_PEOPLE":
            allEntities = entities.people;
            break;
          case "SECTION_TYPE_ENTITY_LIST_VEHICLE":
            allEntities = entities.vehicles;
            break;
          case "SECTION_TYPE_ENTITY_LIST_PROPERTIES":
            allEntities = entities.properties;
            break;
          case "SECTION_TYPE_ENTITY_LIST_ORGANIZATIONS":
            allEntities = entities.organizations;
            break;
        }
      }

      // Create entity references for all entities in our local state
      if (allEntities && allEntities.length > 0) {
        entityRefs = allEntities.map((localEntity) => {
          const localDisplayName = getEntityDisplayName(localEntity);
          const localEntityType = localEntity.entityType.replace(
            "ENTITY_TYPE_",
            ""
          );

          return {
            id: localEntity.id,
            type: localEntityType,
            version: localEntity.version || 1,
            displayName: localDisplayName,
          };
        });
      }
      // Use shared helper to replace refs array
      replaceReportSectionRefs({
        listType: "entityList",
        title,
        refs: entityRefs,
        sectionId: existingSectionId,
        reportId,
        reportSections,
        updateReportSectionMutation,
      });
    }
  }
};

// Shared helper: replace entire refs array in a report section (entityList or propertyList)
export const replaceReportSectionRefs = (params: {
  listType: "entityList" | "propertyList";
  title?: string;
  refs: any[];
  sectionId: string;
  reportId: string;
  reportSections: any;
  updateReportSectionMutation: any;
}) => {
  const { listType, title, refs, sectionId, reportId, reportSections, updateReportSectionMutation } = params;
  if (!reportId || !sectionId) return;
  const section = reportSections?.sections?.find((s: ReportSection) => s.id === sectionId) as ExtendedReportSection | undefined;
  if (!section) return;

  let updatedSection: ExtendedReportSection;

  if (listType === "entityList") {
    const currentEntityList = section.content?.case === "entityList"
      ? section.content.value
      : section.entityList;
    updatedSection = section.content?.case === "entityList"
      ? {
        ...section,
        content: {
          case: "entityList" as const,
          value: {
            ...currentEntityList,
            ...(title ? { title } : {}),
            entityRefs: refs,
          } as EntityListContent,
        },
      }
      : {
        ...section,
        entityList: {
          ...currentEntityList,
          ...(title ? { title } : {}),
          entityRefs: refs,
        } as EntityListContent,
      };
  } else {
    const currentPropertyList = section.content?.case === "propertyList"
      ? section.content.value
      : section.propertyList;
    updatedSection = section.content?.case === "propertyList"
      ? {
        ...section,
        content: {
          case: "propertyList" as const,
          value: {
            ...currentPropertyList,
            ...(title ? { title } : {}),
            propertyRefs: refs,
          } as PropertyListContent,
        },
      }
      : {
        ...section,
        propertyList: {
          ...currentPropertyList,
          ...(title ? { title } : {}),
          propertyRefs: refs,
        } as PropertyListContent,
      };
  }

  updateReportSectionMutation.mutate({ reportId, section: updatedSection });
};

// Shared helper: upsert a single reference into refs array based on id
export const upsertReportSectionRef = (params: {
  listType: "entityList" | "propertyList";
  title?: string;
  newRef: any;
  sectionId: string;
  reportId: string;
  reportSections: any;
  updateReportSectionMutation: any;
}) => {
  const { listType, title, newRef, sectionId, reportId, reportSections, updateReportSectionMutation } = params;
  if (!reportId || !sectionId) return;
  const section = reportSections?.sections?.find((s: ReportSection) => s.id === sectionId) as ExtendedReportSection | undefined;
  if (!section) return;

  const refsKey = listType === "entityList" ? "entityRefs" : "propertyRefs";
  let existingRefs: any[] = [];

  if (listType === "entityList") {
    existingRefs = section?.content?.case === "entityList"
      ? section.content.value.entityRefs || []
      : section?.entityList?.entityRefs || [];
  } else {
    existingRefs = section?.content?.case === "propertyList"
      ? section.content.value.propertyRefs || []
      : section?.propertyList?.propertyRefs || [];
  }

  const existingIndex = existingRefs.findIndex((ref: any) => ref.id === newRef.id);
  const updatedRefs = [...existingRefs];
  if (existingIndex >= 0) {
    updatedRefs[existingIndex] = newRef;
  } else {
    updatedRefs.push(newRef);
  }

  let updatedSection: ExtendedReportSection;

  if (listType === "entityList") {
    const currentEntityList = section.content?.case === "entityList"
      ? section.content.value
      : section.entityList;
    updatedSection = section.content?.case === "entityList"
      ? {
        ...section,
        content: {
          case: "entityList" as const,
          value: {
            ...currentEntityList,
            ...(title ? { title } : {}),
            entityRefs: updatedRefs,
          } as EntityListContent,
        },
      }
      : {
        ...section,
        entityList: {
          ...currentEntityList,
          ...(title ? { title } : {}),
          entityRefs: updatedRefs,
        } as EntityListContent,
      };
  } else {
    const currentPropertyList = section.content?.case === "propertyList"
      ? section.content.value
      : section.propertyList;
    updatedSection = section.content?.case === "propertyList"
      ? {
        ...section,
        content: {
          case: "propertyList" as const,
          value: {
            ...currentPropertyList,
            ...(title ? { title } : {}),
            propertyRefs: updatedRefs,
          } as PropertyListContent,
        },
      }
      : {
        ...section,
        propertyList: {
          ...currentPropertyList,
          ...(title ? { title } : {}),
          propertyRefs: updatedRefs,
        } as PropertyListContent,
      };
  }

  updateReportSectionMutation.mutate({ reportId, section: updatedSection });
};
