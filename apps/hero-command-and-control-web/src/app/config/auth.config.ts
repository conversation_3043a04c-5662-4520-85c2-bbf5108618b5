/**
 * Authentication configuration constants
 * Centralizes all authentication-related configuration values
 */

/**
 * Token refresh configuration
 * @property {number} REFRESH_BUFFER_MS - Time before token expiry to trigger refresh (5 minutes)
 * @property {number} DEFAULT_REFRESH_INTERVAL_MS - Default refresh interval when expiry can't be determined (50 minutes)
 * @property {number} MAX_RETRY_ATTEMPTS - Maximum number of refresh retry attempts
 * @property {number} MAX_BACKOFF_MS - Maximum backoff time for retries (30 seconds)
 * @property {number} BASE_BACKOFF_MS - Base backoff time for exponential backoff (1 second)
 */
export const TOKEN_REFRESH_CONFIG = {
  REFRESH_BUFFER_MS: 5 * 60 * 1000, // 5 minutes before expiry
  DEFAULT_REFRESH_INTERVAL_MS: 50 * 60 * 1000, // 50 minutes
  MAX_RETRY_ATTEMPTS: 3,
  MAX_BACKOFF_MS: 30000, // 30 seconds
  BASE_BACKOFF_MS: 1000, // 1 second
} as const;

/**
 * Authentication timeout configuration
 * @property {number} AUTH_CHECK_TIMEOUT_MS - Maximum time to wait for auth check (30 seconds)
 * @property {number} LOGIN_REDIRECT_TIMEOUT_MS - Maximum time to wait for login redirect (5 seconds)
 */
export const AUTH_TIMEOUT_CONFIG = {
  AUTH_CHECK_TIMEOUT_MS: 30000, // 30 seconds
  LOGIN_REDIRECT_TIMEOUT_MS: 5000, // 5 seconds
} as const;

/**
 * Cookie configuration
 * @property {string} COOKIE_BASE - Base name for Cognito cookies
 * @property {Object} COOKIE_SETTINGS - Cookie security settings
 */
export const COOKIE_CONFIG = {
  COOKIE_BASE: 'CognitoIdentityServiceProvider',
  COOKIE_SETTINGS: {
    httpOnly: true,
    secure: true,
    sameSite: 'Strict' as const,
  },
} as const;

/**
 * Error messages for authentication failures
 * Provides specific, actionable error messages for different auth scenarios
 */
export const AUTH_ERROR_MESSAGES = {
  TOKEN_REFRESH_FAILED: 'Failed to refresh authentication token. Please log in again.',
  TOKEN_DECODE_FAILED: 'Unable to decode authentication token. Token may be corrupted.',
  NO_TOKENS_FOUND: 'No authentication tokens found. Please log in.',
  SESSION_EXPIRED: 'Your session has expired. Please log in again to continue.',
  NETWORK_ERROR: 'Network error during authentication. Please check your connection.',
  MAX_RETRIES_REACHED: 'Maximum authentication retry attempts reached. Logging out for security.',
  INVALID_CREDENTIALS: 'Invalid username or password. Please try again.',
  USER_NOT_FOUND: 'User account not found. Please check your credentials.',
  COGNITO_SERVICE_ERROR: 'Authentication service is temporarily unavailable. Please try again later.',
  UNKNOWN_ERROR: 'An unexpected authentication error occurred. Please try again.',
} as const;

/**
 * Debug configuration
 * Enable verbose logging for authentication troubleshooting
 * Set to true to enable detailed auth operation logs (no sensitive data)
 */
export const AUTH_DEBUG_ENABLED = true;

/**
 * Debug log prefixes for better traceability
 */
export const AUTH_LOG_PREFIX = {
  TOKEN_REFRESH: '[Auth:TokenRefresh]',
  AUTH_CHECK: '[Auth:Check]',
  LOGIN: '[Auth:Login]',
  LOGOUT: '[Auth:Logout]',
  ERROR: '[Auth:Error]',
  COOKIE: '[Auth:Cookie]',
  STORAGE: '[Auth:Storage]',
  SYNC: '[Auth:Sync]',
  SESSION: '[Auth:Session]',
  DEBUG: '[Auth:Debug]',
} as const;