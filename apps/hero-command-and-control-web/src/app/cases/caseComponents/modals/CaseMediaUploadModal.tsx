import { Button } from "@/design-system/components/Button";
import { TextInput } from "@/design-system/components/TextInput";
import { Typography } from "@/design-system/components/Typography";
import { colors } from "@/design-system/tokens";
import CloseIcon from "@mui/icons-material/Close";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import DeleteIcon from "@mui/icons-material/Delete";
import {
  Box,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  LinearProgress,
} from "@mui/material";
import React, { useCallback, useState } from "react";
import { useFileUpload } from "../../../apis/services/filerepository/hooks";
import { useAddAdditionalInfo } from "../../../apis/services/workflow/cases/hooks";
import { create } from "@bufbuild/protobuf";
import { AddAdditionalInfoRequestSchema } from "proto/hero/cases/v1/cases_pb";
import {
  FaFile,
  FaFileAlt,
  FaFileAudio,
  FaFileImage,
  FaFilePdf,
  FaFileVideo,
} from "react-icons/fa";


interface CaseMediaUploadModalProps {
  open: boolean;
  onClose: () => void;
  caseId: string;
  caseData: any;
  onSuccess?: (uploadedFiles: any[]) => void;
  readOnly?: boolean;
}

interface StagedFile {
  id: string;
  file: File;
  displayName: string;
  caption: string;
  fileCategory: string;
  preview?: string;
  isUploading?: boolean;
  uploadProgress?: number;
  uploadError?: string;
  fileId?: string;
}

const CaseMediaUploadModal: React.FC<CaseMediaUploadModalProps> = ({
  open,
  onClose,
  caseId,
  caseData: _caseData,
  onSuccess,
  readOnly = false,
}) => {
  const [stagedFiles, setStagedFiles] = useState<StagedFile[]>([]);
  const [isDragging, setIsDragging] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadErrors, setUploadErrors] = useState<string[]>([]);
  
  // Use the working file upload hook like MediaPanel
  const { uploadFile } = useFileUpload();
  
  const addAdditionalInfo = useAddAdditionalInfo();

  const getFileIcon = (fileType: string) => {
    const iconStyle = { fontSize: 20, color: colors.blue[600] };
    
    if (fileType.startsWith('image/')) return <FaFileImage style={iconStyle} />;
    if (fileType.startsWith('video/')) return <FaFileVideo style={iconStyle} />;
    if (fileType.startsWith('audio/')) return <FaFileAudio style={iconStyle} />;
    if (fileType === 'application/pdf') return <FaFilePdf style={iconStyle} />;
    if (fileType.includes('text/') || fileType.includes('document') || fileType.includes('word')) {
      return <FaFileAlt style={iconStyle} />;
    }
    return <FaFile style={{ fontSize: 20, color: colors.grey[500] }} />;
  };

  const generatePreview = (file: File): string | undefined => {
    if (file.type.startsWith('image/') || file.type.startsWith('video/')) {
      return URL.createObjectURL(file);
    }
    return undefined;
  };

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const newStagedFiles: StagedFile[] = acceptedFiles.map(file => ({
      id: `${Date.now()}-${Math.random()}`,
      file,
      displayName: file.name,
      caption: '',
      fileCategory: '',
      preview: generatePreview(file),
      isUploading: false,
    }));
    
    setStagedFiles(prev => [...prev, ...newStagedFiles]);
  }, []);
  
  const addFiles = useCallback((files: FileList | File[]) => {
    const fileArray = Array.from(files);
    onDrop(fileArray);
  }, [onDrop]);

  const dragHandlers = {
    onDragEnter: useCallback((event: React.DragEvent) => {
      event.preventDefault();
      event.stopPropagation();
      setIsDragging(true);
    }, []),
    onDragOver: useCallback((event: React.DragEvent) => {
      event.preventDefault();
      event.stopPropagation();
      setIsDragging(true);
    }, []),
    onDragLeave: useCallback((event: React.DragEvent) => {
      event.preventDefault();
      event.stopPropagation();
      setIsDragging(false);
    }, []),
    onDrop: useCallback((event: React.DragEvent) => {
      event.preventDefault();
      event.stopPropagation();
      setIsDragging(false);
      if (event.dataTransfer.files) {
        addFiles(event.dataTransfer.files);
      }
    }, [addFiles])
  };

  const removeFile = useCallback((id: string) => {
    setStagedFiles(prev => {
      const fileToRemove = prev.find(f => f.id === id);
      if (fileToRemove?.preview) {
        URL.revokeObjectURL(fileToRemove.preview);
      }
      return prev.filter(f => f.id !== id);
    });
  }, []);
  
  const updateFileMetadata = useCallback((id: string, field: keyof Pick<StagedFile, 'displayName' | 'caption' | 'fileCategory'>, value: string) => {
    setStagedFiles(prev => prev.map(file => 
      file.id === id ? { ...file, [field]: value } : file
    ));
  }, []);

  const handleFileInputChange = (event: Event) => {
    const target = event.target as HTMLInputElement;
    if (target.files) {
      addFiles(target.files);
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return Math.round(bytes / Math.pow(k, i) * 100) / 100 + ' ' + sizes[i];
  };

  const handleUpload = async () => {
    if (stagedFiles.length === 0) return;
    
    const filesToUpload = stagedFiles.filter(f => !f.fileId && !f.isUploading);
    if (filesToUpload.length === 0) return;

    setIsUploading(true);
    setUploadErrors([]);

    try {
      const uploadedFiles: any[] = [];
      
      for (let i = 0; i < filesToUpload.length; i++) {
        const stagedFile = filesToUpload[i];
        
        setStagedFiles(prev => prev.map(f => 
          f.id === stagedFile.id 
            ? { ...f, isUploading: true, uploadProgress: 0 }
            : f
        ));
        
        try {
          const result = await uploadFile(stagedFile.file, undefined, {
            category: 'report-media',
            uploadSource: 'report-media-panel',
            originalSize: stagedFile.file.size
          });
          
          setStagedFiles(prev => prev.map(f => 
            f.id === stagedFile.id 
              ? { ...f, fileId: result.fileId, isUploading: false, uploadProgress: 100 }
              : f
          ));
          
          const fileReference = {
            id: `case-media-${Date.now()}-${Math.random()}`,
            fileId: result.fileId,
            displayName: stagedFile.displayName,
            caption: stagedFile.caption,
            fileCategory: stagedFile.fileCategory,
            metadata: {
              originalFilename: stagedFile.file.name,
              fileSize: stagedFile.file.size,
              fileType: stagedFile.file.type,
            },
          };
          
          uploadedFiles.push(fileReference);
          
        } catch (error) {
          console.error(`Failed to upload ${stagedFile.file.name}:`, error);
          setStagedFiles(prev => prev.map(f => 
            f.id === stagedFile.id 
              ? { ...f, isUploading: false, uploadError: 'Upload failed' }
              : f
          ));
          setUploadErrors(prev => [...prev, `${stagedFile.file.name}: Upload failed`]);
        }
      }
      
      if (uploadedFiles.length > 0) {
        const currentAdditionalInfo = _caseData.additionalInfoJson || {};
        const existingMediaFiles = currentAdditionalInfo.mediaFiles || [];
        
        const updatedAdditionalInfo = {
          ...currentAdditionalInfo,
          mediaFiles: [...existingMediaFiles, ...uploadedFiles]
        };
        
        const request = create(AddAdditionalInfoRequestSchema, {
          caseId,
          additionalInfoJson: JSON.stringify(updatedAdditionalInfo),
        });
        
        addAdditionalInfo.mutate(request);
        
        console.log('Files uploaded and additional info updated successfully:', uploadedFiles);
        
        if (onSuccess) {
          onSuccess(uploadedFiles);
        }
        
        stagedFiles.forEach(f => {
          if (f.preview) {
            URL.revokeObjectURL(f.preview);
          }
        });
        setStagedFiles([]);
        onClose();
      }
      
    } catch (error) {
      console.error('Upload process failed:', error);
      alert('Failed to upload files. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  const handleClose = () => {
    if (!isUploading) {
      onClose();
    }
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      slotProps={{
        paper: {
          sx: {
            borderRadius: 2,
            maxHeight: '80vh',
          }
        }
      }}
      onClick={(e) => e.stopPropagation()}
    >
      <DialogTitle sx={{ pb: 1 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Typography style="h3" color={colors.grey[900]}>
            Add Media to Case
          </Typography>
          <IconButton
            onClick={handleClose}
            disabled={isUploading}
            sx={{ color: colors.grey[500] }}
          >
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent sx={{ pt: 2 }}>
        {isUploading && (
          <Box sx={{ mb: 3 }}>
            <Box sx={{ mb: 1 }}>
              <Typography style="body3" color={colors.grey[600]}>
                Uploading files...
              </Typography>
            </Box>
            <LinearProgress sx={{ borderRadius: 1 }} />
          </Box>
        )}
        
        {uploadErrors.length > 0 && (
          <Box sx={{ mb: 3 }}>
            {uploadErrors.map((error, index) => (
              <Box key={index} sx={{ mb: 0.5 }}>
                <Typography style="body3" color={colors.rose[600]}>
                  {error}
                </Typography>
              </Box>
            ))}
          </Box>
        )}

        <Box
          {...dragHandlers}
          sx={{
            border: `2px dashed ${isDragging ? colors.blue[300] : colors.grey[300]}`,
            borderRadius: 2,
            p: 4,
            textAlign: 'center',
            cursor: readOnly ? 'not-allowed' : 'pointer',
            backgroundColor: isDragging ? colors.blue[50] : colors.grey[50],
            transition: 'all 0.2s ease',
            mb: 3,
            opacity: isUploading ? 0.5 : 1,
            pointerEvents: isUploading ? 'none' : 'auto',
          }}
          onClick={() => {
            if (!readOnly && !isUploading) {
              const input = document.createElement('input');
              input.type = 'file';
              input.multiple = true;
              input.accept = 'image/*,video/*,audio/*,.pdf,.doc,.docx,.txt,.csv';
              input.onchange = handleFileInputChange;
              input.click();
            }
          }}
        >
          <CloudUploadIcon
            sx={{
              fontSize: 48,
              color: isDragging ? colors.blue[600] : colors.grey[400],
              mb: 2,
            }}
          />
          
          {isDragging ? (
            <Typography style="body1" color={colors.blue[600]}>
              Drop files here to upload
            </Typography>
          ) : (
            <>
              <Box sx={{ mb: 1 }}>
                <Typography style="body1" color={colors.grey[700]}>
                  Drag and drop files here, or click to select
                </Typography>
              </Box>
              <Typography style="body3" color={colors.grey[500]}>
                Supports images, videos, audio, PDFs, and documents
              </Typography>
            </>
          )}
        </Box>

        {stagedFiles.length > 0 && (
          <Box>
            <Box sx={{ mb: 2 }}>
              <Typography style="body2" color={colors.grey[700]}>
                Ready to Upload ({stagedFiles.length})
              </Typography>
            </Box>
            
            <Box sx={{ maxHeight: 400, overflowY: 'auto', mb: 2 }}>
              {stagedFiles.map((stagedFile) => (
                <Box
                  key={stagedFile.id}
                  sx={{
                    display: 'flex',
                    gap: 2,
                    p: 2,
                    mb: 2,
                    borderRadius: 1,
                    border: `1px solid ${colors.grey[200]}`,
                    backgroundColor: colors.grey[50],
                  }}
                >
                  <Box
                    sx={{
                      width: 80,
                      height: 80,
                      borderRadius: 1,
                      border: `1px solid ${colors.grey[300]}`,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      backgroundColor: colors.grey[100],
                      flexShrink: 0,
                      position: 'relative',
                      overflow: 'hidden',
                    }}
                  >
                    {stagedFile.preview ? (
                      stagedFile.file.type.startsWith('image/') ? (
                        <img
                          src={stagedFile.preview}
                          alt="Preview"
                          style={{
                            width: '100%',
                            height: '100%',
                            objectFit: 'cover',
                          }}
                        />
                      ) : (
                        <video
                          src={stagedFile.preview}
                          style={{
                            width: '100%',
                            height: '100%',
                            objectFit: 'cover',
                          }}
                          muted
                        />
                      )
                    ) : (
                      getFileIcon(stagedFile.file.type)
                    )}
                    
                    {stagedFile.isUploading && (
                      <Box
                        sx={{
                          position: 'absolute',
                          inset: 0,
                          backgroundColor: 'rgba(0, 0, 0, 0.7)',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          flexDirection: 'column',
                          gap: 0.5,
                        }}
                      >
                        <Box
                          sx={{
                            width: 24,
                            height: 24,
                            border: `2px solid ${colors.blue[200]}`,
                            borderTop: `2px solid ${colors.blue[600]}`,
                            borderRadius: '50%',
                            animation: 'spin 1s linear infinite',
                            '@keyframes spin': {
                              '0%': { transform: 'rotate(0deg)' },
                              '100%': { transform: 'rotate(360deg)' },
                            },
                          }}
                        />
                        {stagedFile.uploadProgress !== undefined && (
                          <Typography style="tag2" color="white">
                            {Math.round(stagedFile.uploadProgress)}%
                          </Typography>
                        )}
                      </Box>
                    )}
                    
                    {stagedFile.fileId && !stagedFile.isUploading && (
                      <Box
                        sx={{
                          position: 'absolute',
                          top: 4,
                          right: 4,
                          backgroundColor: colors.blue[600],
                          borderRadius: '50%',
                          width: 16,
                          height: 16,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                      >
                        <Box sx={{ fontSize: 10, color: "white", fontWeight: 600 }}>
                          ✓
                        </Box>
                      </Box>
                    )}
                    
                    {!stagedFile.isUploading && (
                      <IconButton
                        onClick={() => removeFile(stagedFile.id)}
                        size="small"
                        sx={{
                          position: 'absolute',
                          top: 4,
                          left: 4,
                          backgroundColor: 'rgba(0, 0, 0, 0.6)',
                          color: 'white',
                          width: 20,
                          height: 20,
                          '&:hover': {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                          },
                        }}
                      >
                        <DeleteIcon sx={{ fontSize: 12 }} />
                      </IconButton>
                    )}
                  </Box>
                  
                  <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column', gap: 1.5 }}>
                    <TextInput
                      placeholder="Title"
                      value={stagedFile.displayName}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                        updateFileMetadata(stagedFile.id, 'displayName', e.target.value)
                      }
                      disabled={readOnly || stagedFile.isUploading}
                    />
                    
                    <TextInput
                      placeholder="Category (e.g., Evidence Photo, Crime Scene)"
                      value={stagedFile.fileCategory}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                        updateFileMetadata(stagedFile.id, 'fileCategory', e.target.value)
                      }
                      disabled={readOnly || stagedFile.isUploading}
                    />
                    
                    <TextInput
                      placeholder="Description"
                      value={stagedFile.caption}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                        updateFileMetadata(stagedFile.id, 'caption', e.target.value)
                      }
                      disabled={readOnly || stagedFile.isUploading}
                    />
                    
                    <Typography style="tag2" color={colors.grey[500]}>
                      {stagedFile.file.name} • {formatFileSize(stagedFile.file.size)}
                    </Typography>
                    
                    {stagedFile.uploadError && (
                      <Typography style="tag2" color={colors.rose[600]}>
                        Error: {stagedFile.uploadError}
                      </Typography>
                    )}
                    {stagedFile.fileId && !stagedFile.isUploading && (
                      <Typography style="tag2" color={colors.blue[600]}>
                        ✓ Uploaded successfully
                      </Typography>
                    )}
                    {!stagedFile.fileId && !stagedFile.isUploading && !stagedFile.uploadError && (
                      <Typography style="tag2" color={colors.grey[500]}>
                        Ready to upload
                      </Typography>
                    )}
                  </Box>
                </Box>
              ))}
            </Box>
          </Box>
        )}
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 3, gap: 2 }}>
        <Button
          label="Cancel"
          color="grey"
          prominence={false}
          onClick={handleClose}
          disabled={isUploading}
        />
        <Button
          label={isUploading ? 'Uploading...' : `Upload ${stagedFiles.length} Files`}
          color="blue"
          prominence={true}
          onClick={handleUpload}
          disabled={stagedFiles.length === 0 || isUploading || readOnly}
        />
      </DialogActions>
    </Dialog>
  );
};

export default CaseMediaUploadModal;