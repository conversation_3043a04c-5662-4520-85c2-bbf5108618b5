import { Button } from "@/design-system/components/Button";
import { Checkbox } from "@/design-system/components/Checkbox";
import { Typography } from "@/design-system/components/Typography";
import { colors } from "@/design-system/tokens";
import { Box, Dialog, DialogActions, DialogContent } from "@mui/material";
import { useCallback, useState } from "react";
import { OrganizationData } from "../offenses/OrganizationList";
import { PersonData } from "../offenses/PersonList";
import { PropertyData } from "../offenses/PropertyList";
import { VehicleData } from "../offenses/VehicleList";

interface EntityToRemove {
  id: string;
  name: string;
  type: "person" | "vehicle" | "property" | "organization";
  data: PersonData | VehicleData | PropertyData | OrganizationData;
}

interface EntityRemovalDecision {
  entityId: string;
  entityType: "person" | "vehicle" | "property" | "organization";
  removeFromReport: boolean;
}

interface EntityCleanupResult {
  completed: boolean;
  decisions: EntityRemovalDecision[];
}

export const useEntityCleanupConfirmation = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [currentEntityIndex, setCurrentEntityIndex] = useState(0);
  const [entities, setEntities] = useState<EntityToRemove[]>([]);
  const [decisions, setDecisions] = useState<EntityRemovalDecision[]>([]);
  const [removeFromReport, setRemoveFromReport] = useState(false);
  const [offenseOrIncidentName, setOffenseOrIncidentName] = useState<string>("");
  const [offenseOrIncidentType, setOffenseOrIncidentType] = useState<"offense" | "incident">("offense");
  const [isProcessing, setIsProcessing] = useState(false);
  const [resolvePromise, setResolvePromise] = useState<
    ((result: EntityCleanupResult) => void) | null
  >(null);

  // Helper function to convert text to title case
  const toTitleCase = (str: string): string => {
    return str.toLowerCase().replace(/\w\S*/g, (txt) => 
      txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
    );
  };

  // Helper function to get entity display name
  const getEntityDisplayName = (entity: EntityToRemove): string => {
    switch (entity.type) {
      case "person":
        return (entity.data as PersonData).name;
      case "vehicle": {
        const vehicle = entity.data as VehicleData;
        return `${vehicle.year} ${vehicle.make} ${vehicle.model}`.trim();
      }
      case "property": {
        const property = entity.data as PropertyData;
        return property.propertyType || property.category || "Unknown Property";
      }
      case "organization": {
        const org = entity.data as OrganizationData;
        return org.name || org.type || "Organization";
      }
      default:
        return "Unknown Entity";
    }
  };


  // Helper function to get entities from offense/incident relations
  const getEntitiesFromRelations = useCallback(
    (
      targetOffenseId: string,
      people: PersonData[],
      vehicles: VehicleData[],
      properties: PropertyData[],
      organizations: OrganizationData[],
      relations: any[],
      allOffenseIds: string[] = [],
      allIncidentIds: string[] = []
    ): EntityToRemove[] => {
      const foundEntities: EntityToRemove[] = [];

      // Get all relation types that connect entities to this offense/incident
      const offenseRelationTypes = [
        "RELATION_TYPE_OFFENSE_VICTIM",
        "RELATION_TYPE_OFFENSE_OFFENDER",
        "RELATION_TYPE_OFFENSE_WITNESS",
        "RELATION_TYPE_OFFENSE_SUSPECT",
        "RELATION_TYPE_OFFENSE_INVOLVED_PARTY",
        "RELATION_TYPE_OFFENSE_VEHICLE",
        "RELATION_TYPE_OFFENSE_PROPERTY",
        "RELATION_TYPE_OFFENSE_ORGANIZATION",
        "RELATION_TYPE_VICTIM_ORGANIZATION_OFFENSE",
      ];

      const incidentRelationTypes = [
        "RELATION_TYPE_INCIDENT_PERSON", // Added missing incident-person relation type
        "RELATION_TYPE_INCIDENT_INVOLVED_PARTY",
        "RELATION_TYPE_INCIDENT_VEHICLE",
        "RELATION_TYPE_INCIDENT_PROPERTY",
        "RELATION_TYPE_INCIDENT_ORGANIZATION",
      ];

      const relevantRelationTypes = [
        ...offenseRelationTypes,
        ...incidentRelationTypes,
      ];

      // Find all relations that involve this offense/incident
      const relevantRelations = relations.filter((relation: any) => {
        if (!relevantRelationTypes.includes(relation.relationType))
          return false;

        const isTargetOffenseInvolved =
          (relation.objectA?.objectType === "offense" &&
            relation.objectA?.reportScopedId === targetOffenseId) ||
          (relation.objectB?.objectType === "offense" &&
            relation.objectB?.reportScopedId === targetOffenseId);

        const isTargetIncidentInvolved =
          (relation.objectA?.objectType === "incident" &&
            relation.objectA?.reportScopedId === targetOffenseId) ||
          (relation.objectB?.objectType === "incident" &&
            relation.objectB?.reportScopedId === targetOffenseId);

        return isTargetOffenseInvolved || isTargetIncidentInvolved;
      });

      // Debug logging
      console.log(
        `Entity cleanup - Found ${relevantRelations.length} relevant relations for offense/incident ${targetOffenseId}`
      );
      console.log(
        "Entity cleanup - Relevant relations:",
        relevantRelations.map((r) => ({
          relationType: r.relationType,
          objectA: r.objectA,
          objectB: r.objectB,
        }))
      );

      // Extract entity IDs from these relations
      const entityIds = new Set<string>();
      relevantRelations.forEach((relation: any) => {
        // Get the entity ID (the non-offense object in the relation)
        if (relation.objectA?.objectType === "entity") {
          console.log(
            `Entity cleanup - Adding entity ID from objectA: ${relation.objectA.globalId}`
          );
          entityIds.add(relation.objectA.globalId);
        }
        if (relation.objectB?.objectType === "entity") {
          console.log(
            `Entity cleanup - Adding entity ID from objectB: ${relation.objectB.globalId}`
          );
          entityIds.add(relation.objectB.globalId);
        }
      });

      console.log(
        `Entity cleanup - Found ${entityIds.size} unique entity IDs:`,
        Array.from(entityIds)
      );

      // Find actual entity objects and create EntityToRemove objects
      entityIds.forEach((entityId) => {
        // Check people
        const person = people.find((p) => p.id === entityId);
        if (person) {
          foundEntities.push({
            id: entityId,
            name: person.name,
            type: "person",
            data: person,
          });
          return;
        }

        // Check vehicles
        const vehicle = vehicles.find((v) => v.id === entityId);
        if (vehicle) {
          foundEntities.push({
            id: entityId,
            name: `${vehicle.year} ${vehicle.make} ${vehicle.model}`.trim(),
            type: "vehicle",
            data: vehicle,
          });
          return;
        }

        // Check properties
        const property = properties.find((p) => p.id === entityId);
        if (property) {
          foundEntities.push({
            id: entityId,
            name:
              property.propertyType || property.category || "Unknown Property",
            type: "property",
            data: property,
          });
          return;
        }

        // Check organizations
        const organization = organizations.find((o) => o.id === entityId);
        if (organization) {
          foundEntities.push({
            id: entityId,
            name: organization.name || organization.type || "Organization",
            type: "organization",
            data: organization,
          });
        }
      });

      // Smart filtering: Remove entities that are connected to other offenses/incidents
      const otherOffenseIncidentIds = [
        ...allOffenseIds.filter((id) => id !== targetOffenseId),
        ...allIncidentIds.filter((id) => id !== targetOffenseId),
      ];

      if (otherOffenseIncidentIds.length > 0) {
        console.log(
          `Entity cleanup - Checking ${foundEntities.length} entities against ${otherOffenseIncidentIds.length} other offenses/incidents`
        );

        const entitiesOnlyInTarget = foundEntities.filter((entity) => {
          // Check if this entity is connected to any other offense/incident
          const isConnectedToOthers = relations.some((relation: any) => {
            if (!relevantRelationTypes.includes(relation.relationType))
              return false;

            // Check if this relation involves our entity
            const entityInvolved =
              (relation.objectA?.objectType === "entity" &&
                relation.objectA?.globalId === entity.id) ||
              (relation.objectB?.objectType === "entity" &&
                relation.objectB?.globalId === entity.id);

            if (!entityInvolved) return false;

            // Check if this relation involves any OTHER offense/incident
            const otherOffenseInvolved = otherOffenseIncidentIds.some(
              (otherId) =>
                (relation.objectA?.objectType === "offense" &&
                  relation.objectA?.reportScopedId === otherId) ||
                (relation.objectB?.objectType === "offense" &&
                  relation.objectB?.reportScopedId === otherId) ||
                (relation.objectA?.objectType === "incident" &&
                  relation.objectA?.reportScopedId === otherId) ||
                (relation.objectB?.objectType === "incident" &&
                  relation.objectB?.reportScopedId === otherId)
            );

            return otherOffenseInvolved;
          });

          if (isConnectedToOthers) {
            console.log(
              `Entity cleanup - Skipping ${entity.type} "${entity.name}" (${entity.id}) - connected to other offense/incident`
            );
          } else {
            console.log(
              `Entity cleanup - Including ${entity.type} "${entity.name}" (${entity.id}) - only connected to target`
            );
          }

          return !isConnectedToOthers;
        });

        console.log(
          `Entity cleanup - Filtered from ${foundEntities.length} to ${entitiesOnlyInTarget.length} entities`
        );

        return entitiesOnlyInTarget;
      }

      console.log(
        `Entity cleanup - No other offenses/incidents to check against, including all ${foundEntities.length} entities`
      );

      return foundEntities;
    },
    []
  );

  // Start the confirmation process
  const startEntityCleanup = useCallback(
    (
      targetOffenseId: string,
      offenseOrIncidentName: string,
      offenseOrIncidentType: "offense" | "incident",
      people: PersonData[],
      vehicles: VehicleData[],
      properties: PropertyData[],
      organizations: OrganizationData[],
      relations: any[],
      allOffenseIds: string[] = [],
      allIncidentIds: string[] = []
    ): Promise<EntityCleanupResult> => {
      return new Promise((resolve) => {
        const entitiesToRemove = getEntitiesFromRelations(
          targetOffenseId,
          people,
          vehicles,
          properties,
          organizations,
          relations,
          allOffenseIds,
          allIncidentIds
        );

        // Debug logging
        console.log(
          "Entity cleanup - Entities found:",
          entitiesToRemove.map((e) => ({
            id: e.id,
            name: e.name,
            type: e.type,
          }))
        );

        if (entitiesToRemove.length === 0) {
          // No entities to clean up, proceed with deletion
          resolve({ completed: true, decisions: [] });
          return;
        }

        setEntities(entitiesToRemove);
        setCurrentEntityIndex(0);
        setDecisions([]);
        setRemoveFromReport(false);
        setOffenseOrIncidentName(offenseOrIncidentName);
        setOffenseOrIncidentType(offenseOrIncidentType);
        setIsProcessing(false);
        setIsOpen(true);
        setResolvePromise(() => resolve);
      });
    },
    [getEntitiesFromRelations]
  );

  // Handle confirming removal of current entity
  const handleConfirmRemoval = useCallback(() => {
    const currentEntity = entities[currentEntityIndex];
    if (!currentEntity) return;

    // Check if this is the last entity - if so, show loading state
    const isLastEntity = currentEntityIndex === entities.length - 1;
    if (isLastEntity) {
      setIsProcessing(true);
    }

    // Record the decision
    const newDecision: EntityRemovalDecision = {
      entityId: currentEntity.id,
      entityType: currentEntity.type,
      removeFromReport,
    };

    const updatedDecisions = [...decisions, newDecision];
    setDecisions(updatedDecisions);

    // Debug logging
    console.log("Entity cleanup - Decision recorded:", newDecision);
    console.log("Entity cleanup - All decisions so far:", updatedDecisions);

    // Move to next entity or finish
    const nextIndex = currentEntityIndex + 1;
    if (nextIndex < entities.length) {
      setCurrentEntityIndex(nextIndex);
      setRemoveFromReport(false); // Reset for next entity
    } else {
      // All entities processed - use setTimeout to show loading briefly
      setTimeout(() => {
        console.log(
          "Entity cleanup - Final decisions to process:",
          updatedDecisions
        );
        setIsProcessing(false);
        setIsOpen(false);
        if (resolvePromise) {
          resolvePromise({ completed: true, decisions: updatedDecisions });
        }
      }, 500); // Brief delay to show loading state
    }
  }, [
    entities,
    currentEntityIndex,
    decisions,
    removeFromReport,
    resolvePromise,
  ]);

  // Handle canceling the entire process
  const handleCancel = useCallback(() => {
    setIsProcessing(false);
    setIsOpen(false);
    if (resolvePromise) {
      resolvePromise({ completed: false, decisions: [] });
    }
  }, [resolvePromise]);

  const currentEntity = entities[currentEntityIndex];
  const progress =
    entities.length > 0
      ? `${currentEntityIndex + 1} of ${entities.length}`
      : "";

  const EntityCleanupDialog = (
    <Dialog
      open={isOpen}
      onClose={handleCancel}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: "12px",
          p: 1,
        },
      }}
    >
      <DialogContent sx={{ p: 3 }}>
        {entities.length > 1 && (
          <Box sx={{ mb: 1.5 }}>
            <Typography style="tag2" color={colors.grey[500]}>
              Entity {progress}
            </Typography>
          </Box>
        )}

        <Box sx={{ mb: 2 }}>
          <Typography style="h2" color={colors.grey[900]}>
            Deleting {offenseOrIncidentType === "offense" ? "Offense" : "Incident"}: {toTitleCase(offenseOrIncidentName)}
          </Typography>
        </Box>

        <Box sx={{ mb: 3 }}>
          <Typography style="body3" color={colors.grey[700]}>
            This action will also delete{" "}
            {currentEntity ? getEntityDisplayName(currentEntity) : "this entity"}.
          </Typography>
        </Box>

        <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
          <Checkbox
            checked={removeFromReport}
            onChange={(e) => setRemoveFromReport(e.target.checked)}
            label="Also remove from the report"
            size="small"
          />
        </Box>
      </DialogContent>

      <DialogActions sx={{ p: 3, pt: 0 }}>
        <Button
          label="Cancel All"
          color="grey"
          prominence={false}
          onClick={handleCancel}
          disabled={isProcessing}
        />
        <Button
          label={isProcessing ? "Processing..." : "Confirm"}
          color="blue"
          prominence={true}
          onClick={handleConfirmRemoval}
          disabled={isProcessing}
        />
      </DialogActions>
    </Dialog>
  );

  return {
    startEntityCleanup,
    EntityCleanupDialog,
    isCleanupInProgress: isOpen,
  };
};
