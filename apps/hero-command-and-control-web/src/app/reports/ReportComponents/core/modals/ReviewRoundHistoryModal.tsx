import { useAsset } from "@/app/apis/services/workflow/assets/hooks";
import { colors } from "@/design-system/tokens";
import {
  AccessTime as AccessTimeIcon,
  ArrowForward as ArrowForwardIcon,
  Assignment as AssignmentIcon,
  CheckCircle as CheckCircleIcon,
  Close as CloseIcon,
  Comment as CommentIcon,
  Edit as EditIcon,
  ExpandLess as ExpandLessIcon,
  ExpandMore as ExpandMoreIcon,
  HourglassEmpty as HourglassIcon,
  Person as PersonIcon,
} from "@mui/icons-material";
import {
  Box,
  Chip,
  Collapse,
  Divider,
  IconButton,
  Modal,
  Stack,
  Typography,
} from "@mui/material";
import { ReviewRound, ReviewStatus } from "proto/hero/reports/v2/reports_pb";
import React, { useState } from "react";

interface ReviewRoundHistoryModalProps {
  open: boolean;
  onClose: () => void;
  reviewRounds: ReviewRound[];
  reportId: string;
}

// Helper to get status icon and color
const getStatusDisplay = (status: ReviewStatus | string) => {
  // Handle both enum values and string values
  const statusStr = String(status);
  
  if (statusStr === "REVIEW_STATUS_APPROVED" || status === ReviewStatus.APPROVED) {
    return {
      icon: <CheckCircleIcon sx={{ fontSize: 20 }} />,
      color: colors.vine[600],
      label: "Approved",
      bgColor: colors.vine[50],
    };
  }
  
  if (statusStr === "REVIEW_STATUS_CHANGES_REQUESTED" || status === ReviewStatus.CHANGES_REQUESTED) {
    return {
      icon: <EditIcon sx={{ fontSize: 20 }} />,
      color: colors.amber[600],
      label: "Changes Requested",
      bgColor: colors.amber[50],
    };
  }
  
  if (statusStr === "REVIEW_STATUS_AWAITING_ACTION" || status === ReviewStatus.AWAITING_ACTION) {
    return {
      icon: <HourglassIcon sx={{ fontSize: 20 }} />,
      color: colors.blue[600],
      label: "Awaiting Review",
      bgColor: colors.blue[50],
    };
  }
  
  return {
    icon: <AssignmentIcon sx={{ fontSize: 20 }} />,
    color: colors.grey[600],
    label: "Unknown",
    bgColor: colors.grey[50],
  };
};

// Component for individual review round
const ReviewRoundItem: React.FC<{
  round: ReviewRound;
  isLast: boolean;
  index: number;
}> = ({ round, isLast, index }) => {
  const [expanded, setExpanded] = useState(false);
  const statusDisplay = getStatusDisplay(round.status);

  // Fetch reviewer information
  const { data: reviewerAsset } = useAsset(round.reviewerAssetId || "");
  const reviewerName = reviewerAsset?.asset?.name || "Unassigned";

  // Fetch creator information
  const { data: creatorAsset } = useAsset(round.createByAssetId || "");
  const creatorName = creatorAsset?.asset?.name || "Unknown";

  // Format dates
  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    return date.toLocaleString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const requestedDate = formatDate(round.requestedAt);
  const resolvedDate = formatDate(round.resolvedAt);
  const dueDate = formatDate(round.dueAt);

  return (
    <Box sx={{ display: "flex", position: "relative" }}>
      {/* Timeline connector */}
      {!isLast && (
        <Box
          sx={{
            position: "absolute",
            left: 20,
            top: 44,
            bottom: -20,
            width: 2,
            bgcolor: colors.grey[200],
          }}
        />
      )}

      {/* Timeline dot */}
      <Box
        sx={{
          width: 40,
          height: 40,
          borderRadius: "50%",
          bgcolor: statusDisplay.bgColor,
          border: `2px solid ${statusDisplay.color}`,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          flexShrink: 0,
          color: statusDisplay.color,
          zIndex: 1,
        }}
      >
        {statusDisplay.icon}
      </Box>

      {/* Content */}
      <Box sx={{ flex: 1, ml: 2, mb: 3 }}>
        <Box
          sx={{
            bgcolor: colors.grey[50],
            borderRadius: 2,
            p: 2,
            border: `1px solid ${colors.grey[200]}`,
          }}
        >
          {/* Header */}
          <Box sx={{ display: "flex", alignItems: "flex-start", justifyContent: "space-between" }}>
            <Box sx={{ flex: 1 }}>
              <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 0.5 }}>
                Round {index + 1} - Level {round.level}
              </Typography>

              <Stack direction="row" spacing={1} alignItems="center">
                <Chip
                  label={statusDisplay.label}
                  size="small"
                  icon={statusDisplay.icon}
                  sx={{
                    bgcolor: statusDisplay.bgColor,
                    color: statusDisplay.color,
                    fontWeight: 500,
                    "& .MuiChip-icon": {
                      color: statusDisplay.color,
                    },
                  }}
                />

                {round.snapshotVersion && (
                  <Chip
                    label={`v${round.snapshotVersion}`}
                    size="small"
                    variant="outlined"
                    sx={{ borderColor: colors.grey[300] }}
                  />
                )}
              </Stack>
            </Box>

            <IconButton
              size="small"
              onClick={() => setExpanded(!expanded)}
              sx={{ ml: 1 }}
            >
              {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            </IconButton>
          </Box>

          {/* Basic Info */}
          <Box sx={{ mt: 2 }}>
            <Stack spacing={1}>
              {/* Reviewer */}
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <PersonIcon sx={{ fontSize: 16, color: colors.grey[500] }} />
                <Typography variant="body2" color={colors.grey[600]}>
                  Reviewer:
                </Typography>
                <Typography variant="body2" fontWeight={500}>
                  {reviewerName}
                </Typography>
              </Box>

              {/* Dates */}
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <AccessTimeIcon sx={{ fontSize: 16, color: colors.grey[500] }} />
                <Typography variant="body2" color={colors.grey[600]}>
                  Requested:
                </Typography>
                <Typography variant="body2">
                  {requestedDate}
                </Typography>
                {round.resolvedAt && (
                  <>
                    <ArrowForwardIcon sx={{ fontSize: 14, color: colors.grey[400], mx: 0.5 }} />
                    <Typography variant="body2" color={colors.grey[600]}>
                      Resolved:
                    </Typography>
                    <Typography variant="body2">
                      {resolvedDate}
                    </Typography>
                  </>
                )}
              </Box>
            </Stack>
          </Box>

          {/* Expanded Details */}
          <Collapse in={expanded}>
            <Divider sx={{ my: 2 }} />

            <Stack spacing={1.5}>
              {/* Created By */}
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <Typography variant="body2" color={colors.grey[600]}>
                  Initiated by:
                </Typography>
                <Typography variant="body2">
                  {creatorName}
                </Typography>
              </Box>

              {/* Due Date */}
              {round.dueAt && (
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography variant="body2" color={colors.grey[600]}>
                    Due:
                  </Typography>
                  <Typography variant="body2" color={new Date(round.dueAt) < new Date() ? colors.rose[600] : undefined}>
                    {dueDate}
                  </Typography>
                </Box>
              )}

              {/* Routing Info */}
              {(round.sentToLevel !== undefined || round.sentToAssetId) && (
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography variant="body2" color={colors.grey[600]}>
                    Routed to:
                  </Typography>
                  <Typography variant="body2">
                    {round.sentToLevel === 0 ? "Author" : round.sentToAssetId ? `Asset: ${round.sentToAssetId}` : `Level ${round.sentToLevel}`}
                  </Typography>
                </Box>
              )}

              {/* Reviewer Note */}
              {round.roundNote && (
                <Box sx={{ bgcolor: colors.grey[100], p: 1.5, borderRadius: 1 }}>
                  <Box sx={{ display: "flex", alignItems: "center", gap: 0.5, mb: 0.5 }}>
                    <CommentIcon sx={{ fontSize: 14, color: colors.grey[500] }} />
                    <Typography variant="caption" color={colors.grey[600]} fontWeight={500}>
                      Reviewer Note:
                    </Typography>
                  </Box>
                  <Typography variant="body2">
                    {round.roundNote}
                  </Typography>
                </Box>
              )}

              {/* Instructions for Reviewer */}
              {round.noteForReviewer && (
                <Box sx={{ bgcolor: colors.blue[50], p: 1.5, borderRadius: 1 }}>
                  <Box sx={{ display: "flex", alignItems: "center", gap: 0.5, mb: 0.5 }}>
                    <AssignmentIcon sx={{ fontSize: 14, color: colors.blue[600] }} />
                    <Typography variant="caption" color={colors.blue[700]} fontWeight={500}>
                      Instructions:
                    </Typography>
                  </Box>
                  <Typography variant="body2">
                    {round.noteForReviewer}
                  </Typography>
                </Box>
              )}
            </Stack>
          </Collapse>
        </Box>
      </Box>
    </Box>
  );
};

export const ReviewRoundHistoryModal: React.FC<ReviewRoundHistoryModalProps> = ({
  open,
  onClose,
  reviewRounds,
  reportId,
}) => {
  // Sort review rounds chronologically
  const sortedRounds = [...reviewRounds].sort((a, b) => {
    const dateA = new Date(a.requestedAt || 0).getTime();
    const dateB = new Date(b.requestedAt || 0).getTime();
    return dateA - dateB;
  });

  // Calculate statistics (handle both enum and string values)
  const totalRounds = sortedRounds.length;
  const approvedRounds = sortedRounds.filter(r => 
    String(r.status) === "REVIEW_STATUS_APPROVED" || r.status === ReviewStatus.APPROVED
  ).length;
  const changesRequestedRounds = sortedRounds.filter(r => 
    String(r.status) === "REVIEW_STATUS_CHANGES_REQUESTED" || r.status === ReviewStatus.CHANGES_REQUESTED
  ).length;
  const pendingRounds = sortedRounds.filter(r => 
    String(r.status) === "REVIEW_STATUS_AWAITING_ACTION" || r.status === ReviewStatus.AWAITING_ACTION
  ).length;

  return (
    <Modal
      open={open}
      onClose={onClose}
      aria-labelledby="review-history-modal"
    >
      <Box
        sx={{
          position: "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          bgcolor: "background.paper",
          borderRadius: 2,
          boxShadow: 24,
          width: { xs: "90%", sm: "600px", md: "700px" },
          maxHeight: "85vh",
          display: "flex",
          flexDirection: "column",
        }}
      >
        {/* Header */}
        <Box
          sx={{
            px: 3,
            py: 2,
            borderBottom: `1px solid ${colors.grey[200]}`,
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <Box>
            <Typography variant="h6" fontWeight={600}>
              Review Round History
            </Typography>
            <Typography variant="caption" color={colors.grey[600]}>
              Report ID: {reportId.slice(0, 8).toUpperCase()}
            </Typography>
          </Box>
          <IconButton onClick={onClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>

        {/* Statistics Bar */}
        {totalRounds > 0 && (
          <Box
            sx={{
              px: 3,
              py: 2,
              bgcolor: colors.grey[50],
              borderBottom: `1px solid ${colors.grey[200]}`,
            }}
          >
            <Stack direction="row" spacing={3}>
              <Box>
                <Typography variant="caption" color={colors.grey[600]}>
                  Total Rounds
                </Typography>
                <Typography variant="h6" fontWeight={600}>
                  {totalRounds}
                </Typography>
              </Box>
              <Divider orientation="vertical" flexItem />
              <Box>
                <Typography variant="caption" color={colors.vine[700]}>
                  Approved
                </Typography>
                <Typography variant="h6" fontWeight={600} color={colors.vine[600]}>
                  {approvedRounds}
                </Typography>
              </Box>
              <Box>
                <Typography variant="caption" color={colors.amber[700]}>
                  Changes Requested
                </Typography>
                <Typography variant="h6" fontWeight={600} color={colors.amber[600]}>
                  {changesRequestedRounds}
                </Typography>
              </Box>
              <Box>
                <Typography variant="caption" color={colors.blue[700]}>
                  Pending
                </Typography>
                <Typography variant="h6" fontWeight={600} color={colors.blue[600]}>
                  {pendingRounds}
                </Typography>
              </Box>
            </Stack>
          </Box>
        )}

        {/* Content */}
        <Box sx={{ flex: 1, overflow: "auto", px: 3, py: 3 }}>
          {sortedRounds.length === 0 ? (
            <Box
              sx={{
                textAlign: "center",
                py: 6,
                color: colors.grey[500],
              }}
            >
              <AssignmentIcon sx={{ fontSize: 48, mb: 2, opacity: 0.5 }} />
              <Typography variant="body1">
                No review rounds yet
              </Typography>
              <Typography variant="caption">
                Review rounds will appear here once the report is submitted for review
              </Typography>
            </Box>
          ) : (
            <Box>
              {sortedRounds.map((round, index) => (
                <ReviewRoundItem
                  key={round.id}
                  round={round}
                  isLast={index === sortedRounds.length - 1}
                  index={index}
                />
              ))}
            </Box>
          )}
        </Box>
      </Box>
    </Modal>
  );
};

export default ReviewRoundHistoryModal;