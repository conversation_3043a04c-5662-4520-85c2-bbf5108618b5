import {
  CheckCircle as CheckCircleIcon,
  Person as PersonIcon
} from "@mui/icons-material";
import {
  <PERSON><PERSON>,
  Box,
  Button,
  Chip,
  CircularProgress,
  Divider,
  FormControlLabel,
  Modal,
  Radio,
  RadioGroup,
  Stack,
  TextField,
  Typography,
} from "@mui/material";
import React, { useEffect, useState } from "react";
// Using MUI theme colors instead of design-system tokens
import { useGetEligibleReviewers } from "@/app/apis/services/workflow/reports/v2/hooks";
import type {
  EligibleReviewer,
  ReviewRound
} from "proto/hero/reports/v2/reports_pb";

interface ReviewerSelectionModalProps {
  open: boolean;
  onClose: () => void;
  reportId: string;
  onSubmit: (preferredReviewerId: string, note: string) => void;
  isSubmitting?: boolean;
  isResubmission?: boolean;
  hasExistingReviewRounds?: boolean;
  lastReviewRound?: ReviewRound;
}

export const ReviewerSelectionModal: React.FC<ReviewerSelectionModalProps> = ({
  open,
  onClose,
  reportId,
  onSubmit,
  isSubmitting = false,
  isResubmission = false,
  hasExistingReviewRounds = false,
  lastReviewRound,
}) => {
  const [selectedReviewerId, setSelectedReviewerId] = useState<string>("");
  const [note, setNote] = useState<string>("");

  // Fetch eligible reviewers
  const {
    data: reviewersResponse,
    isLoading,
    error,
  } = useGetEligibleReviewers(reportId, {
    enabled: open && !!reportId,
  });

  const reviewers: EligibleReviewer[] = reviewersResponse?.reviewers || [];
  const nextLevel = reviewersResponse?.nextLevel || 1;
  const levelName = reviewersResponse?.levelName || "Review";

  // Find the original reviewer's name from the eligible reviewers list
  const originalReviewerName = lastReviewRound?.reviewerAssetId
    ? reviewers.find(r => r.assetId === lastReviewRound.reviewerAssetId)?.name
    : null;

  // Reset selection when modal opens
  useEffect(() => {
    if (open) {
      setSelectedReviewerId("");
      setNote("");
    }
  }, [open]);

  const handleSubmit = () => {
    onSubmit(selectedReviewerId, note);
  };

  const getReviewerWorkloadColor = (count: number | undefined) => {
    const reviewCount = count ?? 0;
    if (reviewCount === 0) return "success";
    if (reviewCount <= 2) return "info";
    if (reviewCount <= 4) return "warning";
    return "error";
  };

  const getReviewerWorkloadLabel = (count: number | undefined) => {
    const reviewCount = count ?? 0;
    if (reviewCount === 0) return "Available";
    if (reviewCount === 1) return `${reviewCount} active review`;
    return `${reviewCount} active reviews`;
  };

  return (
    <Modal
      open={open}
      onClose={!isSubmitting ? onClose : undefined}
      aria-labelledby="reviewer-selection-modal"
    >
      <Box
        sx={{
          position: "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          width: 600,
          bgcolor: "background.paper",
          borderRadius: 2,
          boxShadow: 24,
          p: 4,
          maxHeight: "80vh",
          overflow: "auto",
        }}
      >
        <Typography variant="h5" component="h2" gutterBottom>
          {isResubmission ? "Resubmit for Review" : "Submit for Review"}
        </Typography>

        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          Level {nextLevel}: {levelName}
        </Typography>

        {isResubmission && (
          <Alert severity="info" sx={{ mb: 3 }}>
            <Typography variant="body2">
              This report was previously reviewed and changes were requested.
              {hasExistingReviewRounds && originalReviewerName ? (
                <>
                  By default, it will be sent back to <strong>{originalReviewerName}</strong> (the original reviewer) from the same level.
                  You can optionally select a different reviewer below.
                </>
              ) : hasExistingReviewRounds ? (
                <>
                  By default, it will be sent back to the <strong>original reviewer</strong> from the same level.
                  You can optionally select a different reviewer below.
                </>
              ) : (
                "Select a reviewer below or let the system auto-assign."
              )}
            </Typography>
          </Alert>
        )}

        {isLoading ? (
          <Box display="flex" justifyContent="center" py={4}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Alert severity="error" sx={{ mb: 2 }}>
            Failed to load eligible reviewers. You can still submit without selecting a specific reviewer.
          </Alert>
        ) : reviewers.length === 0 ? (
          <Alert severity="warning" sx={{ mb: 2 }}>
            No eligible reviewers found. The system will assign a reviewer automatically when available.
          </Alert>
        ) : (
          <>
            <Typography variant="subtitle1" gutterBottom sx={{ mb: 2 }}>
              {isResubmission && hasExistingReviewRounds
                ? "Override Default Reviewer (Optional)"
                : "Select a Reviewer (Optional)"}
            </Typography>

            <RadioGroup
              value={selectedReviewerId}
              onChange={(e) => setSelectedReviewerId(e.target.value)}
            >
              {/* Auto-select option */}
              <FormControlLabel
                value=""
                control={<Radio />}
                label={
                  <Box display="flex" alignItems="center" gap={1}>
                    <Typography>
                      {isResubmission && hasExistingReviewRounds && originalReviewerName
                        ? `Use default (send to ${originalReviewerName})`
                        : isResubmission && hasExistingReviewRounds
                          ? "Use default (send to original reviewer)"
                          : "Let the system assign automatically"}
                    </Typography>
                    {isResubmission && hasExistingReviewRounds && (
                      <Chip
                        label="Default"
                        size="small"
                        color="primary"
                        variant="outlined"
                      />
                    )}
                  </Box>
                }
                sx={{
                  mb: 2,
                  p: 1,
                  border: 1,
                  borderColor: "divider",
                  borderRadius: 1,
                  "&:hover": {
                    backgroundColor: "action.hover",
                  },
                }}
              />

              <Divider sx={{ my: 2 }}>OR SELECT A SPECIFIC REVIEWER</Divider>

              {/* List of eligible reviewers */}
              {reviewers.map((reviewer: EligibleReviewer) => (
                <FormControlLabel
                  key={reviewer.assetId}
                  value={reviewer.assetId}
                  control={<Radio />}
                  label={
                    <Stack spacing={1} sx={{ width: "100%" }}>
                      <Box display="flex" alignItems="center" gap={2}>
                        <PersonIcon sx={{ color: "text.secondary" }} />
                        <Box flex={1}>
                          <Typography variant="body1">
                            {reviewer.name || "Unknown"}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {reviewer.assetType?.replace("ASSET_TYPE_", "")}
                            {reviewer.email && ` • ${reviewer.email}`}
                          </Typography>
                        </Box>
                        <Chip
                          label={getReviewerWorkloadLabel(reviewer.activeReviewCount)}
                          size="small"
                          color={getReviewerWorkloadColor(reviewer.activeReviewCount)}
                          variant="outlined"
                        />
                      </Box>
                    </Stack>
                  }
                  sx={{
                    mb: 1,
                    p: 1.5,
                    border: 2,
                    borderColor: selectedReviewerId === reviewer.assetId ? "primary.main" : "divider",
                    borderRadius: 1,
                    "&:hover": {
                      backgroundColor: "action.hover",
                    },
                  }}
                />
              ))}
            </RadioGroup>
          </>
        )}

        <TextField
          fullWidth
          multiline
          rows={3}
          label="Note for Reviewer (Optional)"
          value={note}
          onChange={(e) => setNote(e.target.value)}
          placeholder={
            isResubmission
              ? "Explain what changes you've made based on the reviewer's feedback..."
              : "Add any context or instructions for the reviewer..."
          }
          sx={{ mt: 3, mb: 3 }}
        />

        <Box display="flex" justifyContent="flex-end" gap={2}>
          <Button
            variant="outlined"
            onClick={onClose}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            onClick={handleSubmit}
            disabled={isSubmitting}
            startIcon={
              isSubmitting ? (
                <CircularProgress size={20} color="inherit" />
              ) : (
                <CheckCircleIcon />
              )
            }
          >
            {isSubmitting ? "Submitting..." : "Submit for Review"}
          </Button>
        </Box>
      </Box>
    </Modal>
  );
};