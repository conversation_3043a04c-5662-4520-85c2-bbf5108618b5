import { Property } from "@/app/apis/services/workflow/property/types";

/**
 * Property validation utilities
 * All properties now use dynamic schemas exclusively.
 */

export interface ValidationResult {
    isValid: boolean;
    errors: string[];
}

/**
 * Validates that a property has the required fields for dynamic schema
 * @param property - Property to validate
 * @returns ValidationResult with success status and any errors
 */
export const validateProperty = (property: Partial<Property>): ValidationResult => {
    const errors: string[] = [];

    // Check required dynamic schema fields
    if (!property.schemaId) {
        errors.push("schemaId is required for all properties");
    }

    if (typeof property.schemaVersion !== 'number') {
        errors.push("schemaVersion is required for all properties");
    }

    if (!property.details || Object.keys(property.details).length === 0) {
        errors.push("details object is required and must contain form data");
    }

    return {
        isValid: errors.length === 0,
        errors,
    };
};