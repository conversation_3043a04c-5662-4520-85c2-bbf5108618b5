import { stringToPropertyStatus, stringToPropertyType } from '@/app/apis/services/workflow/property/enumConverters';
import { nibrsToPropertyType } from '@/app/apis/services/workflow/property/propertyTypeManager';
import { Property } from '@/app/apis/services/workflow/property/types';
import { NIBRSPropertyType, PropertyDisposalType, PropertyStatus } from "proto/hero/property/v1/property_pb";

// Use NIBRSPropertyType directly

// Type for file attachment metadata
interface FileAttachment {
    fileId: string;
    fileName: string;
    fileSize: number;
    fileType: string;
    uploadedAt: string;
}

// Type for property with metadata that may contain file attachments
interface PropertyWithMetadata extends Property {
    metadata?: {
        fileAttachments?: FileAttachment[];
    };
}

/**
 * Gets property data from dynamic schema details
 * PropertySchema support has been removed - all properties now use dynamic schemas
 */
export const getPropertyData = (property: Property): any => {
    return property.details || {};
};

/**
 * Converts form values to property backend format
 * Maps form data to property backend structure with support for dynamic schemas
 * @param values - Form values from property form
 * @param dispatcherAsset - Dispatcher asset containing orgId and user ID
 * @param schemaId - Optional schema ID for dynamic schema support
 * @param schemaVersion - Optional schema version for dynamic schema support
 */
export const createPropertyFromValues = (
    values: any,
    dispatcherAsset?: { id: string; orgId: number } | null,
    schemaId?: string,
    schemaVersion?: number
): Omit<Property, 'id' | 'createTime' | 'updateTime' | 'version' | 'status' | 'resourceType'> => {

    // Map status from form to PropertyStatus enum
    const mapStatus = (status: string): PropertyStatus => {
        if (!status) return PropertyStatus.UNSPECIFIED;

        const statusMap: Record<string, string> = {
            'intake_pending': 'PROPERTY_STATUS_INTAKE_PENDING',
            'collected': 'PROPERTY_STATUS_COLLECTED',
            'checked_in': 'PROPERTY_STATUS_CHECKED_IN',
            'checked_out': 'PROPERTY_STATUS_CHECKED_OUT',
            'recovered': 'PROPERTY_STATUS_RECOVERED',
            'found': 'PROPERTY_STATUS_FOUND',
            'safekeeping': 'PROPERTY_STATUS_SAFEKEEPING',
            'awaiting_disposition': 'PROPERTY_STATUS_AWAITING_DISPOSITION',
            'disposed': 'PROPERTY_STATUS_DISPOSED',
            'missing': 'PROPERTY_STATUS_MISSING',
            'stolen': 'PROPERTY_STATUS_STOLEN',
        };

        // Align fallback with PropertyResults table and backend default: INTAKE_PENDING ("Logged")
        const enumString = statusMap[status] || 'PROPERTY_STATUS_INTAKE_PENDING';
        return stringToPropertyStatus(enumString);
    };

    // Keep property type as string for dynamic schema details (backend expects string format)
    const normalizePropertyType = (propertyType: any): string => {
        if (!propertyType || typeof propertyType !== 'string') return 'found'; // Default to found
        // Normalize enum format to lowercase string format
        if (propertyType.startsWith('PROPERTY_TYPE_')) {
            const typeMap: Record<string, string> = {
                'PROPERTY_TYPE_FOUND': 'found',
                'PROPERTY_TYPE_SEIZED': 'seized',
                'PROPERTY_TYPE_STOLEN': 'stolen',
                'PROPERTY_TYPE_SAFEKEEPING': 'safekeeping',
                'PROPERTY_TYPE_MISSING': 'missing',
                'PROPERTY_TYPE_RECOVERED': 'recovered',
                'PROPERTY_TYPE_BURNED': 'burned',
                'PROPERTY_TYPE_FORGED': 'forged',
                'PROPERTY_TYPE_DAMAGED': 'damaged',
                'PROPERTY_TYPE_UNKNOWN': 'unknown',
            };
            return typeMap[propertyType] || 'unknown';
        }
        // Already in correct string format
        return propertyType.toLowerCase();
    };

    // Always use dynamic schema approach (PropertySchema support removed)
    // If no entity schema exists, use null to allow foreign key constraint
    const dynamicSchemaId = values._schemaId || schemaId || null;
    const dynamicSchemaVersion = values._schemaVersion || schemaVersion || 0;

    // FIXED: Explicitly filter form values to avoid exposing internal fields
    // Create property details from explicit allowed fields only
    const allowedDetailFields = [
        'propertyType', 'make', 'model', 'serial', 'color', 'description', 'value',
        'condition', 'quantity', 'weight', 'dimensions', 'category', 'subcategory',
        'brand', 'identifiers', 'tags', 'barcode', 'itemNumber', 'dateManufactured',
        'dateAcquired', 'vendor', 'receipt', 'warranty', 'location', 'owner',
        'custodian', 'department', 'room', 'building', 'shelf', 'bin', 'notes'
    ];

    // Start with explicit property details if provided
    let dynamicPropertyDetails = values._propertyDetails || {};

    // Add only allowed fields from form values (excludes internal fields like _schemaId)
    for (const field of allowedDetailFields) {
        if (values[field] !== undefined) {
            dynamicPropertyDetails[field] = values[field];
        }
    }

    // Merge additional details from wizard (NIBRS mappings)
    if (values._additionalDetails) {
        dynamicPropertyDetails = { ...dynamicPropertyDetails, ...values._additionalDetails };
    }

    // Normalize propertyType to string format for backend compatibility  
    const propType = values.propertyType || dynamicPropertyDetails.propertyType;
    // Remove propertyType from details if it exists - it should only be top-level
    delete dynamicPropertyDetails.propertyType;

    // Map property type to top-level protobuf enum field using centralized logic
    const mapPropertyType = (type: any): NIBRSPropertyType => {
        if (!type) return NIBRSPropertyType.PROPERTY_TYPE_UNSPECIFIED;

        if (typeof type === 'number') {
            // Already a protobuf enum value
            return type as NIBRSPropertyType;
        }

        if (typeof type === 'string') {
            // Use centralized NIBRS mapping for known NIBRS types
            const lowerType = type.toLowerCase();
            if (['found', 'seized', 'stolen', 'recovered', 'burned', 'forged', 'damaged', 'unknown'].includes(lowerType)) {
                return nibrsToPropertyType(lowerType as any);
            }

            // Legacy mappings for non-NIBRS types
            if (lowerType === 'safekeeping') return NIBRSPropertyType.PROPERTY_TYPE_FOUND;
            if (lowerType === 'missing') return NIBRSPropertyType.PROPERTY_TYPE_UNKNOWN;
        }

        return NIBRSPropertyType.PROPERTY_TYPE_UNSPECIFIED;
    };

    // Prefer explicit wizard-mapped nibrsPropertyType when provided
    let finalNibrsType: NIBRSPropertyType = NIBRSPropertyType.PROPERTY_TYPE_UNSPECIFIED;
    if (values.nibrsPropertyType !== undefined && values.nibrsPropertyType !== null) {
        if (typeof values.nibrsPropertyType === 'number') {
            finalNibrsType = values.nibrsPropertyType as NIBRSPropertyType;
        } else if (typeof values.nibrsPropertyType === 'string') {
            // Accept enum string like "PROPERTY_TYPE_FOUND" or lowercase like "found"
            const upper = values.nibrsPropertyType.toUpperCase();
            if (upper.startsWith('PROPERTY_TYPE_')) {
                finalNibrsType = stringToPropertyType(upper);
            } else {
                finalNibrsType = mapPropertyType(values.nibrsPropertyType);
            }
        }
    }
    // Fallback: use explicit wizard loss type if present
    if (finalNibrsType === NIBRSPropertyType.PROPERTY_TYPE_UNSPECIFIED && values._nibrsLossType) {
        finalNibrsType = nibrsToPropertyType(values._nibrsLossType as any);
    }
    // Final fallback: derive from propType/property details
    if (finalNibrsType === NIBRSPropertyType.PROPERTY_TYPE_UNSPECIFIED) {
        finalNibrsType = mapPropertyType(propType);
    }


    return {
        orgId: dispatcherAsset?.orgId || 0,
        propertyNumber: values.propertyNumber || '',
        isEvidence: Boolean(values.isEvidence),
        retentionPeriod: values.retentionPeriod || '',
        propertyStatus: mapStatus(values.status),
        nibrsPropertyType: finalNibrsType,
        disposalType: values.disposalType || 0,
        notes: values.notes || '',
        currentCustodian: values.currentCustodian || '',
        currentLocation: values.currentLocation || '',
        custodyChain: [],

        // Dynamic schema fields (required for all properties now)
        schemaId: dynamicSchemaId,
        schemaVersion: dynamicSchemaVersion,
        details: dynamicPropertyDetails,

        createdBy: dispatcherAsset?.id || '',
        updatedBy: dispatcherAsset?.id || '',
    };
};

/**
 * Maps entity property types to property backend types
 */
const mapEntityPropertyType = (type: string): NIBRSPropertyType => {
    if (!type) return NIBRSPropertyType.PROPERTY_TYPE_UNSPECIFIED;

    const typeMap: Record<string, string> = {
        'seized': 'PROPERTY_TYPE_SEIZED',
        'found': 'PROPERTY_TYPE_FOUND',
        'stolen': 'PROPERTY_TYPE_STOLEN',
        'safekeeping': 'PROPERTY_TYPE_SAFEKEEPING',
        'missing': 'PROPERTY_TYPE_MISSING',
        'recovered': 'PROPERTY_TYPE_RECOVERED',
        'evidence': 'PROPERTY_TYPE_SEIZED', // Map evidence to seized
    };

    const enumString = typeMap[type?.toLowerCase()] || 'PROPERTY_TYPE_UNSPECIFIED';
    return stringToPropertyType(enumString);
};

/**
 * Maps disposal types from entity format to property backend format
 */
const mapDisposalType = (disposalType?: string): PropertyDisposalType => {
    if (!disposalType) return PropertyDisposalType.UNSPECIFIED;
    switch (disposalType?.toLowerCase()) {
        case 'released':
            return PropertyDisposalType.RELEASED;
        case 'destroyed':
            return PropertyDisposalType.DESTROYED;
        case 'auctioned':
            return PropertyDisposalType.AUCTIONED;
        case 'agency_retain':
            return PropertyDisposalType.AGENCY_RETAIN;
        case 'transferred':
            return PropertyDisposalType.TRANSFERRED;
        default:
            return PropertyDisposalType.UNSPECIFIED;
    }
};

/**
 * Converts property backend format to form format for UI compatibility
 * All properties now use dynamic schemas (PropertySchema support removed)
 */
export const propertyToEntityFormat = (property: Property): any => {
    const propertyData = getPropertyData(property);

    return {
        id: property.id,
        entityType: "ENTITY_TYPE_PROPERTY",
        // Include propertyStatus at top-level for consumers expecting it
        propertyStatus: property.propertyStatus,

        // Include schema metadata (always present now)
        schemaId: property.schemaId,
        schemaVersion: property.schemaVersion,
        isDynamicSchema: true,

        data: {
            propertyInformationSection: {
                ...propertyData,
                // Add status inside section for older consumers
                status: property.propertyStatus,
                // CRITICAL: Include propertyType from top-level property field
                nibrsPropertyType: property.nibrsPropertyType,
            },
            // Keep the flat structure for backward compatibility
            ...propertyData,
            // CRITICAL: Include propertyType at flat level too for backward compatibility
            nibrsPropertyType: property.nibrsPropertyType,
            propertyNumber: property.propertyNumber,
            isEvidence: property.isEvidence,
            retentionPeriod: property.retentionPeriod,
            disposalType: property.disposalType,
            notes: property.notes,
            currentCustodian: property.currentCustodian,
            currentLocation: property.currentLocation,
            // Handle uploaded images from metadata (placeholder for future file attachment support)
            uploadImage: [],
        },
        createTime: property.createTime,
        updateTime: property.updateTime,
        status: property.status,
        version: property.version,
        createdBy: property.createdBy,
        updatedBy: property.updatedBy,
    };
};