/**
 * Local utility to convert our default property schema to FormRenderer configuration
 * Moved from schemas/ folder since we only use it for the default property schema
 */

import { EntitySchema } from 'proto/hero/entity/v1/entity_pb';
import { getCategoryDropdownOptions, getReadablePropertyCategory } from '../../../../utils/propertyHelpers';


export interface FormRendererConfig {
    id: string;
    title: string;
    sections: FormRendererSection[];
}

export interface FormRendererSection {
    id: string;
    title: string;
    fields: FormRendererField[];
}

export interface FormRendererField {
    id: string;
    type: string;
    title: string;
    placeholder?: string;
    required?: boolean;
    options?: { value: string; label: string }[];
    row?: number;
    width?: string;
    defaultValue?: any;
    validation?: {
        min?: number;
        max?: number;
        minLength?: number;
        maxLength?: number;
    };
    enableSearch?: boolean;
}

/**
 * Apply NIBRS category mapping to the form configuration
 */
const applyCategoryMapping = (config: FormRendererConfig): FormRendererConfig => {
    return {
        ...config,
        sections: config.sections.map(section => ({
            ...section,
            fields: section.fields.map(field => {
                if (field.id === 'category') {
                    return {
                        ...field,
                        options: getCategoryDropdownOptions()
                    };
                }
                return field;
            })
        }))
    };
};

/**
 * Convert EntitySchema to FormRenderer configuration
 */
export const schemaToFormConfig = (schema: EntitySchema): FormRendererConfig => {
    // Handle legacy schema format with sections
    if (schema.sections) {
        const config = {
            id: schema.id || 'default-property-schema',
            title: schema.name || 'Property Information',
            sections: schema.sections.map(section => ({
                id: section.id,
                title: section.title,
                fields: section.fields.map(field => ({
                    id: field.id,
                    type: field.type,
                    title: field.title,
                    placeholder: field.placeholder,
                    required: field.required,
                    options: field.options?.map(opt => ({
                        value: opt.value,
                        label: opt.label
                    })),
                    defaultValue: field.defaultValue,
                    validation: field.validation,
                    enableSearch: (field as any).enableSearch
                }))
            }))
        };
        return applyCategoryMapping(config);
    }

    // Handle schemaDefinition with nested sections (current format)
    if (schema.schemaDefinition && schema.schemaDefinition.sections) {
        const config = {
            id: schema.id || 'default-property-schema',
            title: schema.name || 'Property Information',
            sections: schema.schemaDefinition.sections.map(section => ({
                id: section.id,
                title: section.title,
                fields: section.fields.map(field => ({
                    id: field.id,
                    type: field.type,
                    title: field.title,
                    placeholder: field.placeholder,
                    required: field.required,
                    options: field.options?.map(opt => ({
                        value: opt.value,
                        label: opt.label
                    })),
                    defaultValue: field.defaultValue,
                    validation: field.validation,
                    enableSearch: (field as any).enableSearch
                }))
            }))
        };
        return applyCategoryMapping(config);
    }

    // Handle JSON Schema format (properties-based)
    if (schema.schemaDefinition && schema.schemaDefinition.properties) {
        const schemaDef = schema.schemaDefinition;
        const properties = schemaDef.properties || {};
        const required = schemaDef.required || [];

        // Convert JSON Schema properties to form fields
        const fields: FormRendererField[] = Object.keys(properties).map(fieldId => {
            const prop = properties[fieldId];
            const field: FormRendererField = {
                id: fieldId,
                type: getFieldType(prop),
                title: getFieldTitle(fieldId),
                required: required.includes(fieldId),
                validation: getFieldValidation(prop)
            };

            // Handle enum options
            if (prop.enum) {
                field.options = prop.enum.map((value: string) => ({
                    value,
                    label: getFieldLabel(value)
                }));
            }

            return field;
        });

        const config = {
            id: schema.id || 'default-property-schema',
            title: schema.name || 'Property Information',
            sections: [{
                id: 'property-details',
                title: 'Property Details',
                fields
            }]
        };
        return applyCategoryMapping(config);
    }

    // Fallback to default configuration
    const fallbackConfig = {
        id: 'default-property-schema',
        title: 'Property Information',
        sections: [{
            id: 'property-details',
            title: 'Property Details',
            fields: []
        }]
    };
    return applyCategoryMapping(fallbackConfig);
};

/**
 * Helper function to determine field type from JSON Schema property
 */
const getFieldType = (prop: any): string => {
    if (prop.enum) return 'dropdown';
    if (prop.type === 'number') return 'number';
    if (prop.type === 'string' && prop.maxLength > 200) return 'textarea';
    return 'text';
};

/**
 * Helper function to generate field title from field ID
 */
const getFieldTitle = (fieldId: string): string => {
    const titleMap: Record<string, string> = {
        description: 'Description',
        category: 'Category',
        nibrsPropertyType: 'Property Type',
        quantity: 'Quantity',
        identifiers: 'Make/Model/Brand',
        serialNumber: 'Serial Number',
        value: 'Estimated Value',
        condition: 'Condition',
        owner: 'Owner Information'
    };
    return titleMap[fieldId] || fieldId.charAt(0).toUpperCase() + fieldId.slice(1);
};

/**
 * Helper function to generate field label from enum value
 */
const getFieldLabel = (value: string): string => {
    return value.charAt(0).toUpperCase() + value.slice(1);
};

/**
 * Helper function to extract validation rules from JSON Schema property
 */
const getFieldValidation = (prop: any) => {
    const validation: any = {};
    if (prop.maxLength) validation.maxLength = prop.maxLength;
    if (prop.minLength) validation.minLength = prop.minLength;
    if (prop.minimum) validation.min = prop.minimum;
    if (prop.maximum) validation.max = prop.maximum;
    return validation;
};

/**
 * Convert frontend category label to backend NIBRS enum string
 * "Aircraft" → "NIBRS_PROPERTY_DESCRIPTION_AIRCRAFT"
 */
export const categoryLabelToNIBRSEnum = (label: string): string => {
    const categoryOptions = getCategoryDropdownOptions();
    const option = categoryOptions.find(opt => opt.label === label);
    return option ? option.value : label; // Fallback to original value if not found
};

/**
 * Convert backend NIBRS enum string to frontend category label  
 * "NIBRS_PROPERTY_DESCRIPTION_AIRCRAFT" → "Aircraft"
 */
export const nibrsEnumToCategoryLabel = (enumValue: string): string => {
    return getReadablePropertyCategory(enumValue);
};

/**
 * Extract property details from form values, excluding admin fields
 */
export const formValuesToPropertyDetails = (
    formValues: Record<string, any>,
    schema: EntitySchema
): Record<string, any> => {
    const details: Record<string, any> = {};

    // Define admin field IDs that should be excluded from details
    // nibrsPropertyType should be included in details for dynamic schema
    const adminFieldIds = new Set([
        'propertyNumber',
        'status',
        'isEvidence',
        'retentionPeriod',
        'currentCustodian',
        'currentLocation',
        'notes',
        'caseNumber',
        'disposalType'
    ]);

    // Flatten form values from sectioned format to flat format
    const flatValues: Record<string, any> = {};
    Object.keys(formValues).forEach(key => {
        const value = formValues[key];
        if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
            // Flatten nested objects (sections)
            Object.keys(value).forEach(fieldKey => {
                flatValues[fieldKey] = value[fieldKey];
            });
        } else {
            flatValues[key] = value;
        }
    });

    // Extract only non-admin fields for details
    Object.keys(flatValues).forEach(fieldId => {
        if (!adminFieldIds.has(fieldId) && flatValues[fieldId] !== undefined) {
            // Category is already in NIBRS enum format from dropdown, no conversion needed
            details[fieldId] = flatValues[fieldId];
        }
    });

    return details;
};