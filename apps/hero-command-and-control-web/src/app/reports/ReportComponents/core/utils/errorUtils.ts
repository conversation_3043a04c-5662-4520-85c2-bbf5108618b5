/**
 * Error handling utilities for report operations
 */

/**
 * Converts technical error messages into user-friendly, actionable messages
 */
export const humanizeErrorMessage = (error: any): string => {
  const rawMessage = error?.message || error?.response?.data?.message || "";
  
  // Check for specific error patterns and return user-friendly messages
  if (rawMessage.includes("no available reviewers found")) {
    const levelMatch = rawMessage.match(/level (\d+)/);
    const level = levelMatch ? levelMatch[1] : "the next";
    return `No reviewers are currently available for level ${level}. Please contact your administrator or try again later.`;
  }
  
  if (rawMessage.includes("unauthorized") || rawMessage.includes("permission")) {
    return "You don't have permission to perform this action.";
  }
  
  if (rawMessage.includes("not found")) {
    return "The requested resource could not be found. It may have been deleted or moved.";
  }
  
  if (rawMessage.includes("already approved")) {
    return "This report has already been approved and cannot be modified.";
  }
  
  if (rawMessage.includes("already submitted")) {
    return "This report has already been submitted for review.";
  }
  
  if (rawMessage.includes("validation") || rawMessage.includes("invalid")) {
    return "The report contains invalid data. Please review all required fields.";
  }
  
  if (rawMessage.includes("timeout") || rawMessage.includes("timed out")) {
    return "The request timed out. Please check your connection and try again.";
  }
  
  if (rawMessage.includes("network") || rawMessage.includes("connection")) {
    return "Network error occurred. Please check your internet connection.";
  }
  
  if (rawMessage.includes("conflict")) {
    return "This report has been modified by another user. Please refresh and try again.";
  }
  
  if (rawMessage.includes("required field") || rawMessage.includes("missing")) {
    return "Some required information is missing. Please complete all mandatory fields.";
  }
  
  if (rawMessage.includes("deadline") || rawMessage.includes("overdue")) {
    return "The deadline for this action has passed. Please contact your supervisor.";
  }
  
  if (rawMessage.includes("locked") || rawMessage.includes("in use")) {
    return "This report is currently being edited by another user. Please try again later.";
  }
  
  // If no specific pattern matches, return a generic but friendly message
  if (rawMessage) {
    // Try to extract just the essential part of the message if it's too technical
    const cleanMessage = rawMessage
      .replace(/^.*?:\s*/, '') // Remove prefixes like "Error:" or "SubmitForReview side-effect:"
      .replace(/\(.*?\)/g, '') // Remove content in parentheses
      .trim();
    
    // If the cleaned message is still technical, return a generic message
    if (cleanMessage.length > 100 || cleanMessage.includes('side-effect') || cleanMessage.includes('mutation')) {
      return "An error occurred while processing your request. Please try again or contact support if the problem persists.";
    }
    
    return cleanMessage;
  }
  
  return "An unexpected error occurred. Please try again.";
};

/**
 * Extracts error code from an error object
 */
export const getErrorCode = (error: any): string | null => {
  return error?.code || error?.response?.data?.code || null;
};

/**
 * Determines if an error is retryable
 */
export const isRetryableError = (error: any): boolean => {
  const code = getErrorCode(error);
  const message = error?.message || "";
  
  // Network and timeout errors are typically retryable
  if (code === "NETWORK_ERROR" || code === "TIMEOUT" || code === "ECONNRESET") {
    return true;
  }
  
  if (message.includes("timeout") || message.includes("network") || message.includes("connection")) {
    return true;
  }
  
  // 5xx server errors are often temporary and retryable
  const status = error?.response?.status;
  if (status >= 500 && status < 600) {
    return true;
  }
  
  return false;
};

/**
 * Formats error for logging
 */
export const formatErrorForLogging = (error: any, context?: string): string => {
  const parts = [];
  
  if (context) {
    parts.push(`[${context}]`);
  }
  
  if (error?.code) {
    parts.push(`Code: ${error.code}`);
  }
  
  if (error?.message) {
    parts.push(`Message: ${error.message}`);
  }
  
  if (error?.response?.status) {
    parts.push(`Status: ${error.response.status}`);
  }
  
  if (error?.response?.data) {
    parts.push(`Data: ${JSON.stringify(error.response.data)}`);
  }
  
  return parts.join(" | ");
};