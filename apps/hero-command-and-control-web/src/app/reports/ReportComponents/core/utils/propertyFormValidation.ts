import { Property } from "@/app/apis/services/workflow/property/types";

/**
 * Form validation utilities for properties (all now use dynamic schema)
 */

export interface FieldValidationError {
    field: string;
    message: string;
}

export interface PropertyFormValidationResult {
    isValid: boolean;
    errors: FieldValidationError[];
    warnings: string[];
}

/**
 * Helper function to safely handle both string and number inputs
 * Returns the value as string, or empty string if null/undefined
 */
const toSafeString = (value: any): string => {
    if (value === null || value === undefined) return "";
    return String(value);
};

/**
 * Helper function to check if a value exists and is not empty (handles both string and number)
 */
const hasValue = (value: any): boolean => {
    if (value === null || value === undefined) return false;
    if (typeof value === 'number') return true;
    if (typeof value === 'string') return value.trim() !== "";
    return Boolean(value);
};

/**
 * Validates form values for property creation/update
 * Works with both dynamic schema and legacy PropertySchema formats
 */
export const validatePropertyFormValues = (
    values: any,
    isUpdateMode: boolean = false,
    existingProperty?: Property
): PropertyFormValidationResult => {
    const errors: FieldValidationError[] = [];
    const warnings: string[] = [];

    // Basic required fields validation
    if (!values.description || values.description.trim() === "") {
        errors.push({
            field: "description",
            message: "Description is required"
        });
    }

    // Property type validation
    if (!values.propertyType) {
        errors.push({
            field: "propertyType",
            message: "Property type is required"
        });
    } else {
        // Validate property type value
        const validPropertyTypes = ["found", "seized", "stolen", "safekeeping", "missing", "recovered"];
        if (!validPropertyTypes.includes(values.propertyType)) {
            errors.push({
                field: "propertyType",
                message: "Invalid property type selected"
            });
        }
    }

    // Category validation
    if (values.category) {
        const validCategories = [
            "electronics", "jewelry", "clothing", "documents", "cash", "weapons", "drugs", "vehicles", "tools", "other"
        ];
        if (!validCategories.includes(values.category)) {
            errors.push({
                field: "category",
                message: "Invalid category selected"
            });
        }
    }

    // Value validation (if provided, should be numeric)
    if (hasValue(values.value)) {
        const numericValue = parseFloat(toSafeString(values.value));
        if (isNaN(numericValue) || numericValue < 0) {
            errors.push({
                field: "value",
                message: "Value must be a positive number"
            });
        }
    }

    // Quantity validation
    if (hasValue(values.quantity)) {
        const quantity = parseInt(toSafeString(values.quantity));
        if (isNaN(quantity) || quantity < 1) {
            errors.push({
                field: "quantity",
                message: "Quantity must be a positive whole number"
            });
        }
    }

    // Serial number format validation (basic)
    if (values.serialNumber && values.serialNumber.length > 100) {
        errors.push({
            field: "serialNumber",
            message: "Serial number is too long (max 100 characters)"
        });
    }

    // Description length validation
    if (values.description && values.description.length > 5000) {
        errors.push({
            field: "description",
            message: "Description is too long (max 5000 characters)"
        });
    }

    // Update-specific validations
    if (isUpdateMode && existingProperty) {
        // All properties now use dynamic schema
        warnings.push(
            "This property uses dynamic schema. Ensure all required schema fields are provided."
        );

        // Validate schema fields are present (all properties require these now)
        if (!values.schemaId) {
            warnings.push("Property should have a schemaId for consistency.");
        }
        if (!values.schemaVersion) {
            warnings.push("Property should have a schemaVersion for consistency.");
        }
    }

    return {
        isValid: errors.length === 0,
        errors,
        warnings,
    };
};

/**
 * Validates a complete property object (for backend submission)
 */
export const validatePropertyForSubmission = (property: Partial<Property>): PropertyFormValidationResult => {
    const errors: FieldValidationError[] = [];
    const warnings: string[] = [];

    // Basic property validation
    if (!property.orgId || property.orgId <= 0) {
        errors.push({
            field: "orgId",
            message: "Organization ID is required"
        });
    }

    if (!property.propertyStatus) {
        errors.push({
            field: "propertyStatus",
            message: "Property status is required"
        });
    }

    // Schema-specific validation
    const hasSchemaId = property.schemaId && property.schemaId.trim() !== "";
    const hasDetails = property.details && Object.keys(property.details).length > 0;
    const hasPropertySchema = property.propertySchema && Object.keys(property.propertySchema).length > 0;

    if (hasSchemaId && hasDetails) {
        // Dynamic schema property
        if (!property.schemaVersion || property.schemaVersion <= 0) {
            errors.push({
                field: "schemaVersion",
                message: "Schema version is required for dynamic schema properties"
            });
        }

        // Validate details contain required fields
        const formValidation = validatePropertyFormValues(property.details, false);
        errors.push(...formValidation.errors);
        warnings.push(...formValidation.warnings);

        if (hasPropertySchema) {
            warnings.push(
                "Property has both dynamic schema and legacy PropertySchema. Dynamic schema will take precedence."
            );
        }
    } else if (hasPropertySchema) {
        // Legacy PropertySchema property
        const formValidation = validatePropertyFormValues(property.propertySchema, false);
        errors.push(...formValidation.errors);
        warnings.push(...formValidation.warnings);
    } else {
        errors.push({
            field: "schema",
            message: "Property must have either dynamic schema (schemaId + details) or legacy PropertySchema"
        });
    }

    // Audit fields validation
    if (!property.createdBy || property.createdBy.trim() === "") {
        warnings.push("createdBy field is recommended for audit trail");
    }

    return {
        isValid: errors.length === 0,
        errors,
        warnings,
    };
};

/**
 * Sanitizes form values to prevent common issues
 */
export const sanitizePropertyFormValues = (values: any): any => {
    const sanitized = { ...values };

    // Trim string values
    Object.keys(sanitized).forEach(key => {
        if (typeof sanitized[key] === 'string') {
            sanitized[key] = sanitized[key].trim();
        }
    });

    // Convert empty strings to null for optional fields
    const optionalFields = ['category', 'serialNumber', 'identifiers', 'owner', 'condition', 'value', 'quantity'];
    optionalFields.forEach(field => {
        if (sanitized[field] === '') {
            sanitized[field] = null;
        }
    });

    // Ensure numeric fields are properly formatted
    if (hasValue(sanitized.value)) {
        const numValue = parseFloat(toSafeString(sanitized.value));
        if (!isNaN(numValue)) {
            sanitized.value = numValue.toFixed(2);
        }
    }

    if (hasValue(sanitized.quantity)) {
        const intValue = parseInt(toSafeString(sanitized.quantity));
        if (!isNaN(intValue)) {
            sanitized.quantity = intValue.toString();
        }
    }

    return sanitized;
};

/**
 * Formats validation errors for display to users
 */
export const formatValidationErrors = (errors: FieldValidationError[]): string => {
    if (errors.length === 0) return "";

    if (errors.length === 1) {
        return errors[0].message;
    }

    return `Multiple validation errors:\n${errors.map(e => `• ${e.message}`).join('\n')}`;
};

/**
 * Checks if form data is compatible with a specific schema approach
 */
export const isFormDataCompatible = (
    formValues: any,
    targetApproach: 'dynamic' | 'legacy'
): { compatible: boolean; missingFields: string[]; extraFields: string[] } => {
    const dynamicRequiredFields = ['description', 'propertyType'];
    const legacySchemaFields = ['description', 'quantity', 'category', 'identifiers', 'owner', 'condition', 'serialNumber', 'value', 'propertyType'];

    const formFields = Object.keys(formValues);
    const requiredFields = targetApproach === 'dynamic' ? dynamicRequiredFields : legacySchemaFields;

    const missingFields = requiredFields.filter(field => {
        // Check if field exists in form
        if (!formFields.includes(field)) {
            return true; // Field is missing
        }

        // Check if field has a meaningful value
        const value = formValues[field];
        if (value === undefined || value === null || value === '') {
            return true; // Field exists but is empty
        }

        // For string values, check if they're not just whitespace
        if (typeof value === 'string' && value.trim() === '') {
            return true; // Field exists but is only whitespace
        }

        return false; // Field exists and has a meaningful value
    });
    const extraFields = targetApproach === 'legacy'
        ? formFields.filter(field => !legacySchemaFields.includes(field))
        : []; // Dynamic schema can accept any fields

    return {
        compatible: missingFields.length === 0,
        missingFields,
        extraFields
    };
};