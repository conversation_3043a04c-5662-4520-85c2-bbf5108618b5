import {
    Box
} from "@mui/material";
import React from "react";
import { EntityCard } from "../entities";
import { PanelType } from "./types";
import { entitiesToPropertyData } from "./utils/utils";

interface PropertySectionProps {
    reportId: string;
    onSaveStatusChange: (sectionId: string, status: any) => void;
    readOnly?: boolean;
    onOpenSidePanel?: (panelType: PanelType) => void;
    comments?: any[];
    onAddComment?: (text: string) => void;
    onResolveComment?: (id: string, resolved: boolean) => void;
    commentsInitiallyExpanded?: boolean;
    selectedRowId?: string | null;
    onRowSelect?: (id: string | null) => void;
    onEntityEdit?: (entityId: string, entityType: "person" | "property" | "vehicle" | "organization") => void;
    onEntityDelete?: (entityId: string, entityType: "person" | "property" | "vehicle" | "organization") => void;
    properties?: any[];
    reportSections?: any;
}

export const PropertySection: React.FC<PropertySectionProps> = ({
    reportId,
    onSaveStatusChange,
    readOnly = false,
    onOpenSidePanel,
    comments = [],
    onAddComment,
    onResolveComment,
    commentsInitiallyExpanded = false,
    selectedRowId = null,
    onRowSelect,
    onEntityEdit,
    onEntityDelete,
    properties = [],
    reportSections,
}) => {
    // Filter properties based on the report's SECTION_TYPE_PROPERTY section
    let filteredProperties = properties;

    if (reportSections?.sections) {
        // Find the SECTION_TYPE_PROPERTY section
        const propertySection = reportSections.sections.find(
            (section: any) => section.type === "SECTION_TYPE_PROPERTY"
        );

        if (propertySection?.propertyList?.propertyRefs) {
            // Get the property IDs that are referenced in this report
            const referencedPropertyIds = propertySection.propertyList.propertyRefs.map(
                (ref: any) => ref.id
            );

            // Filter properties to only show those referenced in the report
            filteredProperties = properties.filter((property: any) =>
                referencedPropertyIds.includes(property.id)
            );
        }
    }

    const propertyData = entitiesToPropertyData(filteredProperties);

    const handleAddProperty = () => {
        onOpenSidePanel?.(PanelType.PROPERTY);
    };

    return (
        <Box id="properties">
            <EntityCard
                title="Properties"
                onAddClick={handleAddProperty}
                propertyData={propertyData}
                comments={comments}
                onAddComment={onAddComment}
                onResolveComment={onResolveComment}
                onEntityEdit={onEntityEdit}
                onEntityDelete={onEntityDelete}
                selectedRowId={selectedRowId}
                onRowSelect={onRowSelect || (() => { })}
                commentsInitiallyExpanded={commentsInitiallyExpanded}
                readOnly={readOnly}
            />
        </Box>
    );
}; 