import { InputType } from '@/design-system/components/TextInput';
import { getCategoryDropdownOptions } from '@/app/utils/propertyHelpers';

export const getPropertyFormConfig = (options?: { hideCategory?: boolean }) => {
    const allBasicFields = [
        {
            id: "description",
            type: "text",
            title: "Description",
            placeholder: "Detailed description of the property",
            row: 1
        },
        {
            id: "category",
            type: InputType.Dropdown,
            title: "Category",
            placeholder: "Select NIBRS category",
            options: getCategoryDropdownOptions(),
            row: 2,
            width: "50%"
        },
        {
            id: "quantity",
            type: "text",
            title: "Quantity",
            placeholder: "Number of items",
            row: 2,
            width: "50%"
        },
        {
            id: "identifiers",
            type: "text",
            title: "Make/Model/Brand",
            placeholder: "Make, model, or brand information",
            row: 3,
            width: "50%"
        },
        {
            id: "owner",
            type: "text",
            title: "Owner",
            placeholder: "Owner information if applicable",
            row: 4,
            width: "50%"
        },
        {
            id: "condition",
            type: InputType.Dropdown,
            title: "Condition",
            options: [
                { value: "excellent", label: "Excellent" },
                { value: "good", label: "Good" },
                { value: "fair", label: "Fair" },
                { value: "poor", label: "Poor" },
                { value: "damaged", label: "Damaged" }
            ],
            row: 4,
            width: "50%"
        },
        {
            id: "serialNumber",
            type: "text",
            title: "Serial Number",
            placeholder: "Serial number or unique identifier",
            row: 5,
            width: "50%"
        },
        {
            id: "value",
            type: "currency",
            title: "Property Value",
            placeholder: "$0.00",
            row: 5,
            width: "50%"
        }
    ];

    // Filter out category field if hideCategory is true
    const basicInfoFields = options?.hideCategory 
        ? allBasicFields.filter(field => field.id !== 'category')
        : allBasicFields;

    return {
        id: "property_form",
        title: "Property Information",
        sections: [
            {
                id: "basicInfo",
                title: "Basic Property Information",
                fields: basicInfoFields
            },
            {
                id: "adminInfo",
                title: "Administrative Information",
                fields: [
                    {
                        id: "status",
                        type: InputType.Dropdown,
                        title: "Status",
                        options: [
                            { value: "collected", label: "Collected" },
                            { value: "checked_in", label: "Checked In" },
                            { value: "checked_out", label: "Checked Out" },
                            { value: "disposed", label: "Disposed" },
                            { value: "missing", label: "Missing" },
                            { value: "stolen", label: "Stolen" }
                        ],
                        row: 1,
                        width: "50%"
                    },
                    {
                        id: "propertyNumber",
                        type: "text",
                        title: "Property Number",
                        placeholder: "Enter property or evidence number",
                        row: 1,
                        width: "50%"
                    },
                    {
                        id: "retentionPeriod",
                        type: "text",
                        title: "Retention Period",
                        placeholder: "Enter retention period",
                        row: 2,
                        width: "50%"
                    },
                    {
                        id: "currentCustodian",
                        type: "text",
                        title: "Current Custodian",
                        placeholder: "Name or ID of current custodian",
                        row: 2,
                        width: "50%"
                    },
                    {
                        id: "currentLocation",
                        type: "text",
                        title: "Current Location",
                        placeholder: "Current storage location",
                        row: 3
                    }
                ]
            }
        ]
    };
};

// Keep the old export for backward compatibility
export const PROPERTY_FORM_CONFIG = getPropertyFormConfig();