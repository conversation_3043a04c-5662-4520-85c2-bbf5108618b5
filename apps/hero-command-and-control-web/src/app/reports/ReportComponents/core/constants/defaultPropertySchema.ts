import { EntityType, RecordStatus } from 'proto/hero/entity/v1/entity_pb';

/**
 * Default PropertySchema that works seamlessly with entity schema logic
 * This eliminates the dual support complexity between PropertySchema and EntitySchema
 */
export const DEFAULT_PROPERTY_SCHEMA = {
  id: 'default-property-schema',
  name: 'Default Property Schema',
  description: 'Standard property form schema for Hero platform',
  version: 1,
  entityType: EntityType.PROPERTY,
  status: RecordStatus.ACTIVE,

  schemaDefinition: {
    type: 'object',
    properties: {
      description: { type: 'string', maxLength: 1000 },
      category: {
        type: 'string',
        enum: ['electronics', 'jewelry', 'clothing', 'documents', 'cash', 'weapons', 'drugs', 'vehicles', 'tools', 'other']
      },
      propertyType: {
        type: 'string',
        enum: ['found', 'seized', 'stolen', 'safekeeping', 'missing', 'recovered', 'burned', 'forged', 'damaged', 'unknown']
      },
      quantity: { type: 'string', maxLength: 50 },
      identifiers: { type: 'string', maxLength: 200 },
      serialNumber: { type: 'string', maxLength: 100 },
      value: { type: 'number', minimum: 0 },
      condition: {
        type: 'string',
        enum: ['excellent', 'good', 'fair', 'poor', 'damaged', 'unknown']
      },
      owner: { type: 'string', maxLength: 200 }
    },
    required: ['propertyType']
  },

  // Standard metadata
  createTime: new Date().toISOString(),
  updateTime: new Date().toISOString(),
  createdBy: 'system',
  updatedBy: 'system'
};

/**
 * Gets the default property schema for use in PropertyForm
 * This ensures consistent property handling across the platform
 */
export const getDefaultPropertySchema = () => {
  return DEFAULT_PROPERTY_SCHEMA;
};