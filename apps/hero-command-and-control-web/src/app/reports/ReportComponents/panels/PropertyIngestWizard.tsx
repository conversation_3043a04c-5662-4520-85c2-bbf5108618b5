import { useListAssets } from "@/app/apis/services/workflow/assets/hooks";
import { nibrsToPropertyType } from "@/app/apis/services/workflow/property/propertyTypeManager";
import { Button } from "@/design-system/components/Button";
import { Checkbox } from "@/design-system/components/Checkbox";
import { DatePicker } from "@/design-system/components/DatePicker";
import { Dropdown, DropdownOption } from "@/design-system/components/Dropdown";
import { InputMask, InputType, TextInput } from "@/design-system/components/TextInput";
import { Typography } from "@/design-system/components/Typography";
import { colors } from "@/design-system/tokens";
import { Box, Typography as MuiTypography, Tooltip } from "@mui/material";
import { AssetStatus, AssetType, ListAssetsRequest } from "proto/hero/assets/v2/assets_pb";
import { NIBRSPropertyDescription } from "proto/hero/property/v1/property_pb";
import { useEffect, useState } from "react";
import { FaArrowLeft } from "react-icons/fa";
import {
  useListReportSections,
} from "../../../apis/services/workflow/reports/v2/hooks";
import { STATE_OPTIONS } from "../details/constants";
import { PropertyForm } from "./PropertyForm";

// NIBRS Property Loss Types
export type NIBRSLossType =
  | 'burned'
  | 'counterfeited_forged'
  | 'destroyed_damaged_vandalized'
  | 'recovered'
  | 'seized'
  | 'stolen'
  | 'unknown'
  | 'found';

// NIBRS Location Type Options
const NIBRS_LOCATION_TYPE_OPTIONS: DropdownOption[] = [
  { value: "Air/Bus/Train Terminal", label: "Air/Bus/Train Terminal" },
  { value: "Bank/Savings and Loan", label: "Bank/Savings and Loan" },
  { value: "Bar/Nightclub", label: "Bar/Nightclub" },
  { value: "Church/Synagogue/Temple/Mosque", label: "Church/Synagogue/Temple/Mosque" },
  { value: "Commercial/Office Building", label: "Commercial/Office Building" },
  { value: "Construction Site", label: "Construction Site" },
  { value: "Convenience Store", label: "Convenience Store" },
  { value: "Department/Discount Store", label: "Department/Discount Store" },
  { value: "Drug Store/Doctor's Office/Hospital", label: "Drug Store/Doctor's Office/Hospital" },
  { value: "Field/Woods", label: "Field/Woods" },
  { value: "Government/Public Building", label: "Government/Public Building" },
  { value: "Grocery/Supermarket", label: "Grocery/Supermarket" },
  { value: "Highway/Road/Alley/Street/Sidewalk", label: "Highway/Road/Alley/Street/Sidewalk" },
  { value: "Hotel/Motel/Etc.", label: "Hotel/Motel/Etc." },
  { value: "Jail/Prison/Penitentiary/Corrections Facility", label: "Jail/Prison/Penitentiary/Corrections Facility" },
  { value: "Lake/Waterway/Beach", label: "Lake/Waterway/Beach" },
  { value: "Liquor Store", label: "Liquor Store" },
  { value: "Parking/Drop Lot/Garage", label: "Parking/Drop Lot/Garage" },
  { value: "Rental Storage Facility", label: "Rental Storage Facility" },
  { value: "Residence/Home", label: "Residence/Home" },
  { value: "Restaurant", label: "Restaurant" },
  { value: "Service/Gas Station", label: "Service/Gas Station" },
  { value: "Specialty Store", label: "Specialty Store" },
  { value: "Other/Unknown", label: "Other/Unknown" },
  { value: "Abandoned/Condemned Structure", label: "Abandoned/Condemned Structure" },
  { value: "Amusement Park", label: "Amusement Park" },
  { value: "Arena/Stadium/Fairgrounds/Coliseum", label: "Arena/Stadium/Fairgrounds/Coliseum" },
  { value: "ATM Separate from Bank", label: "ATM Separate from Bank" },
  { value: "Auto Dealership New/Used", label: "Auto Dealership New/Used" },
  { value: "Camp/Campground", label: "Camp/Campground" },
  { value: "Daycare Facility", label: "Daycare Facility" },
  { value: "Dock/Wharf/Freight/Modal Terminal", label: "Dock/Wharf/Freight/Modal Terminal" },
  { value: "Farm Facility", label: "Farm Facility" },
  { value: "Gambling Facility/Casino/Race Track", label: "Gambling Facility/Casino/Race Track" },
  { value: "Industrial Site", label: "Industrial Site" },
  { value: "Military Installation", label: "Military Installation" },
  { value: "Park/Playground", label: "Park/Playground" },
  { value: "Rest Area", label: "Rest Area" },
  { value: "School–College/University", label: "School–College/University" },
  { value: "School–Elementary/Secondary", label: "School–Elementary/Secondary" },
  { value: "Shelter–Mission/Homeless", label: "Shelter–Mission/Homeless" },
  { value: "Shopping Mall", label: "Shopping Mall" },
  { value: "Tribal Lands", label: "Tribal Lands" },
  { value: "Community Center", label: "Community Center" },
  { value: "Cyberspace", label: "Cyberspace" },
];

// Wizard steps
enum PropertyIngestStep {
  CLASSIFICATION = 'classification',
  CHECK_IN = 'check_in',
  PROPERTY_DETAILS = 'property_details'
}

// Use centralized PropertyTypeManager - no more string mapping!
const mapNibrsToPropertyType = nibrsToPropertyType;

const mapNibrsToPropertyStatus = (inPoliceCustody: boolean, nibrsType: NIBRSLossType) => {
  if (inPoliceCustody) {
    return 'checked_in'; // PROPERTY_STATUS_CHECKED_IN - in custody and checked in
  } else {
    // Use specific PropertyStatus values that match the NIBRS type
    // For items not in custody, only set explicit statuses for special cases
    // Let the backend default to "Logged" (INTAKE_PENDING) for most cases
    switch (nibrsType) {
      case 'stolen':
        return 'stolen'; // PROPERTY_STATUS_STOLEN - explicit stolen status
      case 'found':
        return 'found'; // PROPERTY_STATUS_FOUND - explicit found status
      case 'recovered':
        return 'recovered'; // PROPERTY_STATUS_RECOVERED - explicit recovered status
      case 'seized':
        return 'intake_pending'; // PROPERTY_STATUS_COLLECTED (seized into custody)
      case 'burned':
        return 'intake_pending'; // PROPERTY_STATUS_COLLECTED (seized into custody)
      case 'counterfeited_forged':
        return 'intake_pending'; // PROPERTY_STATUS_COLLECTED (seized into custody)
      case 'destroyed_damaged_vandalized':
        return 'intake_pending'; // PROPERTY_STATUS_COLLECTED (seized into custody)
      case 'unknown':
      default:
        return 'intake_pending'; // PROPERTY_STATUS_COLLECTED (seized into custody)
    }
  }
};

const mapNibrsPropertyDescriptionToCategory = (nibrsPropertyDescription: number): string => {
  // Convert numeric enum to string enum name for proper data storage
  const enumValue = nibrsPropertyDescription as NIBRSPropertyDescription;
  const enumName = NIBRSPropertyDescription[enumValue];
  return enumName || "NIBRS_PROPERTY_DESCRIPTION_UNSPECIFIED";
};

const createCustodyEvent = (wizardData: PropertyIngestData) => {
  if (!wizardData.checkIn || !wizardData.classification?.inPoliceCustody) {
    return [];
  }

  const checkIn = wizardData.checkIn;
  const timestamp = `${checkIn.date}T${checkIn.time}:00.000Z`;

  return [{
    timestamp,
    transferring_user_id: '', // Unknown at collection
    transferring_agency: '',
    receiving_user_id: checkIn.officer,
    receiving_agency: '', // Current org
    new_location: checkIn.location,
    action_type: 'CUSTODY_ACTION_TYPE_COLLECTED',
    notes: checkIn.comments || '',
    case_number: '',
    evidence_number: ''
  }];
};

const enhancePropertyData = (propertyData: any, wizardData: PropertyIngestData) => {
  const nibrsType = wizardData.classification?.nibrsLossType || 'found';

  const propertyType = mapNibrsToPropertyType(nibrsType);

  // Remove propertyType from details - we want it in the top-level proto field
  const { propertyType: detailsPropertyType, ...detailsWithoutPropertyType } = propertyData.details || {};

  const enhancedData = {
    ...propertyData,

    // Set the top-level proto field: NIBRSPropertyType nibrs_property_type = 21
    nibrsPropertyType: propertyType,

    // Clean details - no propertyType duplication
    details: detailsWithoutPropertyType,

    // Map NIBRS Property Description to category
    category: mapNibrsPropertyDescriptionToCategory(wizardData.classification?.nibrsPropertyDescription || 0),

    // Set status based on NIBRS type and custody
    status: mapNibrsToPropertyStatus(
      wizardData.classification?.inPoliceCustody || false,
      wizardData.classification?.nibrsLossType || 'found'
    ),

    // Add property number from check-in
    propertyNumber: wizardData.checkIn?.propertyNumber || '',

    // Add evidence flag and owner from classification
    isEvidence: wizardData.classification?.isEvidence || false,
    owner: wizardData.classification?.owner || '',

    // Map check-in data to current location and custodian fields
    currentLocation: wizardData.checkIn?.location || propertyData.currentLocation || '',
    currentCustodian: wizardData.checkIn?.officer || propertyData.currentCustodian || '',

    // Add custody chain if checked in
    custodyChain: createCustodyEvent(wizardData),

    // Add relevant notes
    notes: propertyData.notes || '',

    // Add metadata for future use
    _wizardData: wizardData,
    _nibrsLossType: wizardData.classification?.nibrsLossType,
    _inPoliceCustody: wizardData.classification?.inPoliceCustody,
    _checkInInfo: wizardData.checkIn,

    // Ensure NIBRS-mapped fields are included in property details
    _additionalDetails: {
      category: mapNibrsPropertyDescriptionToCategory(wizardData.classification?.nibrsPropertyDescription || 0)
    }
  };

  return enhancedData;
};

// Step data types
interface ClassificationData {
  nibrsLossType: NIBRSLossType;
  inPoliceCustody: boolean;
  isEvidence: boolean;
  owner: string;
  nibrsPropertyDescription: number; // NIBRSPropertyDescription enum value
}

interface CheckInData {
  officer: string;
  date: string;
  time: string;
  location: string;
  comments: string;
  propertyNumber: string;
  sameAsIncidentLocation: boolean;
  // Field collection time
  fieldCollectionDate: string;
  fieldCollectionTime: string;
  // Incident location fields from report details page
  incidentLocationStreetAddress: string;
  incidentLocationUnitInfo: string;
  incidentLocationType: string;
  incidentLocationCommonName: string;
  incidentLocationCity: string;
  incidentLocationState: string;
  incidentLocationZipCode: string;
}

interface PropertyIngestData {
  classification?: ClassificationData;
  checkIn?: CheckInData;
  propertyDetails?: any;
}

// Determine if custody is required/disabled based on NIBRS type
const getCustodyConstraint = (nibrsType: NIBRSLossType) => {
  switch (nibrsType) {
    case 'seized':
    case 'found':
    case 'recovered':
      return { required: true, disabled: true, value: true };
    case 'stolen':
      return { required: true, disabled: true, value: false };
    default:
      return { required: false, disabled: false, value: undefined };
  }
};

interface PropertyIngestWizardProps {
  onSubmit: (values: any) => void;
  onCancel: () => void;
  onSaveAndAddAnother: (values: any) => void;
  onPropertyUpdated?: (updatedProperty: any) => void;
  initialValues?: any;
  readOnly?: boolean;
  customTitle?: string;
  propertyId?: string;
  reportId?: string;
}

export const PropertyIngestWizard = ({
  onSubmit,
  onCancel,
  onSaveAndAddAnother,
  onPropertyUpdated,
  initialValues,
  readOnly = false,
  customTitle,
  propertyId,
  reportId
}: PropertyIngestWizardProps) => {
  const [currentStep, setCurrentStep] = useState<PropertyIngestStep>(
    propertyId ? PropertyIngestStep.PROPERTY_DETAILS : PropertyIngestStep.CLASSIFICATION
  );
  const [wizardData, setWizardData] = useState<PropertyIngestData>({});


  // Determine next step based on classification data
  const getNextStep = (classificationData: ClassificationData): PropertyIngestStep => {
    const { inPoliceCustody } = classificationData;

    // If property is in police custody, ALWAYS require check-in flow
    if (inPoliceCustody) {
      return PropertyIngestStep.CHECK_IN;
    }

    // If not in custody, go directly to property details
    return PropertyIngestStep.PROPERTY_DETAILS;
  };

  // Handle step navigation
  const handleClassificationNext = (data: ClassificationData) => {
    setWizardData(prev => ({ ...prev, classification: data }));
    const nextStep = getNextStep(data);
    setCurrentStep(nextStep);
  };

  const handleCheckInNext = (data: CheckInData) => {
    setWizardData(prev => ({ ...prev, checkIn: data }));
    setCurrentStep(PropertyIngestStep.PROPERTY_DETAILS);
  };

  const handleBack = () => {
    // If editing existing property, don't allow going back - just cancel
    if (propertyId) {
      onCancel();
      return;
    }

    switch (currentStep) {
      case PropertyIngestStep.CHECK_IN:
        setCurrentStep(PropertyIngestStep.CLASSIFICATION);
        break;
      case PropertyIngestStep.PROPERTY_DETAILS: {
        // Go back to check-in if we have check-in data, otherwise classification
        const previousStep = wizardData.checkIn
          ? PropertyIngestStep.CHECK_IN
          : PropertyIngestStep.CLASSIFICATION;
        setCurrentStep(previousStep);
        break;
      }
    }
  };

  // Handle final submission
  const handlePropertySubmit = (propertyData: any) => {
    const enhancedPropertyData = enhancePropertyData(propertyData, wizardData);
    onSubmit(enhancedPropertyData);
  };

  const handlePropertySaveAndAddAnother = (propertyData: any) => {
    const enhancedPropertyData = enhancePropertyData(propertyData, wizardData);
    onSaveAndAddAnother(enhancedPropertyData);

    // Reset wizard to classification step for next property
    setCurrentStep(PropertyIngestStep.CLASSIFICATION);
    setWizardData({});
  };

  // Get step title
  const getStepTitle = () => {
    switch (currentStep) {
      case PropertyIngestStep.CLASSIFICATION:
        return "Property Classification & Basic Info";
      case PropertyIngestStep.CHECK_IN:
        return "Check-In Property";
      case PropertyIngestStep.PROPERTY_DETAILS:
        return customTitle || "Property Details";
      default:
        return "Property Ingest";
    }
  };

  // Render current step content
  const renderStepContent = () => {
    switch (currentStep) {
      case PropertyIngestStep.CLASSIFICATION:
        return (
          <PropertyClassificationStep
            onNext={handleClassificationNext}
            onCancel={onCancel}
            initialData={wizardData.classification}
            readOnly={readOnly}
          />
        );

      case PropertyIngestStep.CHECK_IN:
        return (
          <PropertyCheckInStep
            onNext={handleCheckInNext}
            onBack={handleBack}
            onCancel={onCancel}
            initialData={wizardData.checkIn}
            readOnly={readOnly}
            reportId={reportId}
          />
        );

      case PropertyIngestStep.PROPERTY_DETAILS:
        return (
          <Box>
            {/* Back button for property details step */}
            <Box sx={{ mb: 3 }}>
              <Button
                label={propertyId ? "Cancel" : "Back"}
                style="ghost"
                color="grey"
                size="medium"
                onClick={handleBack}
                leftIcon={<FaArrowLeft />}
              />
            </Box>

            <PropertyForm
              onSubmit={handlePropertySubmit}
              onCancel={onCancel}
              onSaveAndAddAnother={handlePropertySaveAndAddAnother}
              onPropertyUpdated={onPropertyUpdated}
              initialValues={{
                ...initialValues,
                owner: wizardData.classification?.owner || ''
              }}
              readOnly={readOnly}
              customTitle={getStepTitle()}
              propertyId={propertyId}
              reportId={reportId}
            />
          </Box>
        );

      default:
        return null;
    }
  };

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column', minHeight: 0 }}>
      {renderStepContent()}
    </Box>
  );
};

// Property Classification Step Component
interface PropertyClassificationStepProps {
  onNext: (data: ClassificationData) => void;
  onCancel: () => void;
  initialData?: ClassificationData;
  readOnly?: boolean;
}

const PropertyClassificationStep = ({
  onNext,
  onCancel,
  initialData,
  readOnly = false
}: PropertyClassificationStepProps) => {
  const [nibrsLossType, setNibrsLossType] = useState<NIBRSLossType | null>(
    initialData?.nibrsLossType || null
  );
  const [inPoliceCustody, setInPoliceCustody] = useState<boolean>(
    initialData?.inPoliceCustody || false
  );
  const [isEvidence, setIsEvidence] = useState<boolean>(
    initialData?.isEvidence || false
  );
  const [owner, setOwner] = useState<string>(
    initialData?.owner || ''
  );
  const [nibrsPropertyDescription, setNibrsPropertyDescription] = useState<number>(
    initialData?.nibrsPropertyDescription || 0
  );

  const nibrsOptions: DropdownOption[] = [
    { value: 'burned', label: 'Burned' },
    { value: 'counterfeited_forged', label: 'Counterfeited/Forged' },
    { value: 'destroyed_damaged_vandalized', label: 'Destroyed/Damaged/Vandalized' },
    { value: 'recovered', label: 'Recovered' },
    { value: 'seized', label: 'Seized' },
    { value: 'stolen', label: 'Stolen' },
    { value: 'unknown', label: 'Unknown' },
    { value: 'found', label: 'Found' },
  ];

  const nibrsPropertyDescriptionOptions: DropdownOption[] = [
    { value: '1', label: 'Aircraft' },
    { value: '2', label: 'Alcohol' },
    { value: '3', label: 'Automobiles' },
    { value: '4', label: 'Bicycles' },
    { value: '5', label: 'Buses' },
    { value: '6', label: 'Clothes/Furs' },
    { value: '7', label: 'Computer Hardware/Software' },
    { value: '8', label: 'Consumable Goods' },
    { value: '9', label: 'Credit/Debit Cards' },
    { value: '10', label: 'Drugs/Narcotics' },
    { value: '11', label: 'Drug/Narcotic Equipment' },
    { value: '12', label: 'Farm Equipment' },
    { value: '13', label: 'Firearms' },
    { value: '14', label: 'Gambling Equipment' },
    { value: '15', label: 'Heavy Construction/Industrial Equipment' },
    { value: '16', label: 'Household Goods' },
    { value: '17', label: 'Jewelry/Precious Metals/Gems' },
    { value: '18', label: 'Livestock' },
    { value: '19', label: 'Merchandise' },
    { value: '20', label: 'Money' },
    { value: '21', label: 'Negotiable Instruments' },
    { value: '22', label: 'Nonnegotiable Instruments' },
    { value: '23', label: 'Office-type Equipment' },
    { value: '24', label: 'Other Motor Vehicles' },
    { value: '25', label: 'Purses/Handbags/Wallets' },
    { value: '26', label: 'Radios/TVs/VCRs' },
    { value: '27', label: 'Recordings–Audio/Visual' },
    { value: '28', label: 'Recreational Vehicles' },
    { value: '29', label: 'Structures–Single Occupancy Dwellings' },
    { value: '30', label: 'Structures–Other Dwellings' },
    { value: '31', label: 'Structures–Other Commercial/Business' },
    { value: '32', label: 'Structures–Industrial/Manufacturing' },
    { value: '33', label: 'Structures–Public/Community' },
    { value: '34', label: 'Structures–Storage' },
    { value: '35', label: 'Structures–Other' },
    { value: '36', label: 'Tools' },
    { value: '37', label: 'Trucks' },
    { value: '38', label: 'Vehicle Parts/Accessories' },
    { value: '39', label: 'Watercraft' },
    { value: '41', label: 'Aircraft Parts/Accessories' },
    { value: '42', label: 'Artistic Supplies/Accessories' },
    { value: '43', label: 'Building Materials' },
    { value: '44', label: 'Camping/Hunting/Fishing Equipment/Supplies' },
    { value: '45', label: 'Chemicals' },
    { value: '46', label: 'Collections/Collectibles' },
    { value: '47', label: 'Crops' },
    { value: '48', label: 'Documents/Personal or Business' },
    { value: '49', label: 'Explosives' },
    { value: '59', label: 'Firearm Accessories' },
    { value: '64', label: 'Fuel' },
    { value: '65', label: 'Identity Documents' },
    { value: '66', label: 'Identity–Intangible' },
    { value: '67', label: 'Law Enforcement Equipment' },
    { value: '68', label: 'Lawn/Yard/Garden Equipment' },
    { value: '69', label: 'Logging Equipment' },
    { value: '70', label: 'Medical/Medical Lab Equipment' },
    { value: '71', label: 'Metals, Non-Precious' },
    { value: '72', label: 'Musical Instruments' },
    { value: '73', label: 'Pets' },
    { value: '74', label: 'Photographic/Optical Equipment' },
    { value: '75', label: 'Portable Electronic Communications' },
    { value: '76', label: 'Recreational/Sports Equipment' },
    { value: '77', label: 'Other' },
    { value: '78', label: 'Trailers' },
    { value: '79', label: 'Watercraft Equipment/Parts/Accessories' },
    { value: '80', label: 'Weapons–Other' },
    { value: '88', label: 'Pending Inventory' },
  ];

  const custodyConstraint = nibrsLossType ? getCustodyConstraint(nibrsLossType as NIBRSLossType) : null;

  const handleNext = () => {
    if (nibrsLossType) {
      const custodyValue = custodyConstraint?.value !== undefined
        ? custodyConstraint.value
        : inPoliceCustody;

      const classificationData = {
        nibrsLossType: nibrsLossType,
        inPoliceCustody: custodyValue,
        isEvidence,
        owner,
        nibrsPropertyDescription: parseInt(nibrsPropertyDescription.toString()) || 0
      };
      onNext(classificationData);
    }
  };

  const isValid = nibrsLossType !== null && nibrsPropertyDescription !== 0;

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column', minHeight: 0 }}>
      {/* Content Area - matches PropertyForm styling */}
      <Box sx={{ flex: 1, overflow: 'auto', pb: '128px' }}>
        <Box sx={{ p: 4 }}>
          <MuiTypography variant="h5" sx={{ mb: 4, color: colors.grey[900] }}>
            Property Classification & Basic Info
          </MuiTypography>

          {/* NIBRS Loss Type */}
          <Box sx={{ mb: 4 }}>
            <Dropdown
              title="NIBRS Property Loss Type *"
              placeholder="Select Property Loss Type"
              options={nibrsOptions}
              value={nibrsLossType || null}
              onChange={(value) => {
                setNibrsLossType(value as NIBRSLossType | null);
              }}
              disabled={readOnly}
              enableSearch={true}
            />
          </Box>

          {/* NIBRS Property Description */}
          <Box sx={{ mb: 4 }}>
            <Dropdown
              title="NIBRS Property Description *"
              placeholder="Select NIBRS Property Description"
              options={nibrsPropertyDescriptionOptions}
              value={nibrsPropertyDescription.toString() || null}
              onChange={(value) => setNibrsPropertyDescription(parseInt(value || '0'))}
              disabled={readOnly}
              enableSearch={true}
            />
          </Box>

          {/* Owner */}
          <Box sx={{ mb: 4 }}>
            <TextInput
              title="Owner"
              type={InputType.Text}
              value={owner}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setOwner(e.target.value)}
              placeholder="Owner information if applicable"
              disabled={readOnly}
            />
          </Box>

          {/* Police Custody */}
          <Box sx={{ mb: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Checkbox
                checked={custodyConstraint?.value !== undefined ? custodyConstraint.value : inPoliceCustody}
                onChange={(e) => !custodyConstraint?.disabled && setInPoliceCustody(e.target.checked)}
                disabled={readOnly || custodyConstraint?.disabled}
              />
              <Box sx={{ ml: 2 }}>
                <Typography style="body4" color={colors.grey[500]}>
                  In police custody?
                </Typography>
              </Box>
            </Box>
          </Box>

          {/* Is Evidence Checkbox */}
          <Box sx={{ mb: 4 }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Checkbox
                checked={isEvidence}
                onChange={(e) => setIsEvidence(e.target.checked)}
                disabled={readOnly}
              />
              <Box sx={{ ml: 2 }}>
                <Typography style="body4" color={colors.grey[500]}>
                  Is evidence?
                </Typography>
              </Box>
            </Box>
          </Box>


        </Box>
      </Box>

      {/* Fixed Action Bar - matches PropertyForm styling */}
      <Box sx={{
        position: "fixed",
        bottom: 0,
        left: 0,
        right: 0,
        padding: "16px 24px",
        backgroundColor: "white",
        borderTop: "1px solid #E0E0E0",
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
        gap: 2,
        zIndex: 10,
      }}>
        <Button
          label="Cancel"
          color="grey"
          prominence={false}
          onClick={onCancel}
          disabled={readOnly}
        />

        <Button
          label="Next"
          color="blue"
          prominence={true}
          onClick={handleNext}
          disabled={readOnly || !isValid}
        />
      </Box>
    </Box>
  );
};

// Property Check-In Step Component
interface PropertyCheckInStepProps {
  onNext: (data: CheckInData) => void;
  onBack: () => void;
  onCancel: () => void;
  initialData?: CheckInData;
  readOnly?: boolean;
  reportId?: string;
}

const PropertyCheckInStep = ({
  onNext,
  onBack,
  onCancel,
  initialData,
  readOnly = false,
  reportId
}: PropertyCheckInStepProps) => {
  // Set current date and time - non-editable
  const now = new Date();
  const currentDate = now.toISOString().split('T')[0]; // YYYY-MM-DD format
  const currentTime = now.toTimeString().slice(0, 5); // HH:MM format
  const currentDateTime = now.toLocaleString(); // Display format

  // Generate property number in PROP-XXXXXX format
  const generatePropertyNumber = () => {
    const randomNumber = Math.floor(Math.random() * 900000) + 100000; // 6-digit number
    return `PROP-${randomNumber}`;
  };

  const [officer, setOfficer] = useState(initialData?.officer || '');
  const [location, setLocation] = useState(initialData?.location || '');
  const [comments, setComments] = useState(initialData?.comments || '');
  const [propertyNumber, setPropertyNumber] = useState(initialData?.propertyNumber || generatePropertyNumber());
  const [sameAsIncidentLocation, setSameAsIncidentLocation] = useState(initialData?.sameAsIncidentLocation || false);

  // Field collection time state
  const [fieldCollectionDate, setFieldCollectionDate] = useState(initialData?.fieldCollectionDate || currentDate);
  const [fieldCollectionTime, setFieldCollectionTime] = useState(initialData?.fieldCollectionTime || currentTime);

  // Incident location state
  const [incidentLocationStreetAddress, setIncidentLocationStreetAddress] = useState(initialData?.incidentLocationStreetAddress || '');
  const [incidentLocationUnitInfo, setIncidentLocationUnitInfo] = useState(initialData?.incidentLocationUnitInfo || '');
  const [incidentLocationType, setIncidentLocationType] = useState(initialData?.incidentLocationType || '');
  const [incidentLocationCommonName, setIncidentLocationCommonName] = useState(initialData?.incidentLocationCommonName || '');
  const [incidentLocationCity, setIncidentLocationCity] = useState(initialData?.incidentLocationCity || '');
  const [incidentLocationState, setIncidentLocationState] = useState(initialData?.incidentLocationState || '');
  const [incidentLocationZipCode, setIncidentLocationZipCode] = useState(initialData?.incidentLocationZipCode || '');

  // State for incident location data from report
  const [reportIncidentLocation, setReportIncidentLocation] = useState<any>(null);

  // Fetch report sections to get incident location data
  const { data: reportSections } = useListReportSections(reportId || "", {
    enabled: !!reportId,
    staleTime: 5 * 60 * 1000,
    refetchOnWindowFocus: true,
  });

  // Extract incident location data from report sections
  useEffect(() => {
    if (reportSections?.sections) {
      const foundSection = reportSections.sections.find(
        // @ts-expect-error TODO: Fix type issue
        (section) => section.type === "SECTION_TYPE_INCIDENT_DETAILS"
      );
      if (foundSection) {
        // @ts-expect-error TODO: Fix type issue
        const incidentDetails = foundSection.incidentDetails;
        if (incidentDetails) {
          setReportIncidentLocation(incidentDetails);
        }
      }
    }
  }, [reportSections]);

  // Check if incident location is available
  const hasIncidentLocation = reportIncidentLocation && (
    reportIncidentLocation.incidentLocationStreetAddress ||
    reportIncidentLocation.incidentLocationCity ||
    reportIncidentLocation.incidentLocationType
  );

  // Handle checkbox change to populate fields
  const handleSameAsIncidentLocationChange = (checked: boolean) => {
    setSameAsIncidentLocation(checked);

    if (checked && reportIncidentLocation) {
      // Populate fields with incident location data
      setIncidentLocationStreetAddress(reportIncidentLocation.incidentLocationStreetAddress || '');
      setIncidentLocationUnitInfo(reportIncidentLocation.incidentLocationUnitInfo || '');
      setIncidentLocationType(reportIncidentLocation.incidentLocationType || '');
      setIncidentLocationCommonName(reportIncidentLocation.incidentLocationCommonName || '');
      setIncidentLocationCity(reportIncidentLocation.incidentLocationCity || '');
      setIncidentLocationState(reportIncidentLocation.incidentLocationState || '');
      setIncidentLocationZipCode(reportIncidentLocation.incidentLocationZipCode || '');
    } else if (!checked) {
      // Clear fields when unchecked
      setIncidentLocationStreetAddress('');
      setIncidentLocationUnitInfo('');
      setIncidentLocationType('');
      setIncidentLocationCommonName('');
      setIncidentLocationCity('');
      setIncidentLocationState('');
      setIncidentLocationZipCode('');
    }
  };

  // Get assets for officer dropdown
  const { data: assetsResponse } = useListAssets({
    pageSize: 100,
    pageToken: "",
    type: AssetType.UNSPECIFIED,
    status: AssetStatus.UNSPECIFIED,
    orderBy: "name",
  } as ListAssetsRequest);

  // Create officer options from assets
  const officerOptions: DropdownOption[] = [
    ...(assetsResponse?.assets?.map(asset => ({
      value: asset.id,
      label: asset.name || asset.id
    })) || [])
  ];


  const handleNext = () => {
    onNext({
      officer,
      date: currentDate,
      time: currentTime,
      location,
      comments,
      propertyNumber,
      sameAsIncidentLocation,
      // Field collection time
      fieldCollectionDate,
      fieldCollectionTime,
      // Include incident location details
      incidentLocationStreetAddress,
      incidentLocationUnitInfo,
      incidentLocationType,
      incidentLocationCommonName,
      incidentLocationCity,
      incidentLocationState,
      incidentLocationZipCode
    });
  };

  const isValid = officer && location && propertyNumber;

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column', minHeight: 0 }}>
      {/* Content Area - matches PropertyForm styling */}
      <Box sx={{ flex: 1, overflow: 'auto', pb: '128px' }}>
        <Box sx={{ p: 3 }}>
          <Box sx={{ mb: 4 }}>
            <Typography style="h2" color={colors.grey[900]}>
              Check-In Property
            </Typography>
          </Box>

          {/* Info Banner */}
          <Box sx={{
            mb: 4,
            p: 2,
            backgroundColor: colors.vine[50],
            border: `1px solid ${colors.vine[200]}`,
            borderRadius: 1
          }}>
            <Typography style="body2" color={colors.vine[700]}>
              <strong>Property Check-In Required:</strong> This property type requires custody check-in. Please record the intake details below.
            </Typography>
          </Box>

          {/* Property Number */}
          <Box sx={{ mb: 4 }}>
            <TextInput
              title="Property Number *"
              type={InputType.Text}
              value={propertyNumber}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setPropertyNumber(e.target.value)}
              placeholder="PROP-XXXXXX"
              disabled={true} // Always read-only
            />
          </Box>

          {/* Officer/Asset */}
          <Box sx={{ mb: 4 }}>
            <Dropdown
              title="Receiving Officer/Asset *"
              placeholder="Select Officer or Asset"
              options={officerOptions}
              value={officer || null}
              onChange={(value) => setOfficer(value || '')}
              disabled={readOnly}
            />
          </Box>

          {/* Field Collection Time */}
          <Box sx={{ mb: 4 }}>
            <Box sx={{ display: "flex", gap: "16px" }}>
              <Box sx={{ width: "50%" }}>
                <DatePicker
                  title="Field Collection Date"
                  placeholder="MM/DD/YYYY"
                  value={fieldCollectionDate ? new Date(fieldCollectionDate) : null}
                  onChange={(date) => setFieldCollectionDate(date ? date.toISOString().split('T')[0] : '')}
                  disabled={readOnly}
                />
              </Box>
              <Box sx={{ width: "50%" }}>
                <TextInput
                  title="Field Collection Time"
                  type={InputType.Text}
                  mask={InputMask.Time}
                  value={fieldCollectionTime}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFieldCollectionTime(e.target.value)}
                  disabled={readOnly}
                />
              </Box>
            </Box>
          </Box>

          {/* Current Date and Time (Display Only) */}
          <Box sx={{ mb: 4 }}>
            <Box sx={{ mb: 1 }}>
              <Typography style="body4" color={colors.grey[500]}>
                Check-In Date & Time
              </Typography>
            </Box>
            <Box sx={{
              p: 2,
              backgroundColor: colors.grey[50],
              border: `1px solid ${colors.grey[200]}`,
              borderRadius: 1
            }}>
              <Typography style="body2" color={colors.grey[700]}>
                {currentDateTime}
              </Typography>
            </Box>
          </Box>

          {/* Incident Location Section */}
          <Box sx={{ mb: 4 }}>
            <Box sx={{ mb: 2 }}>
              <Typography style="caps1" color={colors.grey[500]}>
                INCIDENT LOCATION
              </Typography>
            </Box>

            {/* Same as Incident Location Checkbox */}
            <Box sx={{ mb: 3 }}>
              <Tooltip
                title={!hasIncidentLocation ? "No incident location defined" : ""}
                placement="top"
              >
                <span>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Checkbox
                      checked={sameAsIncidentLocation}
                      onChange={(e) => handleSameAsIncidentLocationChange(e.target.checked)}
                      disabled={readOnly || !hasIncidentLocation}
                    />
                    <Box sx={{ ml: 2 }}>
                      <Typography style="body4" color={colors.grey[500]}>
                        Same as incident location
                      </Typography>
                    </Box>
                  </Box>
                </span>
              </Tooltip>
            </Box>

            {/* Street Address and Apt/Suite/Unit # */}
            <Box sx={{ display: "flex", gap: "16px", mb: 2 }}>
              <Box sx={{ width: "70%" }}>
                <TextInput
                  title="Street Address"
                  placeholder="Enter street address"
                  value={incidentLocationStreetAddress}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => setIncidentLocationStreetAddress(e.target.value)}
                  readOnly={readOnly || sameAsIncidentLocation}
                />
              </Box>
              <Box sx={{ width: "30%" }}>
                <TextInput
                  title="Apt/Suite/Unit # • Optional"
                  placeholder=""
                  value={incidentLocationUnitInfo}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => setIncidentLocationUnitInfo(e.target.value)}
                  readOnly={readOnly || sameAsIncidentLocation}
                />
              </Box>
            </Box>

            {/* Location Type and Common Name */}
            <Box sx={{ display: "flex", gap: "16px", mb: 2 }}>
              <Box sx={{ width: "100%" }}>
                <Dropdown
                  title="Location Type"
                  placeholder="Select location type"
                  options={NIBRS_LOCATION_TYPE_OPTIONS}
                  value={incidentLocationType}
                  onChange={(value: string | null) => setIncidentLocationType(value || "")}
                  enableSearch={true}
                  readOnly={readOnly || sameAsIncidentLocation}
                />
              </Box>
            </Box>
            <Box sx={{ display: "flex", gap: "16px", mb: 2 }}>
              <Box sx={{ width: "55%" }}>
                <TextInput
                  title="Common Name • Optional"
                  placeholder=""
                  value={incidentLocationCommonName}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => setIncidentLocationCommonName(e.target.value)}
                  readOnly={readOnly || sameAsIncidentLocation}
                />
              </Box>
            </Box>

            {/* City, State, Zip */}
            <Box sx={{ display: "flex", gap: "16px", mb: 2 }}>
              <Box sx={{ width: "33.33%" }}>
                <TextInput
                  title="City"
                  placeholder=""
                  value={incidentLocationCity}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => setIncidentLocationCity(e.target.value)}
                  readOnly={readOnly || sameAsIncidentLocation}
                />
              </Box>
              <Box sx={{ width: "33.33%" }}>
                <Dropdown
                  title="State"
                  placeholder="Select state"
                  value={incidentLocationState}
                  options={STATE_OPTIONS}
                  onChange={(value: string | null) => setIncidentLocationState(value || "")}
                  enableSearch={true}
                  readOnly={readOnly || sameAsIncidentLocation}
                />
              </Box>
              <Box sx={{ width: "33.33%" }}>
                <TextInput
                  title="Zip"
                  placeholder=""
                  value={incidentLocationZipCode}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => setIncidentLocationZipCode(e.target.value)}
                  readOnly={readOnly || sameAsIncidentLocation}
                />
              </Box>
            </Box>
          </Box>

          {/* Storage Location Section */}
          <Box sx={{ mb: 4 }}>
            <Box sx={{ mb: 2 }}>
              <Typography style="caps1" color={colors.grey[500]}>
                STORAGE LOCATION
              </Typography>
            </Box>


            {/* Storage Location */}
            <Box sx={{ mb: 4 }}>
              <TextInput
                title="Storage Location *"
                type={InputType.Text}
                value={location}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setLocation(e.target.value)}
                placeholder="Enter storage location (e.g., Evidence Room A-12)"
                disabled={readOnly}
              />
            </Box>
          </Box>

          {/* Comments */}
          <Box sx={{ mb: 4 }}>
            <TextInput
              title="Comments"
              type={InputType.Multiline}
              value={comments}
              onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setComments(e.target.value)}
              placeholder="Additional comments or notes about the property intake"
              disabled={readOnly}
              rows={3}
            />
          </Box>
        </Box>
      </Box>

      {/* Fixed Action Bar - matches PropertyForm styling */}
      <Box sx={{
        position: "fixed",
        bottom: 0,
        left: 0,
        right: 0,
        padding: "16px 24px",
        backgroundColor: "white",
        borderTop: "1px solid #E0E0E0",
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
        gap: 2,
        zIndex: 10,
      }}>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            label="Back"
            color="grey"
            prominence={false}
            onClick={onBack}
            disabled={readOnly}
            leftIcon={<FaArrowLeft />}
          />
          <Button
            label="Cancel"
            color="grey"
            prominence={false}
            onClick={onCancel}
            disabled={readOnly}
          />
        </Box>

        <Button
          label="Next"
          color="blue"
          prominence={true}
          onClick={handleNext}
          disabled={readOnly || !isValid}
        />
      </Box>
    </Box>
  );
};