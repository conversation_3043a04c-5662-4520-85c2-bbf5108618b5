import { useSearchProperties } from "@/app/apis/services/workflow/property/hooks";
import { enumStringToPropertyType, propertyTypeToDisplayString } from "@/app/apis/services/workflow/property/propertyTypeManager";
import {
    Property as PropertyRecord,
    SearchOrderBy as PropertySearchOrderBy,
    SearchPropertiesRequest,
} from "@/app/apis/services/workflow/property/types";
import { getPropertyStatusDisplay, getReadablePropertyCategory, getReadablePropertyType } from "@/app/utils/propertyHelpers";
import { Button } from "@/design-system/components/Button";
import { TextInput } from "@/design-system/components/TextInput";
import { Typography } from "@/design-system/components/Typography";
import { colors } from "@/design-system/tokens";
import { Box, CircularProgress } from "@mui/material";
import React, { useEffect, useMemo, useState } from "react";

type PropertySearchFormProps = {
    onPropertySelect: (property: PropertyRecord) => void;
    onStartNewRecord: (searchData?: any) => void;
    handleCloseSidePanel: (isOpen: boolean) => void;
    currentEntities?: any[];
};

export const PropertySearchForm: React.FC<PropertySearchFormProps> = ({
    onPropertySelect,
    onStartNewRecord,
    handleCloseSidePanel,
    currentEntities = [],
}) => {
    const [searchFormData, setSearchFormData] = useState<any>({});
    const [hasSearched, setHasSearched] = useState(false);
    const [isSearching, setIsSearching] = useState(false);
    const [searchError, setSearchError] = useState<string | null>(null);
    const [searchTrigger, setSearchTrigger] = useState<SearchPropertiesRequest | null>(null);

    const buildQueryString = (): string => {
        const fields = [
            searchFormData.serialNumber,
            searchFormData.category,
            searchFormData.value,
            searchFormData.makeModelBrand,
        ]
            .filter((v) => typeof v === "string" && v.trim() !== "")
            .map((v) => v.trim());
        return fields.join(" ");
    };

    const request: SearchPropertiesRequest | null = useMemo(() => {
        const query = buildQueryString();
        if (!query) return null;
        return {
            query,
            pageSize: 20,
            orderBy: PropertySearchOrderBy.SEARCH_ORDER_BY_CREATED_AT,
        };
    }, [searchFormData]);

    const { data, isLoading, isError } = useSearchProperties(searchTrigger || ({} as any), {
        enabled: !!searchTrigger,
    });

    useEffect(() => {
        setIsSearching(isLoading);
    }, [isLoading]);

    useEffect(() => {
        if (isError) {
            setSearchError("Error searching property records. Please try again.");
            setIsSearching(false);
        } else {
            setSearchError(null);
        }
    }, [isError]);

    const handleSearch = () => {
        if (!request) {
            setSearchError("Please enter at least one search criteria.");
            return;
        }
        setHasSearched(true);
        setIsSearching(true);
        setSearchTrigger({ ...request });
    };

    const isAlreadyInReport = (property: PropertyRecord) => {
        return currentEntities.some((e) => e.id === property.id);
    };

    const handleSelect = (property: PropertyRecord) => {
        onPropertySelect(property);
        handleCloseSidePanel(true);
    };

    return (
        <>
            <Box sx={{ display: "flex", flexDirection: "column", pb: 12 }}>
                <Box sx={{ mb: 3 }}>
                    <Typography style="h1" color={colors.grey[900]}>
                        Search Property
                    </Typography>
                    <Box sx={{ mt: 1 }}>
                        <Typography style="body3" color={colors.grey[600]}>
                            Search existing property records or start a new one
                        </Typography>
                    </Box>
                </Box>

                <Box sx={{ mb: 4 }}>
                    <Box
                        sx={{
                            display: "grid",
                            gridTemplateColumns: "1fr 1fr",
                            gap: 2,
                            mb: 2,
                        }}
                    >
                        <TextInput
                            title="Serial Number"
                            placeholder="Enter serial number"
                            value={searchFormData.serialNumber || ""}
                            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                setSearchFormData({ ...searchFormData, serialNumber: e.target.value })
                            }
                        />
                        <TextInput
                            title="Category"
                            placeholder="Enter category"
                            value={searchFormData.category || ""}
                            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                setSearchFormData({ ...searchFormData, category: e.target.value })
                            }
                        />
                    </Box>
                    <Box
                        sx={{
                            display: "grid",
                            gridTemplateColumns: "1fr 1fr",
                            gap: 2,
                            mb: 3,
                        }}
                    >
                        <TextInput
                            title="Value"
                            placeholder="Enter value"
                            value={searchFormData.value || ""}
                            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                setSearchFormData({ ...searchFormData, value: e.target.value })
                            }
                        />
                        <TextInput
                            title="Make/Model/Brand"
                            placeholder="Enter make/model/brand"
                            value={searchFormData.makeModelBrand || ""}
                            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                setSearchFormData({ ...searchFormData, makeModelBrand: e.target.value })
                            }
                        />
                    </Box>

                    <Box sx={{ display: "flex", justifyContent: "flex-end" }}>
                        <Button label="Search" color="blue" prominence={true} onClick={handleSearch} />
                    </Box>
                </Box>

                {hasSearched && (
                    <Box sx={{ mb: 4 }}>
                        <Box sx={{ mb: 2 }}>
                            <Typography style="h2" color={colors.grey[900]}>
                                Search Results
                            </Typography>
                        </Box>

                        {isSearching && (
                            <Box sx={{ display: "flex", justifyContent: "center", p: 4 }}>
                                <CircularProgress />
                            </Box>
                        )}

                        {searchError && (
                            <Box sx={{ p: 2, bgcolor: colors.rose[50], borderRadius: 1, mb: 2 }}>
                                <Typography style="body3" color={colors.rose[700]}>
                                    {searchError}
                                </Typography>
                            </Box>
                        )}

                        {!isSearching && data?.properties && (
                            <>
                                <Box sx={{ mb: 2 }}>
                                    <Typography style="body3" color={colors.grey[600]}>
                                        Found {data.properties.length} results
                                    </Typography>
                                </Box>

                                {data.properties.length > 0 ? (
                                    <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
                                        {data.properties.map((p) => {
                                            const rawCategory = p.details?.category || "";
                                            const name = getReadablePropertyCategory(rawCategory) || p.details?.description || "Property";
                                            const serial = p.details?.serial || p.details?.serialNumber || "---";
                                            const value = p.details?.value ?? "---";
                                            const location = p.currentLocation || "---";
                                            let typeLabel = "Unknown";
                                            const topType: any = (p as any)?.nibrsPropertyType;
                                            if (typeof topType === "number") {
                                                typeLabel = propertyTypeToDisplayString(topType);
                                            } else if (typeof topType === "string" && topType.trim() !== "") {
                                                typeLabel = propertyTypeToDisplayString(enumStringToPropertyType(topType));
                                            }
                                            if (typeLabel === "Unknown") {
                                                const rawType = (p as any)?.details?.nibrsPropertyType || (p as any)?.details?.propertyType;
                                                if (rawType) {
                                                    const readable = getReadablePropertyType(String(rawType));
                                                    typeLabel = readable.replace(/\s*Property$/, "");
                                                }
                                            }
                                            const statusLabel = getPropertyStatusDisplay(p.propertyStatus);
                                            const propertyNumber = p.propertyNumber || "---";
                                            const already = isAlreadyInReport(p);

                                            return (
                                                <Box
                                                    key={p.id}
                                                    sx={{
                                                        padding: 2,
                                                        border: `1px solid ${colors.grey[200]}`,
                                                        borderRadius: "8px",
                                                        cursor: "pointer",
                                                        backgroundColor: already ? colors.blue[50] : colors.grey[50],
                                                        "&:hover": {
                                                            backgroundColor: already ? colors.blue[100] : colors.grey[100],
                                                        },
                                                        "&:active": {
                                                            backgroundColor: already ? colors.blue[200] : colors.grey[200],
                                                        },
                                                        display: "flex",
                                                        justifyContent: "space-between",
                                                        alignItems: "center",
                                                    }}
                                                    onClick={() => handleSelect(p)}
                                                >
                                                    <Box sx={{ flex: 1 }}>
                                                        <Box sx={{ mb: 1, display: "flex", alignItems: "center", gap: 1 }}>
                                                            <Typography style="body3" color={colors.grey[900]}>
                                                                {name}
                                                            </Typography>
                                                            {already && (
                                                                <Box
                                                                    sx={{
                                                                        backgroundColor: colors.blue[100],
                                                                        color: colors.blue[700],
                                                                        px: 1,
                                                                        py: 0.5,
                                                                        borderRadius: 1,
                                                                        fontSize: "10px",
                                                                        fontWeight: 500,
                                                                    }}
                                                                >
                                                                    Already in report
                                                                </Box>
                                                            )}
                                                        </Box>
                                                        <Box sx={{ display: "flex", gap: 1, flexWrap: "wrap" }}>
                                                            <Typography style="tag2" color={colors.grey[500]}>Property #:</Typography>
                                                            <Typography style="tag2" color={colors.grey[900]}>{propertyNumber}</Typography>
                                                            <Typography style="tag2" color={colors.grey[500]}>,</Typography>
                                                            <Typography style="tag2" color={colors.grey[500]}>Serial:</Typography>
                                                            <Typography style="tag2" color={colors.grey[900]}>{serial}</Typography>
                                                            <Typography style="tag2" color={colors.grey[500]}>,</Typography>
                                                            <Typography style="tag2" color={colors.grey[500]}>Type:</Typography>
                                                            <Typography style="tag2" color={colors.grey[900]}>{typeLabel}</Typography>
                                                            <Typography style="tag2" color={colors.grey[500]}>,</Typography>
                                                            <Typography style="tag2" color={colors.grey[500]}>Status:</Typography>
                                                            <Typography style="tag2" color={colors.grey[900]}>{statusLabel}</Typography>
                                                            <Typography style="tag2" color={colors.grey[500]}>,</Typography>
                                                            <Typography style="tag2" color={colors.grey[500]}>Value:</Typography>
                                                            <Typography style="tag2" color={colors.grey[900]}>{value}</Typography>
                                                            <Typography style="tag2" color={colors.grey[500]}>,</Typography>
                                                            <Typography style="tag2" color={colors.grey[500]}>Location:</Typography>
                                                            <Typography style="tag2" color={colors.grey[900]}>{location}</Typography>
                                                        </Box>
                                                    </Box>
                                                </Box>
                                            );
                                        })}
                                    </Box>
                                ) : (
                                    <Box sx={{ p: 3, textAlign: "center", bgcolor: colors.grey[50], borderRadius: 1 }}>
                                        <Typography style="body3" color={colors.grey[600]}>
                                            No existing records found matching your search terms
                                        </Typography>
                                    </Box>
                                )}
                            </>
                        )}

                        {!isSearching && data && !data.properties && (
                            <Box sx={{ p: 3, textAlign: "center", bgcolor: colors.grey[50], borderRadius: 1 }}>
                                <Typography style="body3" color={colors.grey[600]}>
                                    No results found
                                </Typography>
                            </Box>
                        )}
                    </Box>
                )}

                <Box sx={{ display: "flex", justifyContent: "flex-start" }}>
                    <Button
                        label="Start New Property Record"
                        color="blue"
                        prominence={true}
                        style="ghost"
                        onClick={() => onStartNewRecord(searchFormData)}
                        size="small"
                    />
                </Box>
            </Box>
        </>
    );
};

export default PropertySearchForm;


