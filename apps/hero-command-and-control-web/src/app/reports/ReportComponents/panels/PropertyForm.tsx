import { stringToPropertyStatus } from '@/app/apis/services/workflow/property/enumConverters';
import { isValidPropertyTypeEnumString, migrateLegacyPropertyType } from '@/app/apis/services/workflow/property/propertyTypeManager';
import { But<PERSON> } from '@/design-system/components/Button';
import { InputType } from '@/design-system/components/TextInput';
import { Typography } from '@/design-system/components/Typography';
import { FormRenderer, FormRendererRef } from '@/design-system/form/FormRenderer';
import { colors } from '@/design-system/tokens';
import { create } from "@bufbuild/protobuf";
import { Box } from '@mui/material';
import Image from 'next/image';
import { AssetStatus, AssetType, ListAssetsRequest } from "proto/hero/assets/v2/assets_pb";
import { FileReferenceSchema } from "proto/hero/reports/v2/reports_pb";
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useFileUpload } from '../../../apis/services/filerepository/hooks';
import { useListAssets } from '../../../apis/services/workflow/assets/hooks';
import { useAddPropertyFileAttachment, useProperty, useUpdateProperty } from '../../../apis/services/workflow/property/hooks';
// Use entity schema approach like vehicles and persons
import { EntityType } from 'proto/hero/entity/v1/entity_pb';
import { useListLatestEntitySchemas } from '../../../apis/services/workflow/entity/hooks';
import { formValuesToPropertyDetails, schemaToFormConfig } from '../core/utils/schemaToFormConfig';
import {
    FileUploadDropZone,
    StagedFilesList,
    UploadProgressDashboard,
    type StagedFileReference
} from './uiComponents/MediaPanel';

interface UploadResult {
    fileId: string;
    fileName: string;
    fileType: string;
    displayName?: string;
    caption?: string;
    fileCategory?: string;
}

interface PropertyFormProps {
    onSubmit: (values: any) => void;
    onCancel: () => void;
    onSaveAndAddAnother?: (values: any) => void;
    onPropertyUpdated?: (updatedProperty: any) => void; // Callback for when property is updated successfully
    initialValues?: any;
    readOnly?: boolean;
    customTitle?: string;
    propertyId?: string; // For existing properties when editing
    reportId?: string; // For draft persistence
}

export const PropertyForm: React.FC<PropertyFormProps> = ({
    onSubmit,
    onCancel,
    onSaveAndAddAnother,
    onPropertyUpdated,
    initialValues = {},
    readOnly = false,
    customTitle,
    propertyId,
    reportId,
}) => {
    // Remove formValues state to prevent circular dependencies - FormRenderer manages its own state
    const [validationError, setValidationError] = useState<string | null>(null);
    const [_uploadedFiles, setUploadedFiles] = useState<Array<{ fileId: string; fileName: string; fileType: string }>>([]);
    // Remove formKey that was causing form resets
    const formRef = useRef<FormRendererRef>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);

    // Fetch property entity schemas like vehicles and persons
    const { data: propertySchemas } = useListLatestEntitySchemas({
        entityType: EntityType.PROPERTY,
        pageSize: 1,
        pageToken: "",
    } as any);

    // Use the first available property schema, or null if none exist
    const selectedSchema = useMemo(() => {
        return propertySchemas?.schemas?.[0] || null;
    }, [propertySchemas]);

    // Fetch assets for Current Custodian dropdown
    const { data: assetsResponse } = useListAssets({
        pageSize: 100,
        pageToken: "",
        type: AssetType.UNSPECIFIED,
        status: AssetStatus.UNSPECIFIED,
        orderBy: "name",
    } as ListAssetsRequest);

    // New state hooks for file upload UI
    const [isDragging, setIsDragging] = useState(false);
    const [stagedFiles, setStagedFiles] = useState<StagedFileReference[]>([]);
    const [overallProgress, setOverallProgress] = useState(0);
    const [uploadingSummary, setUploadingSummary] = useState({
        totalFiles: 0,
        completedFiles: 0,
        failedFiles: 0,
        totalBytes: 0,
        uploadedBytes: 0,
        averageSpeed: 0,
        estimatedTimeRemaining: 0,
        isComplete: false
    });

    // Draft persistence functions
    const getDraftKey = useCallback(() => {
        return `property_draft_${reportId || 'new'}`;
    }, [reportId]);

    // Use ref to access current stagedFiles without causing re-renders
    const stagedFilesRef = useRef<StagedFileReference[]>([]);
    stagedFilesRef.current = stagedFiles;

    const _saveDraftToStorage = useCallback((data: any) => {
        if (!reportId) return; // Only save for existing reports
        try {
            // Clean the data before saving - only save flat values
            const cleanData: any = {};

            Object.keys(data).forEach(key => {
                const value = data[key];
                // Only save flat field values
                if (key !== 'propertyInformationSection' &&
                    (typeof value !== 'object' || Array.isArray(value))) {
                    cleanData[key] = value;
                }
            });

            // Save staged files using ref to avoid dependency issues
            cleanData.stagedFiles = stagedFilesRef.current.map(file => ({
                ...file,
                // Don't save File objects
                file: undefined,
                // Keep preview URLs for images
                preview: file.preview,
                // Reset upload status
                isUploading: false,
                uploadProgress: file.fileId ? 100 : 0,
                uploadError: undefined,
                uploadSpeed: undefined,
                timeRemaining: undefined,
                bytesUploaded: undefined
            }));

            localStorage.setItem(getDraftKey(), JSON.stringify(cleanData));
        } catch {
            // Failed to save draft
        }
    }, [getDraftKey, reportId]);

    const loadDraftFromStorage = useCallback(() => {
        if (!reportId) return null; // Only load for existing reports
        try {
            const stored = localStorage.getItem(getDraftKey());
            const draft = stored ? JSON.parse(stored) : null;

            // Restore staged files if present
            if (draft?.stagedFiles) {
                setStagedFiles(draft.stagedFiles.map((file: StagedFileReference) => ({
                    ...file,
                    // Reset upload status
                    isUploading: false,
                    uploadProgress: file.fileId ? 100 : 0
                })));
                delete draft.stagedFiles;
            }

            return draft;
        } catch {
            return null;
        }
    }, [getDraftKey, reportId]);

    // Hooks for file operations
    const { uploadFile, isLoading: _isUploadingFile } = useFileUpload();
    const addPropertyFileAttachmentMutation = useAddPropertyFileAttachment();

    // Fetch existing property data if propertyId is provided (for editing)
    const { data: existingProperty, isLoading: isLoadingProperty } = useProperty(propertyId || '');

    // Property update hook
    const updatePropertyMutation = useUpdateProperty();

    // Remove draft loading since we're not using formValues state anymore
    // Draft functionality can be re-implemented later if needed without causing circular dependencies
    useEffect(() => {
        // Draft loading removed to prevent circular dependencies
        return;
    }, [loadDraftFromStorage]);

    // Remove all formValues-dependent effects to prevent circular dependencies
    useEffect(() => {
        // FormRenderer handles its own state - no need for external state management
        return;
    }, []);

    // Remove auto-save entirely to prevent circular dependencies
    useEffect(() => {
        // Auto-save removed to prevent text input issues and circular dependencies
        return;
    }, []);

    // Create form config using the default schema
    const formConfig = useMemo(() => {
        if (!selectedSchema) {
            return null;
        }

        const assetOptions = assetsResponse?.assets?.map(asset => ({
            label: asset.name,
            value: asset.id
        })) || [];

        // Convert default schema to form config
        const config = schemaToFormConfig(selectedSchema);

        // Hide category field during creation (since it's set on wizard page 1)
        if (!propertyId) {
            config.sections.forEach((section: any) => {
                section.fields = section.fields.filter((field: any) => field.id !== 'category');
            });
        }

        // Add asset options to currentCustodian field
        const adminSection = config.sections.find((section: any) => section.id === 'admin');
        if (adminSection) {
            const currentCustodianField = adminSection.fields.find((field: any) => field.id === 'currentCustodian');
            if (currentCustodianField) {
                currentCustodianField.type = InputType.Dropdown;
                currentCustodianField.placeholder = "Select current custodian";
                currentCustodianField.options = assetOptions;
                currentCustodianField.enableSearch = true;
            }
        }

        return {
            ...config,
            title: customTitle || 'Property Information',
        };
    }, [selectedSchema, assetsResponse, customTitle, propertyId]);

    // Use single page config
    const singlePageConfig = formConfig;

    // Memoize initial values to prevent infinite re-renders
    // FormRenderer expects structured format: { sectionId: { fieldId: value } }
    const memoizedInitialValues = useMemo(() => {
        // Extract only the flat field values, excluding nested structures and meta fields
        const cleanValues: any = {};

        // Start with initialValues prop
        if (initialValues && typeof initialValues === 'object') {
            Object.keys(initialValues).forEach(key => {
                const value = initialValues[key];
                if (typeof value !== 'object' || Array.isArray(value)) {
                    cleanValues[key] = value;
                }
            });
        }

        // Handle property type: ensure we use proper proto enum values
        if (cleanValues.nibrsPropertyType && typeof cleanValues.nibrsPropertyType === 'string') {
            if (isValidPropertyTypeEnumString(cleanValues.nibrsPropertyType)) {
                // Already a valid proto enum string, keep as-is
            } else {
                // Convert legacy lowercase property type to proper proto enum
                const migratedType = migrateLegacyPropertyType(cleanValues.nibrsPropertyType);
                cleanValues.nibrsPropertyType = migratedType;
            }
        }

        // Keep category as NIBRS enum for dropdown matching (dropdown has NIBRS values)

        // If we have existing property data (for editing), populate the form fields from details
        if (existingProperty?.details) {
            const detailsMap = existingProperty.details;
            Object.keys(detailsMap).forEach(key => {
                const value = detailsMap[key];
                if (typeof value !== 'object' || Array.isArray(value)) {
                    // Keep category as NIBRS enum for dropdown value matching
                    cleanValues[key] = value;
                }
            });
        }

        // IMPORTANT: FormRenderer expects structured format: { sectionId: { fieldId: value } }
        // We need to organize the flat values into the section structure
        const structuredValues: any = {};

        // Since we have currentConfig available, we can't use it here (circular dependency)
        // For now, let's assume there's a main section called 'propertyInformationSection'
        // and put all values there. The actual section structure will be determined by the config.
        if (Object.keys(cleanValues).length > 0) {
            structuredValues.propertyInformationSection = cleanValues;
        }

        return structuredValues;
    }, [initialValues, existingProperty]);

    // Use single-page config and remove any legacy uploadImage field if present
    // Memoize to avoid changing object identity on unrelated re-renders (e.g., media staging),
    // which can cause FormRenderer to reset form state.
    const currentConfig = useMemo(() => {
        if (!singlePageConfig) {
            return null;
        }

        const config = {
            ...singlePageConfig,
            sections: singlePageConfig.sections.map((section: any) => ({
                ...section,
                fields: section.fields.filter((field: any) => field.id !== 'uploadImage')
            }))
        };
        return config;
    }, [singlePageConfig]);

    // Remove onValueChange entirely to prevent circular dependencies
    // FormRenderer will handle its own state, we'll get values only on submit
    const handleValueChange = useCallback((_values: any) => {
        // Do nothing - let FormRenderer manage its own state
        // We'll get the current values from formRef.current.getFlatValues() when needed
    }, []);

    // If no property schema exists or formConfig failed to generate, show a message
    if (!selectedSchema || !formConfig) {
        return (
            <div className="p-6 text-center">
                <h3 className="text-lg font-semibold mb-2">No Property Schema Available</h3>
                <p className="text-gray-600 mb-4">
                    No property schema has been configured. Please contact your administrator to set up property schemas.
                </p>
                <button
                    onClick={onCancel}
                    className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
                >
                    Cancel
                </button>
            </div>
        );
    }

    const validateRequiredFields = (allValues: any): boolean => {
        // Flatten the values to handle sectioned form structure
        const flattenedValues = flattenValues(allValues);
        const missingFields: string[] = [];

        // Schema validation - check all required fields defined in the form config
        formConfig.sections.forEach(section => {
            section.fields.forEach(field => {
                const fieldValue = flattenedValues[field.id];

                if (field.required && (!fieldValue || String(fieldValue).trim() === '')) {
                    missingFields.push(field.title || field.id);
                }
            });
        });

        if (missingFields.length > 0) {
            setValidationError(
                `Please fill in the following required fields: ${missingFields.join(', ')}`
            );
            return false;
        }

        // Validate property value is a number (if present)
        const propertyValue = flattenedValues.value || flattenedValues.collectedValue;
        if (propertyValue) {
            // More robust currency parsing that handles multiple formats
            const cleanValue = String(propertyValue).replace(/[^0-9.-]/g, '');
            if (cleanValue && isNaN(Number(cleanValue))) {
                setValidationError('Property value must be a valid number');
                return false;
            }
        }

        setValidationError(null);
        return true;
    };

    // Handle file uploads from the form
    const _handleFileUpload = async (files: File[]): Promise<Array<{ fileId: string; fileName: string; fileType: string }>> => {
        const uploadPromises = files.map(async (file, _index) => {
            // Retry configuration
            const maxRetries = 3;
            let lastError: Error;

            for (let attempt = 1; attempt <= maxRetries; attempt++) {
                try {
                    const result = await uploadFile(file, undefined, {
                        propertyId: propertyId || 'pending', // Will be updated after property creation
                        fileCategory: getFileCategory(file),
                        uploadContext: 'property_form'
                    });

                    if (result.success && result.fileId) {
                        const fileData = {
                            fileId: result.fileId!,
                            fileName: file.name,
                            fileType: file.type
                        };

                        return fileData;
                    } else {
                        const error = new Error(result.error || 'File upload failed');
                        lastError = error;

                        // Don't retry on the last attempt
                        if (attempt === maxRetries) {
                            throw error;
                        }
                    }
                } catch (error) {
                    lastError = error instanceof Error ? error : new Error('Unknown upload error');

                    // Don't retry on the last attempt
                    if (attempt === maxRetries) {
                        throw lastError;
                    }
                }

                // Exponential backoff: wait 2^attempt seconds (2s, 4s, 8s, etc.)
                if (attempt < maxRetries) {
                    const delayMs = Math.pow(2, attempt) * 1000;
                    await new Promise(resolve => setTimeout(resolve, delayMs));
                }
            }

            // This should never be reached due to the throw in the loop, but TypeScript requires it
            throw lastError!;
        });

        const uploadedFileData = await Promise.all(uploadPromises);

        // Update state for UI purposes
        setUploadedFiles(prev => {
            const updated = [...prev, ...uploadedFileData];
            return updated;
        });

        return uploadedFileData;
    };

    // Helper functions for file upload UI
    const formatBytes = (bytes: number): string => {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
    };

    const formatDuration = (seconds: number): string => {
        if (seconds < 60) return `${Math.round(seconds)}s`;
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = Math.round(seconds % 60);
        return `${minutes}m ${remainingSeconds}s`;
    };

    const createPreview = (file: File): string => {
        // Create preview for supported file types (same as MediaPanel)
        if (file.type.startsWith('image/') || file.type.startsWith('video/') || file.type === 'application/pdf') {
            return URL.createObjectURL(file);
        }
        return '';
    };

    const getFileCategory = (file: File): string => {
        if (file.type.startsWith('image/')) return 'property_image';
        if (file.type.startsWith('video/')) return 'property_video';
        if (file.type === 'application/pdf') return 'property_document';
        if (file.type.startsWith('audio/')) return 'property_audio';
        return 'property_media';
    };

    const renderFilePreview = (file: StagedFileReference) => {
        if (file.preview) {
            return (
                <Image
                    src={file.preview}
                    alt={file.displayName || 'Preview'}
                    width={100}
                    height={100}
                    style={{
                        width: '100%',
                        height: '100%',
                        objectFit: 'cover',
                    }}
                />
            );
        }
        return null;
    };

    const _updateFileProgress = (fileId: string, progress: number, bytesUploaded: number) => {
        setStagedFiles(prev =>
            prev.map(file => {
                if (file.id === fileId) {
                    const now = Date.now();
                    const timeElapsed = (now - (file.uploadStartTime || now)) / 1000;
                    const uploadSpeed = bytesUploaded / timeElapsed;
                    const remainingBytes = file.file ? file.file.size - bytesUploaded : 0;
                    const timeRemaining = uploadSpeed > 0 ? remainingBytes / uploadSpeed : 0;

                    return {
                        ...file,
                        uploadProgress: progress,
                        bytesUploaded,
                        uploadSpeed,
                        timeRemaining
                    };
                }
                return file;
            })
        );

        // Update overall progress
        updateOverallProgress();
    };

    const updateOverallProgress = () => {
        const summary = stagedFiles.reduce(
            (acc, file) => {
                acc.totalFiles++;
                if (file.uploadProgress === 100) acc.completedFiles++;
                if (file.uploadError) acc.failedFiles++;
                if (file.file) {
                    acc.totalBytes += file.file.size;
                    acc.uploadedBytes += file.bytesUploaded || 0;
                }
                if (file.uploadSpeed) {
                    acc.totalSpeed += file.uploadSpeed;
                    acc.speedCount++;
                }
                return acc;
            },
            {
                totalFiles: 0,
                completedFiles: 0,
                failedFiles: 0,
                totalBytes: 0,
                uploadedBytes: 0,
                totalSpeed: 0,
                speedCount: 0
            }
        );

        const averageSpeed = summary.speedCount > 0 ? summary.totalSpeed / summary.speedCount : 0;
        const remainingBytes = summary.totalBytes - summary.uploadedBytes;
        const estimatedTimeRemaining = averageSpeed > 0 ? remainingBytes / averageSpeed : 0;
        const progress = summary.totalBytes > 0 ? (summary.uploadedBytes / summary.totalBytes) * 100 : 0;

        setOverallProgress(progress);
        setUploadingSummary({
            ...summary,
            averageSpeed,
            estimatedTimeRemaining,
            isComplete: progress === 100
        });
    };

    const uploadStagedFiles = async (): Promise<UploadResult[]> => {
        const results: UploadResult[] = [];
        const filesToUpload = stagedFiles.filter(file =>
            file.file && !file.fileId && !file.isUploading && !file.uploadError
        );

        if (filesToUpload.length === 0) {
            // Return already uploaded files
            return stagedFiles
                .filter(file => file.fileId)
                .map(file => ({
                    fileId: file.fileId!,
                    fileName: String(file.displayName || file.metadata?.originalFilename || ''),
                    fileType: String(file.metadata?.fileType || ''),
                    displayName: file.displayName,
                    caption: file.caption,
                    fileCategory: file.fileCategory
                }));
        }

        for (const stagedFile of filesToUpload) {
            if (!stagedFile.file) continue;

            try {
                // Mark file as uploading
                setStagedFiles(prev =>
                    prev.map(f =>
                        f.id === stagedFile.id
                            ? { ...f, isUploading: true, uploadStartTime: Date.now() }
                            : f
                    )
                );

                // Upload the file with progress tracking
                const result = await uploadFile(
                    stagedFile.file,
                    undefined, // Progress callback not supported in current uploadFile signature
                    {
                        propertyId: propertyId || 'pending',
                        fileCategory: stagedFile.fileCategory || (stagedFile.file ? getFileCategory(stagedFile.file) : 'property_media'),
                        uploadContext: 'property_form',
                        metadata: {
                            displayName: stagedFile.displayName,
                            caption: stagedFile.caption
                        }
                    }
                );

                if (result.success && result.fileId) {
                    // Update staged file with upload result
                    setStagedFiles(prev =>
                        prev.map(f =>
                            f.id === stagedFile.id
                                ? {
                                    ...f,
                                    fileId: result.fileId || '',
                                    isUploading: false,
                                    uploadProgress: 100
                                }
                                : f
                        )
                    );

                    results.push({
                        fileId: result.fileId,
                        fileName: String(stagedFile.displayName || stagedFile.metadata?.originalFilename || ''),
                        fileType: String(stagedFile.metadata?.fileType || ''),
                        displayName: stagedFile.displayName,
                        caption: stagedFile.caption,
                        fileCategory: stagedFile.fileCategory
                    });
                } else {
                    throw new Error(result.error || 'Upload failed');
                }
            } catch (error) {
                // Mark file as failed
                setStagedFiles(prev =>
                    prev.map(f =>
                        f.id === stagedFile.id
                            ? {
                                ...f,
                                isUploading: false,
                                uploadError: error instanceof Error ? error.message : 'Upload failed'
                            }
                            : f
                    )
                );
                console.error(`Failed to upload file ${stagedFile.displayName}:`, error);
            }
        }

        return results;
    };

    // Create file attachment function that captures current uploaded files
    const createAttachFilesToPropertyFunction = (filesToAttach: Array<{ fileId: string; fileName: string; fileType: string }>) => {
        return async (createdPropertyId: string) => {
            if (filesToAttach.length === 0) {
                return;
            }

            const attachmentPromises = filesToAttach.map(async (file, index) => {
                const attachmentData = {
                    propertyId: createdPropertyId,
                    fileAttachment: {
                        propertyId: createdPropertyId,
                        fileId: file.fileId,
                        displayName: file.fileName,
                        fileCategory: file.fileType ? (file.fileType.startsWith('image/') ? 'property_image' :
                            file.fileType.startsWith('video/') ? 'property_video' :
                                file.fileType === 'application/pdf' ? 'property_document' :
                                    file.fileType.startsWith('audio/') ? 'property_audio' : 'property_media') : 'property_media',
                        displayOrder: index,
                        metadata: {
                            uploadContext: 'property_form',
                            fileType: file.fileType
                        }
                    }
                };

                const result = await addPropertyFileAttachmentMutation.mutateAsync(attachmentData);
                return result;
            });

            const results = await Promise.all(attachmentPromises);
            return results;
        };
    };

    const handleSubmit = async () => {
        if (formRef.current) {
            const currentValues = formRef.current.getFlatValues();
            // Use only current values from the form, no need for formValues state
            const allValues = currentValues;

            // Validate all required fields before submitting
            if (validateRequiredFields(allValues)) {
                try {
                    // Upload any remaining staged files
                    const uploadedFiles = await uploadStagedFiles();

                    // Flatten the values to ensure compatibility with parent components
                    const flattenedValues = flattenValues(allValues);

                    // Remove the old uploadImage field since we're using the new system
                    delete flattenedValues.uploadImage;

                    // If we have a propertyId, this is an UPDATE operation - ONLY UPDATE!
                    if (propertyId) {
                        // Use existing property as base and only update changed fields
                        if (!existingProperty) {
                            throw new Error('Cannot update property: existing property data not loaded');
                        }

                        // Build update payload - always use dynamic schema approach now
                        const adminFieldIds = [
                            'status',
                            'propertyNumber',
                            'retentionPeriod',
                            'currentCustodian',
                            'currentLocation',
                            'isEvidence',
                            'notes',
                            'caseNumber',
                            'disposalType',
                        ];

                        // Build details object with non-admin fields
                        const detailsUpdate: Record<string, any> = {};
                        Object.keys(flattenedValues).forEach((key) => {
                            if (!adminFieldIds.includes(key)) {
                                detailsUpdate[key] = (flattenedValues as any)[key];
                            }
                        });

                        const updateData = {
                            ...existingProperty, // Preserve all existing fields (orgId, version, etc.)
                            details: detailsUpdate, // Update property details
                            // Update admin fields if provided
                            propertyStatus: flattenedValues.status
                                ? stringToPropertyStatus(`PROPERTY_STATUS_${String(flattenedValues.status).toUpperCase()}`)
                                : existingProperty.propertyStatus,
                            propertyNumber: flattenedValues.propertyNumber || existingProperty.propertyNumber || '',
                            currentCustodian: flattenedValues.currentCustodian || existingProperty.currentCustodian || '',
                            currentLocation: flattenedValues.currentLocation || existingProperty.currentLocation || '',
                        } as any;

                        const updatedProperty = await updatePropertyMutation.mutateAsync(updateData);

                        // Handle file attachments after update
                        if (uploadedFiles.length > 0) {
                            const attachFilesToProperty = createAttachFilesToPropertyFunction(uploadedFiles);
                            await attachFilesToProperty(propertyId);
                        }

                        // Notify parent component of the updated property
                        if (onPropertyUpdated && updatedProperty) {
                            onPropertyUpdated(updatedProperty);
                        }

                        // Clear draft on successful update
                        _saveDraftToStorage(allValues);

                        // Don't call onCancel() here - let onPropertyUpdated handle closing the panel

                    } else {
                        // This is CREATE MODE - create new property via parent handler

                        // Use entity schema if available, otherwise use default
                        if (selectedSchema && selectedSchema.id && selectedSchema.id !== 'default-property-schema') {
                            flattenedValues._schemaId = selectedSchema.id;
                            flattenedValues._schemaVersion = selectedSchema.version;
                            flattenedValues._schemaName = selectedSchema.name;
                        }

                        // Convert form values to property details format
                        flattenedValues._propertyDetails = formValuesToPropertyDetails(allValues, selectedSchema!);

                        flattenedValues._attachFilesToProperty = createAttachFilesToPropertyFunction(uploadedFiles);

                        // Clear draft on successful submission
                        _saveDraftToStorage(allValues);

                        onSubmit(flattenedValues);
                    }
                } catch (error) {
                    setValidationError(error instanceof Error ? error.message : 'Error processing files');
                    // Don't clear form data on error - keep user's input
                }
            } else {
                // On validation failure, preserve form data - don't clear anything
            }
        }
    };

    const handleSaveAndAddAnother = async () => {
        // Save and Add Another should only work in CREATE mode, not EDIT mode
        if (propertyId) {
            setValidationError('Save and Add Another is only available when creating new properties');
            return;
        }

        if (formRef.current) {
            const currentValues = formRef.current.getFlatValues();
            // Use only current values from the form, no need for formValues state
            const allValues = currentValues;

            // Validate all required fields before submitting
            if (validateRequiredFields(allValues)) {
                try {
                    // Upload any remaining staged files
                    const uploadedFiles = await uploadStagedFiles();

                    // Flatten the values to ensure compatibility with parent components
                    const flattenedValues = flattenValues(allValues);

                    // Remove the old uploadImage field since we're using the new system
                    delete flattenedValues.uploadImage;

                    // Pass the file attachment handler to the parent with uploaded files
                    flattenedValues._attachFilesToProperty = createAttachFilesToPropertyFunction(uploadedFiles);

                    // Clear draft on successful submission
                    _saveDraftToStorage(allValues);

                    if (onSaveAndAddAnother) {
                        onSaveAndAddAnother(flattenedValues);
                    }
                } catch (error) {
                    setValidationError(error instanceof Error ? error.message : 'Error processing files');
                    // Don't clear form data on error - keep user's input
                }
            } else {
                // On validation failure, preserve form data - don't clear anything
            }
        }
    };

    // Helper function to flatten sectioned values
    const flattenValues = (values: any): any => {
        const flattened: any = {};

        Object.keys(values).forEach(key => {
            if (typeof values[key] === 'object' && values[key] !== null) {
                // If it's a section (like page1, page2), flatten its contents
                Object.keys(values[key]).forEach(fieldKey => {
                    flattened[fieldKey] = values[key][fieldKey];
                });
            } else {
                // If it's already a flat value, keep it as is
                flattened[key] = values[key];
            }
        });

        return flattened;
    };

    // Helper function to flatten form values from FormRenderer
    const _flattenFormValues = (values: any): any => {
        const flattened: any = {};

        Object.keys(values).forEach(key => {
            const value = values[key];
            if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
                // If it's a nested object (like propertyInformationSection), flatten it
                Object.keys(value).forEach(fieldKey => {
                    flattened[fieldKey] = value[fieldKey];
                });
            } else {
                // If it's a flat value, keep it as is
                flattened[key] = value;
            }
        });

        return flattened;
    };

    const handleCancel = () => {
        // Reset any local state before canceling
        setValidationError(null);
        setStagedFiles([]);
        setUploadedFiles([]);
        onCancel();
    };

    // File upload event handlers
    const handleDragEnter = (event: React.DragEvent<HTMLDivElement>) => {
        event.preventDefault();
        event.stopPropagation();
        setIsDragging(true);
    };

    const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
        event.preventDefault();
        event.stopPropagation();
        setIsDragging(true);
    };

    const handleDragLeave = (event: React.DragEvent<HTMLDivElement>) => {
        event.preventDefault();
        event.stopPropagation();
        setIsDragging(false);
    };

    const handleDrop = async (event: React.DragEvent<HTMLDivElement>) => {
        event.preventDefault();
        event.stopPropagation();
        setIsDragging(false);

        if (event.dataTransfer.files) {
            const files = Array.from(event.dataTransfer.files);
            await handleFiles(files);
        }
    };

    const handleClickUpload = () => {
        fileInputRef.current?.click();
    };

    const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
        if (event.target.files) {
            const files = Array.from(event.target.files);
            await handleFiles(files);

            // Clear the input
            if (fileInputRef.current) {
                fileInputRef.current.value = '';
            }
        }
    };

    const handleFiles = async (files: File[]) => {
        for (const file of files) {
            // Accept the same file types as MediaPanel: images, videos, PDFs, and audio files
            const isValidFileType = file.type.startsWith('image/') ||
                file.type.startsWith('video/') ||
                file.type === 'application/pdf' ||
                file.type.startsWith('audio/');

            if (!isValidFileType) {
                continue;
            }

            const tempId = Date.now().toString() + Math.random().toString(36).substring(2, 9);
            const baseFileRef = create(FileReferenceSchema, {
                id: tempId,
                fileId: "", // Empty until uploaded
                caption: "",
                displayName: file.name,
                displayOrder: 0,
                fileCategory: getFileCategory(file),
                metadata: {
                    originalFilename: file.name,
                    fileSize: file.size,
                    fileType: file.type,
                },
            });

            const stagedFile: StagedFileReference = {
                ...baseFileRef,
                file,
                preview: createPreview(file),
                isUploading: false,
            };

            setStagedFiles(prev => [...prev, stagedFile]);
        }
    };

    // File metadata handlers
    const handleDisplayNameChange = (id: string, displayName: string) => {
        setStagedFiles(prev =>
            prev.map(file =>
                file.id === id ? { ...file, displayName } : file
            )
        );
    };

    const handleCaptionChange = (id: string, caption: string) => {
        setStagedFiles(prev =>
            prev.map(file =>
                file.id === id ? { ...file, caption } : file
            )
        );
    };

    const handleCategoryChange = (id: string, fileCategory: string) => {
        setStagedFiles(prev =>
            prev.map(file =>
                file.id === id ? { ...file, fileCategory } : file
            )
        );
    };

    const handleRemoveFile = (id: string) => {
        setStagedFiles(prev => {
            const file = prev.find(f => f.id === id);
            if (file?.preview) {
                URL.revokeObjectURL(file.preview);
            }
            return prev.filter(f => f.id !== id);
        });
    };

    // Show loading state while fetching existing property data
    if (propertyId && isLoadingProperty) {
        return (
            <Box sx={{
                height: '100%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                flexDirection: 'column',
                gap: 2
            }}>
                <Typography style="h3">
                    Loading Property Data...
                </Typography>
                <Typography style="body2" color={colors.grey[600]}>
                    Using schema: {selectedSchema.name} v{selectedSchema.version}
                </Typography>
            </Box>
        );
    }

    return (
        <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column', minHeight: 0 }}>
            {/* Simple form header - no need to show schema info */}

            {/* Validation Error Display */}
            {validationError && (
                <Box sx={{
                    p: 2,
                    mb: 2,
                    backgroundColor: colors.rose[50],
                    border: `1px solid ${colors.rose[200]}`,
                    borderRadius: 1
                }}>
                    <Typography style="body2" color={colors.rose[600]}>
                        {validationError}
                    </Typography>
                </Box>
            )}

            {/* Form content */}
            <Box sx={{ flex: 1, overflow: 'auto', pb: '128px' }}>
                {currentConfig && (
                    <FormRenderer
                        ref={formRef}
                        config={currentConfig}
                        initialValues={memoizedInitialValues}
                        readOnly={readOnly}
                        customTitle={customTitle}
                        showSubmitButton={false}
                        onValueChange={handleValueChange}
                    />
                )}

                {/* Add the new file upload UI components */}
                <Box sx={{ mt: 4, px: 3 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <Typography style="caps1" color={colors.grey[500]}>
                            Images
                        </Typography>
                    </Box>
                    <Box>
                        <FileUploadDropZone
                            isDragging={isDragging}
                            onDragEnter={handleDragEnter}
                            onDragOver={handleDragOver}
                            onDragLeave={handleDragLeave}
                            onDrop={handleDrop}
                            onClick={handleClickUpload}
                            fileInputRef={fileInputRef}
                            onFileSelect={handleFileSelect}
                        />

                        {stagedFiles.length > 0 && (
                            <Box sx={{ mt: 3 }}>
                                <StagedFilesList
                                    stagedFiles={stagedFiles}
                                    readOnly={readOnly}
                                    onDisplayNameChange={handleDisplayNameChange}
                                    onCaptionChange={handleCaptionChange}
                                    onCategoryChange={handleCategoryChange}
                                    onRemoveFile={handleRemoveFile}
                                    renderFilePreview={renderFilePreview}
                                    formatBytes={formatBytes}
                                    formatDuration={formatDuration}
                                />
                            </Box>
                        )}

                        {(stagedFiles.some(f => f.isUploading) || uploadingSummary.isComplete) && (
                            <Box sx={{ mt: 3 }}>
                                <UploadProgressDashboard
                                    overallProgress={overallProgress}
                                    uploadingSummary={uploadingSummary}
                                    formatBytes={formatBytes}
                                    formatDuration={formatDuration}
                                />
                            </Box>
                        )}
                    </Box>
                </Box>
            </Box>

            {/* Navigation buttons */}
            <Box sx={{
                position: "fixed",
                bottom: 0,
                left: 0,
                right: 0,
                padding: "16px 24px",
                backgroundColor: "white",
                borderTop: "1px solid #E0E0E0",
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                gap: 2,
                zIndex: 10,
            }}>
                <Button
                    label="Cancel"
                    color="grey"
                    prominence={false}
                    onClick={handleCancel}
                />

                <Box sx={{ display: "flex", gap: 2, marginLeft: "auto" }}>
                    {onSaveAndAddAnother && (
                        <Button
                            label="Save + Add Another"
                            color="blue"
                            prominence={false}
                            onClick={handleSaveAndAddAnother}
                        />
                    )}
                    <Button
                        label="Save"
                        color="blue"
                        prominence={true}
                        onClick={handleSubmit}
                    />
                </Box>
            </Box>
        </Box>
    );
}; 