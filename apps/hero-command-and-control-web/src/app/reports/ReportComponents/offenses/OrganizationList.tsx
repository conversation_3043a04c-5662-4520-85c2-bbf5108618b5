import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  Icon<PERSON>utton,
  <PERSON>lap<PERSON>,
  Menu,
  MenuItem,
  Dialog,
  DialogContent,
  DialogActions,
} from "@mui/material";
import { Typography } from "@/design-system/components/Typography";
import { But<PERSON> } from "@/design-system/components/Button";
import { Checkbox } from "@/design-system/components/Checkbox";
import { colors } from "@/design-system/tokens";
import AddIcon from "@mui/icons-material/Add";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import OpenInNewIcon from "@mui/icons-material/OpenInNew";
import DeleteIcon from "@mui/icons-material/Delete";
import CircularProgress from "@mui/material/CircularProgress";

export interface OrganizationData {
  id: string;
  name: string;
  type: string;
  status?: string;
  phone?: string;
  website?: string;
  email?: string;
  address?: Array<{
    streetAddress1?: string;
    streetAddress2?: string;
    city?: string;
    state?: string;
    zipCode?: string;
  }>;
  primaryContactName?: string;
  primaryContactTitle?: string;
  primaryContactPhone?: string;
  campusOrganization?: string;
}

interface OrganizationListProps {
  organizations: OrganizationData[];
  title: string;
  offenseId: string;
  readOnly?: boolean;
  availableOrganizationsForQuickAdd: OrganizationData[];
  onAddOrganization: () => void;
  onRemoveOrganizationFromOffense?: (
    organizationId: string,
    offenseId: string
  ) => void;
  onRemoveOrganizationFromReport?: (
    organizationId: string,
    entityType: "organization"
  ) => void;
  onQuickAddOrganization: (organizationId: string) => void;
  onEntityEdit?: (entityId: string, entityType: "organization") => void;
}

const OrganizationList: React.FC<OrganizationListProps> = ({
  organizations,
  title,
  offenseId,
  readOnly = false,
  availableOrganizationsForQuickAdd,
  onAddOrganization,
  onRemoveOrganizationFromOffense,
  onRemoveOrganizationFromReport,
  onQuickAddOrganization,
  onEntityEdit,
}) => {
  const [menuAnchorEl, setMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedOrganizationForMenu, setSelectedOrganizationForMenu] =
    useState<OrganizationData | null>(null);
  const [removeModalOpen, setRemoveModalOpen] = useState(false);
  const [removeFromReport, setRemoveFromReport] = useState(false);
  const [quickAddExpanded, setQuickAddExpanded] = useState(true);
  const [loadingIds, setLoadingIds] = useState<Set<string>>(new Set());

  const handleMenuClick = (
    event: React.MouseEvent<HTMLElement>,
    organization: OrganizationData
  ) => {
    event.stopPropagation();
    setMenuAnchorEl(event.currentTarget);
    setSelectedOrganizationForMenu(organization);
  };

  const handleMenuClose = () => {
    setMenuAnchorEl(null);
    setSelectedOrganizationForMenu(null);
  };

  const handleOpenRecord = () => {
    if (selectedOrganizationForMenu) {
      window.open(
        `/entity?entityId=${selectedOrganizationForMenu.id}`,
        "_blank"
      );
    }
    setMenuAnchorEl(null);
    setSelectedOrganizationForMenu(null);
  };

  const handleRemoveClick = () => {
    setRemoveModalOpen(true);
    setMenuAnchorEl(null);
  };

  const handleRemoveCancel = () => {
    setRemoveModalOpen(false);
    setRemoveFromReport(false);
    setSelectedOrganizationForMenu(null);
  };

  const handleRemoveConfirm = () => {
    if (selectedOrganizationForMenu) {
      if (onRemoveOrganizationFromOffense) {
        onRemoveOrganizationFromOffense(
          selectedOrganizationForMenu.id,
          offenseId
        );
      }

      if (removeFromReport && onRemoveOrganizationFromReport) {
        onRemoveOrganizationFromReport(
          selectedOrganizationForMenu.id,
          "organization"
        );
      }
    }

    setRemoveModalOpen(false);
    setRemoveFromReport(false);
    setSelectedOrganizationForMenu(null);
  };

  const handleQuickAddClick = (organizationId: string) => {
    setLoadingIds((prev) => new Set(prev).add(organizationId));
    onQuickAddOrganization(organizationId);
  };

  useEffect(() => {
    setLoadingIds((prev) => {
      const newSet = new Set(
        [...prev].filter((id) =>
          availableOrganizationsForQuickAdd.some((v) => v.id === id)
        )
      );
      return newSet;
    });
  }, [availableOrganizationsForQuickAdd]);

  return (
    <>
      <Box sx={{ my: 4 }}>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mb: 3,
          }}
        >
          <Typography style="caps1" color={colors.grey[900]}>
            {title}
          </Typography>
        </Box>

        {organizations.length > 0 && (
          <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
            {organizations.map((organization) => (
              <Box
                key={organization.id}
                sx={{
                  padding: 2,
                  border: `1px solid ${colors.grey[200]}`,
                  borderRadius: "8px",
                  backgroundColor: colors.grey[50],
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  cursor: readOnly ? "default" : "pointer",
                  "&:hover": {
                    backgroundColor: colors.grey[100],
                  },
                  "&:active": {
                    backgroundColor: colors.grey[200],
                  },
                }}
                onClick={() => {
                  if (readOnly) return;
                  onEntityEdit && onEntityEdit(organization.id, "organization");
                }}
              >
                <Box sx={{ flex: 1 }}>
                  <Box sx={{ mb: 1 }}>
                    <Typography style="body3" color={colors.grey[900]}>
                      {organization.name || organization.type || "Organization"}
                    </Typography>
                  </Box>
                  <Box sx={{ display: "flex", gap: 1, flexWrap: "wrap" }}>
                    <Typography style="tag2" color={colors.grey[500]}>
                      Name:
                    </Typography>
                    <Typography style="tag2" color={colors.grey[900]}>
                      {organization.name || "---"}
                    </Typography>
                    <Typography style="tag2" color={colors.grey[500]}>
                      , Type:
                    </Typography>
                    <Typography style="tag2" color={colors.grey[900]}>
                      {organization.type || "---"}
                    </Typography>
                  </Box>
                </Box>
                {!readOnly && (
                  <IconButton
                    size="small"
                    onClick={(e) => handleMenuClick(e, organization)}
                    sx={{
                      color: colors.grey[600],
                      "&:hover": {
                        bgcolor: colors.grey[200],
                        color: colors.grey[800],
                      },
                    }}
                  >
                    <MoreVertIcon fontSize="small" />
                  </IconButton>
                )}
              </Box>
            ))}
          </Box>
        )}

        {/* Add button below the cards */}
        {!readOnly && (
          <Box sx={{ mt: organizations.length > 0 ? 3 : 0 }}>
            <Button
              label={`Add ${title}`}
              leftIcon={<AddIcon />}
              style="ghost"
              color="blue"
              size="small"
              onClick={onAddOrganization}
            />
          </Box>
        )}

        {/* Quick Add Section */}
        {!readOnly && availableOrganizationsForQuickAdd.length > 0 && (
          <Box sx={{ mt: 2 }}>
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                cursor: "pointer",
                py: 1,
              }}
              onClick={() => setQuickAddExpanded(!quickAddExpanded)}
            >
              <Typography style="caps2" color={colors.grey[500]}>
                QUICK ADD
              </Typography>
              <IconButton size="small">
                {quickAddExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
              </IconButton>
            </Box>

            <Collapse in={quickAddExpanded}>
              <Box
                sx={{
                  display: "grid",
                  gridTemplateColumns: "repeat(3, 1fr)",
                  gap: 1.5,
                  pt: 1,
                }}
              >
                {availableOrganizationsForQuickAdd.map((organization) => (
                  <Box
                    key={organization.id}
                    onClick={() => handleQuickAddClick(organization.id)}
                    sx={{
                      px: 1.5,
                      py: 0,
                      border: `1px solid ${colors.grey[200]}`,
                      borderRadius: "8px",
                      backgroundColor: "white",
                      display: "flex",
                      alignItems: "center",
                      cursor: "pointer",
                      height: "68px",
                      boxShadow: "0px 0px 16px 0px rgba(0, 0, 0, 0.04)",
                      "&:hover": {
                        backgroundColor: colors.grey[50],
                      },
                    }}
                  >
                    <Box sx={{ mr: 2, display: "flex", alignItems: "center" }}>
                      {loadingIds.has(organization.id) ? (
                        <CircularProgress
                          size={20}
                          sx={{ color: colors.grey[900] }}
                        />
                      ) : (
                        <AddIcon
                          sx={{ color: colors.grey[900], fontSize: 20 }}
                        />
                      )}
                    </Box>
                    <Box sx={{ flex: 1, minWidth: 0 }}>
                      <Box sx={{ mb: 0.5 }}>
                        <Typography style="body3" color={colors.grey[900]}>
                          {organization.name || organization.type || "---"}
                        </Typography>
                      </Box>
                      <Box
                        sx={{
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          display: "-webkit-box",
                          WebkitLineClamp: 2,
                          WebkitBoxOrient: "vertical",
                        }}
                      >
                        <Typography style="tag2" color={colors.grey[500]}>
                          {organization.name || "---"},{" "}
                          {organization.type || "---"}
                        </Typography>
                      </Box>
                    </Box>
                  </Box>
                ))}
              </Box>
            </Collapse>
          </Box>
        )}
      </Box>

      {/* Menu for organization actions */}
      <Menu
        anchorEl={menuAnchorEl}
        open={Boolean(menuAnchorEl)}
        onClose={handleMenuClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
        sx={{
          mt: 0.25,
          "& .MuiPaper-root": {
            borderRadius: "8px",
            minWidth: "200px",
          },
          "& .MuiMenuItem-root": {
            py: 1.5,
            display: "flex",
            alignItems: "center",
            gap: 1.5,
          },
        }}
      >
        <MenuItem onClick={handleOpenRecord}>
          <OpenInNewIcon fontSize="small" sx={{ color: colors.grey[500] }} />
          <Typography style="body2" color={colors.grey[500]}>
            Open record in new tab
          </Typography>
        </MenuItem>
        <MenuItem onClick={handleRemoveClick}>
          <DeleteIcon fontSize="small" sx={{ color: colors.grey[500] }} />
          <Typography style="body2" color={colors.grey[500]}>
            Remove from offense
          </Typography>
        </MenuItem>
      </Menu>

      {/* Remove confirmation modal */}
      <Dialog
        open={removeModalOpen}
        onClose={handleRemoveCancel}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: "12px",
            p: 1,
          },
        }}
      >
        {/*

        old version
        
        <Typography style="h2" color={colors.grey[900]}>
              Delete {selectedOrganizationForMenu ? `${selectedOrganizationForMenu.year} ${selectedOrganizationForMenu.name} ${selectedOrganizationForMenu.type}` : "Organization"}
            </Typography> */}

        <DialogContent sx={{ p: 3 }}>
          <Box sx={{ mb: 2 }}>
            <Typography style="h2" color={colors.grey[900]}>
              Delete {selectedOrganizationForMenu?.name || "Organization"}
            </Typography>
          </Box>
          <Box sx={{ mb: 3 }}>
            <Typography style="body3" color={colors.grey[700]}>
              {selectedOrganizationForMenu?.name ||
                selectedOrganizationForMenu?.type ||
                "Organization"}{" "}
              will be deleted from the offense
            </Typography>
          </Box>

          <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
            <Checkbox
              checked={removeFromReport}
              onChange={(e) => setRemoveFromReport(e.target.checked)}
              label="Also delete from the report"
              size="small"
            />
          </Box>
        </DialogContent>

        <DialogActions sx={{ p: 3, pt: 0 }}>
          <Button
            label="Cancel"
            color="grey"
            prominence={false}
            onClick={handleRemoveCancel}
          />
          <Button
            label="Delete"
            color="blue"
            prominence={true}
            onClick={handleRemoveConfirm}
          />
        </DialogActions>
      </Dialog>
    </>
  );
};

export default OrganizationList;
