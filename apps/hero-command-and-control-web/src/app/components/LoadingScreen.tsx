'use client';
import { Box, CircularProgress, Typography } from '@mui/material';
import { useEffect, useState } from 'react';

interface LoadingScreenProps {
  message?: string;
  timeoutMs?: number;
  onTimeout?: () => void;
}

export function LoadingScreen({
  message = 'Loading...',
  timeoutMs = 30000,
  onTimeout
}: LoadingScreenProps) {
  const [showTimeoutMessage, setShowTimeoutMessage] = useState(false);
  const [mounted, setMounted] = useState(false);

  // Ensure component is mounted before rendering MUI components to avoid hydration issues
  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (timeoutMs <= 0) return;

    const timer = setTimeout(() => {
      setShowTimeoutMessage(true);
      onTimeout?.();
    }, timeoutMs);

    return () => clearTimeout(timer);
  }, [timeoutMs, onTimeout]);

  // Return a simple loading state during SSR to avoid hydration mismatch
  if (!mounted) {
    return (
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '100vh',
        gap: '16px'
      }}>
        <div style={{ textAlign: 'center' }}>Loading...</div>
      </div>
    );
  }

  return (
    <Box
      display="flex"
      flexDirection="column"
      justifyContent="center"
      alignItems="center"
      minHeight="100vh"
      bgcolor="background.default"
      gap={2}
    >
      <CircularProgress />
      <Typography variant="body2" color="text.secondary">
        {showTimeoutMessage ? 'This is taking longer than expected...' : message}
      </Typography>
      {showTimeoutMessage && (
        <Typography variant="caption" color="text.secondary">
          Please check your connection and try refreshing the page.
        </Typography>
      )}
    </Box>
  );
}