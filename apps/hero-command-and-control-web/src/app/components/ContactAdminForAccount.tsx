'use client';
import { Box, Typography, Paper, Button, Container } from '@mui/material';
import { AccountCircle, AdminPanelSettings } from '@mui/icons-material';
import { useAuth } from '../contexts/Auth/AuthContext';
import { useRouter } from 'next/navigation';

export function ContactAdminForAccount() {
  const { logout } = useAuth();
  const router = useRouter();
  
  return (
    <Container maxWidth="sm">
      <Box
        display="flex"
        flexDirection="column"
        justifyContent="center"
        alignItems="center"
        minHeight="100vh"
        gap={3}
      >
        <Paper elevation={3} sx={{ p: 4, width: '100%', textAlign: 'center' }}>
          <AccountCircle sx={{ fontSize: 80, color: 'warning.main', mb: 2 }} />
          
          <Typography variant="h4" gutterBottom>
            Account Configuration Required
          </Typography>
          
          <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
            Your account has been successfully created but requires configuration before you can access the system.
          </Typography>
          
          <Box sx={{ bgcolor: 'grey.100', p: 2, borderRadius: 1, mb: 3 }}>
            <AdminPanelSettings sx={{ fontSize: 40, color: 'action.active', mb: 1 }} />
            
            <Typography variant="h6" gutterBottom>
              Action Required
            </Typography>
            
            <Typography variant="body2" color="text.secondary">
              Please contact your organization&apos;s system administrator to complete your account setup.
            </Typography>
          </Box>
          
          <Box sx={{ bgcolor: 'info.lighter', p: 2, borderRadius: 1, mb: 3 }}>
            <Typography variant="body2" color="text.primary">
              <strong>Next Steps:</strong>
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              Your administrator needs to complete your account configuration and assign appropriate roles.
            </Typography>
          </Box>
          
          <Box display="flex" gap={2} justifyContent="center">
            <Button 
              variant="outlined" 
              onClick={() => router.push('/')}
            >
              Check Status
            </Button>
            <Button 
              variant="contained" 
              color="secondary"
              onClick={logout}
            >
              Sign Out
            </Button>
          </Box>
        </Paper>
        
        <Typography variant="caption" color="text.secondary" sx={{ maxWidth: '80%', textAlign: 'center' }}>
          Once your administrator completes the setup, you will be able to access the system. 
          Please allow time for processing based on your organization&apos;s policies.
        </Typography>
      </Box>
    </Container>
  );
}