'use client';
import { useUserAsset } from '../contexts/User/UserAssetContext';
import { ContactAdminForAccount } from './ContactAdminForAccount';
import { LoadingScreen } from './LoadingScreen';

interface AssetGuardProps {
  children: React.ReactNode;
}

export function AssetGuard({ children }: AssetGuardProps) {
  const { asset, isLoading, error } = useUserAsset();
  
  // Show loading while checking for asset
  if (isLoading) {
    return (
      <LoadingScreen 
        message="Loading user profile..." 
        timeoutMs={30000}
      />
    );
  }
  
  // Show ContactAdminForAccount if there's no asset (regardless of the reason)
  if (!asset) {
    if (error) {
      console.error('Asset loading error:', error);
    }
    return <ContactAdminForAccount />;
  }
  
  return <>{children}</>;
}