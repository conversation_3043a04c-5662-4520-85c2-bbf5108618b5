import { useRouter } from 'next/navigation';
import { AssetStatus } from 'proto/hero/assets/v2/assets_pb';
import React, { useEffect, useRef, useState } from 'react';
import { createPortal } from 'react-dom';
import { useUpdateAsset } from '../../apis/services/workflow/assets/hooks';
import BusyPopup from './BusyPopup';

export interface UnitStatusRow {
  id: string;
  unitNumber: string;
  name: string;
  department: string;
  beat: string;
  status: AssetStatus;
  logonDate?: string;
  logonTime?: string;
  logoffDate?: string;
  logoffTime?: string;
  incidentId?: string;
  assetType?: string;
}

interface UnitStatusProps {
  data: UnitStatusRow[];
  isActive: boolean;
}

const statusColors: Record<AssetStatus, string> = {
  [AssetStatus.AVAILABLE]: 'bg-green-100 text-green-800',
  [AssetStatus.MAINTENANCE]: 'bg-orange-100 text-orange-800',
  [AssetStatus.RESERVED]: 'bg-orange-100 text-orange-800',
  [AssetStatus.BUSY]: 'bg-red-100 text-red-800',
  [AssetStatus.UNSPECIFIED]: 'bg-gray-100 text-gray-800',
  [AssetStatus.OFFLINE]: 'bg-red-100 text-red-800',
  [AssetStatus.DEACTIVATED]: 'bg-gray-100 text-gray-800',
  [AssetStatus.ON_BREAK]: 'bg-red-100 text-red-800',
};

const getDisplayStatus = (status: AssetStatus): string => {
  switch (status) {
    case AssetStatus.AVAILABLE:
      return 'Available';
    case AssetStatus.MAINTENANCE:
      return 'Maintenance';
    case AssetStatus.RESERVED:
      return 'Reserved';
    case AssetStatus.BUSY:
      return 'Busy';
    case AssetStatus.UNSPECIFIED:
      return 'Unspecified';
    case AssetStatus.OFFLINE:
      return 'Offline';
    case AssetStatus.DEACTIVATED:
      return 'Deactivated';
    case AssetStatus.ON_BREAK:
      return 'On Break';
    default:
      return 'Unknown';
  }
};

export const UnitStatus: React.FC<UnitStatusProps> = ({ data, isActive }) => {
  const [openMenuId, setOpenMenuId] = useState<number | null>(null);
  const [menuPosition, setMenuPosition] = useState({ top: 0, right: 0 });
  const [busyPopup, setBusyPopup] = useState<{ open: boolean; assetId: string | null }>({ open: false, assetId: null });
  const menuRef = useRef<HTMLDivElement>(null);
  const updateAssetMutation = useUpdateAsset();
  const router = useRouter();

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setOpenMenuId(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleMenuClick = (idx: number, event: React.MouseEvent) => {
    event.stopPropagation();
    const button = event.currentTarget as HTMLButtonElement;
    const rect = button.getBoundingClientRect();
    setMenuPosition({
      top: rect.bottom + window.scrollY,
      right: window.innerWidth - rect.right - 24
    });
    setOpenMenuId(openMenuId === idx ? null : idx);
  };

  const handleLogAction = (id: string, isActive: boolean, status: AssetStatus, incidentId?: string) => {
    if (isActive && status === AssetStatus.BUSY) {
      setBusyPopup({ open: true, assetId: id });
      setOpenMenuId(null);
      return;
    }
    const newStatus = isActive ? AssetStatus.OFFLINE : AssetStatus.AVAILABLE;
    updateAssetMutation.mutate({
      $typeName: 'hero.assets.v2.UpdateAssetRequest',
      asset: {
        $typeName: 'hero.assets.v2.Asset',
        id,
        status: newStatus,
        resourceType: 'ASSET',
      } as any,
    });
    setOpenMenuId(null);
  };



  return (
    <>
      <div className="overflow-x-auto rounded-lg border border-gray-200 bg-white max-h-full">
        <BusyPopup
          open={busyPopup.open}
          onClose={() => setBusyPopup({ open: false, assetId: null })}
          assetId={busyPopup.assetId || ''}
        />
        <table className="w-full divide-y divide-gray-200">
          <thead className="bg-white sticky top-0 z-10">
            <tr>
              <th scope="col" className="w-[100px] px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase whitespace-nowrap">Unit #</th>
              <th scope="col" className="w-[200px] px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase whitespace-nowrap">Name</th>
              <th scope="col" className="w-[100px] px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase whitespace-nowrap">Type</th>
              <th scope="col" className="w-[100px] px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase whitespace-nowrap">Status</th>
              <th scope="col" className="w-[200px] px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase whitespace-nowrap">
                {isActive ? 'Active Since' : 'Last Active'}
              </th>
              <th scope="col" className="w-[50px] px-6 py-3 sticky right-0 bg-white"></th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-100">
            {data.map((row, idx) => (
              <tr key={idx} className="hover:bg-gray-50">
                <td className="w-[100px] px-6 py-2.5 text-[14px] font-bold text-[#364153] whitespace-nowrap">{row.unitNumber}</td>
                <td className="w-[200px] px-6 py-2.5 text-[14px] text-[#364153] truncate">{row.name}</td>
                <td className="w-[100px] px-6 py-2.5 text-[14px] text-[#364153] whitespace-nowrap">{row.assetType || 'N/A'}</td>
                <td className="w-[100px] px-6 py-2.5 text-[14px] whitespace-nowrap">
                  <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${statusColors[row.status] || 'bg-gray-100 text-gray-800'}`}>
                    {getDisplayStatus(row.status)}
                  </span>
                </td>
                <td className="w-[200px] px-6 py-2.5 text-[14px] text-[#364153] whitespace-nowrap">
                  {isActive
                    ? `${row.logonDate || ''} ${row.logonTime || ''}`.trim()
                    : `${row.logoffDate || ''} ${row.logoffTime || ''}`.trim()
                  }
                </td>
                <td className="w-[50px] px-6 py-2.5 text-[14px] text-right sticky right-0 bg-white">
                  <button
                    onClick={(e) => handleMenuClick(idx, e)}
                    className="p-1 hover:bg-gray-100 rounded-full inline-flex items-center justify-center"
                  >
                    <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                    </svg>
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Render dropdown menu in portal to avoid z-index issues */}
      {openMenuId !== null && typeof window !== 'undefined' && createPortal(
        <div
          ref={menuRef}
          className="fixed z-[9999] mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5"
          style={{
            top: `${menuPosition.top}px`,
            right: `${menuPosition.right}px`
          }}
        >
          <div className="py-1" role="menu" aria-orientation="vertical">
            <button
              onClick={() => handleLogAction(data[openMenuId].id, isActive, data[openMenuId].status, data[openMenuId].incidentId)}
              className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 disabled:opacity-50"
              role="menuitem"
              disabled={updateAssetMutation.status === 'pending'}
            >
              {updateAssetMutation.status === 'pending' ? (isActive ? 'Logging Off...' : 'Logging On...') : (isActive ? 'Log Off' : 'Log On')}
            </button>

          </div>
        </div>,
        document.body
      )}
    </>
  );
};

export default UnitStatus;
