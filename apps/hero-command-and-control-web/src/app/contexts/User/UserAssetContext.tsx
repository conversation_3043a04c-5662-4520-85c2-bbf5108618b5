// context/UserAssetContext.tsx
import { Feature, GetEnabledFeaturesRequest } from 'proto/hero/featureflags/v1/featureflags_pb';
import React, { createContext, useContext, useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { AssociatedAsset, useAssociatedAssetQuery, AssetNotFoundError } from '../../apis/custom/auth/GetAssociatedAsset';
import { stringToFeature } from '../../apis/services/featureflags/enumConverters';
import { useGetEnabledFeatures } from '../../apis/services/featureflags/hooks';
import { useAuth } from '../Auth/AuthContext';

interface UserAssetContextValue {
    asset?: AssociatedAsset;
    featureFlags: {
        has: (feature: Feature) => boolean;
    };
    isLoading: boolean;
    error?: Error;
}

const UserAssetContext = createContext<UserAssetContextValue | undefined>(undefined);

export const UserAssetProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const { isAuthenticated, loading: authLoading, user } = useAuth();
    const router = useRouter();
    const pathname = usePathname();

    // Only fetch associated asset after auth is ready and user is authenticated
    // Pass userId to ensure cache is user-specific
    const { data: asset, isLoading: assetLoading, error: assetError } = useAssociatedAssetQuery({
        enabled: !authLoading && isAuthenticated,
        userId: user?.username || user?.userId // Use username or userId as cache key
    });

    // Redirect to account setup page if no asset found (but not if we're already on that page)
    useEffect(() => {
        if (!authLoading && !assetLoading && isAuthenticated && !asset && pathname !== '/account-setup') {
            // Check if it's an AssetNotFoundError
            if (assetError && assetError instanceof AssetNotFoundError) {
                router.push('/account-setup');
            } else if (assetError) {
                // For other errors, still redirect to account setup
                router.push('/account-setup');
            } else if (!assetError && !asset) {
                // No error but no asset means it's still loading or something went wrong
                // Don't redirect in this case
            }
        }
    }, [authLoading, assetLoading, isAuthenticated, asset, assetError, pathname, router]);

    const { data: featuresData } = useGetEnabledFeatures(
        {
            orgId: asset?.orgId || 0,
            assetId: asset?.id || '',
        } as GetEnabledFeaturesRequest,
        {
            enabled: !!asset?.id && !!asset?.orgId,
            refetchInterval: 30000, // Refetch every 30 seconds
        }
    );

    const enabledFeatures = new Set<Feature>();

    if (featuresData?.enabledFeatures) {
        (featuresData.enabledFeatures as any[]).forEach((featureString) => {
            if (typeof featureString === 'string') {
                const feature = stringToFeature(featureString);
                if (feature !== Feature.UNSPECIFIED) {
                    enabledFeatures.add(feature);
                }
            }
        });
    }

    const featureFlags = {
        has: (feature: Feature) => enabledFeatures.has(feature)
    };

    const isLoading = authLoading || assetLoading;
    const error = assetError ?? undefined;

    return (
        <UserAssetContext.Provider value={{ asset, featureFlags, isLoading, error }}>
            {children}
        </UserAssetContext.Provider>
    );
};

export const useUserAsset = (): UserAssetContextValue => {
    const context = useContext(UserAssetContext);
    if (context === undefined) {
        throw new Error('useUserAsset must be used within a UserAssetProvider');
    }
    return context;
};
