# Authentication & User Asset System

**Multi-layer authentication system:** CloudFront Edge Lambda → Amplify Auth SDK → Microservices

A comprehensive authentication system that handles both edge-based and client-side authentication, with built-in user asset validation and cross-user data protection. This documentation covers the web application authentication architecture - mobile apps use a different, simpler architecture without Edge Lambda involvement.

## Quick Start

```typescript
// Check auth state
const { isAuthenticated, user, logout } = useAuth();

// Access user asset and features
const { asset, featureFlags } = useUserAsset();

// Make authenticated API calls
const api = createAuthenticatedAxiosInstance('https://api.gethero.com');
```

## Table of Contents

- [Overview](#overview)
- [Architecture](#architecture)
  - [Layer Responsibilities & Execution Order](#layer-responsibilities--execution-order)
- [Security Features](#security-features)
  - [Cross-User Data Protection](#️-cross-user-data-protection)
  - [Token Storage Strategy](#-token-storage-strategy)
  - [Security Headers](#️-security-headers)
  - [Token Refresh Strategy](#-token-refresh-strategy)
- [Authentication Flows](#authentication-flows)
  - [Unified Authentication Flow](#unified-authentication-flow)
  - [Production Logout Flow](#production-logout-flow)
  - [Development Flow](#development-flow)
- [Configuration](#configuration)
  - [Central Configuration](#central-configuration-authconfigts)
  - [Environment Variables](#environment-variables)
- [Key Components](#key-components)
- [Component Deep Dive](#component-deep-dive)
- [Authentication Kick-out Mechanisms](#authentication-kick-out-mechanisms)
- [Error Handling](#error-handling)
  - [Specific Error Messages](#specific-error-messages)
  - [Debug Logging](#debug-logging)
- [Usage Examples](#usage-examples)
- [Common Pitfalls & Solutions](#common-pitfalls--solutions)
- [Development vs Production](#development-vs-production)
- [Mobile App Differences](#mobile-app-differences)
- [Debugging Authentication Issues](#debugging-authentication-issues)
  - [Check Token State](#check-token-state)
  - [Monitor Token Refresh](#monitor-token-refresh)
  - [Common Error Patterns](#common-error-patterns)
- [Testing Recommendations](#testing-recommendations)
- [Performance Metrics](#performance-metrics)
- [File Structure](#file-structure)
- [Architecture Benefits](#architecture-benefits)
- [Future Enhancements](#future-enhancements)
- [Support & Maintenance](#support--maintenance)

## Overview

A unified authentication system that seamlessly handles both edge-based (Lambda@Edge) and direct authentication flows, combined with a user asset validation system. The architecture synchronizes tokens between cookies and localStorage, enabling AWS Amplify to manage token refresh without page reloads, while ensuring users have properly configured accounts (with associated assets) before accessing the system.

**Critical Architecture Principle:** Edge Lambda runs BEFORE Next.js in production. This execution order is non-negotiable - Edge Lambda intercepts requests at CloudFront before they reach the Next.js application.

## Architecture

```
┌──────────────────────────────────────────────────────────────────────────┐
│                          User Request Flow                               │
├──────────────────────────────────────────────────────────────────────────┤
│                                                                           │
│  Browser ──► CloudFront ──► Lambda@Edge ──► Next.js App ──► APIs        │
│                   │              │              │                        │
│                   └── CDN ───────┴── Auth ──────┴── Token Validation    │
│                                                                           │
└──────────────────────────────────────────────────────────────────────────┘
```

### Critical Lambda@Edge Requirements

**The Lambda@Edge MUST handle three distinct request types correctly:**

1. **Static Assets** (`/_next/*`, `*.js`, `*.css`, etc.)
   - Must bypass authentication completely
   - If authenticated, returns HTML login page causing "Unexpected token '<'" errors
   - **Critical**: Must include ALL `/_next/` paths, not just `/_next/static/`
   
2. **OAuth/SAML Callbacks** (`/?code=...&state=...`)
   - Must be processed by cognito-at-edge
   - Must NOT have URLs modified by nextJSDynamicRouting
   - **Critical**: In Lambda@Edge, `request.uri` and `request.querystring` are SEPARATE fields
   - `request.uri = "/"`, `request.querystring = "code=XXX&state=YYY"`
   - Checking `uri.includes('?')` will ALWAYS be false!

3. **Regular Page Requests** (`/cad`, `/admin`, etc.)
   - Must go through authentication
   - Can have `.html` suffix added for Next.js routing
   - **No public paths needed** - All pages should go through auth

### Lambda@Edge Object Types

**Critical distinction after `authenticator.handle(event)`:**

```javascript
// Response object (has 'status' property)
{
  status: '302',
  statusDescription: 'Found',
  headers: { location: [...] }
}

// Request object (no 'status' property - continue to origin)
{
  uri: '/cad',
  querystring: '',
  headers: { ... }
}
```

**Key Implementation Details:**

1. **OAuth Configuration**: Must include `parseAuthPath: '/'` in Authenticator config
2. **Querystring Handling**: Check `request.querystring` separately from `request.uri`
3. **No Public Paths**: Don't bypass auth for `/login` or `/logout`
   - `/logout` MUST go through authenticator for proper cookie clearing
   - `/login` going through auth is harmless and useful (redirects if already authenticated)
4. **Static Asset Detection**: Use `uri.startsWith('/_next/')` not just `/_next/static/`

### Layer Responsibilities & Execution Order

```
┌─────────────────────┐    ┌─────────────────────┐    ┌─────────────────────┐
│ CloudFront Edge     │    │ Next.js App         │    │ Microservices       │
│ (FIRST)             │    │ (SECOND)            │    │ (THIRD)             │
├─ Intercepts /logout │    ├─ Reads Edge cookies │    ├─ Validates Bearer   │
├─ Sets auth cookies  │    ├─ Client auth state  │    │   tokens            │
├─ Fixes .html routing│    ├─ Token refresh      │    └─ Business logic     │
└─ OAuth redirects    │    └─ Dev-only direct    │                          │
                      │        auth               │                          │
└─────────────────────┘    └─────────────────────┘    └─────────────────────┘
```

| Layer | Responsibility | Key Features |
|-------|---------------|--------------|
| **CloudFront + Lambda@Edge** | Edge authentication gateway (RUNS FIRST) | • Request interception<br>• Token validation<br>• Cookie management<br>• OAuth flow handling<br>• `/logout` URL interception |
| **AWS Cognito** | Identity provider | • User pools<br>• JWT token issuance<br>• OAuth/SAML integration<br>• Hosted UI (`auth.gethero.com`) |
| **Next.js Application** | Client application (RUNS SECOND) | • Auth state management<br>• Token refresh orchestration<br>• UI/UX logic<br>• API integration |
| **Microservices** | Business logic (RUNS THIRD) | • Bearer token validation<br>• Resource access control<br>• Domain operations |

## Security Features

### 🛡️ Cross-User Data Protection

```mermaid
flowchart TB
    subgraph "User Switch Scenario"
        U1[User A Logged In] --> D1[Data Cached for User A]
        D1 --> L1[User A Logs Out]
        L1 --> C1{Cache Cleared?}
        C1 -->|Yes| U2[User B Logs In]
        C1 -->|No| LEAK[❌ User B sees User A's data]
        U2 --> D2[Fresh Data for User B]
    end
    
    subgraph "Protection Mechanisms"
        M1[User-Specific Cache Keys] --> SAFE[✅ Data Isolation]
        M2[Cache Clear on Logout] --> SAFE
        M3[Cache Clear on Auth Change] --> SAFE
        M4[Global QueryClient Access] --> SAFE
    end
    
    style LEAK fill:#ff6b6b,stroke:#c92a2a,color:#fff
    style SAFE fill:#51cf66,stroke:#2f9e44,color:#fff
    style M1 fill:#4c6ef5,stroke:#364fc7,color:#fff
    style M2 fill:#4c6ef5,stroke:#364fc7,color:#fff
    style M3 fill:#4c6ef5,stroke:#364fc7,color:#fff
    style M4 fill:#4c6ef5,stroke:#364fc7,color:#fff
```

#### React Query Cache Management
To prevent data leakage when switching user accounts, the system implements multiple cache clearing strategies:

1. **User-Specific Cache Keys**
   - Asset queries include user ID in cache key: `['associatedAsset', userId]`
   - Ensures each user has isolated cached data

2. **Cache Clearing on Logout**
   - `logout()` function clears React Query cache BEFORE redirect
   - Critical for production where `/logout` page never runs (Lambda@Edge intercepts)
   - Exposed via `window.__queryClient` for global access

3. **Cache Clearing on Auth State Change**
   - Monitors `isAuthenticated` state
   - Automatically clears cache when user becomes unauthenticated
   - Catches token expiry, manual logout, and error scenarios

4. **Production Considerations**
   - **Critical**: The `/logout` page never renders in production
   - Lambda@Edge intercepts the `/logout` URL before Next.js
   - Cache must be cleared in `logout()` function, not in the page component

### 🔒 Token Storage Strategy

#### Cookie Configuration (Lambda@Edge)
```typescript
// All tokens accessible to JavaScript for seamless refresh
{
  idToken: {
    httpOnly: false,     // JavaScript access for auth state
    secure: true,        // HTTPS only
    sameSite: 'Strict'   // CSRF protection
  },
  accessToken: {
    httpOnly: false,     // JavaScript access for API calls
    secure: true,
    sameSite: 'Strict'
  },
  refreshToken: {
    httpOnly: false,     // JavaScript access for Amplify refresh
    secure: true,
    sameSite: 'Strict'
  }
}
```

#### Token Synchronization
On application load, tokens from Lambda@Edge cookies are automatically synchronized to localStorage, enabling Amplify to:
- Access tokens for API calls via `fetchAuthSession()`
- Refresh tokens without page reloads
- Maintain consistent auth state across the application

**Security Note**: We accept the XSS vulnerability trade-off for all tokens to enable seamless token refresh and avoid disruptive page reloads every hour.

### 🛡️ Security Headers
Applied at both Lambda@Edge and Next.js levels:
- `X-Frame-Options: DENY` - Prevents clickjacking
- `X-Content-Type-Options: nosniff` - Blocks MIME sniffing
- `Referrer-Policy: strict-origin-when-cross-origin` - Controls referrer leakage
- `Permissions-Policy` - Restricts browser APIs

### 🔄 Token Refresh Strategy

```mermaid
stateDiagram-v2
    [*] --> CheckingExpiry: Token acquired
    
    CheckingExpiry --> ScheduleRefresh: Has expiry
    CheckingExpiry --> DefaultInterval: No expiry
    
    ScheduleRefresh --> Waiting: Set timer
    DefaultInterval --> Waiting: Set 50min timer
    
    Waiting --> RefreshAttempt: Timer triggers
    
    RefreshAttempt --> Success: Tokens refreshed
    RefreshAttempt --> Retry1: Failed (attempt 1)
    
    Retry1 --> Retry2: Wait 1s
    Retry2 --> Retry3: Wait 2s
    Retry3 --> ForceLogout: Wait 4s
    
    Retry1 --> Success: Tokens refreshed
    Retry2 --> Success: Tokens refreshed
    Retry3 --> Success: Tokens refreshed
    
    Success --> CheckingExpiry: New tokens
    ForceLogout --> [*]: Max retries reached
    
    note right of RefreshAttempt
        Refresh 5 minutes
        before expiry
    end note
    
    note right of ForceLogout
        User logged out
        for security
    end note
```

#### Proactive Refresh
Tokens refresh **5 minutes before expiration** to prevent authentication interruptions:

```typescript
const REFRESH_BUFFER_MS = 5 * 60 * 1000; // 5 minutes
const refreshTime = tokenExpiry - Date.now() - REFRESH_BUFFER_MS;
```

#### Retry with Exponential Backoff
On refresh failure:
1. **Attempt 1**: 1 second delay
2. **Attempt 2**: 2 second delay  
3. **Attempt 3**: 4 second delay
4. **Max 3 attempts**: User logged out for security

#### Performance Optimizations
- **Token expiry caching**: Avoids repeated JWT decoding
- **Promise-based locks**: Prevents concurrent refresh attempts
- **Memory leak prevention**: Uses refs to avoid stale closures

## Authentication Flows

### Production Sign In Flow (Detailed)

```mermaid
sequenceDiagram
    participant U as User Browser
    participant CF as CloudFront
    participant L as Lambda@Edge
    participant C as Cognito Hosted UI
    participant N as Next.js App
    participant S as S3 Origin
    
    Note over U,S: Initial Request (Unauthenticated)
    U->>CF: GET command.gethero.com/dashboard
    CF->>L: Forward request
    L->>L: Check cookies: ❌ No auth tokens
    L-->>U: 302 Redirect to auth.gethero.com
    
    Note over U,C: OAuth/SAML Authentication
    U->>C: Navigate to Hosted UI
    C->>C: User enters credentials
    C->>C: OAuth/SAML provider validation
    C-->>U: Redirect with ?code=ABC&state=XYZ
    
    Note over U,N: Token Exchange & Cookie Setting
    U->>CF: GET command.gethero.com?code=ABC&state=XYZ
    CF->>L: Forward callback
    L->>C: Exchange code for tokens
    C-->>L: Return JWT tokens
    L->>L: Set auth cookies (all readable)
    L->>S: Forward authenticated request
    S->>N: Serve Next.js app
    
    Note over N,U: Client-Side Initialization
    N->>N: AuthContext: Read cookies
    N->>N: Sync to localStorage for Amplify
    N->>N: Set isAuthenticated = true
    N-->>U: Render authenticated app
```

**Step-by-step breakdown:**
1. Browser requests command.gethero.com (any path)
2. CloudFront Edge Lambda (`cognito-at-edge`) detects **no** auth cookies and issues a **302 redirect** to Cognito Hosted UI (`auth.gethero.com`)
3. User authenticates with their OAuth/SAML provider in the Hosted UI
4. Hosted UI redirects back to `command.gethero.com` with `?code=...&state=...`
5. Edge Lambda intercepts the callback, exchanges the **auth code** for tokens, and sets **authentication cookies**
6. The original request (now authenticated) is forwarded to the S3/Next.js origin
7. Next.js serves the app; `AuthContext` copies cookies → localStorage → sets authenticated state
8. User sees the authenticated application

### Production Logout Flow (Detailed)

```mermaid
sequenceDiagram
    participant U as User Browser
    participant A as AuthContext
    participant CF as CloudFront
    participant L as Lambda@Edge
    participant C as Cognito
    participant RQ as React Query
    
    Note over U,RQ: User Initiates Logout
    U->>A: Click logout button
    A->>RQ: Clear cache (prevents cross-user data)
    A->>U: window.location.href = '/logout'
    
    Note over U,C: Edge Lambda Intercepts
    U->>CF: GET /logout
    CF->>L: Intercept request
    L->>L: Detect /logout URL
    L-->>U: 302 Redirect to Cognito logout
    
    Note over U,C: Cognito Server-Side Logout
    U->>C: Navigate to auth.gethero.com/logout
    C->>C: Invalidate server-side session
    C->>C: Revoke all tokens
    C-->>U: Redirect back to command.gethero.com
    
    Note over U,L: Return Without Auth
    U->>CF: GET command.gethero.com
    CF->>L: Check auth cookies
    L->>L: No valid cookies found
    L-->>U: 302 Redirect to login
    
    Note over U: Final State
    U->>C: Land on Cognito login page
```

**Step-by-step breakdown:**
1. User clicks **Logout** → `logout()` function clears React Query cache → browser navigates to `/logout`
2. Edge Lambda intercepts `/logout` **before** Next.js runs
3. `cognito-at-edge` returns a redirect response to `https://auth.gethero.com/logout?redirect_uri=https%3A%2F%2Fcommand.gethero.com&...`
4. Cognito Hosted UI performs server-side logout & token invalidation
5. Cognito redirects the user back to `command.gethero.com`
6. Edge Lambda now detects **no valid auth cookies** and immediately redirects the user to the Cognito Hosted UI **login page**
7. User lands on the Hosted UI login screen (no "unauthenticated app" is ever served)

**Critical**: The Next.js `/logout` page **never runs in production**. The `/logout` URL is a "trigger" for Edge Lambda, not an actual page.

### Unified Authentication Flow
```mermaid
sequenceDiagram
    participant B as Browser
    participant CF as CloudFront
    participant L as Lambda@Edge
    participant C as Cognito
    participant A as Amplify
    participant N as Next.js
    
    Note over B,N: Initial Authentication (Production)
    B->>CF: Request any page
    CF->>L: Intercept request
    L->>L: No auth cookies found
    L-->>B: 302 Redirect to Cognito
    B->>C: SAML/OAuth/Password login
    C-->>B: Redirect with auth code
    B->>CF: Callback with code
    CF->>L: Intercept callback
    L->>C: Exchange code for tokens
    C-->>L: JWT tokens
    L->>L: Set cookies (all readable)
    L-->>N: Forward authenticated request
    
    Note over N,A: Token Synchronization
    N->>N: Read cookies on load
    N->>N: Sync tokens to localStorage
    N->>A: Amplify now has tokens
    
    Note over A,C: Token Refresh (No Page Reload)
    A->>C: fetchAuthSession({ forceRefresh: true })
    C-->>A: New tokens
    A->>N: Update localStorage
    N->>N: Update cookies
    N-->>B: Continue seamlessly
```


### Development Flow

```mermaid
sequenceDiagram
    participant U as User Browser
    participant N as Next.js App
    participant A as Amplify SDK
    participant C as Cognito
    
    Note over U,C: Direct Authentication
    U->>N: Navigate to /login
    N->>N: Render login form
    U->>N: Submit credentials
    N->>A: signIn(username, password)
    A->>C: Direct API call
    C-->>A: Return JWT tokens
    A->>A: Store tokens in localStorage
    A-->>N: Authentication successful
    N->>N: Set isAuthenticated = true
    N-->>U: Redirect to dashboard
```

**Key differences from production:**
- Direct username/password via Amplify `signIn()`
- Tokens stored in localStorage by Amplify
- No Lambda@Edge involvement
- Same token refresh mechanism as production
- `/login` and `/logout` pages render normally

## Configuration

### Central Configuration (`auth.config.ts`)
```typescript
export const TOKEN_REFRESH_CONFIG = {
  REFRESH_BUFFER_MS: 5 * 60 * 1000,           // 5 minutes
  DEFAULT_REFRESH_INTERVAL_MS: 50 * 60 * 1000, // 50 minutes
  MAX_RETRY_ATTEMPTS: 3,
  MAX_BACKOFF_MS: 30000,                      // 30 seconds
  BASE_BACKOFF_MS: 1000,                      // 1 second
};
```

### Environment Variables
```bash
# Next.js (.env)
NEXT_PUBLIC_COGNITO_USER_POOL_ID=us-west-2_xxxxx
NEXT_PUBLIC_COGNITO_CLIENT_ID=xxxxxxxxxxxxx
NEXT_PUBLIC_COGNITO_IDENTITY_POOL_ID=us-west-2:xxxxx

# Lambda@Edge (replaced by CDK during deployment)
const region = '%%REGION%%';
const userPoolId = '%%USER_POOL_ID%%';
const userPoolAppId = '%%USER_POOL_APP_ID%%';
const userPoolDomain = '%%USER_POOL_DOMAIN%%';
```

## Key Components

### AuthContext (`AuthContext.tsx`)
Core authentication provider with unified flow:
- **Cookie to localStorage sync** on application load
- **Amplify-based token refresh** for all environments
- **Automatic cookie updates** after refresh
- **Single code path** for dev and production
- **Performance optimizations** with caching and refs
- **React Query cache clearing** on logout and auth state changes
- **Cross-user data protection** via cache management

### UserAssetContext (`UserAssetContext.tsx`)
User asset validation and feature flag provider:
- **Associated asset fetching** with caching (30s stale, 1m gc)
- **User-specific cache keys** to prevent cross-user data leakage
- **Automatic redirect** to `/account-setup` if no asset found
- **Feature flag management** based on org and asset
- **Integrated with auth system** for seamless experience

### GetAssociatedAsset (`GetAssociatedAsset.ts`)
Asset fetching logic with auth integration:
- **Development mode** auto-creates asset if missing
- **Production mode** requires existing asset
- **AssetNotFoundError** for proper error handling
- **Auth debug logging** when AUTH_DEBUG_ENABLED flag is set
- **React Query caching** to prevent constant refetching

### Root Page (`page.tsx`)
Simple server-side redirect to `/cad`:
- **Server-side redirect** for optimal performance
- **Static generation** maintained
- **OAuth callbacks** should be handled by Lambda@Edge
- **Development**: No OAuth callbacks (uses direct username/password)
- **Production**: Lambda@Edge processes OAuth before reaching Next.js

**Note on OAuth Callbacks:**
If OAuth callbacks (`?code=...&state=...`) reach the Next.js app in production:
- This indicates Lambda@Edge configuration issue
- Should be fixed in `cognito-at-edge` configuration
- Temporary workaround: Client-side URL cleaning (not recommended due to performance impact)

### ContactAdminForAccount (`ContactAdminForAccount.tsx`)
Account setup required page:
- **Generic messaging** (not Hero-specific)
- **Logout option** for users to switch accounts
- **Displayed at `/account-setup`** route

### AuthGuard (`AuthGuard.tsx`)
Route protection component with:
- Open redirect prevention
- Loading states with timeouts
- Public path management
- Memoized performance

### Token Utilities (`auth.ts`)
Secure token management:
- Cookie reading/writing with security flags
- JWT decoding with caching
- Cross-domain cookie handling
- Comprehensive JSDoc documentation

### Axios Configuration (`axiosConfig.ts`)
HTTP client with authentication handling:
- **Request interceptor** adds Bearer token to all API calls
- **Response interceptor** handles authentication errors:
  - **401 Unauthorized** → Redirects to `/logout` for proper cleanup
  - **403 Forbidden** → Logs error but keeps user authenticated
- Automatic token injection from Amplify session

### Lambda@Edge (`authorizationLambda/index.js`)
Edge authentication with:
- Static asset bypass
- Source map blocking
- Security header injection
- Cookie configuration matching frontend
- URL routing interception (special handling for `/logout`)
- Uses `cognito-at-edge` library (v1.5.3) for OAuth flow management
- OAuth callback detection at root path

**Critical Configuration:**
- **`parseAuthPath: '/'`** - MUST be set to handle OAuth callbacks at root
  - Without this, OAuth callbacks (`?code=...&state=...`) pass through unprocessed
  - Must match callback URLs in Cognito User Pool Client configuration
  - Fixes "first Okta login fails" issue where params leak to Next.js
- **`/logout` exclusion from publicPaths** - Required for proper logout flow
  - Must be handled by `authenticator.handle()` not passed through
  - Without this, logout redirects to `/index.txt` error
- **Redirect response preservation** - Don't apply `.html` suffix to redirects
  - Check for status codes 301, 302, 303, 307, 308
  - Return redirect responses as-is to prevent URL corruption

## Component Deep Dive

### AWS Cognito (Identity Provider)
- **User Pool:** Stores user accounts, handles OAuth/SAML providers
- **Hosted UI:** Login/logout pages (`auth.gethero.com`)
- **JWT Tokens:** Issues access/ID/refresh tokens
- **Session Management:** Server-side session validation

### cognito-at-edge (Edge Lambda Library)
- **Request Interception:** Runs on every CloudFront request via `authenticator.handle(event)`
- **Token Validation:** Checks JWT tokens in cookies
- **OAuth Flow Management:** Handles redirects to/from Cognito
- **Cookie Management:** Sets/reads authentication cookies
- **URL Routing:** Intercepts special URLs like `/logout` (configured via `logoutUri: '/logout'`)

### Amplify Auth SDK (Client-Side Library)
**In production, Amplify is simply a *token utility*:**
- **`fetchAuthSession()`** – Reads tokens (copied from cookies) for API calls
- **Token refresh** – Automatically refreshes expired tokens
- **`getCurrentUser()`** – Retrieves user profile data

**Key insight:** Amplify does **NOT** initiate login or logout in production; those flows are fully handled by Edge Lambda & Cognito.

### Next.js App (Client Application)
- **AuthContext:** Manages client-side auth state
- **Token Storage Sync:** Copies cookies to localStorage for Amplify
- **UI State:** Shows authenticated/unauthenticated views
- **API Integration:** Uses Amplify tokens for microservice calls

## Authentication Kick-out Mechanisms

```mermaid
flowchart TD
    subgraph "Detection Layer"
        API[API 401/403] --> KICK[Force Logout]
        REFRESH[Token Refresh Failure] --> KICK
        GUARD[AuthGuard Check] --> KICK
        ASSET[No Asset Found] --> SETUP[Account Setup]
        MANUAL[User Logout] --> KICK
    end
    
    subgraph "Cleanup Process"
        KICK --> CLEAR1[Clear React Query Cache]
        KICK --> CLEAR2[Clear Tokens]
        KICK --> CLEAR3[Clear localStorage]
        KICK --> CLEAR4[Clear Cookies]
        CLEAR1 --> REDIRECT[Redirect to Login]
        CLEAR2 --> REDIRECT
        CLEAR3 --> REDIRECT
        CLEAR4 --> REDIRECT
    end
    
    style API fill:#ff6b6b,stroke:#c92a2a,color:#fff
    style REFRESH fill:#ff6b6b,stroke:#c92a2a,color:#fff
    style GUARD fill:#ff6b6b,stroke:#c92a2a,color:#fff
    style ASSET fill:#ffd43b,stroke:#fab005,color:#000
    style MANUAL fill:#339af0,stroke:#1971c2,color:#fff
    style KICK fill:#ff8787,stroke:#fa5252,color:#fff
    style SETUP fill:#51cf66,stroke:#2f9e44,color:#fff
```

The system ensures users are properly logged out when they become unauthenticated through multiple layers:

### 1. API Response Interceptor
- Monitors all API responses for authentication errors
- **401 Unauthorized**: Token invalid/expired → Redirects to `/logout`
- **403 Forbidden**: User lacks permission → Stays authenticated, error bubbles up

### 2. Token Refresh Failure
- Attempts refresh with exponential backoff (max 3 attempts)
- On final failure: Calls `localLogout()` → Clears tokens → AuthGuard redirects

### 3. AuthGuard Route Protection
- Wraps entire application at root layout
- Continuously monitors `isAuthenticated` state
- Redirects to `/login` when authentication is lost

### 4. Asset Validation
- Checks for associated user asset after authentication
- Missing asset → Redirects to `/account-setup`
- Prevents access without proper account configuration

### 5. Manual Logout
- User-initiated via logout button
- Redirects to `/logout` endpoint
- Production: Lambda@Edge handles Cognito logout
- Development: Calls Amplify `signOut()`
- **Critical**: React Query cache cleared BEFORE redirect (prevents cross-user data leakage)

## Error Handling

### Specific Error Messages
```typescript
AUTH_ERROR_MESSAGES = {
  TOKEN_REFRESH_FAILED: 'Failed to refresh authentication token...',
  SESSION_EXPIRED: 'Your session has expired...',
  NETWORK_ERROR: 'Network error during authentication...',
  MAX_RETRIES_REACHED: 'Maximum authentication retry attempts...'
}
```

### Debug Logging
Enable with `AUTH_DEBUG_ENABLED = true` in `auth.config.ts`

All operations prefixed for filtering:
- `[Auth:TokenRefresh]` - Refresh operations
- `[Auth:Check]` - Authentication checks  
- `[Auth:Error]` - Error conditions
- `[Auth:Login]` - Login operations
- `[Auth:Logout]` - Logout operations
- `[Auth:Debug]` - Asset fetching and validation

## Usage Examples

### Check Authentication State
```typescript
import { useAuth } from '@/app/contexts/Auth/AuthContext';

function MyComponent() {
  const { isAuthenticated, user, loading, logout } = useAuth();
  
  if (loading) return <LoadingScreen />;
  if (!isAuthenticated) return <Redirect to="/login" />;
  
  return <div>Welcome {user.username}</div>;
}
```

### Access User Asset and Feature Flags
```typescript
import { useUserAsset } from '@/app/contexts/User/UserAssetContext';
import { Feature } from 'proto/hero/featureflags/v1/featureflags_pb';

function MyComponent() {
  const { asset, featureFlags, isLoading } = useUserAsset();
  
  if (isLoading) return <LoadingScreen />;
  
  // Asset is guaranteed to exist here due to automatic redirect
  const hasFeature = featureFlags.has(Feature.SOME_FEATURE);
  
  return <div>Asset: {asset?.name}</div>;
}
```

### Make Authenticated API Calls
```typescript
import { createAuthenticatedAxiosInstance } from '@/app/apis/services/axiosConfig';

const api = createAuthenticatedAxiosInstance('https://api.gethero.com');
const response = await api.get('/protected-resource'); // Auto Bearer token
```

### Manual Token Access
```typescript
import { fetchAuthSession } from 'aws-amplify/auth';

const session = await fetchAuthSession();
const accessToken = session.tokens?.accessToken?.toString();
```

### Implementing User-Specific Cache Keys
```typescript
// In your React Query hooks
const { user } = useAuth();
const { data } = useQuery({
  queryKey: ['myData', user?.userId], // Include user ID in cache key
  queryFn: fetchMyData,
});
```

### Accessing Global Query Client
```typescript
// The query client is exposed globally for cache management
const queryClient = (window as any).__queryClient;
if (queryClient) {
  queryClient.clear(); // Clear all cached data
  // or
  queryClient.invalidateQueries({ queryKey: ['specificKey'] });
}
```

## Common Pitfalls & Solutions

| Pitfall | Solution |
|---------|----------|
| ❌ Using `signOut()` in production | ✅ Navigate to `/logout` URL instead |
| ❌ Forgetting cookie → localStorage sync | ✅ AuthContext handles automatically on load |
| ❌ Different code paths for dev/prod | ✅ Unified flow after initial sync |
| ❌ Page reloads for token refresh | ✅ Amplify handles refresh seamlessly |
| ❌ Setting refresh token as httpOnly | ✅ All tokens readable for Amplify refresh |
| ❌ Complex environment branching | ✅ Single authentication flow |
| ❌ No caching for asset fetching | ✅ 30s stale time, 1m gc time prevents lockouts |
| ❌ App blocked when no asset exists | ✅ Auto-redirect to `/account-setup` page |
| ❌ Shared cache between users | ✅ User-specific cache keys + cache clearing on logout |
| ❌ Clearing cache in `/logout` page | ✅ Clear cache in `logout()` function (before redirect) |
| ❌ Using `signOut()` in production | ✅ Navigate to `/logout` URL (Edge Lambda handles it) |
| ❌ Ignoring dual token storage | ✅ Edge Lambda sets cookies, Amplify needs localStorage |
| ❌ Applying dev patterns to production | ✅ Dev = direct Amplify auth, Prod = Edge Lambda + cookies |
| ❌ Adding `/logout` to publicPaths | ✅ Let `authenticator.handle()` process `/logout` |
| ❌ Applying `.html` suffix to redirects | ✅ Preserve redirect responses as-is |
| ❌ OAuth params persisting in URL | ✅ Set `parseAuthPath: '/'` in Lambda@Edge |

## Development vs Production

```mermaid
flowchart LR
    subgraph "Development Flow"
        D1[Browser] --> D2[Next.js App]
        D2 --> D3[Amplify SDK]
        D3 --> D4[Cognito API]
        D4 --> D3
        D3 --> D5[localStorage]
        D5 --> D2
    end
    
    subgraph "Production Flow"
        P1[Browser] --> P2[CloudFront]
        P2 --> P3[Lambda@Edge]
        P3 --> P4[Cognito Hosted UI]
        P4 --> P3
        P3 --> P5[Cookies]
        P5 --> P6[Next.js App]
        P6 --> P7[localStorage sync]
        P7 --> P8[Amplify SDK]
    end
    
    style D1 fill:#339af0,stroke:#1971c2,color:#fff
    style D2 fill:#51cf66,stroke:#2f9e44,color:#fff
    style D3 fill:#ff8787,stroke:#fa5252,color:#fff
    style D4 fill:#ffd43b,stroke:#fab005,color:#000
    style D5 fill:#da77f2,stroke:#ae3ec9,color:#fff
    
    style P1 fill:#339af0,stroke:#1971c2,color:#fff
    style P2 fill:#ff6b6b,stroke:#c92a2a,color:#fff
    style P3 fill:#ff8787,stroke:#fa5252,color:#fff
    style P4 fill:#ffd43b,stroke:#fab005,color:#000
    style P5 fill:#da77f2,stroke:#ae3ec9,color:#fff
    style P6 fill:#51cf66,stroke:#2f9e44,color:#fff
    style P7 fill:#74c0fc,stroke:#339af0,color:#000
    style P8 fill:#ff8787,stroke:#fa5252,color:#fff
```

| Aspect | Development | Production |
|--------|------------|------------|
| **Login Method** | Username/password form | SAML/OAuth/Password via Cognito Hosted UI |
| **Initial Token Source** | Amplify → localStorage | Lambda@Edge → cookies → localStorage |
| **Token Storage** | localStorage (Amplify) | localStorage (synced from cookies) |
| **Token Refresh** | Amplify `fetchAuthSession()` | Amplify `fetchAuthSession()` |
| **Logout Handler** | Next.js page + Amplify | Lambda@Edge intercept |
| **Auth Gateway** | None (direct Amplify) | CloudFront Lambda@Edge |

## Mobile App Differences

Mobile apps use a different architecture:
- **No Lambda@Edge** - Direct API calls
- **Different Client IDs** - App-specific Cognito clients
  - responder-app: `52s9qm0anac1sohgioj2upgp1s`
  - member-app: `6qa92fhko35r4hubv1g8bjno3u`
- **expo-auth-session** - Instead of Amplify Auth
- **expo-secure-store** - For token storage
- **Direct Cognito logout** - Opens browser for logout

```
Web App:    Browser → CloudFront (Edge) → S3 → APIs
Mobile App: App → APIs (direct, no Edge Lambda)
```

**Why this matters:** Mobile apps don't have the .txt redirect issue because they don't serve static files. The complex Edge Lambda setup is **web-specific**.

## Debugging Authentication Issues

### Check Token State
```javascript
// View current auth session
const session = await fetchAuthSession();
console.log('Session:', session);

// Check cookies (all environments)
console.log('Cookies:', document.cookie);

// Parse Cognito cookies (all tokens readable)
const cookies = document.cookie.split('; ');
const cognitoCookies = cookies.filter(c => 
  c.includes('CognitoIdentityServiceProvider')
);
console.log('Cognito Cookies:', cognitoCookies);

// Check localStorage (where Amplify stores tokens)
const clientId = process.env.NEXT_PUBLIC_COGNITO_CLIENT_ID;
const storageKeys = Object.keys(localStorage).filter(key => 
  key.includes(`CognitoIdentityServiceProvider.${clientId}`)
);
console.log('Amplify Storage Keys:', storageKeys);

// In production after sync, you should see tokens in BOTH:
// - Cookies (from Lambda@Edge)
// - localStorage (synced for Amplify)
```

### Monitor Token Refresh
```javascript
// Watch console for refresh logs
// Look for [Auth:TokenRefresh] prefixed messages

// Monitor network tab for token refresh calls
// Filter by: fetchAuthSession

// Check refresh scheduling
console.log('Next refresh scheduled:', refreshIntervalRef.current);
```

### Common Error Patterns

| Error | Cause | Fix |
|-------|-------|-----|
| "Parsing '<'" error | Expired token returning HTML | Implemented static asset bypass |
| Infinite redirect loop | Missing `/logout` in public paths | Removed from public paths (must be handled by authenticator) |
| Token refresh fails | Tokens not in localStorage | Cookie → localStorage sync on load |
| Session expires suddenly | No proactive refresh | Refresh 5 minutes before expiry |
| Amplify can't find tokens | Lambda@Edge cookies not synced | AuthContext syncs automatically |
| App locks/freezes | Constant asset refetching | Added 30s cache to prevent refetching |
| AssetNotFoundError | User has no associated asset | Redirects to `/account-setup` page |
| Stuck on loading screen | Asset fetch timeout | Check network/API health |
| Seeing previous user's data | React Query cache not cleared | User-specific cache keys + clear on logout |
| Cache not clearing in prod | `/logout` page doesn't run | Clear cache in `logout()` function instead |
| Logout redirects to `/index.txt` | `/logout` in publicPaths array | Remove `/logout` from publicPaths |
| OAuth params in URL after login | `uri.includes('?')` check fails - querystring is separate field | Fixed: Check `request.querystring` instead |
| First Okta login fails | OAuth callback at `/` becomes `/index.html` | Fixed: Preserve querystring, add `parseAuthPath: '/'` |
| "Unexpected token '<'" | Build ID dirs like `/_next/6pWa6LX/` not bypassed | Fixed: Bypass ALL `/_next/` paths, not just `/static/` |

## Testing Recommendations

### Unit Tests
- Token expiry calculation with edge cases
- Retry logic with mocked failures  
- Cache behavior and eviction
- Error message generation

### Integration Tests
- Full authentication flow
- Token refresh cycle
- Network failure scenarios
- Cross-component communication

### E2E Tests
- Long session maintenance (>1 hour)
- Logout on max retries
- Browser refresh handling
- Multi-tab synchronization

## Performance Metrics

Monitor these KPIs:
- **Token refresh success rate** - Target: >99.9%
- **Average retry count** - Target: <1.1
- **Refresh timing accuracy** - Target: ±10 seconds
- **Cache hit rate** - Target: >90%
- **Auth check latency** - Target: <100ms

## File Structure

```
src/app/
├── providers.tsx                       # Global providers + queryClient setup
├── config/
│   └── auth.config.ts                  # Centralized configuration
├── contexts/
│   ├── Auth/
│   │   ├── AuthContext.tsx            # Main auth provider
│   │   └── README.md                  # This documentation
│   └── User/
│       └── UserAssetContext.tsx       # Asset validation & feature flags
├── apis/
│   ├── custom/
│   │   └── auth/
│   │       └── GetAssociatedAsset.ts   # Asset fetching logic
│   └── services/
│       └── axiosConfig.ts              # HTTP client with auth interceptors
├── components/
│   ├── AuthGuard.tsx                  # Route protection
│   ├── ContactAdminForAccount.tsx     # Account setup required page
│   └── LoadingScreen.tsx              # Auth loading states
├── utils/
│   └── auth.ts                        # Token utilities
├── account-setup/
│   └── page.tsx                       # Account setup route
├── page.tsx                           # Root page (simple redirect to /cad)
└── login/logout pages                 # Dev-only pages

infra/cloud/apps/
└── authorizationLambda/
    └── index.js                       # Lambda@Edge function
```

## Architecture Benefits

### Current Implementation
- ✅ **No page reloads** - Seamless token refresh via Amplify
- ✅ **Unified code path** - Single flow for all environments
- ✅ **Automatic synchronization** - Cookies → localStorage on load
- ✅ **Simplified maintenance** - No complex environment branching
- ✅ **Better UX** - Uninterrupted user experience

## Future Enhancements

1. **Refresh Token Rotation** - Enhanced security with rotating refresh tokens
2. **Cross-Tab Synchronization** - Share auth state across browser tabs
3. **Offline Support** - Queue actions when offline, sync on reconnect
4. **Session Extension** - Extend session on user activity
5. **Biometric Authentication** - Support for WebAuthn/passkeys

## Support & Maintenance

For issues or questions:
- Check debug logs with prefixes (`[Auth:*]`)
- Review token refresh strategy section in this document
- Verify configuration in auth.config.ts
- Test in both development and production environments

---
