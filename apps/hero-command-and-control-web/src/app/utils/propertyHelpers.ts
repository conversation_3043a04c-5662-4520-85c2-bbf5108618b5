// Shared property helpers
import { stringToPropertyStatus, stringToPropertyType, nibrsPropertyDescriptionToString, stringToNIBRSPropertyDescription } from "../apis/services/workflow/property/enumConverters";
import { propertyTypeToDisplayString } from "../apis/services/workflow/property/propertyTypeManager";
import { PropertyStatus } from "../apis/services/workflow/property/types";
import { NIBRSPropertyDescription } from "proto/hero/property/v1/property_pb";

export const getReadablePropertyType = (propertyType: string): string => {
    // Convert string to enum and use centralized display function
    const enumValue = stringToPropertyType(propertyType);
    const displayString = propertyTypeToDisplayString(enumValue);
    return `${displayString} Property`;
};

export const getReadablePropertyCategory = (category: string): string => {
    if (!category) return "";
    
    // Handle proper NIBRS enum strings (new format)
    if (category.startsWith("NIBRS_PROPERTY_DESCRIPTION_")) {
        const enumValue = stringToNIBRSPropertyDescription(category);
        return nibrsPropertyDescriptionToString(enumValue);
    }
    
    // Handle legacy lowercase strings with proper capitalization mapping
    const legacyCategoryMap: { [key: string]: string } = {
        "aircraft": "Aircraft",
        "alcohol": "Alcohol", 
        "automobiles": "Automobiles",
        "bicycles": "Bicycles",
        "buses": "Buses",
        "clothing": "Clothing",
        "electronics": "Electronics",
        "consumables": "Consumables", 
        "documents": "Documents",
        "drugs": "Drugs",
        "drug equipment": "Drug Equipment",
        "farm equipment": "Farm Equipment",
        "weapons": "Weapons",
        "gambling equipment": "Gambling Equipment",
        "heavy equipment": "Heavy Equipment",
        "household goods": "Household Goods",
        "jewelry": "Jewelry",
        "livestock": "Livestock",
        "merchandise": "Merchandise",
        "cash": "Cash",
        "currency": "Currency",
        "negotiable instruments": "Negotiable Instruments",
        "nonnegotiable instruments": "Nonnegotiable Instruments",
        "office equipment": "Office Equipment",
        "other vehicles": "Other Vehicles",
        "purses/bags": "Purses/Bags",
        "recordings": "Recordings",
        "recreational vehicles": "Recreational Vehicles",
        "structures": "Structures",
        "tools": "Tools",
        "trucks": "Trucks",
        "vehicle parts": "Vehicle Parts",
        "watercraft": "Watercraft",
        "other": "Other"
    };
    
    return legacyCategoryMap[category.toLowerCase()] || category.charAt(0).toUpperCase() + category.slice(1);
};

// Get property status display - shared function for consistent status display
export const getPropertyStatusDisplay = (status: PropertyStatus | string): string => {
    // Handle both string and numeric enum values
    let propertyStatus: PropertyStatus;

    if (typeof status === 'string') {
        propertyStatus = stringToPropertyStatus(status);
    } else {
        propertyStatus = status;
    }

    switch (propertyStatus) {
        case PropertyStatus.INTAKE_PENDING:
            return "Logged";
        case PropertyStatus.COLLECTED:
            return "Collected";
        case PropertyStatus.CHECKED_IN:
            return "Checked In";
        case PropertyStatus.CHECKED_OUT:
            return "Checked Out";
        case PropertyStatus.RECOVERED:
            return "Recovered";
        case PropertyStatus.FOUND:
            return "Found";
        case PropertyStatus.SAFEKEEPING:
            return "Safekeeping";
        case PropertyStatus.AWAITING_DISPOSITION:
            return "Awaiting Disposition";
        case PropertyStatus.DISPOSED:
            return "Disposed";
        case PropertyStatus.MISSING:
            return "Missing";
        case PropertyStatus.STOLEN:
            return "Stolen";
        default:
            return "Unknown";
    }
};

/**
 * Gets all NIBRS property categories for form dropdowns
 * Returns proper enum string values with human-readable labels
 */
export function getCategoryDropdownOptions() {
    return [
        { value: "NIBRS_PROPERTY_DESCRIPTION_AIRCRAFT", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_AIRCRAFT) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_ALCOHOL", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_ALCOHOL) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_AUTOMOBILES", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_AUTOMOBILES) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_BICYCLES", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_BICYCLES) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_BUSES", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_BUSES) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_CLOTHES_FURS", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_CLOTHES_FURS) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_COMPUTER_HARDWARE_SOFTWARE", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_COMPUTER_HARDWARE_SOFTWARE) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_CONSUMABLE_GOODS", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_CONSUMABLE_GOODS) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_CREDIT_DEBIT_CARDS", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_CREDIT_DEBIT_CARDS) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_DRUGS_NARCOTICS", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_DRUGS_NARCOTICS) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_DRUG_NARCOTIC_EQUIPMENT", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_DRUG_NARCOTIC_EQUIPMENT) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_FARM_EQUIPMENT", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_FARM_EQUIPMENT) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_FIREARMS", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_FIREARMS) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_GAMBLING_EQUIPMENT", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_GAMBLING_EQUIPMENT) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_HEAVY_CONSTRUCTION_INDUSTRIAL_EQUIPMENT", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_HEAVY_CONSTRUCTION_INDUSTRIAL_EQUIPMENT) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_HOUSEHOLD_GOODS", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_HOUSEHOLD_GOODS) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_JEWELRY_PRECIOUS_METALS_GEMS", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_JEWELRY_PRECIOUS_METALS_GEMS) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_LIVESTOCK", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_LIVESTOCK) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_MERCHANDISE", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_MERCHANDISE) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_MONEY", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_MONEY) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_NEGOTIABLE_INSTRUMENTS", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_NEGOTIABLE_INSTRUMENTS) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_NONNEGOTIABLE_INSTRUMENTS", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_NONNEGOTIABLE_INSTRUMENTS) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_OFFICE_TYPE_EQUIPMENT", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_OFFICE_TYPE_EQUIPMENT) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_OTHER_MOTOR_VEHICLES", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_OTHER_MOTOR_VEHICLES) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_PURSES_HANDBAGS_WALLETS", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_PURSES_HANDBAGS_WALLETS) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_RADIOS_TVS_VCRS", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_RADIOS_TVS_VCRS) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_RECORDINGS_AUDIO_VISUAL", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_RECORDINGS_AUDIO_VISUAL) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_RECREATIONAL_VEHICLES", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_RECREATIONAL_VEHICLES) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_SINGLE_OCCUPANCY_DWELLINGS", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_SINGLE_OCCUPANCY_DWELLINGS) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_OTHER_DWELLINGS", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_OTHER_DWELLINGS) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_OTHER_COMMERCIAL_BUSINESS", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_OTHER_COMMERCIAL_BUSINESS) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_INDUSTRIAL_MANUFACTURING", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_INDUSTRIAL_MANUFACTURING) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_PUBLIC_COMMUNITY", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_PUBLIC_COMMUNITY) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_STORAGE", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_STORAGE) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_OTHER", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_STRUCTURES_OTHER) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_TOOLS", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_TOOLS) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_TRUCKS", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_TRUCKS) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_VEHICLE_PARTS_ACCESSORIES", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_VEHICLE_PARTS_ACCESSORIES) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_WATERCRAFT", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_WATERCRAFT) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_AIRCRAFT_PARTS_ACCESSORIES", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_AIRCRAFT_PARTS_ACCESSORIES) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_ARTISTIC_SUPPLIES_ACCESSORIES", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_ARTISTIC_SUPPLIES_ACCESSORIES) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_BUILDING_MATERIALS", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_BUILDING_MATERIALS) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_CAMPING_HUNTING_FISHING_EQUIPMENT", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_CAMPING_HUNTING_FISHING_EQUIPMENT) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_CHEMICALS", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_CHEMICALS) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_COLLECTIONS_COLLECTIBLES", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_COLLECTIONS_COLLECTIBLES) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_CROPS", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_CROPS) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_DOCUMENTS_PERSONAL_OR_BUSINESS", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_DOCUMENTS_PERSONAL_OR_BUSINESS) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_EXPLOSIVES", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_EXPLOSIVES) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_FIREARM_ACCESSORIES", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_FIREARM_ACCESSORIES) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_FUEL", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_FUEL) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_IDENTITY_DOCUMENTS", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_IDENTITY_DOCUMENTS) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_IDENTITY_INTANGIBLE", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_IDENTITY_INTANGIBLE) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_LAW_ENFORCEMENT_EQUIPMENT", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_LAW_ENFORCEMENT_EQUIPMENT) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_LAWN_YARD_GARDEN_EQUIPMENT", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_LAWN_YARD_GARDEN_EQUIPMENT) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_LOGGING_EQUIPMENT", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_LOGGING_EQUIPMENT) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_MEDICAL_MEDICAL_LAB_EQUIPMENT", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_MEDICAL_MEDICAL_LAB_EQUIPMENT) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_METALS_NON_PRECIOUS", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_METALS_NON_PRECIOUS) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_MUSICAL_INSTRUMENTS", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_MUSICAL_INSTRUMENTS) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_PETS", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_PETS) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_PHOTOGRAPHIC_OPTICAL_EQUIPMENT", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_PHOTOGRAPHIC_OPTICAL_EQUIPMENT) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_PORTABLE_ELECTRONIC_COMMUNICATIONS", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_PORTABLE_ELECTRONIC_COMMUNICATIONS) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_RECREATIONAL_SPORTS_EQUIPMENT", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_RECREATIONAL_SPORTS_EQUIPMENT) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_OTHER", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_OTHER) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_TRAILERS", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_TRAILERS) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_WATERCRAFT_EQUIPMENT_PARTS_ACCESSORIES", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_WATERCRAFT_EQUIPMENT_PARTS_ACCESSORIES) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_WEAPONS_OTHER", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_WEAPONS_OTHER) },
        { value: "NIBRS_PROPERTY_DESCRIPTION_PENDING_INVENTORY", label: nibrsPropertyDescriptionToString(NIBRSPropertyDescription.NIBRS_PROPERTY_DESCRIPTION_PENDING_INVENTORY) },
    ];
}

