import { useState, useEffect } from "react";
import { Box } from "@mui/material";
import { DateRangePicker } from "../../../../design-system/components/DatePicker";
import { Typography } from "../../../../design-system/components/Typography";
import { colors } from "../../../../design-system/tokens";
import { RecordStatus, SearchOrderBy } from "proto/hero/entity/v1/entity_pb";

interface OrganizationSearchFiltersProps {
  selectedStatuses: RecordStatus[];
  setSelectedStatuses: (statuses: RecordStatus[]) => void;
  selectedTags: string[];
  setSelectedTags: (tags: string[]) => void;
  dateFilters: {
    createTime?: { from: string; to: string };
    updateTime?: { from: string; to: string };
  };
  setDateFilters: (filters: any) => void;
}

const ENTITY_STATUS_OPTIONS = [
  { value: "active", label: "Active", enumValue: RecordStatus.ACTIVE },
  { value: "draft", label: "Draft", enumValue: RecordStatus.DRAFT },
  { value: "archived", label: "Archived", enumValue: RecordStatus.ARCHIVED },
  { value: "deprecated", label: "Deprecated", enumValue: RecordStatus.DEPRECATED },
];

const ENTITY_SORT_OPTIONS = [
  { value: "created_at", label: "Created Date", enumValue: SearchOrderBy.CREATED_AT },
  { value: "updated_at", label: "Updated Date", enumValue: SearchOrderBy.UPDATED_AT },
  { value: "status", label: "Status", enumValue: SearchOrderBy.STATUS },
];

export const OrganizationSearchFilters = ({
  selectedStatuses,
  setSelectedStatuses,
  selectedTags,
  setSelectedTags,
  dateFilters,
  setDateFilters,
}: OrganizationSearchFiltersProps) => {
  const [createdDateRange, setCreatedDateRange] = useState<[Date | null, Date | null]>([null, null]);
  const [updatedDateRange, setUpdatedDateRange] = useState<[Date | null, Date | null]>([null, null]);
  const [status, setStatus] = useState<string | null>(null);

  // Sync local UI state with props - Created Date Range
  useEffect(() => {
    if (dateFilters.createTime?.from && dateFilters.createTime?.to) {
      const fromDate = new Date(dateFilters.createTime.from);
      const toDate = new Date(dateFilters.createTime.to);
      setCreatedDateRange([fromDate, toDate]);
    } else {
      setCreatedDateRange([null, null]);
    }
  }, [dateFilters.createTime]);

  // Sync local UI state with props - Updated Date Range
  useEffect(() => {
    if (dateFilters.updateTime?.from && dateFilters.updateTime?.to) {
      const fromDate = new Date(dateFilters.updateTime.from);
      const toDate = new Date(dateFilters.updateTime.to);
      setUpdatedDateRange([fromDate, toDate]);
    } else {
      setUpdatedDateRange([null, null]);
    }
  }, [dateFilters.updateTime]);

  // Sync local UI state with props - Status
  useEffect(() => {
    if (selectedStatuses?.length > 0) {
      const statusOption = ENTITY_STATUS_OPTIONS.find(option => 
        option.enumValue === selectedStatuses[0]
      );
      setStatus(statusOption?.value || null);
    } else {
      setStatus(null);
    }
  }, [selectedStatuses]);

  return (
    <Box
      sx={{
        width: 320,
        bgcolor: "white",
        borderRadius: 2,
        boxShadow: "none",
        p: "24px 32px",
        mr: "24px",
        overflow: "auto",
        border: `1px solid ${colors.grey[200]}`,
      }}
    >
      <Box sx={{ mb: "16px" }}>
        <Typography style="caps1" color={colors.grey[900]}>
          FILTERS
        </Typography>
      </Box>

      {/* Date Range Filters */}
      <Box sx={{ mb: "16px" }}>
        <DateRangePicker
          title="Created Date"
          placeholder="Select date range"
          value={createdDateRange}
          onChange={(newRange) => {
            setCreatedDateRange(newRange);
            if (newRange[0] && newRange[1]) {
              const from = newRange[0].toISOString();
              const to = newRange[1].toISOString();
              setDateFilters({
                ...dateFilters,
                createTime: { from, to },
              });
            } else {
              const newFilters = { ...dateFilters };
              delete newFilters.createTime;
              setDateFilters(newFilters);
            }
          }}
        />
      </Box>

      <Box sx={{ mb: "16px" }}>
        <DateRangePicker
          title="Last Updated"
          placeholder="Select date range"
          value={updatedDateRange}
          onChange={(newRange) => {
            setUpdatedDateRange(newRange);
            if (newRange[0] && newRange[1]) {
              const from = newRange[0].toISOString();
              const to = newRange[1].toISOString();
              setDateFilters({
                ...dateFilters,
                updateTime: { from, to },
              });
            } else {
              const newFilters = { ...dateFilters };
              delete newFilters.updateTime;
              setDateFilters(newFilters);
            }
          }}
        />
      </Box>

    </Box>
  );
};