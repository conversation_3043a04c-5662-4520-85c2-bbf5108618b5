import { JsonObject } from "@bufbuild/protobuf";
import CheckIcon from "@mui/icons-material/Check";
import * as Sentry from "@sentry/nextjs";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import KeyboardArrowLeftIcon from "@mui/icons-material/KeyboardArrowLeft";
import KeyboardArrowRightIcon from "@mui/icons-material/KeyboardArrowRight";
import SwapVertIcon from "@mui/icons-material/SwapVert";
import {
  Box,
  Checkbox,
  Menu,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from "@mui/material";
import { useRouter } from "next/navigation";
import { Case, CaseStatus, CaseType, SearchOrderBy } from "proto/hero/cases/v1/cases_pb";
import { Report, ReportType } from "proto/hero/reports/v2/reports_pb";
import React, { useCallback, useEffect, useState } from "react";
import { Button } from "../../../../design-system/components/Button";
import { Typography } from "../../../../design-system/components/Typography";
import { colors } from "../../../../design-system/tokens";
import { SectionTypeString } from "../../../../export/pdf/utils/sectionTypes";
import { useSearchCases } from "../../../apis/services/workflow/cases/hooks";
import { useBatchGetReports } from "../../../apis/services/workflow/reports/v2/hooks";
import { ExportModal } from "../../../components/ExportModal";
import { saveSearchStateForNavigation, SearchState } from "../../utils/searchStateManager";
import { TableSkeleton } from "../TableSkeleton";

// Helper function to strip HTML tags from text
const stripHtmlTags = (html: string): string => {
  const processedHtml = html
    .replace(/<\/(h[1-6])>/gi, ' ')
    .replace(/<\/(p|div|section|article)>/gi, ' ')
    .replace(/<br\s*\/?>/gi, ' ')
    .replace(/<\/(li|ul|ol)>/gi, ' ');

  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = processedHtml;
  return tempDiv.textContent || tempDiv.innerText || '';
};

interface ReportData {
  incidentDetailsSection?: {
    incidentType?: string;
    location?: string;
    involvedAgencies?: string[];
    offences?: string[];
    incidentStartTime?: string;
    incidentEndTime?: string;
    reporterName?: string;
    reporterRole?: string;
    reporterPhone?: string;
    responders?: string[];
  };
  peopleSection?: {
    entityList?: {
      entityRefs?: Array<{
        displayName: string;
      }>;
    };
  };
  vehiclesSection?: {
    entityList?: {
      entityRefs?: Array<{
        displayName: string;
      }>;
    };
  };
  propertiesSection?: {
    entityList?: {
      entityRefs?: Array<{
        displayName: string;
      }>;
    };
  };
}

interface ExtendedReport extends Report {
  additionalInfoJson?: JsonObject;
}

const CASES_SORT_OPTIONS = [
  { value: SearchOrderBy.CREATE_TIME, label: "Created Date" },
  { value: SearchOrderBy.UPDATE_TIME, label: "Updated Date" },
  { value: SearchOrderBy.PRIORITY, label: "Priority" },
  { value: SearchOrderBy.STATUS, label: "Status" },
  { value: SearchOrderBy.DUE_DATE, label: "Due Date" },
];

interface CasesResultsTableProps {
  data: {
    cases?: Case[];
  };
  isLoading: boolean;
  isError: boolean;
  error: any;
  page: number;
  rowsPerPage: number;
  totalPages: number;
  totalResults: number;
  nextPageToken?: string;
  handleChangePage: (newPage: number) => void;
  onChangeRowsPerPage: (newRowsPerPage: number) => void;
  selectedSort: SearchOrderBy;
  onSortChange: (newSort: SearchOrderBy) => void;
  getCurrentSearchState: () => SearchState;
}

// Helper type to handle the raw section data
interface RawReportSection {
  id: string;
  type: SectionTypeString;
  narrative?: {
    id: string;
    richText: string;
  };
  incidentDetails?: {
    id: string;
    initialType: string;
    finalType: string;
    incidentStartTime: string;
    incidentEndTime: string;
    incidentLocationStreetAddress: string;
    incidentLocationCity: string;
    incidentLocationState: string;
    incidentLocationZipCode: string;
    responders: Array<{
      displayName: string;
      role: string;
    }>;
    reportingPerson: {
      firstName: string;
      middleName?: string;
      lastName: string;
      phoneNumber?: string;
      reporterRole?: string;
    };
  };
  entityList?: {
    id: string;
    entityRefs: Array<{
      displayName: string;
      id: string;
    }>;
  };
  offenses?: Array<any>;
  metadata?: {
    primaryClassification?: string;
  };
  createdAt: string;
  updatedAt: string;
}

interface RawResponder {
  displayName: string;
  role: string;
}

const CasesResultsTableComponent: React.FC<CasesResultsTableProps> = ({
  data,
  isLoading,
  isError,
  error,
  page,
  rowsPerPage,
  totalPages,
  totalResults,
  nextPageToken,
  handleChangePage,
  onChangeRowsPerPage,
  selectedSort,
  onSortChange,
  getCurrentSearchState,
}: CasesResultsTableProps): JSX.Element => {
  const router = useRouter();
  const [sortMenuAnchor, setSortMenuAnchor] = useState<null | HTMLElement>(null);
  const [isExportModalOpen, setIsExportModalOpen] = useState(false);
  const [selectedCases, setSelectedCases] = useState<Set<string>>(new Set());
  const [exportMenuAnchor, setExportMenuAnchor] = useState<null | HTMLElement>(null);
  const [csvData, setCsvData] = useState<{ data: any[]; headers: any[] }>({ data: [], headers: [] });
  const [allFetchedResults, setAllFetchedResults] = useState<Case[]>([]);
  const [isFetchingAll, setIsFetchingAll] = useState(false);
  const [currentPageToken, setCurrentPageToken] = useState<string>("");

  const isSortMenuOpen = Boolean(sortMenuAnchor);

  const handleSortMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setSortMenuAnchor(event.currentTarget);
  };

  const handleSortMenuClose = () => {
    setSortMenuAnchor(null);
  };

  const handleSortSelect = (sortValue: SearchOrderBy) => {
    onSortChange(sortValue);
    handleSortMenuClose();
  };

  const handleCaseClick = (caseItem: Case) => {
    const currentState = getCurrentSearchState();
    saveSearchStateForNavigation(currentState);
    router.push(`/cases?caseId=${caseItem.id}`);
  };

  // Helper functions for data extraction
  const extractCaseId = (caseItem: Case): string => {
    try {
      return caseItem.id || "N/A";
    } catch (error) {
      return "N/A";
    }
  };

  const extractCaseTitle = (caseItem: Case): string => {
    try {
      return caseItem.title || "N/A";
    } catch (error) {
      return "N/A";
    }
  };

  const extractCaseStatus = (caseItem: Case): string => {
    try {
      return getCaseStatusDisplay(caseItem.status);
    } catch (error) {
      return "Unknown";
    }
  };

  const extractCaseType = (caseItem: Case): string => {
    try {
      return getCaseTypeDisplay(caseItem.type);
    } catch (error) {
      return "Unknown";
    }
  };

  const extractCasePriority = (caseItem: Case): string => {
    try {
      const priority = caseItem.priority || 0;
      return `P${priority}`;
    } catch (error) {
      return "P0";
    }
  };

  const extractCaseCreatedTime = (caseItem: Case): string => {
    try {
      if (caseItem.createTime) {
        return new Date(caseItem.createTime).toLocaleDateString();
      }
      return "---";
    } catch (error) {
      return "---";
    }
  };

  // Status display function
  const getCaseStatusDisplay = (status: any): string => {
    switch (status) {
      case "CASE_STATUS_NEW":
      case CaseStatus.NEW:
        return "New";
      case "CASE_STATUS_OPEN":
      case CaseStatus.OPEN:
        return "Open";
      case "CASE_STATUS_UNDER_REVIEW":
      case CaseStatus.UNDER_REVIEW:
        return "Under Review";
      case "CASE_STATUS_INVESTIGATING":
      case CaseStatus.INVESTIGATING:
        return "Investigating";
      case "CASE_STATUS_PENDING_INFORMATION":
      case CaseStatus.PENDING_INFORMATION:
        return "Pending Information";
      case "CASE_STATUS_ON_HOLD":
      case CaseStatus.ON_HOLD:
        return "On Hold";
      case "CASE_STATUS_ESCALATED":
      case CaseStatus.ESCALATED:
        return "Escalated";
      case "CASE_STATUS_RESOLVED":
      case CaseStatus.RESOLVED:
        return "Resolved";
      case "CASE_STATUS_CLOSED":
      case CaseStatus.CLOSED:
        return "Closed";
      case "CASE_STATUS_ARCHIVED":
      case CaseStatus.ARCHIVED:
        return "Archived";
      default:
        return "Unknown";
    }
  };

  // Type display function
  const getCaseTypeDisplay = (type: any): string => {
    switch (type) {
      case "CASE_TYPE_SECURITY_INCIDENT":
      case CaseType.SECURITY_INCIDENT:
        return "Security Incident";
      case "CASE_TYPE_SAFETY_INCIDENT":
      case CaseType.SAFETY_INCIDENT:
        return "Safety Incident";
      case "CASE_TYPE_OPERATIONAL_TASK":
      case CaseType.OPERATIONAL_TASK:
        return "Operational Task";
      case "CASE_TYPE_CUSTOMER_COMPLAINT":
      case CaseType.CUSTOMER_COMPLAINT:
        return "Customer Complaint";
      case "CASE_TYPE_INVESTIGATION":
      case CaseType.INVESTIGATION:
        return "Investigation";
      case "CASE_TYPE_COMPLIANCE_REVIEW":
      case CaseType.COMPLIANCE_REVIEW:
        return "Compliance Review";
      case "CASE_TYPE_INSURANCE_CLAIM":
      case CaseType.INSURANCE_CLAIM:
        return "Insurance Claim";
      case "CASE_TYPE_ADMINISTRATIVE":
      case CaseType.ADMINISTRATIVE:
        return "Administrative";
      case "CASE_TYPE_OTHER":
      case CaseType.OTHER:
        return "Other";
      default:
        return "Unknown";
    }
  };

  // Get the current search state for export
  const currentSearchState = getCurrentSearchState();

  // Fetch all results using the hook
  const { data: allResultsData, isLoading: isExportDataLoading } = useSearchCases(
    {
      $typeName: "hero.cases.v1.SearchCasesRequest",
      query: currentSearchState.searchParams.query ?? "",
      searchFields: [],
      fieldQueries: [],
      status: [],
      type: [],
      priority: [],
      tags: [],
      createdByAssetIds: [],
      updatedByAssetIds: [],
      releaseStatus: [],
      situationIds: [],
      reportIds: [],
      relatedCaseIds: [],
      assetIds: [],
      associationTypes: [],
      entityRefIds: [],
      entityRefTypes: [],
      watcherAssetIds: [],
      createTime: undefined,
      updateTime: undefined,
      dueDate: undefined,
      resolvedTime: undefined,
      closeTime: undefined,
      pageSize: 100,
      pageToken: currentPageToken,
      orderBy: currentSearchState.searchParams.orderBy,
      ascending: currentSearchState.searchParams.ascending,
    },
    {
      enabled: isExportModalOpen && selectedCases.size === 0 && isFetchingAll,
    }
  );

  // Effect to handle fetching all results
  useEffect(() => {
    if (isExportModalOpen && selectedCases.size === 0 && !isFetchingAll) {
      setIsFetchingAll(true);
      setCurrentPageToken("");
      setAllFetchedResults([]);
    }
  }, [isExportModalOpen, selectedCases.size]);

  // Effect to accumulate results
  useEffect(() => {
    if (isFetchingAll && allResultsData?.cases) {
      setAllFetchedResults(prev => [...prev, ...allResultsData.cases]);

      if (allResultsData.nextPageToken) {
        setCurrentPageToken(allResultsData.nextPageToken);
      } else {
        setIsFetchingAll(false);
      }
    }
  }, [allResultsData, isFetchingAll]);

  // Fetch reports for all cases
  const { data: reportsData, isLoading: isReportsLoading } = useBatchGetReports(
    (selectedCases.size > 0
      ? data?.cases?.filter(c => selectedCases.has(c.id))
      : allFetchedResults
    )?.flatMap(case_ => case_.reportIds || []) || [],
    {
      enabled: isExportModalOpen && (
        (selectedCases.size > 0 && (data?.cases?.length ?? 0) > 0) ||
        allFetchedResults.length > 0
      ),
    }
  );

  const formatDate = (date: string | undefined) => {
    if (!date) return "";
    return new Date(date).toLocaleString();
  };

  // Update CSV data when export modal opens or selected cases change
  useEffect(() => {
    if (isExportModalOpen) {
      const result = handleDownloadCsv();
      setCsvData(result);
    }
  }, [isExportModalOpen, selectedCases, allResultsData, allFetchedResults, reportsData, isFetchingAll]);

  const handleDownloadCsv = () => {
    try {
      const headers = [
      { label: "ID", key: "id" },
      { label: "Title", key: "title" },
      { label: "Status", key: "status" },
      { label: "Type", key: "type" },
      { label: "Priority", key: "priority" },
      { label: "Created Date", key: "createdDate" },
      { label: "Updated Date", key: "updatedDate" },
      { label: "Due Date", key: "dueDate" },
      { label: "Resolved Date", key: "resolvedDate" },
      { label: "Closed Date", key: "closedDate" },
      { label: "Tags", key: "tags" },
      { label: "Description", key: "description" },
      { label: "Primary Report ID", key: "primaryReportId" },
      { label: "Primary Report Title", key: "primaryReportTitle" },
      { label: "Primary Report Status", key: "primaryReportStatus" },
      { label: "Primary Report Created Date", key: "primaryReportCreatedDate" },
      { label: "Primary Report Updated Date", key: "primaryReportUpdatedDate" },
      { label: "Primary Report Assigned Date", key: "primaryReportAssignedDate" },
      { label: "Primary Report Completed Date", key: "primaryReportCompletedDate" },
      { label: "Primary Report Version", key: "primaryReportVersion" },
      { label: "Primary Report Type", key: "primaryReportType" },
      { label: "Primary Report Incident Type", key: "primaryReportIncidentType" },
      { label: "Primary Report Location", key: "primaryReportLocation" },
      { label: "Primary Report Involved Agencies", key: "primaryReportInvolvedAgencies" },
      { label: "Primary Report Offences", key: "primaryReportOffences" },
      { label: "Primary Report Incident Start Time", key: "primaryReportIncidentStartTime" },
      { label: "Primary Report Incident End Time", key: "primaryReportIncidentEndTime" },
      { label: "Primary Report Reporter Name", key: "primaryReportReporterName" },
      { label: "Primary Report Reporter Role", key: "primaryReportReporterRole" },
      { label: "Primary Report Reporter Phone", key: "primaryReportReporterPhone" },
      { label: "Primary Report Responders", key: "primaryReportResponders" },
      { label: "Primary Report Related People", key: "primaryReportRelatedPeople" },
      { label: "Primary Report Related Vehicles", key: "primaryReportRelatedVehicles" },
      { label: "Primary Report Related Properties", key: "primaryReportRelatedProperties" },
    ];

    let casesToExport: Case[] = [];

    if (selectedCases.size > 0) {
      // If specific cases are selected, use those from the current page
      const currentPageCases = data?.cases || [];
      casesToExport = Array.from(selectedCases)
        .map(id => currentPageCases.find((c: Case) => c.id === id))
        .filter((c): c is Case => c !== undefined);
    } else {
      // If no cases are selected, use all fetched results
      casesToExport = allFetchedResults;
    }

    const csvData = casesToExport.map((case_) => {
      if (typeof case_ === "string") return {};

      // Find the primary report if it exists
      const primaryReport = case_.reportIds?.length > 0
        ? reportsData?.reports?.find((report: ExtendedReport) =>
          case_.reportIds?.includes(report.id) &&
          report.reportType === ReportType.INCIDENT_PRIMARY
        )
        : undefined;

      // If no primary report found, try to get the first report as fallback
      const fallbackReport = !primaryReport && case_.reportIds?.length > 0
        ? reportsData?.reports?.find((report: ExtendedReport) =>
          case_.reportIds?.includes(report.id)
        )
        : undefined;

      // Use primary report if found, otherwise use fallback
      const report = primaryReport || fallbackReport;

      // Get incident details section
      const rawSections = report?.sections as unknown as RawReportSection[];
      const incidentDetailsSection = rawSections?.find(
        section => section.type === 'SECTION_TYPE_INCIDENT_DETAILS'
      );
      const incidentDetails = incidentDetailsSection?.incidentDetails;

      // Get narrative section
      const narrativeSection = rawSections?.find(
        section => section.type === 'SECTION_TYPE_NARRATIVE'
      );
      const narrative = narrativeSection?.narrative?.richText
        ? stripHtmlTags(narrativeSection.narrative.richText).trim()
        : "---";

      // Get entity list sections
      const peopleSection = rawSections?.find(
        section => section.type === 'SECTION_TYPE_ENTITY_LIST_PEOPLE'
      );
      const vehiclesSection = rawSections?.find(
        section => section.type === 'SECTION_TYPE_ENTITY_LIST_VEHICLE'
      );
      const propertySection = rawSections?.find(
        section => section.type === 'SECTION_TYPE_ENTITY_LIST_PROPERTIES' || section.type === 'SECTION_TYPE_PROPERTY'
      );

      const peopleContent = peopleSection?.entityList;
      const vehiclesContent = vehiclesSection?.entityList;
      const propertiesContent = propertySection?.entityList;

      // Get full location
      const locationParts = [
        incidentDetails?.incidentLocationStreetAddress,
        incidentDetails?.incidentLocationCity,
        incidentDetails?.incidentLocationState,
        incidentDetails?.incidentLocationZipCode
      ].filter(Boolean);
      const fullLocation = locationParts.length > 0 ? locationParts.join(", ") : "---";

      // Get involved agencies
      const involvedAgencies = incidentDetails?.responders?.length
        ? incidentDetails.responders
          .map((responder: RawResponder) => `${responder.displayName} (${responder.role})`)
          .join(", ")
        : "---";

      // Get offences summary
      const offenceSection = rawSections?.find(
        section => section.type === 'SECTION_TYPE_OFFENSE'
      );
      const offences = offenceSection?.offenses
        ? `${offenceSection.metadata?.primaryClassification || "Multiple"} (${offenceSection.offenses.length} offences)`
        : "---";

      return {
        id: case_.id,
        title: case_.title,
        status: case_.status,
        type: case_.type,
        priority: case_.priority,
        createdDate: formatDate(case_.createTime),
        updatedDate: formatDate(case_.updateTime),
        dueDate: formatDate(case_.dueDate),
        resolvedDate: formatDate(case_.resolvedTime),
        closedDate: formatDate(case_.closeTime),
        tags: case_.tags?.join(", ") || "",
        description: case_.description,
        primaryReportId: report?.id || "",
        primaryReportTitle: report?.title || "",
        primaryReportStatus: report?.status || "",
        primaryReportCreatedDate: formatDate(report?.createdAt) || "",
        primaryReportUpdatedDate: formatDate(report?.updatedAt) || "",
        primaryReportAssignedDate: formatDate(report?.assignedAt) || "",
        primaryReportCompletedDate: formatDate(report?.completedAt) || "",
        primaryReportVersion: report?.version || "",
        primaryReportType: report?.reportType || "",
        primaryReportIncidentType: incidentDetails?.finalType || incidentDetails?.initialType || "---",
        primaryReportLocation: fullLocation,
        primaryReportInvolvedAgencies: involvedAgencies,
        primaryReportOffences: offences,
        primaryReportIncidentStartTime: formatDate(incidentDetails?.incidentStartTime) || "---",
        primaryReportIncidentEndTime: formatDate(incidentDetails?.incidentEndTime) || "---",
        primaryReportReporterName: incidentDetails?.reportingPerson ?
          `${incidentDetails.reportingPerson.firstName} ${incidentDetails.reportingPerson.middleName || ''} ${incidentDetails.reportingPerson.lastName}`.trim() :
          "---",
        primaryReportReporterRole: incidentDetails?.reportingPerson?.reporterRole || "---",
        primaryReportReporterPhone: incidentDetails?.reportingPerson?.phoneNumber || "---",
        primaryReportResponders: incidentDetails?.responders?.map((r: RawResponder) => `${r.displayName} (${r.role})`).join(", ") || "---",
        primaryReportRelatedPeople: peopleContent?.entityRefs?.map((e: any) => e.displayName).join(", ") || "---",
        primaryReportRelatedVehicles: vehiclesContent?.entityRefs?.map((e: any) => e.displayName).join(", ") || "---",
        primaryReportRelatedProperties: propertiesContent?.entityRefs?.map((e: any) => e.displayName).join(", ") || "---",
      };
    });

    return { data: csvData, headers };
    } catch (error) {
      Sentry.withScope((scope) => {
        scope.setTag("error_key", "csv-export");
        scope.setTag("export_type", "cases");
        scope.setTag("failure_stage", "data_processing");
        scope.setContext("export_info", {
          selectedCount: selectedCases.size,
          totalFetched: allFetchedResults.length,
          hasReportsData: !!reportsData,
          currentPage: page,
          totalPages: totalPages
        });
        Sentry.captureException(error);
      });
      console.error("Failed to generate CSV data:", error);
      return { data: [], headers: [] };
    }
  };

  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      const newSelected = new Set<string>(data?.cases?.map((caseItem: Case) => caseItem.id) || []);
      setSelectedCases(newSelected);
    } else {
      setSelectedCases(new Set<string>());
    }
  };

  const handleSelectCase = useCallback((caseId: string) => {
    setSelectedCases(prev => {
      const newSelected = new Set(prev);
      if (newSelected.has(caseId)) {
        newSelected.delete(caseId);
      } else {
        newSelected.add(caseId);
      }
      return newSelected;
    });
  }, []);

  // Custom pagination component
  const CustomPagination = () => {
    const getPageNumbers = () => {
      if (totalPages <= 1) {
        return [0];
      }

      const pageNumbers = [];

      if (totalPages <= 7) {
        for (let i = 0; i < totalPages; i++) {
          pageNumbers.push(i);
        }
      } else {
        if (page <= 3) {
          // Near beginning: 1 2 3 4 5 ... 10
          for (let i = 0; i < 5; i++) {
            pageNumbers.push(i);
          }
          pageNumbers.push(-1);
          pageNumbers.push(totalPages - 1);
        } else if (page >= totalPages - 4) {
          // Near end: 1 ... 6 7 8 9 10
          pageNumbers.push(0);
          pageNumbers.push(-1);
          for (let i = totalPages - 5; i < totalPages; i++) {
            pageNumbers.push(i);
          }
        } else {
          // Middle: 1 ... 3 4 5 ... 10
          pageNumbers.push(0);
          pageNumbers.push(-1);
          pageNumbers.push(page - 1);
          pageNumbers.push(page);
          pageNumbers.push(page + 1);
          pageNumbers.push(-2);
          pageNumbers.push(totalPages - 1);
        }
      }

      return pageNumbers;
    };

    const pageNumbers = getPageNumbers();

    if (totalResults === 0) {
      return null;
    }

    return (
      <Box sx={{ display: "flex", justifyContent: "center", mt: 2, mb: 2 }}>
        <Button
          label="Previous"
          style="ghost"
          color="grey"
          onClick={() => {
            if (page > 0 && !isLoading) {
              handleChangePage(page - 1);
            }
          }}
          disabled={page === 0 || isLoading}
          leftIcon={<KeyboardArrowLeftIcon />}
        />

        {pageNumbers.map((pageNum, index) => {
          if (pageNum === -1 || pageNum === -2) {
            return (
              <Box
                key={`ellipsis-${index}`}
                sx={{
                  display: "flex",
                  alignItems: "center",
                  mx: 1,
                }}
              >
                <Typography style="body2" color={colors.grey[500]}>
                  ...
                </Typography>
              </Box>
            );
          }

          return (
            <Box key={pageNum} sx={{ mx: 0.5 }}>
              <Button
                label={(pageNum + 1).toString()}
                style={page === pageNum ? "filled" : "ghost"}
                color="grey"
                prominence={false}
                onClick={() => !isLoading && handleChangePage(pageNum)}
                disabled={isLoading}
              />
            </Box>
          );
        })}

        <Button
          label="Next"
          style="ghost"
          color="grey"
          onClick={() => {
            if (page < totalPages - 1 && !isLoading) {
              handleChangePage(page + 1);
            }
          }}
          disabled={page >= totalPages - 1 || !nextPageToken || isLoading}
          rightIcon={<KeyboardArrowRightIcon />}
        />
      </Box>
    );
  };

  return (
    <Box
      sx={{
        flex: 1,
        display: "flex",
        flexDirection: "column",
        bgcolor: "white",
        borderRadius: 2,
        boxShadow: "none",
        overflow: "hidden",
        border: `1px solid ${colors.grey[200]}`,
      }}
    >
      <Box
        sx={{
          px: "32px",
          py: "24px",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Typography style="caps1" color={colors.grey[900]}>
          RESULTS
        </Typography>
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <Box sx={{ mr: 3, display: "flex", alignItems: "center" }}>
            <Button
              label="Table Sort"
              size="small"
              style={isSortMenuOpen ? "filled" : "ghost"}
              color="grey"
              prominence={isSortMenuOpen ? true : false}
              leftIcon={<SwapVertIcon />}
              rightIcon={<KeyboardArrowDownIcon />}
              onClick={handleSortMenuOpen}
            />
          </Box>
          <Box sx={{ display: "flex", alignItems: "center" }}>
            <Button
              label="Export to CSV"
              size="small"
              style={selectedCases.size > 0 ? "filled" : "ghost"}
              color="grey"
              prominence={selectedCases.size > 0}
              rightIcon={<KeyboardArrowDownIcon />}
              onClick={(e) => {
                const menuAnchor = e.currentTarget;
                setExportMenuAnchor(menuAnchor);
              }}
            />
            <Menu
              anchorEl={exportMenuAnchor}
              open={Boolean(exportMenuAnchor)}
              onClose={() => setExportMenuAnchor(null)}
              PaperProps={{
                sx: {
                  mt: 1,
                  minWidth: 200,
                  borderRadius: 2,
                  border: `1px solid ${colors.grey[200]}`,
                  boxShadow: "0px 4px 16px rgba(0, 0, 0, 0.1)",
                },
              }}
            >
              <MenuItem
                onClick={() => {
                  setExportMenuAnchor(null);
                  setIsExportModalOpen(true);
                }}
                sx={{
                  px: 3,
                  py: 2,
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  "&:hover": {
                    backgroundColor: colors.grey[50],
                  },
                }}
              >
                <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                  <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 22 22" fill="none">
                    <path d="M20.1673 4.74825L9.70815 15.2166L5.82148 11.3299L7.11398 10.0374L9.70815 12.6316L18.8748 3.46492L20.1673 4.74825ZM18.1415 9.36825C18.2607 9.89075 18.334 10.4408 18.334 10.9999C18.334 15.0516 15.0523 18.3333 11.0007 18.3333C6.94898 18.3333 3.66732 15.0516 3.66732 10.9999C3.66732 6.94825 6.94898 3.66659 11.0007 3.66659C12.449 3.66659 13.7873 4.08825 14.924 4.81242L16.244 3.49242C14.759 2.44742 12.9532 1.83325 11.0007 1.83325C5.94065 1.83325 1.83398 5.93992 1.83398 10.9999C1.83398 16.0599 5.94065 20.1666 11.0007 20.1666C16.0607 20.1666 20.1673 16.0599 20.1673 10.9999C20.1673 9.90909 19.9657 8.86408 19.6173 7.89242L18.1415 9.36825Z" fill="#364153" />
                  </svg>
                  <Typography style="body2" color={colors.grey[900]}>
                    Selected Results ({selectedCases.size})
                  </Typography>
                </Box>
              </MenuItem>
              <MenuItem
                onClick={() => {
                  setExportMenuAnchor(null);
                  setIsExportModalOpen(true);
                  setSelectedCases(new Set());
                }}
                sx={{
                  px: 3,
                  py: 2,
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  "&:hover": {
                    backgroundColor: colors.grey[50],
                  },
                }}
              >
                <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                  <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 22 22" fill="none">
                    <path d="M6.42183 5.50008H15.5885L10.996 11.2751L6.42183 5.50008ZM3.901 5.14258C5.75267 7.51675 9.17183 11.9167 9.17183 11.9167V17.4167C9.17183 17.9209 9.58433 18.3334 10.0885 18.3334H11.9218C12.426 18.3334 12.8385 17.9209 12.8385 17.4167V11.9167C12.8385 11.9167 16.2485 7.51675 18.1002 5.14258C18.5677 4.53758 18.1368 3.66675 17.376 3.66675H4.62517C3.86433 3.66675 3.4335 4.53758 3.901 5.14258Z" fill="#4A5565" />
                  </svg>
                  <Typography style="body2" color={colors.grey[900]}>
                    {currentSearchState.searchParams.query ? "All Search Results" : "All Results"}
                  </Typography>
                </Box>
              </MenuItem>
            </Menu>
          </Box>
        </Box>
      </Box>

      <TableContainer
        component={Box}
        sx={{
          flexGrow: 1,
          width: "100%",
          overflowY: "auto",
          overflowX: "auto",
          px: "24px",
        }}
      >
        {isLoading ? (
          <TableSkeleton tableType="Cases" />
        ) : isError ? (
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              height: "100%",
              p: 4,
            }}
          >
            <Box
              sx={{
                bgcolor: colors.rose[50],
                color: colors.rose[700],
                p: 4,
                borderRadius: 1,
              }}
            >
              Error:{" "}
              {error?.message || "An error occurred while fetching cases"}
            </Box>
          </Box>
        ) : (
          <Table
            stickyHeader
            aria-label="cases table"
            sx={{
              tableLayout: "fixed",
              width: "100%",
              minWidth: "800px",
            }}
          >
            <TableHead key="cases-table-header">
              <TableRow key="cases-header-row">
                <TableCell sx={{ width: "48px" }} />
                <TableCell sx={{ width: "15%" }}>
                  <Box sx={{ display: "flex", alignItems: "center" }}>
                    <Typography style="caps3" color={colors.grey[800]}>
                      CASE ID
                    </Typography>
                  </Box>
                </TableCell>
                <TableCell sx={{ width: "30%" }}>
                  <Box sx={{ display: "flex", alignItems: "center" }}>
                    <Typography style="caps3" color={colors.grey[800]}>
                      TITLE
                    </Typography>
                  </Box>
                </TableCell>
                <TableCell sx={{ width: "20%" }}>
                  <Box sx={{ display: "flex", alignItems: "center" }}>
                    <Typography style="caps3" color={colors.grey[800]}>
                      TYPE
                    </Typography>
                  </Box>
                </TableCell>
                <TableCell sx={{ width: "8%" }}>
                  <Box sx={{ display: "flex", alignItems: "center" }}>
                    <Typography style="caps3" color={colors.grey[800]}>
                      PRIORITY
                    </Typography>
                  </Box>
                </TableCell>
                <TableCell sx={{ width: "12%" }}>
                  <Box sx={{ display: "flex", alignItems: "center" }}>
                    <Typography style="caps3" color={colors.grey[800]}>
                      CREATED
                    </Typography>
                  </Box>
                </TableCell>
                <TableCell sx={{ width: "15%" }}>
                  <Box sx={{ display: "flex", alignItems: "center" }}>
                    <Typography style="caps3" color={colors.grey[800]}>
                      STATUS
                    </Typography>
                  </Box>
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody key="cases-table-body">
              {data?.cases && data.cases.length > 0 ? (
                data.cases.map((caseItem: Case) => {
                  const caseId = extractCaseId(caseItem);
                  const title = extractCaseTitle(caseItem);
                  const status = extractCaseStatus(caseItem);
                  const type = extractCaseType(caseItem);
                  const priority = extractCasePriority(caseItem);
                  const createdTime = extractCaseCreatedTime(caseItem);

                  return (
                    <TableRow
                      key={`case-row-${caseId}`}
                      hover
                      onClick={() => handleCaseClick(caseItem)}
                      sx={{ cursor: "pointer", height: "54px" }}
                    >
                      <TableCell padding="checkbox" onClick={(e) => e.stopPropagation()}>
                        <Checkbox
                          checked={selectedCases.has(caseItem.id)}
                          onChange={(e) => {
                            e.stopPropagation();
                            handleSelectCase(caseItem.id);
                          }}
                        />
                      </TableCell>
                      <TableCell>
                        <Box
                          sx={{
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            whiteSpace: "nowrap",
                            maxWidth: "150px"
                          }}
                        >
                          {caseId?.replace(/[^0-9]/g, "").slice(0, 7)}
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Box
                          sx={{
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            whiteSpace: "nowrap",
                            maxWidth: "300px"
                          }}
                        >
                          {title}
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Box
                          sx={{
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            whiteSpace: "nowrap",
                            maxWidth: "200px"
                          }}
                        >
                          {type}
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Box
                          sx={{
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            whiteSpace: "nowrap",
                            maxWidth: "80px"
                          }}
                        >
                          {priority}
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Box
                          sx={{
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            whiteSpace: "nowrap",
                            maxWidth: "120px"
                          }}
                        >
                          {createdTime}
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Box
                          sx={{
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            whiteSpace: "nowrap",
                            maxWidth: "150px"
                          }}
                        >
                          {status}
                        </Box>
                      </TableCell>
                    </TableRow>
                  );
                })
              ) : (
                <TableRow sx={{ height: 300 }}>
                  <TableCell colSpan={7} align="center">
                    <Typography style="body1" color={colors.grey[600]}>
                      {data
                        ? "No cases found. Try adjusting your filters."
                        : "Use the search and filter options to find cases."}
                    </Typography>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        )}
      </TableContainer>

      {/* Show pagination if we have pages to navigate (persists during page navigation) */}
      {(totalPages > 1 || totalResults > 0) && <CustomPagination />}

      <Menu
        anchorEl={sortMenuAnchor}
        open={isSortMenuOpen}
        onClose={handleSortMenuClose}
        PaperProps={{
          sx: {
            mt: 1,
            minWidth: 200,
            borderRadius: 2,
            border: `1px solid ${colors.grey[200]}`,
            boxShadow: "0px 4px 16px rgba(0, 0, 0, 0.1)",
          },
        }}
      >
        {CASES_SORT_OPTIONS.map((option) => (
          <MenuItem
            key={option.value}
            onClick={() => handleSortSelect(option.value)}
            sx={{
              px: 3,
              py: 2,
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              "&:hover": {
                backgroundColor: colors.grey[50],
              },
            }}
          >
            <Typography
              style={selectedSort === option.value ? "body1" : "body2"}
              color={
                selectedSort === option.value
                  ? colors.grey[900]
                  : colors.grey[500]
              }
            >
              {option.label}
            </Typography>
            {selectedSort === option.value && (
              <CheckIcon
                sx={{
                  color: colors.blue[600],
                  fontSize: 20,
                  ml: 2,
                }}
              />
            )}
          </MenuItem>
        ))}
      </Menu>

      <ExportModal
        open={isExportModalOpen}
        onClose={() => setIsExportModalOpen(false)}
        columns={csvData.headers.length}
        rows={selectedCases.size > 0 ? selectedCases.size : allFetchedResults.length}
        fileSize={`${Math.round((selectedCases.size > 0 ? selectedCases.size : allFetchedResults.length) * 0.5)} KB`}
        csvData={csvData.data}
        csvHeaders={csvData.headers}
        isLoading={isExportDataLoading || isFetchingAll || isReportsLoading}
        filename="cases_export.csv"
      />
    </Box>
  );
};

// Memoized export to prevent unnecessary re-renders
export const CasesResultsTable = React.memo(CasesResultsTableComponent); 