'use client';
import { useUserAsset } from "@/app/contexts/User/UserAssetContext";
import { Feature, GetEnabledFeaturesRequest, SetFeatureTargetRequest } from 'proto/hero/featureflags/v1/featureflags_pb';
import { useEffect, useState } from 'react';
import { stringToFeature } from '../apis/services/featureflags/enumConverters';
import { useGetEnabledFeatures, useSetFeatureTarget } from '../apis/services/featureflags/hooks';
import SidebarComponent from '../components/Sidebar';

const FEATURE_DISPLAY_NAMES: Record<Feature, string> = {
  [Feature.UNSPECIFIED]: 'Unspecified',
  [Feature.EXPERIMENTAL_CAMERA]: 'Experimental Camera',
  [Feature.EXPERIMENTAL_PUSH_TO_TALK]: 'Experimental Push to Talk',
  [Feature.EXPERIMENTAL_DEMO]: 'Experimental Demo',
};

const FEATURE_DESCRIPTIONS: Record<Feature, string> = {
  [Feature.UNSPECIFIED]: '',
  [Feature.EXPERIMENTAL_CAMERA]: 'Enable experimental camera features for testing',
  [Feature.EXPERIMENTAL_PUSH_TO_TALK]: 'Enable push-to-talk functionality',
  [Feature.EXPERIMENTAL_DEMO]: 'Enable demo mode features',
};

interface Toast {
  id: string;
  message: string;
  type: 'enabled' | 'disabled' | 'error';
}

export default function FeatureFlagsPage() {
  const { asset: dispatcherAsset } = useUserAsset();
  const [enabledFeatures, setEnabledFeatures] = useState<Set<Feature>>(new Set());
  const [toasts, setToasts] = useState<Toast[]>([]);

  const orgId = dispatcherAsset?.orgId || 0;
  const assetId = dispatcherAsset?.id || '';

  const { data: featuresData, isLoading } = useGetEnabledFeatures(
    {
      orgId,
      assetId: assetId,
    } as GetEnabledFeaturesRequest,
    {
      enabled: orgId > 0 && !!assetId,
      refetchInterval: 5000,
    }
  );

  const addToast = (message: string, type: 'enabled' | 'disabled' | 'error') => {
    const id = Date.now().toString();
    setToasts(prev => [...prev, { id, message, type }]);
    // Auto-remove toast after 3 seconds
    setTimeout(() => {
      setToasts(prev => prev.filter(t => t.id !== id));
    }, 3000);
  };

  const setFeatureTargetMutation = useSetFeatureTarget({
    onSuccess: (_, variables) => {
      const featureName = FEATURE_DISPLAY_NAMES[variables.feature];
      const action = variables.enabled ? 'enabled' : 'disabled';
      addToast(`${featureName} ${action}`, variables.enabled ? 'enabled' : 'disabled');
    },
    onError: (_, variables) => {
      // Revert the optimistic update
      setEnabledFeatures(prev => {
        const newSet = new Set(prev);
        if (variables.enabled) {
          newSet.delete(variables.feature);
        } else {
          newSet.add(variables.feature);
        }
        return newSet;
      });
      addToast('Failed to update feature flag', 'error');
    }
  });

  useEffect(() => {
    if (featuresData) {
      // Convert string feature names from API to Feature enum values
      const features = (featuresData.enabledFeatures || []) as any[];
      const enabledFeatureEnums = features
        .map((featureString) => {
          // The API returns strings like "FEATURE_EXPERIMENTAL_CAMERA"
          if (typeof featureString === 'string') {
            return stringToFeature(featureString);
          }
          // If it's already an enum value, return it
          return featureString as Feature;
        })
        .filter((f) => f !== Feature.UNSPECIFIED); // Filter out any unrecognized features

      setEnabledFeatures(new Set(enabledFeatureEnums));
    }
  }, [featuresData]);

  const handleToggleFeature = async (feature: Feature, enabled: boolean) => {
    // Optimistic update
    setEnabledFeatures(prev => {
      const newSet = new Set(prev);
      if (enabled) {
        newSet.add(feature);
      } else {
        newSet.delete(feature);
      }
      return newSet;
    });

    // Make the API call
    setFeatureTargetMutation.mutate({
      orgId,
      feature,
      assetId: assetId,
      enabled,
    } as SetFeatureTargetRequest);
  };

  const availableFeatures = Object.values(Feature).filter(
    (f) => typeof f === 'number' && f !== Feature.UNSPECIFIED
  ) as Feature[];

  return (
    <div className="flex w-full h-screen bg-gray-50">
      <SidebarComponent />
      <main className="flex-1 ml-[75px] flex flex-col overflow-hidden">
        <div className="bg-white shadow-sm border-b">
          <div className="px-6 py-4">
            <div className="text-2xl font-semibold text-gray-900">Feature Flags</div>
            <p className="mt-1 text-sm text-gray-600">
              Manage experimental features for specific assets
            </p>
          </div>
        </div>

        <div className="flex-1 overflow-auto bg-gray-50">
          <div className="max-w-4xl mx-auto p-6">
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Current Session Asset ID
              </label>
              <div className="flex items-center">
                <div className="px-3 py-2 bg-gray-100 border border-gray-300 rounded-md text-gray-700">
                  {assetId || 'Loading...'}
                </div>
              </div>
            </div>

            <div className="bg-white shadow rounded-lg">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">Available Features</h2>
              </div>

              {isLoading && !featuresData ? (
                <div className="p-6 text-center text-gray-500">Loading features...</div>
              ) : (
                <div className="divide-y divide-gray-200">
                  {availableFeatures.map((feature) => (
                    <div key={feature} className="px-6 py-4">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <h3 className="text-sm font-medium text-gray-900">
                            {FEATURE_DISPLAY_NAMES[feature]}
                          </h3>
                          {FEATURE_DESCRIPTIONS[feature] && (
                            <p className="mt-1 text-sm text-gray-500">
                              {FEATURE_DESCRIPTIONS[feature]}
                            </p>
                          )}
                        </div>
                        <div className="ml-4">
                          <button
                            onClick={() => handleToggleFeature(feature, !enabledFeatures.has(feature))}
                            className={`
                              relative inline-flex h-6 w-11 items-center rounded-full cursor-pointer
                              transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2
                              ${enabledFeatures.has(feature) ? 'bg-indigo-600' : 'bg-gray-200'}
                            `}
                          >
                            <span
                              className={`
                                inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200
                                ${enabledFeatures.has(feature) ? 'translate-x-6' : 'translate-x-1'}
                              `}
                            />
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

          </div>
        </div>
      </main>

      {/* Toast Container - Centered */}
      <div className="fixed top-4 left-1/2 transform -translate-x-1/2 space-y-2 z-50">
        {toasts.map(toast => (
          <div
            key={toast.id}
            className={`
              px-6 py-3 rounded-full shadow-lg text-white text-sm font-medium
              transform transition-all duration-300 ease-in-out animate-in fade-in slide-in-from-top-2
              ${toast.type === 'enabled' ? 'bg-green-600' : ''}
              ${toast.type === 'disabled' ? 'bg-gray-600' : ''}
              ${toast.type === 'error' ? 'bg-red-600' : ''}
            `}
          >
            <div className="flex items-center space-x-2">
              {toast.type === 'enabled' && (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              )}
              {toast.type === 'disabled' && (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              )}
              {toast.type === 'error' && (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              )}
              <span>{toast.message}</span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}