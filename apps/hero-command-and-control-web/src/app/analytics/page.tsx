"use client";

import { Box, Tab, Tabs } from "@mui/material";
import { useState } from "react";
import { Typography } from "../../design-system/components/Typography";
import { colors } from "../../design-system/tokens";
import Sidebar from "../components/Sidebar";
import { useBreadcrumbHeader } from "../hooks/useBreadcrumbHeader";
import { CaseOverviewAnalytics } from "./components/cases/CaseOverviewAnalytics";
import { CaseTimeAnalytics } from "./components/cases/CaseTimeAnalytics";
import { CaseTypeAnalytics } from "./components/cases/CaseTypeAnalytics";
import { IncidentOverviewAnalytics } from "./components/incidents/IncidentOverviewAnalytics";
import { IncidentTimeAnalytics } from "./components/incidents/IncidentTimeAnalytics";
import { IncidentTypeAnalytics } from "./components/incidents/IncidentTypeAnalytics";
import { CallOverviewAnalytics, CallFrequencyHeatmap } from "./components/calls";
import type { DateRangeOption } from "./types";
import { useAllCalls } from "./hooks/useAllCalls";
import { create } from "@bufbuild/protobuf";
import { ListCallsRequestSchema } from "proto/hero/communications/v1/conversation_pb";
import { useMemo } from "react";

export default function AnalyticsPage() {
  const [activeTab, setActiveTab] = useState(0);
  const [incidentSelectedRange, setIncidentSelectedRange] = useState<DateRangeOption>("7d");
  const [caseSelectedRange, setCaseSelectedRange] = useState<DateRangeOption>("7d");
  const [callSelectedRange, setCallSelectedRange] = useState<DateRangeOption>("7d");
  const [heatmapSelectedRange, setHeatmapSelectedRange] = useState<DateRangeOption>("7d");

  useBreadcrumbHeader({
    id: "analytics",
    label: "Analytics",
    path: "/analytics",
  });

  const tabs = ["Calls", "Incidents", "Cases", "Reports", "Staff"];

  const handleTabChange = (_: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  // Create request for heatmap data based on selected range
  const heatmapRequest = useMemo(() => {
    const endDate = new Date();
    const startDate = new Date();
    
    // Calculate start date based on selected range
    switch (heatmapSelectedRange) {
      case "7d":
        startDate.setDate(startDate.getDate() - 7);
        break;
      case "1m":
        startDate.setMonth(startDate.getMonth() - 1);
        break;
      case "3m":
        startDate.setMonth(startDate.getMonth() - 3);
        break;
      default:
        startDate.setDate(startDate.getDate() - 7);
    }

    return create(ListCallsRequestSchema, {
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      page: 1,
      pageSize: 100,
      sortOrder: "desc",
    });
  }, [heatmapSelectedRange]);

  // Fetch all calls for heatmap
  const { data: heatmapData, isLoading: heatmapLoading } = useAllCalls(heatmapRequest);

  return (
    <Box
      sx={{
        display: "flex",
        height: "100vh",
        width: "100vw",
        bgcolor: "#f4f5f7",
        overflow: "hidden",
      }}
    >
      <Sidebar />
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          height: "100vh",
          width: "100%",
          bgcolor: "white",
          marginLeft: "40px",
          overflow: "hidden",
        }}
      >
        {/* Header Section */}
        <Box sx={{ mb: "5px", px: "40px", pt: "20px" }}>
          <Typography style="h1" color={colors.grey[900]}>
            Analytics
          </Typography>
        </Box>

        {/* Tabs */}
        <Box sx={{ position: "relative" }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            sx={{
              px: "40px",
              "& .MuiTabs-indicator": {
                backgroundColor: "#2563EB",
                height: "3px",
                zIndex: 2,
              },
              "& .MuiTab-root": {
                textTransform: "none",
                fontSize: "14px",
                fontWeight: 500,
                color: colors.grey[500],
                minHeight: "48px",
                "&.Mui-selected": {
                  color: colors.grey[900],
                  fontWeight: 600,
                },
              },
              "& .MuiTabs-flexContainer": {
                position: "relative",
              },
              "&:before": {
                content: '""',
                position: "absolute",
                bottom: 0,
                left: 0,
                right: 0,
                height: "1px",
                backgroundColor: colors.grey[200],
                zIndex: 1,
              },
            }}
          >
            {tabs.map((tab) => (
              <Tab key={tab} label={tab} />
            ))}
          </Tabs>
        </Box>

        {/* Analytics Content */}
        <Box sx={{ px: "32px", py: "24px", flex: 1, overflow: "auto" }}>
          {/* Calls tab */}
          <Box sx={{ display: activeTab === 0 ? "block" : "none" }}>
            <CallOverviewAnalytics
              selectedRange={callSelectedRange}
              onRangeChange={setCallSelectedRange}
            />
            <Box sx={{ mt: "24px" }}>
              <CallFrequencyHeatmap
                calls={heatmapData?.calls || []}
                isLoading={heatmapLoading}
                selectedRange={heatmapSelectedRange}
                onRangeChange={setHeatmapSelectedRange}
              />
            </Box>
          </Box>

          {/* Incidents tab */}
          <Box sx={{ display: activeTab === 1 ? "block" : "none" }}>
            <IncidentOverviewAnalytics
              selectedRange={incidentSelectedRange}
              onRangeChange={setIncidentSelectedRange}
            />
            <IncidentTypeAnalytics />
            <IncidentTimeAnalytics />
          </Box>

          {/* Cases tab */}
          <Box sx={{ display: activeTab === 2 ? "block" : "none" }}>
            <CaseOverviewAnalytics
              selectedRange={caseSelectedRange}
              onRangeChange={setCaseSelectedRange}
            />
            <CaseTypeAnalytics />
            <CaseTimeAnalytics />
          </Box>

          {/* Other tabs placeholder */}
          {activeTab !== 0 && activeTab !== 1 && activeTab !== 2 && (
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                justifyContent: "center",
                height: "400px",
                border: `2px dashed ${colors.grey[300]}`,
                borderRadius: "12px",
                bgcolor: colors.grey[50],
              }}
            >
              <Box sx={{ mb: "8px" }}>
                <Typography style="h3" color={colors.grey[500]}>
                  {tabs[activeTab]} Analytics
                </Typography>
              </Box>
              <Typography style="body2" color={colors.grey[400]}>
                Coming soon - analytics for {tabs[activeTab].toLowerCase()} will
                be available here
              </Typography>
            </Box>
          )}
        </Box>
      </Box>
    </Box>
  );
}
