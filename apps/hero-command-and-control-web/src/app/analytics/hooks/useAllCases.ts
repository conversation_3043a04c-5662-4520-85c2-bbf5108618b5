import { useState, useEffect, useMemo, useRef, useCallback } from 'react';
import { useSearchCases } from '../../apis/services/workflow/cases/hooks';
import { SearchCasesRequest } from 'proto/hero/cases/v1/cases_pb';

export const useAllCases = (baseRequest: SearchCasesRequest) => {
  const [allCases, setAllCases] = useState<any[]>([]);
  const [isLoadingAll, setIsLoadingAll] = useState(false);
  const [totalResults, setTotalResults] = useState(0);
  const [requestKey, setRequestKey] = useState(0); // Force re-fetch when this changes
  
  // Use refs to track current state without causing re-renders
  const currentPageTokenRef = useRef<string | undefined>(undefined);
  const isFirstPageRef = useRef(true);

  // Create a stable key for the base request to detect changes
  const baseRequestKey = useMemo(() => {
    return JSON.stringify({
      query: baseRequest.query || "",
      from: baseRequest.createTime?.from,
      to: baseRequest.createTime?.to,
    });
  }, [baseRequest.query, baseRequest.createTime?.from, baseRequest.createTime?.to]);

  // Create the current request
  const currentRequest = useMemo(() => {
    const request = { ...baseRequest };
    request.pageSize = 500; // Use maximum page size for efficiency
    if (currentPageTokenRef.current) {
      request.pageToken = currentPageTokenRef.current;
    } else {
      delete (request as any).pageToken; // Remove pageToken for first page
    }
    return request;
  }, [baseRequest, requestKey]); // requestKey forces new request when we need to paginate

  const { data, isLoading, error } = useSearchCases(currentRequest as any);

  // Reset when base request changes
  const resetPagination = useCallback(() => {
    setAllCases([]);
    setTotalResults(0);
    setIsLoadingAll(true);
    currentPageTokenRef.current = undefined;
    isFirstPageRef.current = true;
    setRequestKey(0); // Reset to trigger first page fetch
  }, []);

  useEffect(() => {
    resetPagination();
  }, [baseRequestKey, resetPagination]);

  // Handle data fetching and pagination
  useEffect(() => {
    if (!data) return;

    setIsLoadingAll(true);
    
    // If this is the first page, reset the accumulated data
    if (isFirstPageRef.current) {
      setAllCases(data.cases || []);
      setTotalResults(data.cases?.length || 0);
      isFirstPageRef.current = false;
    } else {
      // Append new cases to existing ones
      setAllCases(prev => [...prev, ...(data.cases || [])]);
    }

    // Check if there are more pages to fetch
    if (data.nextPageToken && data.cases?.length > 0) {
      // Set up for next page fetch
      currentPageTokenRef.current = data.nextPageToken;
      setRequestKey(prev => prev + 1); // Trigger next page fetch
    } else {
      // All pages have been fetched
      currentPageTokenRef.current = undefined;
      setIsLoadingAll(false);
    }
  }, [data]);

  return {
    data: {
      cases: allCases,
      totalResults,
    },
    isLoading: isLoading || isLoadingAll,
    error,
  };
};