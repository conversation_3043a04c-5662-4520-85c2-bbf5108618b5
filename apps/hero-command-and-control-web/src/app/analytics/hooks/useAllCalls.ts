import { useState, useEffect, useMemo, useRef, useCallback } from 'react';
import { useListCalls } from '../../apis/services/communications/cellularcall/hooks';
import { ListCallsRequest } from 'proto/hero/communications/v1/conversation_pb';

export const useAllCalls = (baseRequest: ListCallsRequest) => {
  const [allCalls, setAllCalls] = useState<any[]>([]);
  const [isLoadingAll, setIsLoadingAll] = useState(false);
  const [totalResults, setTotalResults] = useState(0);
  const [requestKey, setRequestKey] = useState(0); // Force re-fetch when this changes
  
  // Use refs to track current state without causing re-renders
  const currentPageRef = useRef<number>(1);
  const isFirstPageRef = useRef(true);

  // Create a stable key for the base request to detect changes
  const baseRequestKey = useMemo(() => {
    return JSON.stringify({
      startDate: baseRequest.startDate || "",
      endDate: baseRequest.endDate || "",
      state: baseRequest.state || "",
      direction: baseRequest.direction || "",
      sortOrder: baseRequest.sortOrder || "",
    });
  }, [baseRequest.startDate, baseRequest.endDate, baseRequest.state, baseRequest.direction, baseRequest.sortOrder]);

  // Create the current request
  const currentRequest = useMemo(() => {
    const request = { ...baseRequest };
    request.pageSize = 100; // Use maximum page size for efficiency
    request.page = currentPageRef.current;
    return request;
  }, [baseRequest, requestKey]); // requestKey forces new request when we need to paginate

  const { data, isLoading, error } = useListCalls(currentRequest);

  // Reset when base request changes
  const resetPagination = useCallback(() => {
    setAllCalls([]);
    setTotalResults(0);
    setIsLoadingAll(true);
    currentPageRef.current = 1;
    isFirstPageRef.current = true;
    setRequestKey(0); // Reset to trigger first page fetch
  }, []);

  useEffect(() => {
    resetPagination();
  }, [baseRequestKey, resetPagination]);

  // Handle data fetching and pagination
  useEffect(() => {
    if (!data) return;

    setIsLoadingAll(true);
    
    // If this is the first page, reset the accumulated data
    if (isFirstPageRef.current) {
      setAllCalls(data.calls || []);
      setTotalResults(data.totalCount || 0);
      isFirstPageRef.current = false;
    } else {
      // Append new calls to existing ones
      setAllCalls(prev => [...prev, ...(data.calls || [])]);
    }

    // Check if there are more pages to fetch
    if (data.nextPage && data.calls?.length > 0) {
      // Set up for next page fetch
      currentPageRef.current = data.nextPage;
      setRequestKey(prev => prev + 1); // Trigger next page fetch
    } else {
      // All pages have been fetched
      setIsLoadingAll(false);
    }
  }, [data]);

  return {
    data: {
      calls: allCalls,
      totalResults,
    },
    isLoading: isLoading || isLoadingAll,
    error,
  };
};