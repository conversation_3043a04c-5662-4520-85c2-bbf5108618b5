// Incident-specific constants for analytics components

/**
 * Tab options for incident time analytics
 */
export const INCIDENT_TIME_TABS = ["Incidents", "Response Time", "Resolution Time"];

/**
 * Priority options for incident analytics
 */
export const INCIDENT_PRIORITY_OPTIONS = [
  { value: 1, label: "Priority 1 (Low)" },
  { value: 2, label: "Priority 2" },
  { value: 3, label: "Priority 3" }, 
  { value: 4, label: "Priority 4" },
  { value: 5, label: "Priority 5 (Critical)" },
];

/**
 * Mock metrics data structure for overview analytics
 */
export const MOCK_INCIDENT_METRICS = {
  cleryIncidents: { current: 12, previous: 11 },
  nibrsIncidents: { current: 38, previous: 34 },
};