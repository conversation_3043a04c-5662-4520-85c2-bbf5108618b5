"use client";

import { <PERSON>, Card } from "@mui/material";
import {
  DateRange,
  SearchSituationsRequest,
} from "proto/hero/situations/v2/situations_pb";
import { useMemo } from "react";
import { Typography } from "../../../../design-system/components/Typography";
import { colors } from "../../../../design-system/tokens";
import { incidentTypes } from "../../../cad/components/ActionPane/constants";
import { useAllSituations } from "../../hooks/useAllSituations";
import type { DateRangeOption } from "../../types";
import { AnalyticsCard } from "../common/AnalyticsCard";
import { DateRangePicker } from "../common/DateRangePicker";
import {
  DATE_RANGE_OPTIONS,
  getDateRange,
  getPreviousDateRange,
  getComparisonText,
  formatIncidentTimeFromMinutes,
} from "../common/analyticsUtils";
import { MOCK_INCIDENT_METRICS } from "./constants";

interface IncidentOverviewAnalyticsProps {
  selectedRange: DateRangeOption;
  onRangeChange: (range: DateRangeOption) => void;
}

export const IncidentOverviewAnalytics = ({
  selectedRange,
  onRangeChange,
}: IncidentOverviewAnalyticsProps) => {
  // Calculate date ranges using shared utilities
  const dateRanges = useMemo(() => {
    return {
      current: getDateRange(selectedRange) as DateRange,
      previous: getPreviousDateRange(selectedRange) as DateRange,
    };
  }, [selectedRange]);

  // Current period data
  const currentPeriodRequest = useMemo(
    () =>
      ({
        query: "",
        createTime: dateRanges.current,
      } as SearchSituationsRequest),
    [dateRanges.current]
  );

  // Previous period data
  const previousPeriodRequest = useMemo(
    () =>
      ({
        query: "",
        createTime: dateRanges.previous,
      } as SearchSituationsRequest),
    [dateRanges.previous]
  );

  const { data: currentData, isLoading: currentLoading } =
    useAllSituations(currentPeriodRequest);

  const { data: previousData, isLoading: previousLoading } = useAllSituations(
    previousPeriodRequest
  );

  const isLoading = currentLoading || previousLoading;

  // Calculate real metrics from API data
  const currentTotal = currentData?.totalResults || 0;
  const previousTotal = previousData?.totalResults || 0;

  // Calculate most prevalent incident type
  const getMostPrevalentType = (data: any) => {
    if (!data?.situations?.length) return "N/A";

    const typeCounts: { [key: string]: number } = {};
    data.situations.forEach((situation: any) => {
      const typeStr = String(situation.type);
      typeCounts[typeStr] = (typeCounts[typeStr] || 0) + 1;
    });

    const maxCount = Math.max(...Object.values(typeCounts));
    const mostPrevalentTypeStr = Object.keys(typeCounts).find(
      (key) => typeCounts[key] === maxCount
    );

    if (!mostPrevalentTypeStr) return "Other";

    // Find the label from the constants
    const incidentType = incidentTypes.find(
      (type) => type.value === mostPrevalentTypeStr
    );
    return incidentType?.label || "Other";
  };

  // Calculate average response time (from dispatch to resolution)
  const calculateAvgResponseTime = (data: any) => {
    if (!data?.situations?.length) return 0;

    let totalResponseMinutes = 0;
    let count = 0;

    data.situations.forEach((situation: any) => {
      if (situation.statusUpdates?.length > 0) {
        // Find the dispatching timestamp
        const dispatchUpdate = situation.statusUpdates.find(
          (update: any) => update.newStatus === "SITUATION_STATUS_DISPATCHING"
        );

        // Find the resolved timestamp
        const resolvedUpdate = situation.statusUpdates.find(
          (update: any) => update.newStatus === "SITUATION_STATUS_RESOLVED"
        );

        if (dispatchUpdate && resolvedUpdate) {
          const dispatchTime = new Date(dispatchUpdate.timestamp);
          const resolvedTime = new Date(resolvedUpdate.timestamp);
          const diffMinutes =
            (resolvedTime.getTime() - dispatchTime.getTime()) / (1000 * 60);

          if (diffMinutes > 0) {
            totalResponseMinutes += diffMinutes;
            count++;
          }
        }
      }
    });

    if (count === 0) return 0;
    return totalResponseMinutes / count;
  };

  // Calculate average resolution time for resolved incidents
  const calculateAvgResolutionTime = (data: any) => {
    if (!data?.situations?.length) return 0;

    let totalResolutionMinutes = 0;
    let count = 0;

    data.situations.forEach((situation: any) => {
      if (situation.resolvedTime && situation.createTime) {
        const createTime = new Date(situation.createTime);
        const resolvedTime = new Date(situation.resolvedTime);
        const diffMinutes =
          (resolvedTime.getTime() - createTime.getTime()) / (1000 * 60);
        if (diffMinutes > 0) {
          totalResolutionMinutes += diffMinutes;
          count++;
        }
      }
    });

    if (count === 0) return 0;
    return totalResolutionMinutes / count;
  };

  const currentMostPrevalentType = getMostPrevalentType(currentData);
  const previousMostPrevalentType = getMostPrevalentType(previousData);
  const currentAvgResponseTime = calculateAvgResponseTime(currentData);
  const currentAvgResolutionTime = calculateAvgResolutionTime(currentData);

  const previousAvgResponseTime = calculateAvgResponseTime(previousData);
  const previousAvgResolutionTime = calculateAvgResolutionTime(previousData);

  // Now using shared parseTimeString utility

  // Using shared mock metrics

  // Now using shared utilities for comparison text and date range options

  return (
    <Card
      sx={{
        bgcolor: colors.grey[50],
        border: `1px solid ${colors.grey[300]}`,
        borderRadius: "12px",
        p: "24px",
        boxShadow: "none",
      }}
    >
      {/* Incident Overview Header */}
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: "24px",
        }}
      >
        <Typography style="h2" color={colors.grey[900]}>
          <span style={{ fontSize: "20px", fontWeight: 600 }}>
            Incident Overview
          </span>
        </Typography>

        {/* Date Range Filter */}
        <DateRangePicker
          value={selectedRange}
          onChange={onRangeChange}
          options={DATE_RANGE_OPTIONS}
        />
      </Box>

      {/* Analytics Cards Grid */}
      <Box
        sx={{
          display: "grid",
          gridTemplateColumns: {
            xs: "1fr",
            sm: "repeat(2, 1fr)",
            lg: "repeat(3, 1fr)",
          },
          gap: "24px",
        }}
      >
        <AnalyticsCard
          title="Total Incidents"
          currentValue={currentTotal}
          previousValue={previousTotal}
          isLoading={isLoading}
          comparisonText={getComparisonText(selectedRange)}
        />
        <AnalyticsCard
          title="Most Prevalent Incident Type"
          currentValue={currentMostPrevalentType}
          previousValue={previousMostPrevalentType}
          isLoading={isLoading}
          comparisonText={getComparisonText(selectedRange)}
          showPreviousValueForStrings={true}
        />
        <AnalyticsCard
          title="CLERY Incidents"
          currentValue={MOCK_INCIDENT_METRICS.cleryIncidents.current}
          previousValue={MOCK_INCIDENT_METRICS.cleryIncidents.previous}
          isLoading={isLoading}
          comparisonText={getComparisonText(selectedRange)}
        />
        <AnalyticsCard
          title="Average Incident Response Time"
          currentValue={currentAvgResponseTime}
          previousValue={previousAvgResponseTime}
          isLoading={isLoading}
          comparisonText={getComparisonText(selectedRange)}
          formatValue={formatIncidentTimeFromMinutes}
        />
        <AnalyticsCard
          title="Average Incident Resolution Time"
          currentValue={currentAvgResolutionTime}
          previousValue={previousAvgResolutionTime}
          isLoading={isLoading}
          comparisonText={getComparisonText(selectedRange)}
          formatValue={formatIncidentTimeFromMinutes}
        />
        <AnalyticsCard
          title="NIBRS Incidents"
          currentValue={MOCK_INCIDENT_METRICS.nibrsIncidents.current}
          previousValue={MOCK_INCIDENT_METRICS.nibrsIncidents.previous}
          isLoading={isLoading}
          comparisonText={getComparisonText(selectedRange)}
        />
      </Box>
    </Card>
  );
};
