"use client";

import CheckIcon from "@mui/icons-material/Check";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import { Box, Card, Menu, MenuItem, Tab, Tabs } from "@mui/material";
import {
  DateRange,
  SearchSituationsRequest,
} from "proto/hero/situations/v2/situations_pb";
import { useEffect, useMemo, useState } from "react";
import { Button } from "../../../../design-system/components/Button";
import { Typography } from "../../../../design-system/components/Typography";
import { colors } from "../../../../design-system/tokens";
import { incidentTypes } from "../../../cad/components/ActionPane/constants";
import { SITUATION_TYPE_OPTIONS } from "../../../search/utils/constants";
import { useAllSituations } from "../../hooks/useAllSituations";
import type { DateRangeOption } from "../../types";
import { DateRangePicker } from "../common/DateRangePicker";
import { BarChartHorizontal } from "../common/charts/BarChartHorizontal";
import { BarChartVertical } from "../common/charts/BarChartVertical";
import { LineChart } from "../common/charts/LineChart";
import { PieChart } from "../common/charts/PieChart";
import { ScatterChart } from "../common/charts/ScatterChart";
import { IncidentAnalyticsFilters } from "./IncidentAnalyticsFilters";
import { IncidentAnalyticsTable } from "./IncidentAnalyticsTable";
import IncidentHeatmapComponent from "./IncidentHeatmapComponent";
import {
  exportToCSV,
  exportChartAsImage,
  exportHeatmapAsImage,
  isDownloadDisabled,
  shouldShowDownload,
  DATE_RANGE_OPTIONS,
  useDownloadState,
} from "../common/analyticsUtils";
import {
  GraphIcon,
  HeatViewIcon,
  BubbleViewIcon,
  BarChartHorizontalIcon,
  BarChartVerticalIcon,
  PieChartIcon,
  ScatterplotIcon,
  LineChartIcon,
  TableIcon,
  DownloadIcon,
  HeatMapIcon,
} from "../common/analyticsIcons";

export const IncidentTypeAnalytics = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [viewType, setViewType] = useState<"graph" | "table" | "heatmap">(
    "graph"
  );
  
  // Download loading state
  const { isDownloading, withLoadingState } = useDownloadState();
  const [chartType, setChartType] = useState<
    "bar-horizontal" | "bar-vertical" | "pie" | "scatter" | "line"
  >("bar-horizontal");
  const [heatmapViewType, setHeatmapViewType] = useState<"heat" | "bubble">(
    "heat"
  );
  const [selectedTypes, setSelectedTypes] = useState<string[]>([]);
  const [selectedPriorities, setSelectedPriorities] = useState<number[]>([]);
  const [selectedDateRange, setSelectedDateRange] =
    useState<DateRangeOption>("7d");
  const [customDateRange, setCustomDateRange] = useState<
    [Date | null, Date | null]
  >([null, null]);
  const [viewMenuAnchor, setViewMenuAnchor] = useState<null | HTMLElement>(
    null
  );
  const [chartMenuAnchor, setChartMenuAnchor] = useState<null | HTMLElement>(
    null
  );
  const [heatmapViewMenuAnchor, setHeatmapViewMenuAnchor] =
    useState<null | HTMLElement>(null);

  // Table selection state
  const [selectedTableRows, setSelectedTableRows] = useState<Set<string>>(
    new Set()
  );

  // Clear table selection when view type changes
  useEffect(() => {
    setSelectedTableRows(new Set());
  }, [viewType]);

  // Using shared CSV export utility

  // Using shared download disabled check utility

  // Using shared download visibility utility

  // Using shared chart image export utility

  // Using shared heatmap image export utility

  // Main download handler
  const handleDownload = () => {
    withLoadingState(async () => {
    const timestamp = new Date().toISOString().split("T")[0];

    switch (viewType) {
      case "graph": {
        if (dataset.length === 0) {
          alert("No data to export");
          return;
        }

        const chartTypeLabel =
          chartType === "bar-horizontal"
            ? "BarChart"
            : chartType === "bar-vertical"
            ? "BarChart"
            : chartType === "pie"
            ? "PieChart"
            : chartType === "scatter"
            ? "ScatterChart"
            : chartType === "line"
            ? "LineChart"
            : "Chart";

        const csvData = dataset.map((item) => ({
          "Incident Type": item.type,
          Count: item.count,
        }));

        await exportChartAsImage(
          `incident-analytics-${chartTypeLabel.toLowerCase()}-${timestamp}.png`,
          csvData,
          "[data-chart-container]"
        );
        break;
      }

      case "table": {
        if (selectedTableRows.size === 0) {
          alert("No data to export");
          return;
        }

        const selectedSituations = filteredSituations.filter((situation: any) =>
          selectedTableRows.has(situation.id)
        );

        const tableData = selectedSituations.map((situation: any) => {
          const typeStr = String(situation.type);
          const incidentType = incidentTypes.find(
            (type) => type.value === typeStr
          );

          return {
            ID: situation.id || "N/A",
            Type: incidentType?.label || "Unknown Type",
            Priority: situation.priority || "N/A",
            Status: situation.status || "N/A",
            Created: situation.createTime
              ? new Date(situation.createTime).toLocaleString()
              : "N/A",
            Location: situation.location?.description || "N/A",
            Description: situation.description || "N/A",
          };
        });

        exportToCSV(tableData, `incident-analytics-table-${timestamp}.csv`);
        break;
      }

      case "heatmap":
        await exportHeatmapAsImage(
          `incident-analytics-heatmap-${heatmapViewType}-${timestamp}.png`
        );
        break;

      default:
        alert("Unknown view type");
    }
    });
  };

  // Helper function to get the current view type details
  const getViewTypeDetails = () => {
    switch (viewType) {
      case "graph":
        return { label: "Graph", icon: <GraphIcon /> };
      case "table":
        return { label: "Table", icon: <TableIcon /> };
      case "heatmap":
        return { label: "Heat map", icon: <HeatMapIcon /> };
      default:
        return { label: "Graph", icon: <GraphIcon /> };
    }
  };

  // Helper function to get the current heatmap view type details
  const getHeatmapViewTypeDetails = () => {
    switch (heatmapViewType) {
      case "heat":
        return { label: "Color Heat View", icon: <HeatViewIcon /> };
      case "bubble":
        return { label: "Bubble View", icon: <BubbleViewIcon /> };
      default:
        return { label: "Color Heat View", icon: <HeatViewIcon /> };
    }
  };

  const tabs = ["Incident Type"];

  const handleTabChange = (_: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  // Using shared date range options

  // Calculate date range for the chart's own data
  const dateRange = useMemo(() => {
    const now = new Date();

    // If custom date range is set, use it instead of predefined options
    if (customDateRange[0] && customDateRange[1]) {
      return {
        from: customDateRange[0].toISOString(),
        to: customDateRange[1].toISOString(),
      } as DateRange;
    }

    // Otherwise use predefined date range
    const end = new Date(now);
    const start = new Date(now);

    switch (selectedDateRange) {
      case "1h":
        start.setHours(start.getHours() - 1);
        break;
      case "1d":
        start.setDate(start.getDate() - 1);
        break;
      case "7d":
        start.setDate(start.getDate() - 7);
        break;
      case "1m":
        start.setMonth(start.getMonth() - 1);
        break;
      case "3m":
        start.setMonth(start.getMonth() - 3);
        break;
      case "1y":
        start.setFullYear(start.getFullYear() - 1);
        break;
    }

    return {
      from: start.toISOString(),
      to: end.toISOString(),
    } as DateRange;
  }, [selectedDateRange, customDateRange]);

  // Independent data fetching for this component
  const searchRequest = useMemo(
    () =>
      ({
        query: "",
        createTime: dateRange,
      } as SearchSituationsRequest),
    [dateRange]
  );

  const { data, isLoading } = useAllSituations(searchRequest);

  // Filter situations based on selected filters
  const getFilteredSituations = () => {
    if (!data?.situations?.length) return [];

    return data.situations.filter((situation: any) => {
      // Filter by incident type (remember: types come back as strings like "SITUATION_TYPE_MEDICAL_EMERGENCY")
      if (selectedTypes.length > 0) {
        const typeStr = String(situation.type);

        const matchesType = selectedTypes.some((selectedValue) => {
          // Find the corresponding enum string for the selected short value
          const situationTypeOption = SITUATION_TYPE_OPTIONS.find(
            (option) => option.value === selectedValue
          );
          if (!situationTypeOption) return false;

          // Find the matching incident type by label
          const incidentType = incidentTypes.find(
            (type) => type.label === situationTypeOption.label
          );
          if (!incidentType) return false;

          // Compare the enum string from incidentTypes with the backend data
          return incidentType.value === typeStr;
        });

        if (!matchesType) return false;
      }

      // Filter by priority (priorities come back as numbers)
      if (selectedPriorities.length > 0) {
        const situationPriority = Number(situation.priority);
        if (!selectedPriorities.includes(situationPriority)) return false;
      }

      return true;
    });
  };

  // Process filtered data for chart visualization
  const getChartData = () => {
    const filteredSituations = getFilteredSituations();

    const typeCounts: { [key: string]: number } = {};

    filteredSituations.forEach((situation: any) => {
      const typeStr = String(situation.type);
      const incidentType = incidentTypes.find((type) => type.value === typeStr);
      const label = incidentType?.label || "Unknown Type";
      typeCounts[label] = (typeCounts[label] || 0) + 1;
    });

    return Object.entries(typeCounts)
      .map(([type, count]) => ({ type, count }))
      .sort((a, b) => a.type.localeCompare(b.type)); // Sort alphabetically by type
  };

  const filteredSituations = getFilteredSituations();
  const dataset = getChartData();

  return (
    <Card
      sx={{
        bgcolor: colors.grey[50],
        border: `1px solid ${colors.grey[300]}`,
        borderRadius: "12px",
        boxShadow: "none",
        mt: "24px",
        p: "24px",
      }}
    >
      <Card
        sx={{
          bgcolor: "white",
          border: `1px solid ${colors.grey[300]}`,
          borderRadius: "12px",
          boxShadow: "none",
        }}
      >
        <Box sx={{ display: "flex", minHeight: "600px" }}>
          {/* Left Side - Filters */}
          <Box
            sx={{
              width: 320,
              borderRight: `1px solid ${colors.grey[200]}`,
              p: "24px",
            }}
          >
            <IncidentAnalyticsFilters
              selectedTypes={selectedTypes}
              setSelectedTypes={setSelectedTypes}
              selectedPriorities={selectedPriorities}
              setSelectedPriorities={setSelectedPriorities}
              customDateRange={customDateRange}
              setCustomDateRange={(newRange) => {
                setCustomDateRange(newRange);
                // If custom date range is being set, reset the predefined date range selection
                if (newRange[0] && newRange[1]) {
                  // Don't change selectedDateRange state, but the UI will show as unselected
                  // due to the custom range taking priority
                }
              }}
            />
          </Box>

          {/* Right Side - Chart Area */}
          <Box
            sx={{
              flex: 1,
              display: "flex",
              flexDirection: "column",
            }}
          >
            {/* Tabs */}
            <Box sx={{ mb: "12px", position: "relative" }}>
              <Tabs
                value={activeTab}
                onChange={handleTabChange}
                sx={{
                  "& .MuiTabs-indicator": {
                    backgroundColor: "#2563EB",
                    height: "3px",
                    zIndex: 2,
                  },
                  "& .MuiTab-root": {
                    textTransform: "none",
                    fontSize: "14px",
                    fontWeight: 500,
                    color: colors.grey[500],
                    minHeight: "48px",
                    "&.Mui-selected": {
                      color: colors.grey[900],
                      fontWeight: 600,
                    },
                  },
                  "& .MuiTabs-flexContainer": {
                    position: "relative",
                  },
                  "&:before": {
                    content: '""',
                    position: "absolute",
                    bottom: 0,
                    left: 0,
                    right: 0,
                    height: "1px",
                    backgroundColor: colors.grey[200],
                    zIndex: 1,
                  },
                }}
              >
                {tabs.map((tab) => (
                  <Tab key={tab} label={tab} />
                ))}
              </Tabs>
            </Box>

            {/* View Controls */}
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                mb: "24px",
                paddingX: "24px",
              }}
            >
              <Box sx={{ display: "flex", gap: "16px" }}>
                <Button
                  label={getViewTypeDetails().label}
                  style={viewMenuAnchor ? "filled" : "ghost"}
                  color="grey"
                  prominence={!viewMenuAnchor}
                  size="small"
                  leftIcon={getViewTypeDetails().icon}
                  rightIcon={<KeyboardArrowDownIcon />}
                  onClick={(e) => setViewMenuAnchor(e.currentTarget)}
                />

                {/* Show chart type selector only for graph view */}
                {viewType === "graph" && (
                  <Button
                    label={
                      chartType === "bar-horizontal"
                        ? "Bar Chart Horizontal"
                        : chartType === "bar-vertical"
                        ? "Bar Chart Vertical"
                        : chartType === "pie"
                        ? "Pie Chart"
                        : chartType === "scatter"
                        ? "Scatterplot"
                        : chartType === "line"
                        ? "Line Chart"
                        : "Bar Chart Horizontal"
                    }
                    style={chartMenuAnchor ? "filled" : "ghost"}
                    color="grey"
                    prominence={!chartMenuAnchor}
                    size="small"
                    leftIcon={
                      chartType === "bar-horizontal" ? (
                        <BarChartHorizontalIcon />
                      ) : chartType === "bar-vertical" ? (
                        <BarChartVerticalIcon />
                      ) : chartType === "pie" ? (
                        <PieChartIcon />
                      ) : chartType === "scatter" ? (
                        <ScatterplotIcon />
                      ) : chartType === "line" ? (
                        <LineChartIcon />
                      ) : (
                        <BarChartHorizontalIcon />
                      )
                    }
                    rightIcon={<KeyboardArrowDownIcon />}
                    onClick={(e) => setChartMenuAnchor(e.currentTarget)}
                  />
                )}

                {/* Show heatmap view selector only when heatmap is selected */}
                {viewType === "heatmap" && (
                  <Button
                    label={getHeatmapViewTypeDetails().label}
                    style={heatmapViewMenuAnchor ? "filled" : "ghost"}
                    color="grey"
                    prominence={!heatmapViewMenuAnchor}
                    size="small"
                    leftIcon={getHeatmapViewTypeDetails().icon}
                    rightIcon={<KeyboardArrowDownIcon />}
                    onClick={(e) => setHeatmapViewMenuAnchor(e.currentTarget)}
                  />
                )}

                {/* Download Button */}
                {shouldShowDownload(viewType) && (
                  <Button
                    label="Download"
                    style="ghost"
                    color="grey"
                    prominence={true}
                    size="small"
                    leftIcon={<DownloadIcon />}
                    onClick={handleDownload}
                    disabled={isDownloadDisabled(viewType, dataset.length, selectedTableRows.size) || isDownloading}
                    isLoading={isDownloading}
                  />
                )}
              </Box>

              {/* Date Range Picker */}
              <DateRangePicker
                value={
                  customDateRange[0] && customDateRange[1]
                    ? ("custom" as DateRangeOption)
                    : selectedDateRange
                }
                onChange={(newValue) => {
                  setSelectedDateRange(newValue);
                  // Clear custom date range when predefined option is selected
                  setCustomDateRange([null, null]);
                }}
                options={DATE_RANGE_OPTIONS}
              />
            </Box>

            {/* Chart Content */}
            <Box sx={{ flex: 1 }}>
              {viewType === "graph" ? (
                isLoading ? (
                  <Box sx={{ p: "40px", textAlign: "center" }}>
                    <Typography style="body2" color={colors.grey[500]}>
                      Loading chart data...
                    </Typography>
                  </Box>
                ) : dataset.length > 0 ? (
                  <Box
                    data-chart-container
                    sx={{ height: "100%", width: "100%" }}
                  >
                    {chartType === "bar-horizontal" && (
                      <BarChartHorizontal dataset={dataset} />
                    )}
                    {chartType === "bar-vertical" && (
                      <BarChartVertical dataset={dataset} />
                    )}
                    {chartType === "pie" && <PieChart dataset={dataset} />}
                    {chartType === "scatter" && (
                      <ScatterChart dataset={dataset} />
                    )}
                    {chartType === "line" && <LineChart dataset={dataset} />}
                  </Box>
                ) : (
                  <Box sx={{ p: "40px", textAlign: "center" }}>
                    <Typography style="body2" color={colors.grey[500]}>
                      No data matches the selected filters
                    </Typography>
                  </Box>
                )
              ) : viewType === "table" ? (
                <IncidentAnalyticsTable
                  data={filteredSituations}
                  isLoading={isLoading}
                  selectedSituations={selectedTableRows}
                  onSelectionChange={setSelectedTableRows}
                />
              ) : viewType === "heatmap" ? (
                <Box
                  data-heatmap-container
                  sx={{ height: "100%", width: "100%" }}
                >
                  <IncidentHeatmapComponent
                    data={{ situations: filteredSituations }}
                    isLoading={isLoading}
                    viewType={heatmapViewType}
                  />
                </Box>
              ) : (
                <Box sx={{ p: "40px", textAlign: "center" }}>
                  <Typography style="body2" color={colors.grey[500]}>
                    Unknown view type
                  </Typography>
                </Box>
              )}
            </Box>
          </Box>
        </Box>
      </Card>

      {/* View Type Menu */}
      <Menu
        anchorEl={viewMenuAnchor}
        open={Boolean(viewMenuAnchor)}
        onClose={() => setViewMenuAnchor(null)}
        slotProps={{
          paper: {
            sx: {
              mt: 1,
              minWidth: 200,
              borderRadius: 2,
              border: `1px solid ${colors.grey[200]}`,
              boxShadow: "0px 4px 16px rgba(0, 0, 0, 0.1)",
            },
          },
        }}
      >
        <MenuItem
          onClick={() => {
            setViewType("graph");
            setViewMenuAnchor(null);
          }}
          sx={{
            px: 3,
            py: 2,
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            "&:hover": {
              backgroundColor: colors.grey[50],
            },
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
            <GraphIcon />
            <Typography
              style={viewType === "graph" ? "body1" : "body2"}
              color={viewType === "graph" ? colors.grey[900] : colors.grey[500]}
            >
              Graph
            </Typography>
          </Box>
          {viewType === "graph" && (
            <CheckIcon
              sx={{
                color: colors.blue[600],
                fontSize: 20,
                ml: 2,
              }}
            />
          )}
        </MenuItem>
        <MenuItem
          onClick={() => {
            setViewType("table");
            setViewMenuAnchor(null);
          }}
          sx={{
            px: 3,
            py: 2,
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            "&:hover": {
              backgroundColor: colors.grey[50],
            },
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
            <TableIcon />
            <Typography
              style={viewType === "table" ? "body1" : "body2"}
              color={viewType === "table" ? colors.grey[900] : colors.grey[500]}
            >
              Table
            </Typography>
          </Box>
          {viewType === "table" && (
            <CheckIcon
              sx={{
                color: colors.blue[600],
                fontSize: 20,
                ml: 2,
              }}
            />
          )}
        </MenuItem>
        <MenuItem
          onClick={() => {
            setViewType("heatmap");
            setViewMenuAnchor(null);
          }}
          sx={{
            px: 3,
            py: 2,
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            "&:hover": {
              backgroundColor: colors.grey[50],
            },
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
            <HeatMapIcon />
            <Typography
              style={viewType === "heatmap" ? "body1" : "body2"}
              color={
                viewType === "heatmap" ? colors.grey[900] : colors.grey[500]
              }
            >
              Heat map
            </Typography>
          </Box>
          {viewType === "heatmap" && (
            <CheckIcon
              sx={{
                color: colors.blue[600],
                fontSize: 20,
                ml: 2,
              }}
            />
          )}
        </MenuItem>
      </Menu>

      {/* Heatmap View Type Menu */}
      <Menu
        anchorEl={heatmapViewMenuAnchor}
        open={Boolean(heatmapViewMenuAnchor)}
        onClose={() => setHeatmapViewMenuAnchor(null)}
        slotProps={{
          paper: {
            sx: {
              mt: 1,
              minWidth: 200,
              borderRadius: 2,
              border: `1px solid ${colors.grey[200]}`,
              boxShadow: "0px 4px 16px rgba(0, 0, 0, 0.1)",
            },
          },
        }}
      >
        <MenuItem
          onClick={() => {
            setHeatmapViewType("heat");
            setHeatmapViewMenuAnchor(null);
          }}
          sx={{
            px: 3,
            py: 2,
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            "&:hover": {
              backgroundColor: colors.grey[50],
            },
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
            <HeatViewIcon />
            <Typography
              style={heatmapViewType === "heat" ? "body1" : "body2"}
              color={
                heatmapViewType === "heat" ? colors.grey[900] : colors.grey[500]
              }
            >
              Color Heat View
            </Typography>
          </Box>
          {heatmapViewType === "heat" && (
            <CheckIcon
              sx={{
                color: colors.blue[600],
                fontSize: 20,
                ml: 2,
              }}
            />
          )}
        </MenuItem>
        <MenuItem
          onClick={() => {
            setHeatmapViewType("bubble");
            setHeatmapViewMenuAnchor(null);
          }}
          sx={{
            px: 3,
            py: 2,
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            "&:hover": {
              backgroundColor: colors.grey[50],
            },
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
            <BubbleViewIcon />
            <Typography
              style={heatmapViewType === "bubble" ? "body1" : "body2"}
              color={
                heatmapViewType === "bubble"
                  ? colors.grey[900]
                  : colors.grey[500]
              }
            >
              Bubble View
            </Typography>
          </Box>
          {heatmapViewType === "bubble" && (
            <CheckIcon
              sx={{
                color: colors.blue[600],
                fontSize: 20,
                ml: 2,
              }}
            />
          )}
        </MenuItem>
      </Menu>

      {/* Chart Type Menu */}
      <Menu
        anchorEl={chartMenuAnchor}
        open={Boolean(chartMenuAnchor)}
        onClose={() => setChartMenuAnchor(null)}
        slotProps={{
          paper: {
            sx: {
              mt: 1,
              minWidth: 200,
              borderRadius: 2,
              border: `1px solid ${colors.grey[200]}`,
              boxShadow: "0px 4px 16px rgba(0, 0, 0, 0.1)",
            },
          },
        }}
      >
        <MenuItem
          onClick={() => {
            setChartType("bar-horizontal");
            setChartMenuAnchor(null);
          }}
          sx={{
            px: 3,
            py: 2,
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            "&:hover": {
              backgroundColor: colors.grey[50],
            },
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
            <BarChartHorizontalIcon />
            <Typography
              style={chartType === "bar-horizontal" ? "body1" : "body2"}
              color={
                chartType === "bar-horizontal"
                  ? colors.grey[900]
                  : colors.grey[500]
              }
            >
              Bar Chart Horizontal
            </Typography>
          </Box>
          {chartType === "bar-horizontal" && (
            <CheckIcon
              sx={{
                color: colors.blue[600],
                fontSize: 20,
                ml: 2,
              }}
            />
          )}
        </MenuItem>

        <MenuItem
          onClick={() => {
            setChartType("bar-vertical");
            setChartMenuAnchor(null);
          }}
          sx={{
            px: 3,
            py: 2,
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            "&:hover": {
              backgroundColor: colors.grey[50],
            },
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
            <BarChartVerticalIcon />
            <Typography
              style={chartType === "bar-vertical" ? "body1" : "body2"}
              color={
                chartType === "bar-vertical"
                  ? colors.grey[900]
                  : colors.grey[500]
              }
            >
              Bar Chart Vertical
            </Typography>
          </Box>
          {chartType === "bar-vertical" && (
            <CheckIcon
              sx={{
                color: colors.blue[600],
                fontSize: 20,
                ml: 2,
              }}
            />
          )}
        </MenuItem>

        <MenuItem
          onClick={() => {
            setChartType("pie");
            setChartMenuAnchor(null);
          }}
          sx={{
            px: 3,
            py: 2,
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            "&:hover": {
              backgroundColor: colors.grey[50],
            },
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
            <PieChartIcon />
            <Typography
              style={chartType === "pie" ? "body1" : "body2"}
              color={chartType === "pie" ? colors.grey[900] : colors.grey[500]}
            >
              Pie Chart
            </Typography>
          </Box>
          {chartType === "pie" && (
            <CheckIcon
              sx={{
                color: colors.blue[600],
                fontSize: 20,
                ml: 2,
              }}
            />
          )}
        </MenuItem>

        <MenuItem
          onClick={() => {
            setChartType("scatter");
            setChartMenuAnchor(null);
          }}
          sx={{
            px: 3,
            py: 2,
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            "&:hover": {
              backgroundColor: colors.grey[50],
            },
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
            <ScatterplotIcon />
            <Typography
              style={chartType === "scatter" ? "body1" : "body2"}
              color={
                chartType === "scatter" ? colors.grey[900] : colors.grey[500]
              }
            >
              Scatterplot
            </Typography>
          </Box>
          {chartType === "scatter" && (
            <CheckIcon
              sx={{
                color: colors.blue[600],
                fontSize: 20,
                ml: 2,
              }}
            />
          )}
        </MenuItem>

        <MenuItem
          onClick={() => {
            setChartType("line");
            setChartMenuAnchor(null);
          }}
          sx={{
            px: 3,
            py: 2,
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            "&:hover": {
              backgroundColor: colors.grey[50],
            },
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
            <LineChartIcon />
            <Typography
              style={chartType === "line" ? "body1" : "body2"}
              color={chartType === "line" ? colors.grey[900] : colors.grey[500]}
            >
              Line Chart
            </Typography>
          </Box>
          {chartType === "line" && (
            <CheckIcon
              sx={{
                color: colors.blue[600],
                fontSize: 20,
                ml: 2,
              }}
            />
          )}
        </MenuItem>
      </Menu>
    </Card>
  );
};
