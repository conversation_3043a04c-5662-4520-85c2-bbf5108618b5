"use client";

import {
  Box,
  Checkbox,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from "@mui/material";
import { Situation } from "proto/hero/situations/v2/situations_pb";
import { Typography } from "../../../../design-system/components/Typography";
import { colors } from "../../../../design-system/tokens";
import {
  SITUATION_STATUS_OPTIONS,
  SITUATION_TYPE_OPTIONS,
} from "../../../search/utils/constants";

interface IncidentAnalyticsTableProps {
  data: Situation[];
  isLoading: boolean;
  selectedSituations?: Set<string>;
  onSelectionChange?: (selected: Set<string>) => void;
}

export const IncidentAnalyticsTable = ({
  data,
  isLoading,
  selectedSituations = new Set(),
  onSelectionChange,
}: IncidentAnalyticsTableProps) => {
  // Handle select all situations
  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      const newSelected = new Set<string>(
        data?.map((situation: Situation) => situation.id) || []
      );
      onSelectionChange?.(newSelected);
    } else {
      onSelectionChange?.(new Set<string>());
    }
  };

  // Handle select individual situation
  const handleSelectSituation = (situationId: string) => {
    const newSelected = new Set(selectedSituations);
    if (newSelected.has(situationId)) {
      newSelected.delete(situationId);
    } else {
      newSelected.add(situationId);
    }
    onSelectionChange?.(newSelected);
  };

  // Get status display name (disposition)
  const getStatusDisplay = (status: any) => {
    if (typeof status === "string") {
      const matchingStatus = SITUATION_STATUS_OPTIONS.find(
        (option) =>
          option.value === status.toLowerCase() ||
          `SITUATION_STATUS_${option.label.toUpperCase()}` === status
      );

      if (matchingStatus) {
        return matchingStatus.label;
      }

      const parts = status.split("_");
      if (parts.length > 0) {
        const lastPart = parts[parts.length - 1];
        return (
          lastPart.charAt(0).toUpperCase() + lastPart.slice(1).toLowerCase()
        );
      }
      return status;
    }

    const matchingStatus = SITUATION_STATUS_OPTIONS.find(
      (option) => option.enumValue === status
    );

    if (matchingStatus) {
      return matchingStatus.label;
    }

    return "Unknown";
  };

  // Get type display name
  const getTypeDisplay = (type: any) => {
    if (typeof type === "string") {
      const matchingType = SITUATION_TYPE_OPTIONS.find(
        (option) =>
          option.value === type.toLowerCase() ||
          `SITUATION_TYPE_${option.label.toUpperCase().replace(" ", "_")}` ===
            type
      );

      if (matchingType) {
        return matchingType.label;
      }

      if (type.startsWith("SITUATION_TYPE_")) {
        const typeName = type?.replace("SITUATION_TYPE_", "");
        return typeName
          ?.split("_")
          ?.map(
            (word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
          )
          ?.join(" ");
      }
      return type;
    }

    const matchingType = SITUATION_TYPE_OPTIONS.find(
      (option) => option.enumValue === type
    );

    if (matchingType) {
      return matchingType.label;
    }

    return "Unknown";
  };

  // Get caller number from additional info JSON or contact number
  const getReporterPhone = (situation: Situation): string => {
    if (situation.contactNo) return situation.contactNo;

    if (situation.additionalInfoJson) {
      try {
        const parsed = JSON.parse(situation.additionalInfoJson);
        if (parsed.callerNo) return parsed.callerNo;
      } catch {
        // Continue to fallback
      }
    }

    return "---";
  };

  return (
    <Box
      sx={{
        flex: 1,
        display: "flex",
        flexDirection: "column",
        bgcolor: "white",
        boxShadow: "none",
        overflow: "hidden",
      }}
    >
      <TableContainer
        component={Box}
        sx={{
          flexGrow: 1,
          width: "100%",
          overflowY: "auto",
          overflowX: "auto",
          maxHeight: "500px",
        }}
      >
        <Table
          stickyHeader
          aria-label="incidents analytics table"
          sx={{
            tableLayout: "fixed",
            width: "100%",
          }}
        >
          <TableHead>
            <TableRow sx={{ backgroundColor: colors.grey[100] }}>
              <TableCell
                padding="checkbox"
                sx={{ width: "48px", backgroundColor: colors.grey[100] }}
              >
                <Checkbox
                  checked={
                    data?.length > 0 && selectedSituations.size === data.length
                  }
                  indeterminate={
                    selectedSituations.size > 0 &&
                    selectedSituations.size < (data?.length || 0)
                  }
                  onChange={handleSelectAll}
                  sx={{
                    color: "transparent",
                    "&.Mui-checked": {
                      color: "#2563EB",
                    },
                    "&.MuiCheckbox-indeterminate": {
                      color: "#2563EB",
                    },
                    "& .MuiSvgIcon-root": {
                      width: 18,
                      height: 18,
                      border: `1px solid ${colors.grey[200]}`,
                      borderRadius: "4px",
                      backgroundColor: "white",
                      "& path": {
                        display: "none",
                      },
                    },
                    "&.Mui-checked .MuiSvgIcon-root": {
                      backgroundColor: "#2563EB",
                      borderColor: "#2563EB",
                      "& path": {
                        display: "block",
                        fill: "white",
                      },
                    },
                    "&.MuiCheckbox-indeterminate .MuiSvgIcon-root": {
                      backgroundColor: "#2563EB",
                      borderColor: "#2563EB",
                      "& path": {
                        display: "block",
                        fill: "white",
                      },
                    },
                  }}
                />
              </TableCell>
              <TableCell
                sx={{ width: "10%", backgroundColor: colors.grey[100] }}
              >
                <Box sx={{ display: "flex", alignItems: "center" }}>
                  <Typography style="tag2" color={colors.grey[800]}>
                    ID
                  </Typography>
                </Box>
              </TableCell>
              <TableCell
                sx={{ width: "20%", backgroundColor: colors.grey[100] }}
              >
                <Box sx={{ display: "flex", alignItems: "center" }}>
                  <Typography style="tag2" color={colors.grey[800]}>
                    LOCATION
                  </Typography>
                </Box>
              </TableCell>
              <TableCell
                sx={{ width: "25%", backgroundColor: colors.grey[100] }}
              >
                <Box sx={{ display: "flex", alignItems: "center" }}>
                  <Typography style="tag2" color={colors.grey[800]}>
                    DESCRIPTION
                  </Typography>
                </Box>
              </TableCell>
              <TableCell
                sx={{ width: "15%", backgroundColor: colors.grey[100] }}
              >
                <Box sx={{ display: "flex", alignItems: "center" }}>
                  <Typography style="tag2" color={colors.grey[800]}>
                    TYPE
                  </Typography>
                </Box>
              </TableCell>
              <TableCell
                sx={{ width: "15%", backgroundColor: colors.grey[100] }}
              >
                <Box sx={{ display: "flex", alignItems: "center" }}>
                  <Typography style="tag2" color={colors.grey[800]}>
                    STATUS
                  </Typography>
                </Box>
              </TableCell>
              <TableCell
                sx={{ width: "15%", backgroundColor: colors.grey[100] }}
              >
                <Box sx={{ display: "flex", alignItems: "center" }}>
                  <Typography style="tag2" color={colors.grey[800]}>
                    REPORTER PHONE
                  </Typography>
                </Box>
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {data && data.length > 0 ? (
              data.map((situation: Situation, index: number) => (
                <TableRow
                  hover
                  key={`${situation.id || 'no-id'}-${index}`}
                  sx={{
                    height: "54px",
                    "&:last-child td, &:last-child th": { border: 0 },
                    "&:hover": {
                      backgroundColor: colors.grey[50],
                    },
                  }}
                >
                  <TableCell padding="checkbox">
                    <Checkbox
                      checked={selectedSituations.has(situation.id)}
                      onChange={() => handleSelectSituation(situation.id)}
                      sx={{
                        color: "transparent",
                        "&.Mui-checked": {
                          color: "#2563EB",
                        },
                        "& .MuiSvgIcon-root": {
                          width: 18,
                          height: 18,
                          border: `1px solid ${colors.grey[200]}`,
                          borderRadius: "4px",
                          backgroundColor: "white",
                          "& path": {
                            display: "none",
                          },
                        },
                        "&.Mui-checked .MuiSvgIcon-root": {
                          backgroundColor: "#2563EB",
                          borderColor: "#2563EB",
                          "& path": {
                            display: "block",
                            fill: "white",
                          },
                        },
                      }}
                    />
                  </TableCell>
                  <TableCell
                    component="th"
                    scope="row"
                    sx={{ width: "10%", maxWidth: "10%" }}
                  >
                    <Box
                      sx={{
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        whiteSpace: "nowrap",
                        width: "100%",
                      }}
                    >
                      <Typography style="body4" color={colors.grey[900]}>
                        {situation.id?.replace(/[^0-9]/g, "").slice(0, 7) ||
                          "---"}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell
                    align="left"
                    sx={{ width: "20%", maxWidth: "20%" }}
                  >
                    <Box
                      sx={{
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        whiteSpace: "nowrap",
                        width: "100%",
                      }}
                    >
                      <Typography style="body4" color={colors.grey[900]}>
                        {situation.address || "---"}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell
                    align="left"
                    sx={{ width: "25%", maxWidth: "25%" }}
                  >
                    <Box
                      sx={{
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        whiteSpace: "nowrap",
                        width: "100%",
                      }}
                    >
                      <Typography style="body4" color={colors.grey[900]}>
                        {situation.description || "---"}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell
                    align="left"
                    sx={{ width: "15%", maxWidth: "15%" }}
                  >
                    <Box
                      sx={{
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        whiteSpace: "nowrap",
                        width: "100%",
                      }}
                    >
                      <Typography style="body4" color={colors.grey[900]}>
                        {getTypeDisplay(situation.type)}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell
                    align="left"
                    sx={{ width: "15%", maxWidth: "15%" }}
                  >
                    <Box
                      sx={{
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        whiteSpace: "nowrap",
                        width: "100%",
                      }}
                    >
                      <Typography style="body4" color={colors.grey[900]}>
                        {getStatusDisplay(situation.status)}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell
                    align="left"
                    sx={{ width: "15%", maxWidth: "15%" }}
                  >
                    <Box
                      sx={{
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        whiteSpace: "nowrap",
                        width: "100%",
                      }}
                    >
                      <Typography style="body4" color={colors.grey[900]}>
                        {getReporterPhone(situation)}
                      </Typography>
                    </Box>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow sx={{ height: 200 }}>
                <TableCell colSpan={7} align="center">
                  <Typography style="body3" color={colors.grey[600]}>
                    {isLoading
                      ? "Loading incident data..."
                      : "No incidents match the selected filters."}
                  </Typography>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
};
