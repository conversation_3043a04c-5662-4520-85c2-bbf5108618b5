"use client";

import { Box, Divider } from "@mui/material";
import { useEffect, useState } from "react";
import { DateRangePicker } from "../../../../design-system/components/DatePicker";
import { Dropdown } from "../../../../design-system/components/Dropdown";
import { Typography } from "../../../../design-system/components/Typography";
import { colors } from "../../../../design-system/tokens";
import {
  PRIORITY_OPTIONS,
  SITUATION_TYPE_OPTIONS,
} from "../../../search/utils/constants";

interface IncidentAnalyticsFiltersProps {
  selectedTypes: string[];
  setSelectedTypes: (types: string[]) => void;
  selectedPriorities: number[];
  setSelectedPriorities: (priorities: number[]) => void;
  customDateRange: [Date | null, Date | null];
  setCustomDateRange: (range: [Date | null, Date | null]) => void;
}

export const IncidentAnalyticsFilters = ({
  selectedTypes,
  setSelectedTypes,
  selectedPriorities,
  setSelectedPriorities,
  customDateRange,
  setCustomDateRange,
}: IncidentAnalyticsFiltersProps) => {
  const [type, setType] = useState<string | null>(null);
  const [priority, setPriority] = useState<string | null>(null);

  // Sync local UI state with props - Priority
  useEffect(() => {
    if (selectedPriorities.length > 0) {
      const priorityOption = PRIORITY_OPTIONS.find(
        (option) => option.value === selectedPriorities[0].toString()
      );
      setPriority(priorityOption?.value || null);
    } else {
      setPriority(null);
    }
  }, [selectedPriorities]);

  // Sync local UI state with props - Type
  useEffect(() => {
    if (selectedTypes.length > 0) {
      const typeOption = SITUATION_TYPE_OPTIONS.find(
        (option) => option.value === selectedTypes[0]
      );
      setType(typeOption?.value || null);
    } else {
      setType(null);
    }
  }, [selectedTypes]);

  return (
    <Box>
      <Box sx={{ mb: "20px" }}>
        <Typography style="caps1" color={colors.grey[900]}>
          FILTERS
        </Typography>
      </Box>

      <Box sx={{ mb: "20px" }}>
        <Dropdown
          title="Incident Type"
          placeholder="Select type"
          enableSearch
          options={SITUATION_TYPE_OPTIONS}
          value={type}
          onChange={(value) => {
            setType(value);
            if (value) {
              const selectedOption = SITUATION_TYPE_OPTIONS.find(
                (option) => option.value === value
              );
              if (selectedOption) {
                setSelectedTypes([selectedOption.value]);
              }
            } else {
              setSelectedTypes([]);
            }
          }}
        />
      </Box>

      <Box sx={{ mb: "20px" }}>
        <Dropdown
          title="Priority"
          placeholder="Select priority"
          options={PRIORITY_OPTIONS}
          value={priority}
          onChange={(value) => {
            setPriority(value);
            if (value) {
              setSelectedPriorities([parseInt(value)]);
            } else {
              setSelectedPriorities([]);
            }
          }}
        />
      </Box>

      <Divider sx={{ borderColor: colors.grey[200], my: "20px" }} />

      <Box sx={{ mb: "20px" }}>
        <DateRangePicker
          title="Custom Date Range"
          placeholder="Select custom date range"
          value={customDateRange}
          onChange={(newRange) => {
            setCustomDateRange(newRange);
          }}
        />
      </Box>
    </Box>
  );
};
