"use client";

import { Box } from "@mui/material";
import mapboxgl from "mapbox-gl";
import "mapbox-gl/dist/mapbox-gl.css";
import React, { useEffect, useRef, useState } from "react";
import ReactDOM from "react-dom/client";
import { Typography } from "../../../../design-system/components/Typography";
import { colors } from "../../../../design-system/tokens";
import { incidentTypes } from "../../../cad/components/ActionPane/constants";
import { DEFAULT_LOCATION } from "../../../constants";
import AnalyticsIncidentPopup from "./AnalyticsIncidentPopup";

// Use the same Mapbox access token as the main MapComponent
mapboxgl.accessToken =
  "pk.eyJ1IjoiaGVyby1zYWZldHkiLCJhIjoiY202bDM3bGlzMDRybTJrcHJibm5sYTFzMSJ9.-ekjZGG1E_cWYCOnBrdEag";

interface IncidentHeatmapComponentProps {
  data: any;
  isLoading: boolean;
  viewType: "heat" | "bubble";
}

const IncidentHeatmapComponent: React.FC<IncidentHeatmapComponentProps> = ({
  data,
  isLoading,
  viewType,
}) => {
  const mapContainer = useRef<HTMLDivElement>(null);
  const map = useRef<mapboxgl.Map | null>(null);
  const [isMapLoaded, setIsMapLoaded] = useState(false);

  // Store event handlers to properly remove them later
  const clickHandler = useRef<((e: any) => void) | null>(null);
  const mouseEnterHandler = useRef<(() => void) | null>(null);
  const mouseLeaveHandler = useRef<(() => void) | null>(null);

  // Initialize the map
  useEffect(() => {
    if (!mapContainer.current) return;

    map.current = new mapboxgl.Map({
      container: mapContainer.current,
      // Use the same custom style as the main MapComponent
      style: "mapbox://styles/hero-safety/cm9n1pd7j004d01ss66zrbjwm",
      center: [DEFAULT_LOCATION.longitude, DEFAULT_LOCATION.latitude],
      zoom: 12.5,
      attributionControl: false,
      logoPosition: "bottom-right",
    });

    map.current.on("load", () => {
      setIsMapLoaded(true);
    });

    // Add navigation controls
    map.current.addControl(new mapboxgl.NavigationControl(), "top-right");

    // Cleanup function
    return () => {
      map.current?.remove();
    };
  }, []);

  // Handle container resize
  useEffect(() => {
    if (!mapContainer.current || !map.current) return;

    const resizeObserver = new ResizeObserver(() => {
      map.current?.resize();
    });

    resizeObserver.observe(mapContainer.current);

    return () => {
      resizeObserver.disconnect();
    };
  }, []);

  // Setup map layers once when map is loaded and view type changes
  useEffect(() => {
    if (!isMapLoaded || !map.current) return;

    // Remove existing layers and sources first
    const layersToRemove = [
      "situations-heatmap",
      "situations-points",
      "situations-clusters",
      "situations-cluster-count",
    ];

    layersToRemove.forEach((layerId) => {
      try {
        if (map.current && map.current.getLayer(layerId)) {
          map.current.removeLayer(layerId);
        }
      } catch (e) {
        // Layer doesn't exist, continue
      }
    });

    try {
      if (map.current && map.current.getSource("situations")) {
        map.current.removeSource("situations");
      }
    } catch (e) {
      // Source doesn't exist, continue
    }

    // Add empty source initially
    const sourceConfig: any = {
      type: "geojson",
      data: {
        type: "FeatureCollection",
        features: [],
      },
    };

    // Add clustering configuration for bubble view
    if (viewType === "bubble") {
      sourceConfig.cluster = true;
      sourceConfig.clusterMaxZoom = 14;
      sourceConfig.clusterRadius = 50;
    }

    map.current.addSource("situations", sourceConfig);

    // Create layers based on view type
    if (viewType === "heat") {
      // Heat map implementation
      map.current.addLayer({
        id: "situations-heatmap",
        type: "heatmap",
        source: "situations",
        maxzoom: 15,
        paint: {
          // Increase the heatmap weight based on frequency and property magnitude
          "heatmap-weight": [
            "interpolate",
            ["linear"],
            ["get", "weight"],
            0,
            0,
            6,
            1,
          ],
          // Increase the heatmap color weight by zoom level
          // heatmap-intensity is a multiplier on top of heatmap-weight
          "heatmap-intensity": [
            "interpolate",
            ["linear"],
            ["zoom"],
            0,
            1,
            15,
            3,
          ],
          // Color ramp for heatmap.  Domain is 0 (low) to 1 (high).
          "heatmap-color": [
            "interpolate",
            ["linear"],
            ["heatmap-density"],
            0,
            "rgba(33,102,172,0)",
            0.2,
            "rgb(103,169,207)",
            0.4,
            "rgb(209,229,240)",
            0.6,
            "rgb(253,219,199)",
            0.8,
            "rgb(239,138,98)",
            1,
            "rgb(178,24,43)",
          ],
          // Adjust the heatmap radius by zoom level
          "heatmap-radius": [
            "interpolate",
            ["linear"],
            ["zoom"],
            0,
            20,
            15,
            40,
          ],
          // Transition from heatmap to circle layer by zoom level
          "heatmap-opacity": ["interpolate", ["linear"], ["zoom"], 7, 1, 15, 0],
        },
      });

      // Add a circle layer for individual points at high zoom levels
      map.current.addLayer({
        id: "situations-points",
        type: "circle",
        source: "situations",
        minzoom: 10,
        paint: {
          // Size circle radius by zoom level and weight
          "circle-radius": ["interpolate", ["linear"], ["zoom"], 10, 3, 15, 6],
          // Color circles by incident type
          "circle-color": [
            "case",
            ["==", ["get", "incidentType"], "Medical Emergency"],
            "#ef4444",
            ["==", ["get", "incidentType"], "Fire"],
            "#f97316",
            ["==", ["get", "incidentType"], "Theft"],
            "#8b5cf6",
            ["==", ["get", "incidentType"], "Fight"],
            "#dc2626",
            ["==", ["get", "incidentType"], "Vandalism"],
            "#0ea5e9",
            ["==", ["get", "incidentType"], "Intrusion"],
            "#059669",
            ["==", ["get", "incidentType"], "Suspicious Activity"],
            "#eab308",
            ["==", ["get", "incidentType"], "Noise Complaint"],
            "#6b7280",
            ["==", ["get", "incidentType"], "Weapon Detected"],
            "#991b1b",
            "#374151", // Default color for "Other"
          ],
          "circle-stroke-color": "white",
          "circle-stroke-width": 1,
          // Transition from transparent to opaque by zoom level
          "circle-opacity": ["interpolate", ["linear"], ["zoom"], 10, 0, 15, 1],
        },
      });
    } else {
      // Bubble cluster implementation
      // Add clustered circles layer
      map.current.addLayer({
        id: "situations-clusters",
        type: "circle",
        source: "situations",
        filter: ["has", "point_count"],
        paint: {
          // Color based on cluster size: amber for <3, pink for >=3
          "circle-color": [
            "case",
            ["<", ["get", "point_count"], 3],
            "#fef3c7", // amber-100
            "#fce7f3", // pink-100
          ],
          "circle-stroke-color": [
            "case",
            ["<", ["get", "point_count"], 3],
            "#d97706", // amber-600
            "#be185d", // pink-600
          ],
          "circle-stroke-width": 2,
          "circle-radius": [
            "step",
            ["get", "point_count"],
            15, // radius for count 1-2
            3,
            18, // radius for count 3-9
            10,
            20, // radius for count 10-19
            20,
            24, // radius for count 20+
          ],
          // Add smooth transitions for radius changes
          "circle-radius-transition": {
            duration: 300,
            delay: 0,
          },
          "circle-color-transition": {
            duration: 300,
            delay: 0,
          },
        },
      });

      // Add cluster count labels
      map.current.addLayer({
        id: "situations-cluster-count",
        type: "symbol",
        source: "situations",
        filter: ["has", "point_count"],
        layout: {
          "text-field": "{point_count_abbreviated}",
          "text-font": ["DIN Offc Pro Medium", "Arial Unicode MS Bold"],
          "text-size": 12,
        },
        paint: {
          "text-color": [
            "case",
            ["<", ["get", "point_count"], 3],
            "#d97706", // amber-600
            "#be185d", // pink-600
          ],
        },
      });

      // Add unclustered individual points
      map.current.addLayer({
        id: "situations-points",
        type: "circle",
        source: "situations",
        filter: ["!", ["has", "point_count"]],
        paint: {
          "circle-color": "#fef3c7", // amber-100 for individual points
          "circle-stroke-color": "#d97706", // amber-600
          "circle-stroke-width": 2,
          "circle-radius": 8,
          // Add smooth transitions
          "circle-radius-transition": {
            duration: 300,
            delay: 0,
          },
          "circle-color-transition": {
            duration: 300,
            delay: 0,
          },
        },
      });
    }

    // Setup event handlers
    setupEventHandlers();
  }, [isMapLoaded, viewType]); // Only depend on map loaded state and view type

  // Separate function to setup event handlers
  const setupEventHandlers = () => {
    if (!map.current) return;

    // Remove existing event handlers first
    removeEventHandlers();

    // Store and add click handlers for different view types
    clickHandler.current = (e) => {
      if (e.features && e.features[0]) {
        const feature = e.features[0];
        const properties = feature.properties;

        // Handle cluster clicks differently for bubble view
        if (viewType === "bubble" && properties?.cluster) {
          const clusterId = properties.cluster_id;
          const source = map.current!.getSource(
            "situations"
          ) as mapboxgl.GeoJSONSource;

          source.getClusterExpansionZoom(clusterId, (err, zoom) => {
            if (err || zoom === null || zoom === undefined) return;

            map.current!.easeTo({
              center: (feature.geometry as any).coordinates,
              zoom: zoom,
              duration: 500, // Smooth 500ms animation
              essential: true, // This animation is considered essential with respect to prefers-reduced-motion
            });
          });

          return;
        }

        // Create popup element
        const popupElement = document.createElement('div');
        
        // Create React root and render the popup component
        const popupRoot = ReactDOM.createRoot(popupElement);
        popupRoot.render(
          <AnalyticsIncidentPopup
            incident={{
              id: properties?.id || "Unknown",
              type: properties?.incidentType || "Unknown",
              status: properties?.status || "Unknown",
              address: properties?.address,
              location: properties?.location ? { description: properties?.location } : undefined,
              description: properties?.description,
            }}
          />
        );

        // Show popup with the rendered React component
        const popup = new mapboxgl.Popup()
          .setLngLat(e.lngLat)
          .setDOMContent(popupElement)
          .addTo(map.current!);

        // Store the root for cleanup
        (popupElement as any)._reactRoot = popupRoot;
        
        // Clean up React root when popup is closed
        popup.on('close', () => {
          if ((popupElement as any)._reactRoot) {
            (popupElement as any)._reactRoot.unmount();
          }
        });
      }
    };

    // Add click handlers based on view type
    if (viewType === "bubble") {
      map.current.on("click", "situations-clusters", clickHandler.current);
      map.current.on("click", "situations-points", clickHandler.current);
    } else {
      map.current.on("click", "situations-points", clickHandler.current);
    }

    // Store and add mouse enter handler to change cursor
    mouseEnterHandler.current = () => {
      map.current!.getCanvas().style.cursor = "pointer";
    };

    // Store and add mouse leave handler to reset cursor
    mouseLeaveHandler.current = () => {
      map.current!.getCanvas().style.cursor = "";
    };

    // Add mouse handlers based on view type
    if (viewType === "bubble") {
      map.current.on(
        "mouseenter",
        "situations-clusters",
        mouseEnterHandler.current
      );
      map.current.on(
        "mouseleave",
        "situations-clusters",
        mouseLeaveHandler.current
      );
      map.current.on(
        "mouseenter",
        "situations-points",
        mouseEnterHandler.current
      );
      map.current.on(
        "mouseleave",
        "situations-points",
        mouseLeaveHandler.current
      );
    } else {
      map.current.on(
        "mouseenter",
        "situations-points",
        mouseEnterHandler.current
      );
      map.current.on(
        "mouseleave",
        "situations-points",
        mouseLeaveHandler.current
      );
    }
  };

  // Separate function to remove event handlers
  const removeEventHandlers = () => {
    if (!map.current) return;

    // Remove click handlers
    if (clickHandler.current) {
      map.current.off("click", "situations-clusters", clickHandler.current);
      map.current.off("click", "situations-points", clickHandler.current);
    }

    // Remove mouse enter handlers
    if (mouseEnterHandler.current) {
      map.current.off(
        "mouseenter",
        "situations-clusters",
        mouseEnterHandler.current
      );
      map.current.off(
        "mouseenter",
        "situations-points",
        mouseEnterHandler.current
      );
    }

    // Remove mouse leave handlers
    if (mouseLeaveHandler.current) {
      map.current.off(
        "mouseleave",
        "situations-clusters",
        mouseLeaveHandler.current
      );
      map.current.off(
        "mouseleave",
        "situations-points",
        mouseLeaveHandler.current
      );
    }
  };

  // Update data only when data changes - this won't re-render the map
  useEffect(() => {
    if (!isMapLoaded || !map.current) return;

    const situations = data?.situations || [];

    // Filter situations with valid coordinates
    const validSituations = situations.filter(
      (situation: any) =>
        typeof situation.latitude === "number" &&
        typeof situation.longitude === "number" &&
        !isNaN(situation.latitude) &&
        !isNaN(situation.longitude)
    );

    // Create unified GeoJSON data for all situations
    const geoJsonData = {
      type: "FeatureCollection" as const,
      features: validSituations.map((situation: any) => {
        const typeStr = String(situation.type);
        const incidentType = incidentTypes.find(
          (type) => type.value === typeStr
        );
        const typeLabel = incidentType?.label || "Type Not Defined";

        return {
          type: "Feature" as const,
          properties: {
            id: situation.id,
            title: situation.title,
            status: situation.status,
            incidentType: typeStr, // Keep the raw type value for the utility function
            incidentTypeLabel: typeLabel, // Keep the formatted label for display
            address: situation.address,
            location: situation.location?.description,
            description: situation.description,
            weight: 1, // Each situation has equal weight, but you can modify this based on priority, etc.
          },
          geometry: {
            type: "Point" as const,
            coordinates: [situation.longitude, situation.latitude],
          },
        };
      }),
    };

    // Update the source data without recreating layers
    const source = map.current.getSource(
      "situations"
    ) as mapboxgl.GeoJSONSource;
    if (source) {
      source.setData(geoJsonData);
    }

    console.log(
      `Updated ${viewType} view with ${validSituations.length} situations`
    );
  }, [isMapLoaded, data]); // Only depend on map loaded state and data changes

  // Cleanup effect for when component unmounts
  useEffect(() => {
    return () => {
      if (map.current) {
        // Remove event handlers
        removeEventHandlers();

        // Safely remove all layers and sources on cleanup
        const layersToRemove = [
          "situations-heatmap",
          "situations-points",
          "situations-clusters",
          "situations-cluster-count",
        ];

        layersToRemove.forEach((layerId) => {
          try {
            if (map.current && map.current.getLayer(layerId)) {
              map.current.removeLayer(layerId);
            }
          } catch (e) {
            // Layer doesn't exist, continue
          }
        });

        try {
          if (map.current && map.current.getSource("situations")) {
            map.current.removeSource("situations");
          }
        } catch (e) {
          // Source doesn't exist, continue
        }
      }
    };
  }, []);

  return (
    <Box sx={{ position: "relative", height: "500px", overflow: "hidden" }}>
      <div ref={mapContainer} style={{ width: "100%", height: "100%" }} />

      {/* Subtle loading indicator in top-right corner */}
      {isLoading && (
        <Box
          sx={{
            position: "absolute",
            top: 12,
            right: 12,
            bgcolor: "rgba(255, 255, 255, 0.95)",
            border: `1px solid ${colors.grey[200]}`,
            borderRadius: "6px",
            px: 2,
            py: 1,
            display: "flex",
            alignItems: "center",
            gap: 1,
            zIndex: 1000,
            boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
          }}
        >
          <Box
            sx={{
              width: 12,
              height: 12,
              borderRadius: "50%",
              border: "2px solid transparent",
              borderTop: `2px solid ${colors.blue[600]}`,
              animation: "spin 1s linear infinite",
              "@keyframes spin": {
                "0%": { transform: "rotate(0deg)" },
                "100%": { transform: "rotate(360deg)" },
              },
            }}
          />
          <Typography style="caps1" color={colors.grey[600]}>
            Updating
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default IncidentHeatmapComponent;
