"use client";

import {
  Box,
  Checkbox,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from "@mui/material";
import { Typography } from "../../../../design-system/components/Typography";
import { colors } from "../../../../design-system/tokens";
import { incidentTypes } from "../../../cad/components/ActionPane/constants";
import { SITUATION_STATUS_OPTIONS } from "../../../search/utils/constants";

interface IncidentTimeAnalyticsTableProps {
  data: any[];
  isLoading: boolean;
  activeTab: number;
  selectedSituations?: Set<string>;
  onSelectionChange?: (selected: Set<string>) => void;
}

export const IncidentTimeAnalyticsTable: React.FC<
  IncidentTimeAnalyticsTableProps
> = ({
  data,
  isLoading,
  activeTab,
  selectedSituations = new Set(),
  onSelectionChange,
}) => {
  // Handle select all situations
  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      const newSelected = new Set<string>(
        data?.map((situation: any) => situation.id) || []
      );
      onSelectionChange?.(newSelected);
    } else {
      onSelectionChange?.(new Set<string>());
    }
  };

  // Handle select individual situation
  const handleSelectSituation = (situationId: string) => {
    const newSelected = new Set(selectedSituations);
    if (newSelected.has(situationId)) {
      newSelected.delete(situationId);
    } else {
      newSelected.add(situationId);
    }
    onSelectionChange?.(newSelected);
  };
  if (isLoading) {
    return (
      <div style={{ padding: "40px", textAlign: "center" }}>
        <Typography style="body2" color={colors.grey[500]}>
          Loading table data...
        </Typography>
      </div>
    );
  }

  if (!data || data.length === 0) {
    return (
      <div style={{ padding: "40px", textAlign: "center" }}>
        <Typography style="body2" color={colors.grey[500]}>
          No data matches the selected filters
        </Typography>
      </div>
    );
  }

  // Helper function to calculate response time (dispatch to resolved)
  const calculateResponseTime = (situation: any): string => {
    if (!situation.statusUpdates?.length) return "N/A";

    // Find the dispatching timestamp
    const dispatchUpdate = situation.statusUpdates.find(
      (update: any) => update.newStatus === "SITUATION_STATUS_DISPATCHING"
    );

    // Find the resolved timestamp
    const resolvedUpdate = situation.statusUpdates.find(
      (update: any) => update.newStatus === "SITUATION_STATUS_RESOLVED"
    );

    if (!dispatchUpdate || !resolvedUpdate) return "N/A";

    const dispatchTime = new Date(dispatchUpdate.timestamp);
    const resolvedTime = new Date(resolvedUpdate.timestamp);
    const diffMinutes = Math.round(
      (resolvedTime.getTime() - dispatchTime.getTime()) / (1000 * 60)
    );

    if (diffMinutes <= 0) return "N/A";

    if (diffMinutes < 60) {
      return `${diffMinutes}m`;
    } else {
      const hours = Math.floor(diffMinutes / 60);
      const minutes = diffMinutes % 60;
      return `${hours}h ${minutes}m`;
    }
  };

  // Helper function to calculate resolution time (create to resolved)
  const calculateResolutionTime = (situation: any): string => {
    if (!situation.resolvedTime || !situation.createTime) return "N/A";

    const createTime = new Date(situation.createTime);
    const resolvedTime = new Date(situation.resolvedTime);
    const diffMinutes = Math.round(
      (resolvedTime.getTime() - createTime.getTime()) / (1000 * 60)
    );

    if (diffMinutes <= 0) return "N/A";

    if (diffMinutes < 60) {
      return `${diffMinutes}m`;
    } else {
      const hours = Math.floor(diffMinutes / 60);
      const minutes = diffMinutes % 60;
      return `${hours}h ${minutes}m`;
    }
  };

  const getIncidentTypeLabel = (typeStr: string): string => {
    const incidentType = incidentTypes.find((type) => type.value === typeStr);
    return incidentType?.label || "Unknown Type";
  };

  // Get status display name (same logic as IncidentAnalyticsTable)
  const getStatusDisplay = (status: any) => {
    if (typeof status === "string") {
      const matchingStatus = SITUATION_STATUS_OPTIONS.find(
        (option) =>
          option.value === status.toLowerCase() ||
          `SITUATION_STATUS_${option.label.toUpperCase()}` === status
      );

      if (matchingStatus) {
        return matchingStatus.label;
      }

      const parts = status.split("_");
      if (parts.length > 0) {
        const lastPart = parts[parts.length - 1];
        return (
          lastPart.charAt(0).toUpperCase() + lastPart.slice(1).toLowerCase()
        );
      }
      return status;
    }

    const matchingStatus = SITUATION_STATUS_OPTIONS.find(
      (option) => option.enumValue === status
    );

    if (matchingStatus) {
      return matchingStatus.label;
    }

    return "Unknown";
  };

  const getPriorityLabel = (priority: number): string => {
    if (!priority || isNaN(priority)) return "N/A";
    return `P${priority}`;
  };

  // Common columns for all tabs
  const commonColumns = [
    { key: "createTime", label: "CREATED", width: "120px" },
    { key: "type", label: "TYPE", width: "150px" },
    { key: "priority", label: "PRIORITY", width: "80px" },
    { key: "status", label: "STATUS", width: "100px" },
  ];

  // Additional columns based on active tab
  const getColumns = () => {
    if (activeTab === 1) {
      // Response Time tab
      return [
        ...commonColumns,
        { key: "responseTime", label: "RESPONSE TIME", width: "120px" },
      ];
    } else if (activeTab === 2) {
      // Resolution Time tab
      return [
        ...commonColumns,
        { key: "responseTime", label: "RESPONSE TIME", width: "120px" },
        { key: "resolutionTime", label: "RESOLUTION TIME", width: "120px" },
      ];
    } else {
      // Incidents tab - just common columns
      return commonColumns;
    }
  };

  const columns = getColumns();

  return (
    <Box
      sx={{
        flex: 1,
        display: "flex",
        flexDirection: "column",
        bgcolor: "white",
        boxShadow: "none",
        overflow: "hidden",
      }}
    >
      <TableContainer
        component={Box}
        sx={{
          flexGrow: 1,
          width: "100%",
          overflowY: "auto",
          overflowX: "auto",
          maxHeight: "500px",
        }}
      >
        <Table
          stickyHeader
          aria-label="incidents time analytics table"
          sx={{
            tableLayout: "fixed",
            width: "100%",
          }}
        >
          <TableHead>
            <TableRow sx={{ backgroundColor: colors.grey[100] }}>
              <TableCell
                padding="checkbox"
                sx={{ width: "48px", backgroundColor: colors.grey[100] }}
              >
                <Checkbox
                  checked={
                    data?.length > 0 && selectedSituations.size === data.length
                  }
                  indeterminate={
                    selectedSituations.size > 0 &&
                    selectedSituations.size < (data?.length || 0)
                  }
                  onChange={handleSelectAll}
                  sx={{
                    color: "transparent",
                    "&.Mui-checked": {
                      color: "#2563EB",
                    },
                    "&.MuiCheckbox-indeterminate": {
                      color: "#2563EB",
                    },
                    "& .MuiSvgIcon-root": {
                      width: 18,
                      height: 18,
                      border: `1px solid ${colors.grey[200]}`,
                      borderRadius: "4px",
                      backgroundColor: "white",
                      "& path": {
                        display: "none",
                      },
                    },
                    "&.Mui-checked .MuiSvgIcon-root": {
                      backgroundColor: "#2563EB",
                      borderColor: "#2563EB",
                      "& path": {
                        display: "block",
                        fill: "white",
                      },
                    },
                    "&.MuiCheckbox-indeterminate .MuiSvgIcon-root": {
                      backgroundColor: "#2563EB",
                      borderColor: "#2563EB",
                      "& path": {
                        display: "block",
                        fill: "white",
                      },
                    },
                  }}
                />
              </TableCell>
              {columns.map((column) => (
                <TableCell
                  key={column.key}
                  sx={{
                    width: column.width,
                    backgroundColor: colors.grey[100],
                  }}
                >
                  <Box sx={{ display: "flex", alignItems: "center" }}>
                    <Typography style="tag2" color={colors.grey[800]}>
                      {column.label}
                    </Typography>
                  </Box>
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {data && data.length > 0 ? (
              data.map((situation: any, index: number) => (
                <TableRow
                  hover
                  key={`${situation.id || 'no-id'}-${index}`}
                  sx={{
                    height: "54px",
                    "&:last-child td, &:last-child th": { border: 0 },
                    "&:hover": {
                      backgroundColor: colors.grey[50],
                    },
                  }}
                >
                  <TableCell padding="checkbox">
                    <Checkbox
                      checked={selectedSituations.has(situation.id)}
                      onChange={() => handleSelectSituation(situation.id)}
                      sx={{
                        color: "transparent",
                        "&.Mui-checked": {
                          color: "#2563EB",
                        },
                        "& .MuiSvgIcon-root": {
                          width: 18,
                          height: 18,
                          border: `1px solid ${colors.grey[200]}`,
                          borderRadius: "4px",
                          backgroundColor: "white",
                          "& path": {
                            display: "none",
                          },
                        },
                        "&.Mui-checked .MuiSvgIcon-root": {
                          backgroundColor: "#2563EB",
                          borderColor: "#2563EB",
                          "& path": {
                            display: "block",
                            fill: "white",
                          },
                        },
                      }}
                    />
                  </TableCell>
                  <TableCell
                    component="th"
                    scope="row"
                    sx={{ width: "120px", maxWidth: "120px" }}
                  >
                    <Box
                      sx={{
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        whiteSpace: "nowrap",
                        width: "100%",
                      }}
                    >
                      <Typography style="body4" color={colors.grey[900]}>
                        {new Date(situation.createTime).toLocaleDateString(
                          "en-US",
                          {
                            month: "short",
                            day: "numeric",
                            hour: "2-digit",
                            minute: "2-digit",
                          }
                        )}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell
                    align="left"
                    sx={{ width: "150px", maxWidth: "150px" }}
                  >
                    <Box
                      sx={{
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        whiteSpace: "nowrap",
                        width: "100%",
                      }}
                    >
                      <Typography style="body4" color={colors.grey[900]}>
                        {getIncidentTypeLabel(String(situation.type))}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell
                    align="left"
                    sx={{ width: "80px", maxWidth: "80px" }}
                  >
                    <Box
                      sx={{
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        whiteSpace: "nowrap",
                        width: "100%",
                      }}
                    >
                      <Typography style="body4" color={colors.grey[900]}>
                        {getPriorityLabel(Number(situation.priority))}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell
                    align="left"
                    sx={{ width: "100px", maxWidth: "100px" }}
                  >
                    <Box
                      sx={{
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        whiteSpace: "nowrap",
                        width: "100%",
                      }}
                    >
                      <Typography style="body4" color={colors.grey[900]}>
                        {getStatusDisplay(situation.status)}
                      </Typography>
                    </Box>
                  </TableCell>
                  {(activeTab === 1 || activeTab === 2) && (
                    <TableCell
                      align="left"
                      sx={{ width: "120px", maxWidth: "120px" }}
                    >
                      <Box
                        sx={{
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          whiteSpace: "nowrap",
                          width: "100%",
                        }}
                      >
                        <Typography style="body4" color={colors.grey[900]}>
                          {calculateResponseTime(situation)}
                        </Typography>
                      </Box>
                    </TableCell>
                  )}
                  {activeTab === 2 && (
                    <TableCell
                      align="left"
                      sx={{ width: "120px", maxWidth: "120px" }}
                    >
                      <Box
                        sx={{
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          whiteSpace: "nowrap",
                          width: "100%",
                        }}
                      >
                        <Typography style="body4" color={colors.grey[900]}>
                          {calculateResolutionTime(situation)}
                        </Typography>
                      </Box>
                    </TableCell>
                  )}
                </TableRow>
              ))
            ) : (
              <TableRow sx={{ height: 200 }}>
                <TableCell colSpan={columns.length + 1} align="center">
                  <Typography style="body3" color={colors.grey[600]}>
                    {isLoading
                      ? "Loading incident data..."
                      : "No incidents match the selected filters."}
                  </Typography>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
};
