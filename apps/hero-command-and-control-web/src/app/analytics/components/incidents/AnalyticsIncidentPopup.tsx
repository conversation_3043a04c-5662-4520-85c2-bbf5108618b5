import { getIncidentLabel, getIncidentStatusLabel } from "@/app/utils/utils";
import FileOpenIcon from "@mui/icons-material/FileOpen";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Chip from "@mui/material/Chip";
import Typography from "@mui/material/Typography";
import React from "react";

interface AnalyticsIncidentPopupProps {
  incident: {
    id: string;
    type: string;
    status: string;
    address?: string;
    location?: {
      description?: string;
    };
    description?: string;
  };
}

const AnalyticsIncidentPopup: React.FC<AnalyticsIncidentPopupProps> = ({
  incident,
}) => {
  const handleGoToIncident = () => {
    // Open incident page in new tab
    const incidentUrl = `/incidents?incidentId=${incident.id}`;
    window.open(incidentUrl, '_blank');
  };

  // Get location text - prioritize address, fall back to location description
  const locationText = incident.address || incident.location?.description || "Location not specified";

  return (
    <Box sx={{ p: 1, color: "black", minWidth: "200px" }}>
      {/* Header section: incident details on left and status chip on right */}
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "flex-start",
          gap: 4,
        }}
      >
        <Box sx={{ textAlign: "left" }}>
          <Typography sx={{ fontWeight: 400, fontSize: 16 }}>
            <strong>{incident.id.replace(/[^0-9]/g, "").slice(0, 3)}</strong>{" "}
            {getIncidentLabel(incident.type) || ""}
          </Typography>
          <Typography sx={{ mt: 0.5, fontSize: 10, color: "#374151" }}>
            {locationText}
          </Typography>
        </Box>
        <Chip
          label={getIncidentStatusLabel(String(incident.status || ""))}
          size="small"
          sx={{ fontSize: 12, borderRadius: "7px" }}
        />
      </Box>

      {/* Bottom button */}
      <Box sx={{ width: "100%", mt: 3 }}>
        <Button
          variant="contained"
          startIcon={<FileOpenIcon fontSize={"small"} />}
          disableRipple
          sx={{
            width: "100%",
            borderRadius: "8px",
            bgcolor: "#0060FF",
            textTransform: "none",
            boxShadow: "none",
            fontSize: 12,
            py: 1,
          }}
          onClick={handleGoToIncident}
        >
          Go To Incident
        </Button>
      </Box>
    </Box>
  );
};

export default AnalyticsIncidentPopup;