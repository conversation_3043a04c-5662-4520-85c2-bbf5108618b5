"use client";

import { <PERSON>, Card, Typography as <PERSON><PERSON><PERSON><PERSON>po<PERSON>, Toolt<PERSON> } from "@mui/material";
import type { QueuedCall } from "proto/hero/communications/v1/conversation_pb";
import { useEffect, useMemo, useRef, useState } from "react";
import { Button } from "../../../../design-system/components/Button";
import { Typography } from "../../../../design-system/components/Typography";
import { colors } from "../../../../design-system/tokens";
import { protobufTimestampToMillis } from "../../../cad/components/CommsComponents/utils/timestampUtils";
import type { DateRangeOption } from "../../types";
import { DateRangePicker } from "../common/DateRangePicker";
import { DownloadIcon } from "../common/analyticsIcons";
import { useDownloadState } from "../common/analyticsUtils";

interface CallFrequencyHeatmapProps {
  calls: QueuedCall[];
  isLoading?: boolean;
  selectedRange: DateRangeOption;
  onRangeChange: (range: DateRangeOption) => void;
}

interface HeatmapCell {
  date: string;
  timeSlot: number; // 0-5 representing 4-hour intervals
  count: number;
  intensity: number; // 0-4 for color intensity
}

// Time slot labels for Y-axis
const TIME_SLOTS = ["12am", "4am", "8am", "12pm", "4pm", "8pm"];

// Color scale using design system blues
const INTENSITY_COLORS = [
  colors.blue[50], // No calls
  colors.blue[100], // Low
  colors.blue[300], // Medium-low
  colors.blue[600], // Medium
  colors.blue[700], // Medium-high
  colors.blue[800], // High
];

// Date range options for heatmap (minimum 7 days)
const HEATMAP_DATE_RANGE_OPTIONS = [
  { value: "7d" as const, label: "7d" },
  { value: "1m" as const, label: "1m" },
  { value: "3m" as const, label: "3m" },
];

export const CallFrequencyHeatmap = ({
  calls,
  isLoading,
  selectedRange,
  onRangeChange,
}: CallFrequencyHeatmapProps) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [containerWidth, setContainerWidth] = useState<number>(0);

  // Download state management
  const { isDownloading, withLoadingState } = useDownloadState();

  // Download handler
  const handleDownload = () => {
    withLoadingState(async () => {
      const timestamp = new Date().toISOString().split("T")[0];

      // Custom export function for heatmap to capture full content
      try {
        // Import html2canvas dynamically to avoid SSR issues
        const html2canvas = (await import("html2canvas")).default;

        // Find the inner heatmap content (not the scrollable container)
        const heatmapContent = document.querySelector(
          "[data-heatmap-container] > [data-heatmap-content]"
        ) as HTMLElement;

        if (!heatmapContent) {
          alert("Heatmap content not found for export");
          return;
        }

        // Wait a bit to ensure heatmap is fully rendered
        await new Promise((resolve) => setTimeout(resolve, 500));

        // Temporarily add padding to prevent label cutoff
        const originalPadding = heatmapContent.style.padding;
        heatmapContent.style.padding = "30px";

        const canvas = await html2canvas(heatmapContent, {
          useCORS: true,
          allowTaint: true,
        });

        // Restore original padding
        heatmapContent.style.padding = originalPadding;

        const link = document.createElement("a");
        link.download = `call-frequency-heatmap-${selectedRange}-${timestamp}.png`;
        link.href = canvas.toDataURL("image/png");
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } catch (error) {
        console.error("Error exporting heatmap:", error);
        alert("Failed to export heatmap. Please try again.");
      }
    });
  };

  // Track container width for responsive sizing
  useEffect(() => {
    const updateWidth = () => {
      if (containerRef.current) {
        setContainerWidth(containerRef.current.offsetWidth);
      }
    };

    updateWidth();
    window.addEventListener("resize", updateWidth);
    return () => window.removeEventListener("resize", updateWidth);
  }, []);

  const heatmapData = useMemo(() => {
    // Calculate date range based on selectedRange
    const endDate = new Date();
    endDate.setHours(23, 59, 59, 999);
    const startDate = new Date();

    switch (selectedRange) {
      case "7d":
        startDate.setDate(startDate.getDate() - 6); // 7 days including today
        break;
      case "1m":
        startDate.setMonth(startDate.getMonth() - 1);
        break;
      case "3m":
        startDate.setMonth(startDate.getMonth() - 3);
        break;
    }
    startDate.setHours(0, 0, 0, 0);

    // Generate all dates in range
    const allDates: string[] = [];
    const currentDate = new Date(startDate);
    while (currentDate <= endDate) {
      allDates.push(currentDate.toISOString().split("T")[0]);
      currentDate.setDate(currentDate.getDate() + 1);
    }

    if (!calls || calls.length === 0)
      return { cells: [], dates: allDates, maxCount: 0 };

    // Group calls by date and time slot
    const callsByDateAndSlot: Record<string, Record<number, number>> = {};
    let maxCount = 0;

    calls.forEach((call) => {
      // Parse enqueue time - handle both protobuf Timestamp and ISO string formats
      let enqueueTime: Date | undefined;
      if (call.enqueueTime) {
        const millis = protobufTimestampToMillis(call.enqueueTime);
        if (millis) {
          enqueueTime = new Date(millis);
        }
      }

      if (!enqueueTime || isNaN(enqueueTime.getTime())) return;

      // Get date in YYYY-MM-DD format
      const dateKey = enqueueTime.toISOString().split("T")[0];

      // Get hour and determine time slot (0-5)
      const hour = enqueueTime.getHours();
      const timeSlot = Math.floor(hour / 4);

      // Initialize if needed
      if (!callsByDateAndSlot[dateKey]) {
        callsByDateAndSlot[dateKey] = {};
      }

      // Increment count
      callsByDateAndSlot[dateKey][timeSlot] =
        (callsByDateAndSlot[dateKey][timeSlot] || 0) + 1;
      maxCount = Math.max(maxCount, callsByDateAndSlot[dateKey][timeSlot]);
    });

    // Use all dates from the range
    const dates = allDates;

    // Generate cells for the heatmap
    const cells: HeatmapCell[] = [];
    dates.forEach((date) => {
      for (let slot = 0; slot < 6; slot++) {
        const count = callsByDateAndSlot[date]?.[slot] || 0;
        // Calculate intensity: 0 for no data, 1-5 for data based on relative frequency
        let intensity = 0;
        if (count > 0 && maxCount > 0) {
          intensity = Math.min(5, Math.floor((count / maxCount) * 5) + 1);
        }

        cells.push({
          date,
          timeSlot: slot,
          count,
          intensity,
        });
      }
    });

    return { cells, dates, maxCount };
  }, [calls, selectedRange]);

  // Calculate responsive cell dimensions
  const { cellSize, gap, shouldScroll, shouldRotateLabels } = useMemo(() => {
    const numDates = heatmapData.dates.length;
    const yAxisWidth = 40; // Width of time slot labels
    const padding = 48; // Container padding (24px * 2)
    const minCellSize = 24; // Minimum cell size
    const maxCellSize = 45; // Maximum cell size
    const preferredGap = 4;

    if (containerWidth === 0 || numDates === 0) {
      return {
        cellSize: 38,
        gap: 4,
        shouldScroll: false,
        shouldRotateLabels: false,
      };
    }

    const availableWidth = containerWidth - padding - yAxisWidth;

    // Calculate cell size including gaps
    const totalGapWidth = (numDates - 1) * preferredGap;
    const availableCellWidth = availableWidth - totalGapWidth;
    const calculatedCellSize = Math.floor(availableCellWidth / numDates);

    // Clamp cell size between min and max
    const finalCellSize = Math.max(
      minCellSize,
      Math.min(maxCellSize, calculatedCellSize)
    );

    // Check if we need horizontal scrolling
    const totalRequiredWidth =
      yAxisWidth + numDates * finalCellSize + (numDates - 1) * preferredGap;
    const needsScroll = totalRequiredWidth > containerWidth - padding;

    const rotateLabels = finalCellSize < 34;

    return {
      cellSize: finalCellSize,
      gap: preferredGap,
      shouldScroll: needsScroll,
      shouldRotateLabels: rotateLabels,
    };
  }, [containerWidth, heatmapData.dates.length]);

  // Format date for display
  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);
    return date.toLocaleDateString("en-US", { month: "short", day: "numeric" });
  };

  // Main card content with fixed height
  const cardContent = (
    <>
      {/* Call Frequency Header */}
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: "24px",
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center", gap: "12px" }}>
          <Typography style="h2" color={colors.grey[900]}>
            <span style={{ fontSize: "20px", fontWeight: 600 }}>
              Call Frequency Heatmap
            </span>
          </Typography>
          {isLoading && (
            <Box sx={{ display: "flex", alignItems: "center", gap: "4px" }}>
              <Box
                sx={{
                  width: "8px",
                  height: "8px",
                  borderRadius: "50%",
                  bgcolor: colors.blue[600],
                  animation: "bounce 1.4s ease-in-out infinite both",
                  "@keyframes bounce": {
                    "0%, 80%, 100%": {
                      transform: "scale(0)",
                    },
                    "40%": {
                      transform: "scale(1)",
                    },
                  },
                }}
              />
              <Box
                sx={{
                  width: "8px",
                  height: "8px",
                  borderRadius: "50%",
                  bgcolor: colors.blue[600],
                  animation: "bounce 1.4s ease-in-out infinite both",
                  animationDelay: "0.15s",
                }}
              />
              <Box
                sx={{
                  width: "8px",
                  height: "8px",
                  borderRadius: "50%",
                  bgcolor: colors.blue[600],
                  animation: "bounce 1.4s ease-in-out infinite both",
                  animationDelay: "0.3s",
                }}
              />
            </Box>
          )}
        </Box>

        {/* Controls */}
        <Box sx={{ display: "flex", gap: "16px", alignItems: "center" }}>
          {/* Download Button */}
          <Button
            label="Download"
            style="ghost"
            color="grey"
            prominence={true}
            size="small"
            leftIcon={<DownloadIcon />}
            onClick={handleDownload}
            disabled={
              isDownloading || isLoading || heatmapData.dates.length === 0
            }
            isLoading={isDownloading}
          />

          {/* Date Range Filter */}
          <DateRangePicker
            value={selectedRange}
            onChange={onRangeChange}
            options={HEATMAP_DATE_RANGE_OPTIONS}
          />
        </Box>
      </Box>

      {/* Heatmap Container */}
      <Box
        ref={containerRef}
        data-heatmap-container
        sx={{
          height: "340px",
          borderRadius: "8px",
          border: `1px solid ${colors.grey[300]}`,
          bgcolor: "#FFF",
          p: "24px",
          overflowX: shouldScroll ? "auto" : "hidden",
          overflowY: "hidden",
          display: "flex",
          alignItems: "center",
          justifyContent:
            heatmapData.dates.length === 0 && !isLoading
              ? "center"
              : "flex-start",
        }}
      >
        {isLoading ? (
          <Box
            sx={{ display: "inline-block", minWidth: "fit-content" }}
            data-heatmap-content
          >
            {/* Heatmap grid - same structure as real data */}
            {TIME_SLOTS.map((slot, slotIndex) => (
              <Box
                key={slot}
                sx={{ display: "flex", gap: `${gap}px`, mb: `${gap}px` }}
              >
                {/* Time slot label */}
                <Box
                  sx={{
                    width: "40px",
                    height: `${cellSize}px`,
                    pr: 1,
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "flex-end",
                    fontSize: "14px",
                    color: colors.grey[600],
                  }}
                >
                  {slot}
                </Box>

                {/* Heatmap cells - mirror the real structure exactly */}
                {Array.from({
                  length:
                    selectedRange === "7d"
                      ? 7
                      : selectedRange === "1m"
                      ? 30
                      : 90,
                }).map((_, index) => (
                  <Box
                    key={index}
                    sx={{
                      width: `${cellSize}px`,
                      height: `${cellSize}px`,
                      bgcolor: colors.grey[200],
                      borderRadius: "4px",
                      cursor: "pointer",
                      transition: "all 0.2s",
                      animation: "pulse 1.5s ease-in-out infinite",
                      animationDelay: `${slotIndex * 0.1 + index * 0.02}s`,
                      "@keyframes pulse": {
                        "0%, 100%": {
                          opacity: 0.4,
                        },
                        "50%": {
                          opacity: 0.8,
                        },
                      },
                    }}
                  />
                ))}
              </Box>
            ))}

            {/* X-axis labels (dates) - same structure as real data */}
            <Box
              sx={{
                display: "flex",
                gap: `${gap}px`,
                mt: shouldRotateLabels ? "16px" : "8px",
                mb: shouldRotateLabels ? "8px" : "0px",
              }}
            >
              <Box sx={{ width: "40px" }}>
                {/* Empty space for alignment with Y-axis labels */}
              </Box>
              {Array.from({
                length:
                  selectedRange === "7d" ? 7 : selectedRange === "1m" ? 30 : 90,
              }).map((_, index) => (
                <Box
                  key={index}
                  sx={{
                    width: `${cellSize}px`,
                    height: shouldRotateLabels ? "24px" : "auto",
                    display: "flex",
                    alignItems: shouldRotateLabels ? "flex-start" : "center",
                    justifyContent: "center",
                    fontSize: "12px",
                    color: colors.grey[600],
                    overflow: "visible",
                  }}
                >
                  <Box
                    sx={{
                      transform: shouldRotateLabels ? "rotate(-45deg)" : "none",
                      transformOrigin: "center center",
                      whiteSpace: "nowrap",
                      textAlign: "center",
                      width: "20px",
                      height: "8px",
                      bgcolor: colors.grey[200],
                      borderRadius: "2px",
                      animation: "pulse 1.5s ease-in-out infinite",
                      animationDelay: `${index * 0.05}s`,
                    }}
                  />
                </Box>
              ))}
            </Box>
          </Box>
        ) : heatmapData.dates.length === 0 ? (
          <Typography style="body2" color={colors.grey[500]}>
            No call data available
          </Typography>
        ) : (
          <Box
            sx={{ display: "inline-block", minWidth: "fit-content" }}
            data-heatmap-content
          >
            {/* Heatmap grid */}
            {TIME_SLOTS.map((slot, slotIndex) => (
              <Box
                key={slot}
                sx={{ display: "flex", gap: `${gap}px`, mb: `${gap}px` }}
              >
                {/* Time slot label */}
                <Box
                  sx={{
                    width: "40px",
                    height: `${cellSize}px`,
                    pr: 1,
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "flex-end",
                    fontSize: "14px",
                    color: colors.grey[600],
                  }}
                >
                  {slot}
                </Box>

                {/* Heatmap cells */}
                {heatmapData.dates.map((date) => {
                  const cell = heatmapData.cells.find(
                    (c) => c.date === date && c.timeSlot === slotIndex
                  );
                  const count = cell?.count || 0;
                  const intensity = cell?.intensity || 0;

                  return (
                    <Tooltip
                      key={`${date}-${slotIndex}`}
                      title={
                        <Box>
                          <MuiTypography variant="body2">
                            {formatDate(date)} {slot}
                          </MuiTypography>
                          <MuiTypography variant="body2" fontWeight="bold">
                            {count} {count === 1 ? "call" : "calls"}
                          </MuiTypography>
                        </Box>
                      }
                      placement="top"
                    >
                      <Box
                        sx={{
                          width: `${cellSize}px`,
                          height: `${cellSize}px`,
                          bgcolor: INTENSITY_COLORS[intensity],
                          borderRadius: "4px",
                          cursor: "pointer",
                          transition: "all 0.2s",
                          "&:hover": {
                            transform: "scale(1.1)",
                            zIndex: 1,
                            boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
                          },
                        }}
                      />
                    </Tooltip>
                  );
                })}
              </Box>
            ))}

            {/* X-axis labels (dates) */}
            <Box
              sx={{
                display: "flex",
                gap: `${gap}px`,
                mt: shouldRotateLabels ? "16px" : "8px",
                mb: shouldRotateLabels ? "8px" : "0px",
              }}
            >
              <Box sx={{ width: "40px" }}>
                {/* Empty space for alignment with Y-axis labels */}
              </Box>
              {heatmapData.dates.map((date) => (
                <Box
                  key={date}
                  sx={{
                    width: `${cellSize}px`,
                    height: shouldRotateLabels ? "24px" : "auto",
                    display: "flex",
                    alignItems: shouldRotateLabels ? "flex-start" : "center",
                    justifyContent: "center",
                    fontSize: "12px",
                    color: colors.grey[600],
                    overflow: "visible",
                  }}
                >
                  <Box
                    sx={{
                      transform: shouldRotateLabels ? "rotate(-45deg)" : "none",
                      transformOrigin: "center center",
                      whiteSpace: "nowrap",
                      textAlign: "center",
                    }}
                  >
                    {formatDate(date)}
                  </Box>
                </Box>
              ))}
            </Box>
          </Box>
        )}
      </Box>
    </>
  );

  return (
    <Card
      sx={{
        bgcolor: colors.grey[50],
        border: `1px solid ${colors.grey[300]}`,
        borderRadius: "12px",
        p: "24px",
        boxShadow: "none",
      }}
    >
      {cardContent}
    </Card>
  );
};
