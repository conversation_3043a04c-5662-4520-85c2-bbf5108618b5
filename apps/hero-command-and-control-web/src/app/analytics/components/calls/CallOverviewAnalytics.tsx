"use client";

import { Box, Card } from "@mui/material";
import { useMemo } from "react";
import { Typography } from "../../../../design-system/components/Typography";
import { colors } from "../../../../design-system/tokens";
import { useAllCalls } from "../../hooks/useAllCalls";
import type { DateRangeOption } from "../../types";
import { AnalyticsCard } from "../common/AnalyticsCard";
import { DateRangePicker } from "../common/DateRangePicker";
import {
  DATE_RANGE_OPTIONS,
  getDateRange,
  getPreviousDateRange,
  getComparisonText,
  formatTimeFromMinutes,
} from "../common/analyticsUtils";
import { create } from "@bufbuild/protobuf";
import { ListCallsRequestSchema } from "proto/hero/communications/v1/conversation_pb";

interface CallOverviewAnalyticsProps {
  selectedRange: DateRangeOption;
  onRangeChange: (range: DateRangeOption) => void;
}

export const CallOverviewAnalytics = ({
  selectedRange,
  onRangeChange,
}: CallOverviewAnalyticsProps) => {
  // Calculate date ranges using shared utilities
  const dateRanges = useMemo(() => {
    return {
      current: getDateRange(selectedRange),
      previous: getPreviousDateRange(selectedRange),
    };
  }, [selectedRange]);

  // Create request for current period
  const currentPeriodRequest = useMemo(() => {
    const currentRange = dateRanges.current;
    return create(ListCallsRequestSchema, {
      startDate: currentRange.from || "",
      endDate: currentRange.to || "",
      page: 1,
      pageSize: 100,
      sortOrder: "desc",
    });
  }, [dateRanges.current]);

  // Create request for previous period
  const previousPeriodRequest = useMemo(() => {
    const previousRange = dateRanges.previous;
    return create(ListCallsRequestSchema, {
      startDate: previousRange.from || "",
      endDate: previousRange.to || "",
      page: 1,
      pageSize: 100,
      sortOrder: "desc",
    });
  }, [dateRanges.previous]);

  const { data: currentData, isLoading: currentLoading } = useAllCalls(currentPeriodRequest);
  const { data: previousData, isLoading: previousLoading } = useAllCalls(previousPeriodRequest);

  const isLoading = currentLoading || previousLoading;

  // Calculate metrics for current period
  const calculateMetrics = (data: any) => {
    if (!data?.calls) {
      return {
        totalCalls: 0,
        inboundCalls: 0,
        outboundCalls: 0,
        avgCallDurationMinutes: 0,
        avgWaitTimeMinutes: 0,
        abandonedCalls: 0,
      };
    }

    let inbound = 0;
    let outbound = 0;
    let totalDurationMinutes = 0;
    let callsWithDuration = 0;
    let totalWaitTimeMinutes = 0;
    let callsWithWaitTime = 0;
    let abandoned = 0;

    data.calls.forEach((call: any) => {
      if (call.direction === "inbound") {
        inbound++;
      } else if (call.direction === "outbound") {
        outbound++;
      }

      // Check for abandoned calls: have enqueue time but no call start time
      if (call.enqueueTime && !call.callStartTime) {
        abandoned++;
      }

      // Calculate duration if both start and end times exist
      if (call.callStartTime && call.callEndTime) {
        // Handle both protobuf Timestamp and ISO string formats
        const startTime = typeof call.callStartTime === 'string' 
          ? new Date(call.callStartTime) 
          : call.callStartTime.toDate();
        const endTime = typeof call.callEndTime === 'string' 
          ? new Date(call.callEndTime) 
          : call.callEndTime.toDate();
        
        const durationMinutes = (endTime.getTime() - startTime.getTime()) / (1000 * 60); // in minutes
        totalDurationMinutes += durationMinutes;
        callsWithDuration++;
      }

      // Calculate wait time if both enqueue and start times exist
      if (call.enqueueTime && call.callStartTime) {
        // Handle both protobuf Timestamp and ISO string formats
        const enqueueTime = typeof call.enqueueTime === 'string' 
          ? new Date(call.enqueueTime) 
          : call.enqueueTime.toDate();
        const startTime = typeof call.callStartTime === 'string' 
          ? new Date(call.callStartTime) 
          : call.callStartTime.toDate();
        
        const waitTimeMinutes = (startTime.getTime() - enqueueTime.getTime()) / (1000 * 60); // in minutes
        if (waitTimeMinutes >= 0) { // Only count positive wait times
          totalWaitTimeMinutes += waitTimeMinutes;
          callsWithWaitTime++;
        }
      }
    });

    return {
      totalCalls: data.totalResults || data.calls.length,
      inboundCalls: inbound,
      outboundCalls: outbound,
      avgCallDurationMinutes: callsWithDuration > 0 ? totalDurationMinutes / callsWithDuration : 0,
      avgWaitTimeMinutes: callsWithWaitTime > 0 ? totalWaitTimeMinutes / callsWithWaitTime : 0,
      abandonedCalls: abandoned,
    };
  };

  const currentMetrics = calculateMetrics(currentData);
  const previousMetrics = calculateMetrics(previousData);

  return (
    <Card
      sx={{
        bgcolor: colors.grey[50],
        border: `1px solid ${colors.grey[300]}`,
        borderRadius: "12px",
        p: "24px",
        boxShadow: "none",
      }}
    >
      {/* Call Overview Header */}
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: "24px",
        }}
      >
        <Typography style="h2" color={colors.grey[900]}>
          <span style={{ fontSize: "20px", fontWeight: 600 }}>
            Call Overview
          </span>
        </Typography>

        {/* Date Range Filter */}
        <DateRangePicker
          value={selectedRange}
          onChange={onRangeChange}
          options={DATE_RANGE_OPTIONS}
        />
      </Box>

      {/* Analytics Cards Grid */}
      <Box
        sx={{
          display: "grid",
          gridTemplateColumns: {
            xs: "1fr",
            sm: "repeat(2, 1fr)",
            lg: "repeat(3, 1fr)",
          },
          gap: "24px",
        }}
      >
        <AnalyticsCard
          title="Total Calls"
          currentValue={currentMetrics.totalCalls}
          previousValue={previousMetrics.totalCalls}
          isLoading={isLoading}
          comparisonText={getComparisonText(selectedRange)}
        />
        <AnalyticsCard
          title="Inbound Calls"
          currentValue={currentMetrics.inboundCalls}
          previousValue={previousMetrics.inboundCalls}
          isLoading={isLoading}
          comparisonText={getComparisonText(selectedRange)}
        />
        <AnalyticsCard
          title="Outbound Calls"
          currentValue={currentMetrics.outboundCalls}
          previousValue={previousMetrics.outboundCalls}
          isLoading={isLoading}
          comparisonText={getComparisonText(selectedRange)}
        />
        <AnalyticsCard
          title="Avg. Call Duration"
          currentValue={currentMetrics.avgCallDurationMinutes}
          previousValue={previousMetrics.avgCallDurationMinutes}
          isLoading={isLoading}
          comparisonText={getComparisonText(selectedRange)}
          formatValue={formatTimeFromMinutes}
        />
        <AnalyticsCard
          title="Abandoned Calls"
          currentValue={currentMetrics.abandonedCalls}
          previousValue={previousMetrics.abandonedCalls}
          isLoading={isLoading}
          comparisonText={getComparisonText(selectedRange)}
        />
        <AnalyticsCard
          title="Avg. Wait Time"
          currentValue={currentMetrics.avgWaitTimeMinutes}
          previousValue={previousMetrics.avgWaitTimeMinutes}
          isLoading={isLoading}
          comparisonText={getComparisonText(selectedRange)}
          formatValue={formatTimeFromMinutes}
        />
      </Box>
    </Card>
  );
};