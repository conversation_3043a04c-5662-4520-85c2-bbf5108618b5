"use client";

import { Box, Card } from "@mui/material";
import { DateRange, SearchCasesRequest } from "proto/hero/cases/v1/cases_pb";
import { useMemo } from "react";
import { Typography } from "../../../../design-system/components/Typography";
import { colors } from "../../../../design-system/tokens";
import { useAllCases } from "../../hooks/useAllCases";
import type { DateRangeOption } from "../../types";
import { AnalyticsCard } from "../common/AnalyticsCard";
import { DateRangePicker } from "../common/DateRangePicker";
import {
  DATE_RANGE_OPTIONS,
  getDateRange,
  getPreviousDateRange,
  getComparisonText,
  formatTimeFromMinutes,
} from "../common/analyticsUtils";

interface CaseOverviewAnalyticsProps {
  selectedRange: DateRangeOption;
  onRangeChange: (range: DateRangeOption) => void;
}

export const CaseOverviewAnalytics = ({
  selectedRange,
  onRangeChange,
}: CaseOverviewAnalyticsProps) => {
  // Calculate date ranges
  const dateRanges = useMemo(() => {
    return {
      current: getDateRange(selectedRange) as DateRange,
      previous: getPreviousDateRange(selectedRange) as DateRange,
    };
  }, [selectedRange]);

  // Current period data
  const currentPeriodRequest = useMemo(
    () =>
      ({
        query: "",
        createTime: dateRanges.current,
      } as SearchCasesRequest),
    [dateRanges.current]
  );

  // Previous period data
  const previousPeriodRequest = useMemo(
    () =>
      ({
        query: "",
        createTime: dateRanges.previous,
      } as SearchCasesRequest),
    [dateRanges.previous]
  );

  const { data: currentData, isLoading: currentLoading } =
    useAllCases(currentPeriodRequest);

  const { data: previousData, isLoading: previousLoading } = useAllCases(
    previousPeriodRequest
  );

  const isLoading = currentLoading || previousLoading;

  // Calculate real metrics from API data
  const currentTotal = currentData?.cases?.length || 0;
  const previousTotal = previousData?.cases?.length || 0;

  // Calculate average case closure time (createTime to closeTime) - returns raw minutes
  const calculateAvgClosureTimeMinutes = (data: any): number => {
    if (!data?.cases?.length) return 0;

    let totalClosureMinutes = 0;
    let count = 0;

    data.cases.forEach((caseItem: any) => {
      if (caseItem.closeTime && caseItem.createTime) {
        const createTime = new Date(caseItem.createTime);
        const closeTime = new Date(caseItem.closeTime);
        const diffMinutes =
          (closeTime.getTime() - createTime.getTime()) / (1000 * 60);
        if (diffMinutes > 0) {
          totalClosureMinutes += diffMinutes;
          count++;
        }
      }
    });

    if (count === 0) return 0;
    return totalClosureMinutes / count;
  };

  // Calculate open cases (status not CASE_STATUS_CLOSED)
  const calculateOpenCases = (data: any) => {
    if (!data?.cases?.length) return 0;

    return data.cases.filter(
      (caseItem: any) => caseItem.status !== "CASE_STATUS_CLOSED"
    ).length;
  };

  const currentAvgClosureTimeMinutes = calculateAvgClosureTimeMinutes(currentData);
  const previousAvgClosureTimeMinutes = calculateAvgClosureTimeMinutes(previousData);

  const currentOpenCases = calculateOpenCases(currentData);
  const previousOpenCases = calculateOpenCases(previousData);


  // Now using shared formatTimeFromMinutes utility

  // Mock data for CLERY, Warrants, and NIBRS (as requested)
  const mockMetrics = {
    cleryCases: { current: 8, previous: 6 },
    warrantsInvolved: { current: 15, previous: 18 },
    nibrsCases: { current: 24, previous: 21 },
  };

  // Now using shared utilities for comparison text and date range options

  return (
    <Card
      sx={{
        bgcolor: colors.grey[50],
        border: `1px solid ${colors.grey[300]}`,
        borderRadius: "12px",
        p: "24px",
        boxShadow: "none",
      }}
    >
      {/* Case Overview Header */}
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: "24px",
        }}
      >
        <Typography style="h2" color={colors.grey[900]}>
          <span style={{ fontSize: "20px", fontWeight: 600 }}>
            Case Overview
          </span>
        </Typography>

        {/* Date Range Filter */}
        <DateRangePicker
          value={selectedRange}
          onChange={onRangeChange}
          options={DATE_RANGE_OPTIONS}
        />
      </Box>

      {/* Analytics Cards Grid */}
      <Box
        sx={{
          display: "grid",
          gridTemplateColumns: {
            xs: "1fr",
            sm: "repeat(2, 1fr)",
            lg: "repeat(3, 1fr)",
          },
          gap: "24px",
        }}
      >
        <AnalyticsCard
          title="Total Cases"
          currentValue={currentTotal}
          previousValue={previousTotal}
          isLoading={isLoading}
          comparisonText={getComparisonText(selectedRange)}
        />
        <AnalyticsCard
          title="Avg. Case Closure Time"
          currentValue={currentAvgClosureTimeMinutes}
          previousValue={previousAvgClosureTimeMinutes}
          isLoading={isLoading}
          comparisonText={getComparisonText(selectedRange)}
          formatValue={formatTimeFromMinutes}
        />
        <AnalyticsCard
          title="CLERY Cases"
          currentValue={mockMetrics.cleryCases.current}
          previousValue={mockMetrics.cleryCases.previous}
          isLoading={isLoading}
          comparisonText={getComparisonText(selectedRange)}
        />
        <AnalyticsCard
          title="Open Cases"
          currentValue={currentOpenCases}
          previousValue={previousOpenCases}
          isLoading={isLoading}
          comparisonText={getComparisonText(selectedRange)}
        />
        <AnalyticsCard
          title="Cases Involving Warrants"
          currentValue={mockMetrics.warrantsInvolved.current}
          previousValue={mockMetrics.warrantsInvolved.previous}
          isLoading={isLoading}
          comparisonText={getComparisonText(selectedRange)}
        />
        <AnalyticsCard
          title="NIBRS Cases"
          currentValue={mockMetrics.nibrsCases.current}
          previousValue={mockMetrics.nibrsCases.previous}
          isLoading={isLoading}
          comparisonText={getComparisonText(selectedRange)}
        />
      </Box>
    </Card>
  );
};
