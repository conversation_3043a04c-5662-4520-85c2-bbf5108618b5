import { CaseType } from "proto/hero/cases/v1/cases_pb";

// Case Type options for display mapping
export const CASE_TYPE_OPTIONS = [
  { value: "CASE_TYPE_SECURITY_INCIDENT", label: "Security Incident" },
  { value: "CASE_TYPE_SAFETY_INCIDENT", label: "Safety Incident" },
  { value: "CASE_TYPE_OPERATIONAL_TASK", label: "Operational Task" },
  { value: "CASE_TYPE_CUSTOMER_COMPLAINT", label: "Customer Complaint" },
  { value: "CASE_TYPE_INVESTIGATION", label: "Investigation" },
  { value: "CASE_TYPE_COMPLIANCE_REVIEW", label: "Compliance Review" },
  { value: "CASE_TYPE_INSURANCE_CLAIM", label: "Insurance Claim" },
  { value: "CASE_TYPE_ADMINISTRATIVE", label: "Administrative" },
  { value: "CASE_TYPE_OTHER", label: "Other" },
];

// Case Priority options
export const CASE_PRIORITY_OPTIONS = [
  { value: 1, label: "Low" },
  { value: 2, label: "Medium" },
  { value: 3, label: "High" },
  { value: 4, label: "Critical" },
];

// Case Status options
export const CASE_STATUS_OPTIONS = [
  { value: "CASE_STATUS_NEW", label: "New" },
  { value: "CASE_STATUS_OPEN", label: "Open" },
  { value: "CASE_STATUS_UNDER_REVIEW", label: "Under Review" },
  { value: "CASE_STATUS_INVESTIGATING", label: "Investigating" },
  { value: "CASE_STATUS_PENDING_INFORMATION", label: "Pending Information" },
  { value: "CASE_STATUS_ON_HOLD", label: "On Hold" },
  { value: "CASE_STATUS_ESCALATED", label: "Escalated" },
  { value: "CASE_STATUS_RESOLVED", label: "Resolved" },
  { value: "CASE_STATUS_CLOSED", label: "Closed" },
  { value: "CASE_STATUS_ARCHIVED", label: "Archived" },
];

// Case Type options for filters (with enum values)
export const CASE_TYPE_FILTER_OPTIONS = [
  { value: "security_incident", label: "Security Incident", enumValue: CaseType.SECURITY_INCIDENT },
  { value: "safety_incident", label: "Safety Incident", enumValue: CaseType.SAFETY_INCIDENT },
  { value: "operational_task", label: "Operational Task", enumValue: CaseType.OPERATIONAL_TASK },
  { value: "customer_complaint", label: "Customer Complaint", enumValue: CaseType.CUSTOMER_COMPLAINT },
  { value: "investigation", label: "Investigation", enumValue: CaseType.INVESTIGATION },
  { value: "compliance_review", label: "Compliance Review", enumValue: CaseType.COMPLIANCE_REVIEW },
  { value: "insurance_claim", label: "Insurance Claim", enumValue: CaseType.INSURANCE_CLAIM },
  { value: "administrative", label: "Administrative", enumValue: CaseType.ADMINISTRATIVE },
  { value: "other", label: "Other", enumValue: CaseType.OTHER },
];

// Priority options for filters
export const CASE_PRIORITY_FILTER_OPTIONS = [
  { value: "1", label: "Priority 1" },
  { value: "2", label: "Priority 2" },
  { value: "3", label: "Priority 3" },
  { value: "4", label: "Priority 4" },
  { value: "5", label: "Priority 5" },
];

// Case Time Analytics tabs
export const CASE_TIME_TABS = ["Cases", "Resolution Time"];

// Case Tag options 
export const CASE_TAG_OPTIONS = [
  { value: "CLERY", label: "CLERY" },
  { value: "NIBRS", label: "NIBRS" },
  { value: "Title IX", label: "Title IX" },
  { value: "Use of Force", label: "Use of Force" },
  { value: "Pursuit", label: "Pursuit" },
  { value: "Juvenile Involved", label: "Juvenile Involved" },
  { value: "Confidential", label: "Confidential" },
  { value: "Repeat Offender", label: "Repeat Offender" },
  { value: "Pattern Crime", label: "Pattern Crime" },
  { value: "FSL", label: "FSL" },
  { value: "Transient Involved", label: "Transient Involved" },
];

// Search filters and other consumers expecting a full list of tags including the deleted tags 
export const ALL_TAG_OPTIONS = [
  { value: "CLERY", label: "CLERY" },
  { value: "NIBRS", label: "NIBRS" },
  { value: "Title IX", label: "Title IX" },
  { value: "Use of Force", label: "Use of Force" },
  { value: "Pursuit", label: "Pursuit" },
  { value: "Juvenile Involved", label: "Juvenile Involved" },
  { value: "Confidential", label: "Confidential" },
  { value: "Repeat Offender", label: "Repeat Offender" },
  { value: "Pattern Crime", label: "Pattern Crime" },
  { value: "FSL", label: "FSL" },
  { value: "Transient Involved", label: "Transient Involved" },
];

