"use client";

import CheckIcon from "@mui/icons-material/Check";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import { Box, Card, Menu, MenuItem, Tab, Tabs } from "@mui/material";
import { DateRange, SearchCasesRequest } from "proto/hero/cases/v1/cases_pb";
import { useEffect, useMemo, useState } from "react";
import { Button } from "../../../../design-system/components/Button";
import { Typography } from "../../../../design-system/components/Typography";
import { colors } from "../../../../design-system/tokens";
import { useAllCases } from "../../hooks/useAllCases";
import type { DateRangeOption } from "../../types";
import {
  BarChartHorizontalIcon,
  BarChartVerticalIcon,
  DownloadIcon,
  GraphIcon,
  LineChartIcon,
  TableIcon,
} from "../common/analyticsIcons";
import {
  DATE_RANGE_OPTIONS,
  exportChartAsImage,
  exportToCSV,
  formatPeriodForDisplay,
  generateTimeRange,
  getPeriodKey,
  getTimeScale,
  isDownloadDisabled,
  useDownloadState,
} from "../common/analyticsUtils";
import { TimeSeriesBarChartHorizontal } from "../common/charts/TimeSeriesBarChartHorizontal";
import { TimeSeriesBarChartVertical } from "../common/charts/TimeSeriesBarChartVertical";
import { TimeSeriesLineChart } from "../common/charts/TimeSeriesLineChart";
import { DateRangePicker } from "../common/DateRangePicker";
import { CaseAnalyticsFilters } from "./CaseAnalyticsFilters";
import { CaseAnalyticsTable } from "./CaseAnalyticsTable";
import { CASE_TIME_TABS, CASE_TYPE_OPTIONS } from "./constants";

export const CaseTimeAnalytics = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [viewType, setViewType] = useState<"graph" | "table">("graph");

  // Download loading state
  const { isDownloading, withLoadingState } = useDownloadState();
  const [chartType, setChartType] = useState<
    "bar-horizontal" | "bar-vertical" | "line"
  >("bar-vertical");
  const [selectedTypes, setSelectedTypes] = useState<string[]>([]);
  const [selectedPriorities, setSelectedPriorities] = useState<number[]>([]);
  const [selectedDateRange, setSelectedDateRange] =
    useState<DateRangeOption>("7d");
  const [customDateRange, setCustomDateRange] = useState<
    [Date | null, Date | null]
  >([null, null]);
  const [viewMenuAnchor, setViewMenuAnchor] = useState<null | HTMLElement>(
    null
  );
  const [chartMenuAnchor, setChartMenuAnchor] = useState<null | HTMLElement>(
    null
  );

  // Table selection state
  const [selectedTableRows, setSelectedTableRows] = useState<Set<string>>(
    new Set()
  );

  // Clear table selection when view type changes
  useEffect(() => {
    setSelectedTableRows(new Set());
  }, [viewType]);

  // Helper function to get the current view type details
  const getViewTypeDetails = () => {
    switch (viewType) {
      case "graph":
        return { label: "Graph", icon: <GraphIcon /> };
      case "table":
        return { label: "Table", icon: <TableIcon /> };
      default:
        return { label: "Graph", icon: <GraphIcon /> };
    }
  };

  const tabs = CASE_TIME_TABS;

  const handleTabChange = (_: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  // Calculate date range for the chart's own data
  const dateRange = useMemo(() => {
    const now = new Date();

    // If custom date range is set, use it instead of predefined options
    if (customDateRange[0] && customDateRange[1]) {
      return {
        from: customDateRange[0].toISOString(),
        to: customDateRange[1].toISOString(),
      } as DateRange;
    }

    // Otherwise use predefined date range
    const end = new Date(now);
    const start = new Date(now);

    switch (selectedDateRange) {
      case "1h":
        start.setHours(start.getHours() - 1);
        break;
      case "1d":
        start.setDate(start.getDate() - 1);
        break;
      case "7d":
        start.setDate(start.getDate() - 7);
        break;
      case "1m":
        start.setMonth(start.getMonth() - 1);
        break;
      case "3m":
        start.setMonth(start.getMonth() - 3);
        break;
      case "1y":
        start.setFullYear(start.getFullYear() - 1);
        break;
    }

    return {
      from: start.toISOString(),
      to: end.toISOString(),
    } as DateRange;
  }, [selectedDateRange, customDateRange]);

  // Independent data fetching for this component
  const searchRequest = useMemo(
    () =>
      ({
        query: "",
        createTime: dateRange,
      } as SearchCasesRequest),
    [dateRange]
  );

  const { data, isLoading } = useAllCases(searchRequest);

  // Filter cases based on selected filters
  const getFilteredCases = () => {
    if (!data?.cases?.length) return [];

    return data.cases.filter((caseItem: any) => {
      // Filter by case type
      if (selectedTypes.length > 0) {
        const typeStr = String(caseItem.type);

        const matchesType = selectedTypes.some((selectedValue) => {
          // Find the corresponding case type option
          const caseTypeOption = CASE_TYPE_OPTIONS.find(
            (option) =>
              option.label.toLowerCase().replace(/\s+/g, "_") === selectedValue
          );
          if (!caseTypeOption) return false;

          // Compare the enum string with the backend data
          return caseTypeOption.value === typeStr;
        });

        if (!matchesType) return false;
      }

      // Filter by priority
      if (selectedPriorities.length > 0) {
        const casePriority = Number(caseItem.priority);
        if (!selectedPriorities.includes(casePriority)) return false;
      }

      return true;
    });
  };

  // Helper function to calculate resolution time (create to resolved/closed)
  const calculateResolutionTime = (caseItem: any): number | null => {
    if (!caseItem.closeTime || !caseItem.createTime) return null;

    const createTime = new Date(caseItem.createTime);
    const closeTime = new Date(caseItem.closeTime);
    const diffMinutes =
      (closeTime.getTime() - createTime.getTime()) / (1000 * 60);

    return diffMinutes > 0 ? diffMinutes : null;
  };

  // Process filtered data for time-series visualization
  const getTimeSeriesData = () => {
    const filteredCases = getFilteredCases();
    const allPeriods = generateTimeRange(dateRange, selectedDateRange);
    const timeScale = getTimeScale(selectedDateRange);

    if (activeTab === 0) {
      // Cases tab - count by period
      const periodCounts: { [key: string]: number } = {};

      // Initialize all periods with 0
      allPeriods.forEach((period) => {
        periodCounts[period] = 0;
      });

      // Count actual cases
      filteredCases.forEach((caseItem: any) => {
        const date = new Date(caseItem.createTime);
        const periodKey = getPeriodKey(date, timeScale);
        if (Object.prototype.hasOwnProperty.call(periodCounts, periodKey)) {
          periodCounts[periodKey] = (periodCounts[periodKey] || 0) + 1;
        }
      });

      return allPeriods.map((period) => ({
        date:
          timeScale === "hour"
            ? new Date(period)
            : timeScale === "month"
            ? new Date(period + "-01")
            : new Date(period),
        value: periodCounts[period],
        label: `${periodCounts[period]} cases`,
        displayLabel: formatPeriodForDisplay(
          period,
          timeScale,
          selectedDateRange
        ),
      }));
    } else {
      // Resolution Time tab - average resolution time by period
      const periodResolutionTimes: { [key: string]: number[] } = {};

      // Initialize all periods with empty arrays
      allPeriods.forEach((period) => {
        periodResolutionTimes[period] = [];
      });

      // Collect resolution times
      filteredCases.forEach((caseItem: any) => {
        const resolutionTime = calculateResolutionTime(caseItem);
        if (resolutionTime !== null) {
          const date = new Date(caseItem.createTime);
          const periodKey = getPeriodKey(date, timeScale);
          if (
            Object.prototype.hasOwnProperty.call(
              periodResolutionTimes,
              periodKey
            )
          ) {
            periodResolutionTimes[periodKey].push(resolutionTime);
          }
        }
      });

      return allPeriods.map((period) => {
        const times = periodResolutionTimes[period];
        if (times.length === 0) {
          return {
            date:
              timeScale === "hour"
                ? new Date(period)
                : timeScale === "month"
                ? new Date(period + "-01")
                : new Date(period),
            value: 0,
            label: "No data",
            displayLabel: formatPeriodForDisplay(
              period,
              timeScale,
              selectedDateRange
            ),
          };
        }
        const avgTime =
          times.reduce((sum, time) => sum + time, 0) / times.length;
        const avgMinutes = Math.round(avgTime);
        return {
          date:
            timeScale === "hour"
              ? new Date(period)
              : timeScale === "month"
              ? new Date(period + "-01")
              : new Date(period),
          value: avgMinutes,
          label: `${avgMinutes}m avg`,
          displayLabel: formatPeriodForDisplay(
            period,
            timeScale,
            selectedDateRange
          ),
        };
      });
    }
  };

  const filteredCases = getFilteredCases();
  const timeSeriesData = getTimeSeriesData();

  // Get current tab info for labels
  const getCurrentTabInfo = () => {
    switch (activeTab) {
      case 0:
        return { yLabel: "Count", title: "Cases Over Time" };
      case 1:
        return { yLabel: "Minutes", title: "Resolution Time Over Time" };
      default:
        return { yLabel: "Count", title: "Cases Over Time" };
    }
  };

  const tabInfo = getCurrentTabInfo();

  // Main download handler
  const handleDownload = () => {
    withLoadingState(async () => {
      const timestamp = new Date().toISOString().split("T")[0];

      switch (viewType) {
        case "graph": {
          if (timeSeriesData.length === 0) {
            alert("No data to export");
            return;
          }

          const chartTypeLabel =
            chartType === "bar-horizontal"
              ? "BarChart"
              : chartType === "bar-vertical"
              ? "BarChart"
              : chartType === "line"
              ? "LineChart"
              : "Chart";

          const tabLabel =
            activeTab === 0
              ? "cases"
              : activeTab === 1
              ? "resolution-time"
              : "time";

          const csvData = timeSeriesData.map((item) => ({
            Date: item.displayLabel,
            Value: item.value,
            Label: item.label,
          }));

          await exportChartAsImage(
            `case-time-analytics-${tabLabel}-${chartTypeLabel.toLowerCase()}-${timestamp}.png`,
            csvData,
            "[data-case-time-chart-container]"
          );
          break;
        }

        case "table": {
          if (selectedTableRows.size === 0) {
            alert("No data to export");
            return;
          }

          const selectedCases = filteredCases.filter((caseItem: any) =>
            selectedTableRows.has(caseItem.id)
          );

          const tableData = selectedCases.map((caseItem: any) => {
            const typeStr = String(caseItem.type);
            const caseType = CASE_TYPE_OPTIONS.find(
              (type) => type.value === typeStr
            );

            return {
              ID: caseItem.id || "N/A",
              Title: caseItem.title || "N/A",
              Description: caseItem.description || "N/A",
              Type: caseType?.label || "Unknown Type",
              Priority: caseItem.priority || "N/A",
              Status: caseItem.status || "N/A",
              Created: caseItem.createTime
                ? new Date(caseItem.createTime).toLocaleString()
                : "N/A",
            };
          });

          const tabLabelCsv =
            activeTab === 0
              ? "cases"
              : activeTab === 1
              ? "resolution-time"
              : "time";

          exportToCSV(
            tableData,
            `case-time-analytics-${tabLabelCsv}-table-${timestamp}.csv`
          );
          break;
        }

        default:
          alert("Unknown view type");
      }
    });
  };

  return (
    <Card
      sx={{
        bgcolor: colors.grey[50],
        border: `1px solid ${colors.grey[300]}`,
        borderRadius: "12px",
        boxShadow: "none",
        mt: "24px",
        p: "24px",
      }}
    >
      <Card
        sx={{
          bgcolor: "white",
          border: `1px solid ${colors.grey[300]}`,
          borderRadius: "12px",
          boxShadow: "none",
        }}
      >
        <Box sx={{ display: "flex", minHeight: "600px" }}>
          {/* Left Side - Filters */}
          <Box
            sx={{
              width: 320,
              borderRight: `1px solid ${colors.grey[200]}`,
              p: "24px",
            }}
          >
            <CaseAnalyticsFilters
              selectedTypes={selectedTypes}
              setSelectedTypes={setSelectedTypes}
              selectedPriorities={selectedPriorities}
              setSelectedPriorities={setSelectedPriorities}
              customDateRange={customDateRange}
              setCustomDateRange={(newRange) => {
                setCustomDateRange(newRange);
                if (newRange[0] && newRange[1]) {
                  // Custom date range is being set
                }
              }}
            />
          </Box>

          {/* Right Side - Chart Area */}
          <Box
            sx={{
              flex: 1,
              display: "flex",
              flexDirection: "column",
            }}
          >
            {/* Tabs */}
            <Box sx={{ mb: "12px", position: "relative" }}>
              <Tabs
                value={activeTab}
                onChange={handleTabChange}
                sx={{
                  "& .MuiTabs-indicator": {
                    backgroundColor: "#2563EB",
                    height: "3px",
                    zIndex: 2,
                  },
                  "& .MuiTab-root": {
                    textTransform: "none",
                    fontSize: "14px",
                    fontWeight: 500,
                    color: colors.grey[500],
                    minHeight: "48px",
                    "&.Mui-selected": {
                      color: colors.grey[900],
                      fontWeight: 600,
                    },
                  },
                  "& .MuiTabs-flexContainer": {
                    position: "relative",
                  },
                  "&:before": {
                    content: '""',
                    position: "absolute",
                    bottom: 0,
                    left: 0,
                    right: 0,
                    height: "1px",
                    backgroundColor: colors.grey[200],
                    zIndex: 1,
                  },
                }}
              >
                {tabs.map((tab) => (
                  <Tab key={tab} label={tab} />
                ))}
              </Tabs>
            </Box>

            {/* View Controls */}
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                mb: "24px",
                paddingX: "24px",
              }}
            >
              <Box sx={{ display: "flex", gap: "16px" }}>
                <Button
                  label={getViewTypeDetails().label}
                  style={viewMenuAnchor ? "filled" : "ghost"}
                  color="grey"
                  prominence={!viewMenuAnchor}
                  size="small"
                  leftIcon={getViewTypeDetails().icon}
                  rightIcon={<KeyboardArrowDownIcon />}
                  onClick={(e) => setViewMenuAnchor(e.currentTarget)}
                />

                {/* Show chart type selector only for graph view */}
                {viewType === "graph" && (
                  <Button
                    label={
                      chartType === "bar-vertical"
                        ? "Bar Chart Vertical"
                        : chartType === "bar-horizontal"
                        ? "Bar Chart Horizontal"
                        : chartType === "line"
                        ? "Line Chart"
                        : "Bar Chart Vertical"
                    }
                    style={chartMenuAnchor ? "filled" : "ghost"}
                    color="grey"
                    prominence={!chartMenuAnchor}
                    size="small"
                    leftIcon={
                      chartType === "bar-vertical" ? (
                        <BarChartVerticalIcon />
                      ) : chartType === "bar-horizontal" ? (
                        <BarChartHorizontalIcon />
                      ) : chartType === "line" ? (
                        <LineChartIcon />
                      ) : (
                        <BarChartVerticalIcon />
                      )
                    }
                    rightIcon={<KeyboardArrowDownIcon />}
                    onClick={(e) => setChartMenuAnchor(e.currentTarget)}
                  />
                )}

                {/* Download Button */}
                <Button
                  label="Download"
                  style="ghost"
                  color="grey"
                  prominence={true}
                  size="small"
                  leftIcon={<DownloadIcon />}
                  onClick={handleDownload}
                  disabled={
                    isDownloadDisabled(
                      viewType,
                      timeSeriesData.length,
                      selectedTableRows.size
                    ) || isDownloading
                  }
                  isLoading={isDownloading}
                />
              </Box>

              {/* Date Range Picker */}
              <DateRangePicker
                value={
                  customDateRange[0] && customDateRange[1]
                    ? ("custom" as DateRangeOption)
                    : selectedDateRange
                }
                onChange={(newValue) => {
                  setSelectedDateRange(newValue);
                  // Clear custom date range when predefined option is selected
                  setCustomDateRange([null, null]);
                }}
                options={DATE_RANGE_OPTIONS}
              />
            </Box>

            {/* Chart Content */}
            <Box sx={{ flex: 1 }}>
              {viewType === "graph" ? (
                isLoading ? (
                  <Box sx={{ p: "40px", textAlign: "center" }}>
                    <Typography style="body2" color={colors.grey[500]}>
                      Loading chart data...
                    </Typography>
                  </Box>
                ) : timeSeriesData.length > 0 ? (
                  <Box
                    data-case-time-chart-container
                    sx={{ height: "100%", width: "100%" }}
                  >
                    {chartType === "bar-vertical" && (
                      <TimeSeriesBarChartVertical
                        data={timeSeriesData}
                        yLabel={tabInfo.yLabel}
                        dateRange={selectedDateRange}
                      />
                    )}
                    {chartType === "bar-horizontal" && (
                      <TimeSeriesBarChartHorizontal
                        data={timeSeriesData}
                        yLabel={tabInfo.yLabel}
                        dateRange={selectedDateRange}
                      />
                    )}
                    {chartType === "line" && (
                      <TimeSeriesLineChart
                        data={timeSeriesData}
                        yLabel={tabInfo.yLabel}
                        dateRange={selectedDateRange}
                      />
                    )}
                  </Box>
                ) : (
                  <Box sx={{ p: "40px", textAlign: "center" }}>
                    <Typography style="body2" color={colors.grey[500]}>
                      No data matches the selected filters
                    </Typography>
                  </Box>
                )
              ) : (
                <CaseAnalyticsTable
                  data={filteredCases}
                  isLoading={isLoading}
                  selectedCases={selectedTableRows}
                  onSelectionChange={setSelectedTableRows}
                />
              )}
            </Box>
          </Box>
        </Box>
      </Card>

      {/* View Type Menu */}
      <Menu
        anchorEl={viewMenuAnchor}
        open={Boolean(viewMenuAnchor)}
        onClose={() => setViewMenuAnchor(null)}
        slotProps={{
          paper: {
            sx: {
              mt: 1,
              minWidth: 200,
              borderRadius: 2,
              border: `1px solid ${colors.grey[200]}`,
              boxShadow: "0px 4px 16px rgba(0, 0, 0, 0.1)",
            },
          },
        }}
      >
        <MenuItem
          onClick={() => {
            setViewType("graph");
            setViewMenuAnchor(null);
          }}
          sx={{
            px: 3,
            py: 2,
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            "&:hover": {
              backgroundColor: colors.grey[50],
            },
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
            <GraphIcon />
            <Typography
              style={viewType === "graph" ? "body1" : "body2"}
              color={viewType === "graph" ? colors.grey[900] : colors.grey[500]}
            >
              Graph
            </Typography>
          </Box>
          {viewType === "graph" && (
            <CheckIcon
              sx={{
                color: colors.blue[600],
                fontSize: 20,
                ml: 2,
              }}
            />
          )}
        </MenuItem>
        <MenuItem
          onClick={() => {
            setViewType("table");
            setViewMenuAnchor(null);
          }}
          sx={{
            px: 3,
            py: 2,
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            "&:hover": {
              backgroundColor: colors.grey[50],
            },
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
            <TableIcon />
            <Typography
              style={viewType === "table" ? "body1" : "body2"}
              color={viewType === "table" ? colors.grey[900] : colors.grey[500]}
            >
              Table
            </Typography>
          </Box>
          {viewType === "table" && (
            <CheckIcon
              sx={{
                color: colors.blue[600],
                fontSize: 20,
                ml: 2,
              }}
            />
          )}
        </MenuItem>
      </Menu>

      {/* Chart Type Menu */}
      <Menu
        anchorEl={chartMenuAnchor}
        open={Boolean(chartMenuAnchor)}
        onClose={() => setChartMenuAnchor(null)}
        slotProps={{
          paper: {
            sx: {
              mt: 1,
              minWidth: 200,
              borderRadius: 2,
              border: `1px solid ${colors.grey[200]}`,
              boxShadow: "0px 4px 16px rgba(0, 0, 0, 0.1)",
            },
          },
        }}
      >
        <MenuItem
          onClick={() => {
            setChartType("bar-vertical");
            setChartMenuAnchor(null);
          }}
          sx={{
            px: 3,
            py: 2,
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            "&:hover": {
              backgroundColor: colors.grey[50],
            },
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
            <BarChartVerticalIcon />
            <Typography
              style={chartType === "bar-vertical" ? "body1" : "body2"}
              color={
                chartType === "bar-vertical"
                  ? colors.grey[900]
                  : colors.grey[500]
              }
            >
              Bar Chart Vertical
            </Typography>
          </Box>
          {chartType === "bar-vertical" && (
            <CheckIcon
              sx={{
                color: colors.blue[600],
                fontSize: 20,
                ml: 2,
              }}
            />
          )}
        </MenuItem>

        <MenuItem
          onClick={() => {
            setChartType("bar-horizontal");
            setChartMenuAnchor(null);
          }}
          sx={{
            px: 3,
            py: 2,
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            "&:hover": {
              backgroundColor: colors.grey[50],
            },
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
            <BarChartHorizontalIcon />
            <Typography
              style={chartType === "bar-horizontal" ? "body1" : "body2"}
              color={
                chartType === "bar-horizontal"
                  ? colors.grey[900]
                  : colors.grey[500]
              }
            >
              Bar Chart Horizontal
            </Typography>
          </Box>
          {chartType === "bar-horizontal" && (
            <CheckIcon
              sx={{
                color: colors.blue[600],
                fontSize: 20,
                ml: 2,
              }}
            />
          )}
        </MenuItem>

        <MenuItem
          onClick={() => {
            setChartType("line");
            setChartMenuAnchor(null);
          }}
          sx={{
            px: 3,
            py: 2,
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            "&:hover": {
              backgroundColor: colors.grey[50],
            },
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
            <LineChartIcon />
            <Typography
              style={chartType === "line" ? "body1" : "body2"}
              color={chartType === "line" ? colors.grey[900] : colors.grey[500]}
            >
              Line Chart
            </Typography>
          </Box>
          {chartType === "line" && (
            <CheckIcon
              sx={{
                color: colors.blue[600],
                fontSize: 20,
                ml: 2,
              }}
            />
          )}
        </MenuItem>
      </Menu>
    </Card>
  );
};
