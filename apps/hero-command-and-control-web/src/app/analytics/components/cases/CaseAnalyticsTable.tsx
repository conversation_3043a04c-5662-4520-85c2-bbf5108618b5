"use client";

import {
  Box,
  Checkbox,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from "@mui/material";
import { Case } from "proto/hero/cases/v1/cases_pb";
import { Typography } from "../../../../design-system/components/Typography";
import { colors } from "../../../../design-system/tokens";
import { CASE_TYPE_OPTIONS, CASE_STATUS_OPTIONS } from "./constants";

interface CaseAnalyticsTableProps {
  data: Case[];
  isLoading: boolean;
  selectedCases?: Set<string>;
  onSelectionChange?: (selected: Set<string>) => void;
}

export const CaseAnalyticsTable = ({
  data,
  isLoading,
  selectedCases = new Set(),
  onSelectionChange,
}: CaseAnalyticsTableProps) => {
  // Handle select all cases
  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      const newSelected = new Set<string>(
        data?.map((caseItem: Case) => caseItem.id) || []
      );
      onSelectionChange?.(newSelected);
    } else {
      onSelectionChange?.(new Set<string>());
    }
  };

  // Handle select individual case
  const handleSelectCase = (caseId: string) => {
    const newSelected = new Set(selectedCases);
    if (newSelected.has(caseId)) {
      newSelected.delete(caseId);
    } else {
      newSelected.add(caseId);
    }
    onSelectionChange?.(newSelected);
  };

  // Get status display name
  const getStatusDisplay = (status: any) => {
    if (typeof status === "string") {
      const matchingStatus = CASE_STATUS_OPTIONS.find(
        (option) =>
          option.value === status ||
          option.label.toUpperCase() === status.replace("CASE_STATUS_", "")
      );

      if (matchingStatus) {
        return matchingStatus.label;
      }

      const parts = status.split("_");
      if (parts.length > 0) {
        const lastPart = parts[parts.length - 1];
        return (
          lastPart.charAt(0).toUpperCase() + lastPart.slice(1).toLowerCase()
        );
      }
      return status;
    }

    const matchingStatus = CASE_STATUS_OPTIONS.find(
      (option) => option.value === `CASE_STATUS_${String(status).toUpperCase()}`
    );

    if (matchingStatus) {
      return matchingStatus.label;
    }

    return "Unknown";
  };

  // Get type display name
  const getTypeDisplay = (type: any) => {
    if (typeof type === "string") {
      const matchingType = CASE_TYPE_OPTIONS.find(
        (option) =>
          option.value === type ||
          option.label.toUpperCase() === type.replace("CASE_TYPE_", "")
      );

      if (matchingType) {
        return matchingType.label;
      }

      if (type.startsWith("CASE_TYPE_")) {
        const typeName = type?.replace("CASE_TYPE_", "");
        return typeName
          ?.split("_")
          ?.map(
            (word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
          )
          ?.join(" ");
      }
      return type;
    }

    const matchingType = CASE_TYPE_OPTIONS.find(
      (option) => option.value === `CASE_TYPE_${String(type).toUpperCase()}`
    );

    if (matchingType) {
      return matchingType.label;
    }

    return "Unknown";
  };

  // Get priority display
  const getPriorityLabel = (priority: number): string => {
    if (!priority || isNaN(priority)) return "N/A";
    return `P${priority}`;
  };

  return (
    <Box
      sx={{
        flex: 1,
        display: "flex",
        flexDirection: "column",
        bgcolor: "white",
        boxShadow: "none",
        overflow: "hidden",
      }}
    >
      <TableContainer
        component={Box}
        sx={{
          flexGrow: 1,
          width: "100%",
          overflowY: "auto",
          overflowX: "auto",
          maxHeight: "500px",
        }}
      >
        <Table
          stickyHeader
          aria-label="cases analytics table"
          sx={{
            tableLayout: "fixed",
            width: "100%",
          }}
        >
          <TableHead>
            <TableRow sx={{ backgroundColor: colors.grey[100] }}>
              <TableCell
                padding="checkbox"
                sx={{ width: "48px", backgroundColor: colors.grey[100] }}
              >
                <Checkbox
                  checked={
                    data?.length > 0 && selectedCases.size === data.length
                  }
                  indeterminate={
                    selectedCases.size > 0 &&
                    selectedCases.size < (data?.length || 0)
                  }
                  onChange={handleSelectAll}
                  sx={{
                    color: "transparent",
                    "&.Mui-checked": {
                      color: "#2563EB",
                    },
                    "&.MuiCheckbox-indeterminate": {
                      color: "#2563EB",
                    },
                    "& .MuiSvgIcon-root": {
                      width: 18,
                      height: 18,
                      border: `1px solid ${colors.grey[200]}`,
                      borderRadius: "4px",
                      backgroundColor: "white",
                      "& path": {
                        display: "none",
                      },
                    },
                    "&.Mui-checked .MuiSvgIcon-root": {
                      backgroundColor: "#2563EB",
                      borderColor: "#2563EB",
                      "& path": {
                        display: "block",
                        fill: "white",
                      },
                    },
                    "&.MuiCheckbox-indeterminate .MuiSvgIcon-root": {
                      backgroundColor: "#2563EB",
                      borderColor: "#2563EB",
                      "& path": {
                        display: "block",
                        fill: "white",
                      },
                    },
                  }}
                />
              </TableCell>
              <TableCell
                sx={{ width: "10%", backgroundColor: colors.grey[100] }}
              >
                <Box sx={{ display: "flex", alignItems: "center" }}>
                  <Typography style="tag2" color={colors.grey[800]}>
                    ID
                  </Typography>
                </Box>
              </TableCell>
              <TableCell
                sx={{ width: "25%", backgroundColor: colors.grey[100] }}
              >
                <Box sx={{ display: "flex", alignItems: "center" }}>
                  <Typography style="tag2" color={colors.grey[800]}>
                    TITLE
                  </Typography>
                </Box>
              </TableCell>
              <TableCell
                sx={{ width: "25%", backgroundColor: colors.grey[100] }}
              >
                <Box sx={{ display: "flex", alignItems: "center" }}>
                  <Typography style="tag2" color={colors.grey[800]}>
                    DESCRIPTION
                  </Typography>
                </Box>
              </TableCell>
              <TableCell
                sx={{ width: "15%", backgroundColor: colors.grey[100] }}
              >
                <Box sx={{ display: "flex", alignItems: "center" }}>
                  <Typography style="tag2" color={colors.grey[800]}>
                    TYPE
                  </Typography>
                </Box>
              </TableCell>
              <TableCell
                sx={{ width: "15%", backgroundColor: colors.grey[100] }}
              >
                <Box sx={{ display: "flex", alignItems: "center" }}>
                  <Typography style="tag2" color={colors.grey[800]}>
                    STATUS
                  </Typography>
                </Box>
              </TableCell>
              <TableCell
                sx={{ width: "10%", backgroundColor: colors.grey[100] }}
              >
                <Box sx={{ display: "flex", alignItems: "center" }}>
                  <Typography style="tag2" color={colors.grey[800]}>
                    PRIORITY
                  </Typography>
                </Box>
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {data && data.length > 0 ? (
              data.map((caseItem: Case, index: number) => (
                <TableRow
                  hover
                  key={`${caseItem.id || 'no-id'}-${index}`}
                  sx={{
                    height: "54px",
                    "&:last-child td, &:last-child th": { border: 0 },
                    "&:hover": {
                      backgroundColor: colors.grey[50],
                    },
                  }}
                >
                  <TableCell padding="checkbox">
                    <Checkbox
                      checked={selectedCases.has(caseItem.id)}
                      onChange={() => handleSelectCase(caseItem.id)}
                      sx={{
                        color: "transparent",
                        "&.Mui-checked": {
                          color: "#2563EB",
                        },
                        "& .MuiSvgIcon-root": {
                          width: 18,
                          height: 18,
                          border: `1px solid ${colors.grey[200]}`,
                          borderRadius: "4px",
                          backgroundColor: "white",
                          "& path": {
                            display: "none",
                          },
                        },
                        "&.Mui-checked .MuiSvgIcon-root": {
                          backgroundColor: "#2563EB",
                          borderColor: "#2563EB",
                          "& path": {
                            display: "block",
                            fill: "white",
                          },
                        },
                      }}
                    />
                  </TableCell>
                  <TableCell
                    component="th"
                    scope="row"
                    sx={{ width: "10%", maxWidth: "10%" }}
                  >
                    <Box
                      sx={{
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        whiteSpace: "nowrap",
                        width: "100%",
                      }}
                    >
                      <Typography style="body4" color={colors.grey[900]}>
                        {caseItem.id?.replace(/[^0-9]/g, "").slice(0, 7) ||
                          "---"}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell
                    align="left"
                    sx={{ width: "25%", maxWidth: "25%" }}
                  >
                    <Box
                      sx={{
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        whiteSpace: "nowrap",
                        width: "100%",
                      }}
                    >
                      <Typography style="body4" color={colors.grey[900]}>
                        {caseItem.title || "---"}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell
                    align="left"
                    sx={{ width: "25%", maxWidth: "25%" }}
                  >
                    <Box
                      sx={{
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        whiteSpace: "nowrap",
                        width: "100%",
                      }}
                    >
                      <Typography style="body4" color={colors.grey[900]}>
                        {caseItem.description || "---"}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell
                    align="left"
                    sx={{ width: "15%", maxWidth: "15%" }}
                  >
                    <Box
                      sx={{
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        whiteSpace: "nowrap",
                        width: "100%",
                      }}
                    >
                      <Typography style="body4" color={colors.grey[900]}>
                        {getTypeDisplay(caseItem.type)}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell
                    align="left"
                    sx={{ width: "15%", maxWidth: "15%" }}
                  >
                    <Box
                      sx={{
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        whiteSpace: "nowrap",
                        width: "100%",
                      }}
                    >
                      <Typography style="body4" color={colors.grey[900]}>
                        {getStatusDisplay(caseItem.status)}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell
                    align="left"
                    sx={{ width: "10%", maxWidth: "10%" }}
                  >
                    <Box
                      sx={{
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        whiteSpace: "nowrap",
                        width: "100%",
                      }}
                    >
                      <Typography style="body4" color={colors.grey[900]}>
                        {getPriorityLabel(Number(caseItem.priority))}
                      </Typography>
                    </Box>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow sx={{ height: 200 }}>
                <TableCell colSpan={7} align="center">
                  <Typography style="body3" color={colors.grey[600]}>
                    {isLoading
                      ? "Loading case data..."
                      : "No cases match the selected filters."}
                  </Typography>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
};