"use client";

import { Box, Divider } from "@mui/material";
import { useState, useEffect } from "react";
import { DateRangePicker } from "../../../../design-system/components/DatePicker";
import { Dropdown } from "../../../../design-system/components/Dropdown";
import { Typography } from "../../../../design-system/components/Typography";
import { colors } from "../../../../design-system/tokens";
import { CASE_TYPE_FILTER_OPTIONS, CASE_PRIORITY_FILTER_OPTIONS } from "./constants";

interface CaseAnalyticsFiltersProps {
  selectedTypes: string[];
  setSelectedTypes: (types: string[]) => void;
  selectedPriorities: number[];
  setSelectedPriorities: (priorities: number[]) => void;
  customDateRange: [Date | null, Date | null];
  setCustomDateRange: (range: [Date | null, Date | null]) => void;
}

export const CaseAnalyticsFilters = ({
  selectedTypes,
  setSelectedTypes,
  selectedPriorities,
  setSelectedPriorities,
  customDateRange,
  setCustomDateRange,
}: CaseAnalyticsFiltersProps) => {
  const [type, setType] = useState<string | null>(null);
  const [priority, setPriority] = useState<string | null>(null);

  // Sync local UI state with props - Priority
  useEffect(() => {
    if (selectedPriorities.length > 0) {
      const priorityOption = CASE_PRIORITY_FILTER_OPTIONS.find(
        (option) => option.value === selectedPriorities[0].toString()
      );
      setPriority(priorityOption?.value || null);
    } else {
      setPriority(null);
    }
  }, [selectedPriorities]);

  // Sync local UI state with props - Type
  useEffect(() => {
    if (selectedTypes.length > 0) {
      const typeOption = CASE_TYPE_FILTER_OPTIONS.find(
        (option) => option.value === selectedTypes[0]
      );
      setType(typeOption?.value || null);
    } else {
      setType(null);
    }
  }, [selectedTypes]);

  return (
    <Box>
      <Box sx={{ mb: "20px" }}>
        <Typography style="caps1" color={colors.grey[900]}>
          FILTERS
        </Typography>
      </Box>

      <Box sx={{ mb: "20px" }}>
        <Dropdown
          title="Case Type"
          placeholder="Select type"
          enableSearch
          options={CASE_TYPE_FILTER_OPTIONS}
          value={type}
          onChange={(value) => {
            setType(value);
            if (value) {
              const selectedOption = CASE_TYPE_FILTER_OPTIONS.find(
                (option) => option.value === value
              );
              if (selectedOption) {
                setSelectedTypes([selectedOption.value]);
              }
            } else {
              setSelectedTypes([]);
            }
          }}
        />
      </Box>

      <Box sx={{ mb: "20px" }}>
        <Dropdown
          title="Priority"
          placeholder="Select priority"
          options={CASE_PRIORITY_FILTER_OPTIONS}
          value={priority}
          onChange={(value) => {
            setPriority(value);
            if (value) {
              setSelectedPriorities([parseInt(value)]);
            } else {
              setSelectedPriorities([]);
            }
          }}
        />
      </Box>

      <Divider sx={{ borderColor: colors.grey[200], my: "20px" }} />

      <Box sx={{ mb: "20px" }}>
        <DateRangePicker
          title="Custom Date Range"
          placeholder="Select custom date range"
          value={customDateRange}
          onChange={(newRange) => {
            setCustomDateRange(newRange);
          }}
        />
      </Box>
    </Box>
  );
};