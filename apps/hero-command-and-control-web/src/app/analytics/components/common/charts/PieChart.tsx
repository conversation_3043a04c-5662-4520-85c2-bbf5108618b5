"use client";

import { <PERSON><PERSON><PERSON> as M<PERSON><PERSON><PERSON><PERSON><PERSON> } from "@mui/x-charts/Pie<PERSON><PERSON>";
import { colors } from "../../../../../design-system/tokens";

interface PieChartProps {
  dataset: Array<{ type: string; count: number }>;
}

// Define color palette for incident types
const CHART_COLORS = [
  "#2563EB", // blue-600
  "#dc2626", // red-600
  "#059669", // emerald-600
  "#d97706", // amber-600
  "#7c3aed", // violet-600
  "#db2777", // pink-600
  "#0891b2", // cyan-600
  "#65a30d", // lime-600
  "#ea580c", // orange-600
  "#6b7280", // gray-500
];

export const PieChart: React.FC<PieChartProps> = ({ dataset }) => {
  // Transform data for MUI PieChart
  const pieData = dataset.map((item, index) => ({
    id: index,
    value: item.count,
    label: item.type,
    color: CHART_COLORS[index % CHART_COLORS.length],
  }));

  return (
    <MUIPie<PERSON>hart
      series={[
        {
          data: pieData,
          highlightScope: { fade: 'global', highlight: 'item' },
          faded: { innerRadius: 30, additionalRadius: -30, color: 'gray' },
        },
      ]}
      height={400}
      margin={{ top: 20, bottom: 20, left: 20, right: 20 }}
      sx={{
        ".MuiChartsTooltip-root": {
          backgroundColor: "white",
          border: `1px solid ${colors.grey[300]}`,
          borderRadius: "8px",
          fontSize: "14px",
        },
        ".MuiChartsLegend-root": {
          fontSize: "12px",
          marginRight: "30px",
        },
      }}
    />
  );
};