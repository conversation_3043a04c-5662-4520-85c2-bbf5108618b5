"use client";

import { Bar<PERSON><PERSON> } from "@mui/x-charts/BarChart";
import { colors } from "../../../../../design-system/tokens";

interface TimeSeriesBarChartHorizontalProps {
  data: Array<{ date: Date; value: number; label: string; displayLabel?: string }>;
  yLabel: string;
  dateRange?: string;
}

export const TimeSeriesBarChartHorizontal: React.FC<TimeSeriesBarChartHorizontalProps> = ({ 
  data, 
  yLabel,
  dateRange
}) => {
  // Format dates for display
  const formattedData = data.map(item => ({
    date: item.displayLabel || item.date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric' 
    }),
    value: item.value,
    label: item.label
  }));

  return (
    <BarChart
      dataset={formattedData}
      yAxis={[
        {
          scaleType: "band",
          dataKey: "date",
          label: "Date",
          width: 120,
        },
      ]}
      xAxis={[
        {
          label: yLabel,
          scaleType: "linear",
          tickMinStep: 1,
        },
      ]}
      series={[
        {
          dataKey: "value",
          label: yLabel,
          color: "#2563EB",
        },
      ]}
      layout="horizontal"
      height={400}
      margin={{ left: 30, right: 30, top: 20, bottom: 20 }}
      sx={{
        ".MuiChartsAxis-tick": {
          fontSize: "12px",
          fill: colors.grey[600],
        },
        ".MuiChartsAxis-label": {
          fontSize: "12px",
          fill: colors.grey[600],
        },
        ".MuiChartsAxis-tickLabel": {
          fontSize: "12px",
          fill: colors.grey[600],
        },
        ".MuiChartsTooltip-root": {
          backgroundColor: "white",
          border: `1px solid ${colors.grey[300]}`,
          borderRadius: "8px",
          fontSize: "14px",
        },
      }}
    />
  );
};