"use client";

import { <PERSON><PERSON><PERSON> } from "@mui/x-charts/BarChart";
import { colors } from "../../../../../design-system/tokens";

interface BarChartHorizontalProps {
  dataset: Array<{ type: string; count: number }>;
}

export const BarChartHorizontal: React.FC<BarChartHorizontalProps> = ({
  dataset,
}) => {
  return (
    <BarChart
      dataset={dataset}
      yAxis={[
        {
          scaleType: "band",
          dataKey: "type",
          label: "Incident Type",
          width: 200,
        },
      ]}
      xAxis={[
        {
          label: "Count",
          scaleType: "linear",
          tickMinStep: 1,
        },
      ]}
      series={[
        {
          dataKey: "count",
          label: "Incident Count",
          color: "#2563EB",
        },
      ]}
      layout="horizontal"
      height={400}
      margin={{ left: 30, right: 30, top: 20, bottom: 20 }}
      sx={{
        ".MuiChartsAxis-tick": {
          fontSize: "12px",
          fill: colors.grey[600],
        },
        ".MuiChartsAxis-label": {
          fontSize: "12px",
          fill: colors.grey[600],
        },
        ".MuiChartsAxis-tickLabel": {
          fontSize: "12px",
          fill: colors.grey[600],
        },
        ".MuiChartsTooltip-root": {
          backgroundColor: "white",
          border: `1px solid ${colors.grey[300]}`,
          borderRadius: "8px",
          fontSize: "14px",
        },
      }}
    />
  );
};
