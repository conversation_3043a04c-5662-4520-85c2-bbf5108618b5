"use client";

import { <PERSON><PERSON><PERSON> } from "@mui/x-charts/BarChart";
import { colors } from "../../../../../design-system/tokens";

interface BarChartVerticalProps {
  dataset: Array<{ type: string; count: number }>;
}

export const BarChartVertical: React.FC<BarChartVerticalProps> = ({ dataset }) => {
  // Rotate labels if there are many items or if any label is long
  const shouldRotateLabels = dataset.length > 5 || dataset.some(item => item.type.length > 12);
  
  return (
    <BarChart
      dataset={dataset}
      xAxis={[
        {
          scaleType: "band",
          dataKey: "type",
          label: "Incident Type",
          tickLabelStyle: shouldRotateLabels ? {
            angle: -45,
            textAnchor: 'end',
            dominantBaseline: 'hanging',
            fontSize: 12,
          } : {
            fontSize: 12,
          },
          height: shouldRotateLabels ? 100 : undefined,
        },
      ]}
      yAxis={[
        {
          label: "Count",
          scaleType: "linear",
          tickMinStep: 1,
        },
      ]}
      series={[
        {
          dataKey: "count",
          label: "Incident Count",
          color: "#2563EB",
        },
      ]}
      height={400}
      margin={{ 
        left: 50, 
        right: 50, 
        top: 20, 
        bottom: 10 
      }}
      sx={{
        ".MuiChartsAxis-tick": {
          fill: colors.grey[600],
        },
        ".MuiChartsAxis-label": {
          fontSize: "12px",
          fill: colors.grey[600],
        },
        ".MuiChartsTooltip-root": {
          backgroundColor: "white",
          border: `1px solid ${colors.grey[300]}`,
          borderRadius: "8px",
          fontSize: "14px",
        },
      }}
    />
  );
};