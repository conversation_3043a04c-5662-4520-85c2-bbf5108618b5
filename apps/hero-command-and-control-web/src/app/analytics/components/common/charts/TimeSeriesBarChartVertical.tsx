"use client";

import { <PERSON><PERSON><PERSON> } from "@mui/x-charts/BarChart";
import { colors } from "../../../../../design-system/tokens";

interface TimeSeriesBarChartVerticalProps {
  data: Array<{ date: Date; value: number; label: string; displayLabel?: string }>;
  yLabel: string;
  dateRange?: string;
}

export const TimeSeriesBarChartVertical: React.FC<TimeSeriesBarChartVerticalProps> = ({ 
  data, 
  yLabel,
  dateRange
}) => {
  // Determine if labels should be rotated (for 1d and 1m date ranges)
  const shouldRotateLabels = dateRange === "1d" || dateRange === "1m";
  
  // Format dates for display
  const formattedData = data.map(item => ({
    date: item.displayLabel || item.date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric' 
    }),
    value: item.value,
    label: item.label
  }));

  return (
    <Bar<PERSON>hart
      dataset={formattedData}
      xAxis={[
        {
          scaleType: "band",
          dataKey: "date",
          label: "Date",
          tickLabelStyle: shouldRotateLabels ? {
            angle: -45,
            textAnchor: 'end',
            dominantBaseline: 'hanging',
            fontSize: 12,
          } : {
            fontSize: 12,
          },
          height: shouldRotateLabels ? 80 : undefined,
        },
      ]}
      yAxis={[
        {
          label: yLabel,
          scaleType: "linear",
          tickMinStep: 1,
        },
      ]}
      series={[
        {
          dataKey: "value",
          label: yLabel,
          color: "#2563EB",
        },
      ]}
      height={400}
      margin={{ left: 50, right: 50, top: 20, bottom: 20 }}
      sx={{
        ".MuiChartsAxis-tick": {
          fontSize: "12px",
          fill: colors.grey[600],
        },
        ".MuiChartsAxis-label": {
          fontSize: "12px",
          fill: colors.grey[600],
        },
        ".MuiChartsAxis-tickLabel": {
          fontSize: "12px",
          fill: colors.grey[600],
        },
        ".MuiChartsTooltip-root": {
          backgroundColor: "white",
          border: `1px solid ${colors.grey[300]}`,
          borderRadius: "8px",
          fontSize: "14px",
        },
      }}
    />
  );
};