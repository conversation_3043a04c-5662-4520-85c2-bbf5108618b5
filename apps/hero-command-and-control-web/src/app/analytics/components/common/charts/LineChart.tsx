"use client";

import { Line<PERSON><PERSON> as M<PERSON><PERSON><PERSON><PERSON><PERSON> } from "@mui/x-charts/LineChart";
import { colors } from "../../../../../design-system/tokens";

interface LineChartProps {
  dataset: Array<{ type: string; count: number }>;
}

export const LineChart: React.FC<LineChartProps> = ({ dataset }) => {
  // Transform data for line chart
  const xLabels = dataset.map((item) => item.type);
  const data = dataset.map((item) => item.count);
  
  // Rotate labels if there are many items or if any label is long
  const shouldRotateLabels = dataset.length > 5 || dataset.some(item => item.type.length > 12);

  return (
    <MUILineChart
      xAxis={[
        {
          scaleType: "point",
          data: xLabels,
          label: "Incident Type",
          tickLabelStyle: shouldRotateLabels ? {
            angle: -45,
            textAnchor: 'end',
            dominantBaseline: 'hanging',
            fontSize: 12,
          } : {
            fontSize: 12,
          },
          height: shouldRotateLabels ? 100 : undefined,
        },
      ]}
      yAxis={[
        {
          label: "Count",
          scaleType: "linear",
          tickMinStep: 1,
          min: 0,
        },
      ]}
      series={[
        {
          data: data,
          label: "Incident Count",
          color: "#2563EB",
          curve: "linear",
        },
      ]}
      height={400}
      margin={{ 
        left: 40, 
        right: 50, 
        top: 20, 
        bottom: 10  
      }}
      sx={{
        ".MuiChartsAxis-tick": {
          fill: colors.grey[600],
        },
        ".MuiChartsAxis-label": {
          fontSize: "12px",
          fill: colors.grey[600],
        },
        ".MuiChartsTooltip-root": {
          backgroundColor: "white",
          border: `1px solid ${colors.grey[300]}`,
          borderRadius: "8px",
          fontSize: "14px",
        },
      }}
    />
  );
};
