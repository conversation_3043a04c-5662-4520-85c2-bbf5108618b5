"use client";

import { <PERSON><PERSON><PERSON><PERSON><PERSON> as M<PERSON><PERSON>catter<PERSON><PERSON> } from "@mui/x-charts/ScatterChart";
import { colors } from "../../../../../design-system/tokens";

interface ScatterChartProps {
  dataset: Array<{ type: string; count: number }>;
}

export const ScatterChart: React.FC<ScatterChartProps> = ({ dataset }) => {
  // Transform data for scatter plot - use alphabetical index as x and count as y
  // This creates a scatter plot where each incident type gets a position on x-axis
  const scatterData = dataset.map((item, index) => ({
    x: index + 1, // Alphabetical order position (since dataset is already sorted alphabetically)
    y: item.count,
    id: item.type,
  }));

  // Create x-axis labels from the incident types
  const xAxisLabels = dataset.map((item, index) => `${index + 1}`);

  return (
    <MUIScatterChart
      height={400}
      series={[
        {
          data: scatterData,
          label: "Incident Distribution",
          color: "#2563EB",
        },
      ]}
      xAxis={[
        {
          label: "Incident Type",
          min: 0,
          max: dataset.length + 1,
          tickMinStep: 1,
        },
      ]}
      yAxis={[
        {
          label: "Count",
          tickMinStep: 1,
        },
      ]}
      margin={{ left: 50, right: 50, top: 20, bottom: 20 }}
      sx={{
        ".MuiChartsAxis-tick": {
          fontSize: "12px",
          fill: colors.grey[600],
        },
        ".MuiChartsAxis-label": {
          fontSize: "12px",
          fill: colors.grey[600],
        },
        ".MuiChartsAxis-tickLabel": {
          fontSize: "12px",
          fill: colors.grey[600],
        },
        ".MuiChartsTooltip-root": {
          backgroundColor: "white",
          border: `1px solid ${colors.grey[300]}`,
          borderRadius: "8px",
          fontSize: "14px",
        },
      }}
    />
  );
};