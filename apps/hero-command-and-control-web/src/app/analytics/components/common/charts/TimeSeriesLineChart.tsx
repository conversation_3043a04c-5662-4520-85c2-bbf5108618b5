"use client";

import { LineChart } from "@mui/x-charts/LineChart";
import { colors } from "../../../../../design-system/tokens";

interface TimeSeriesLineChartProps {
  data: Array<{
    date: Date;
    value: number;
    label: string;
    displayLabel?: string;
  }>;
  yLabel: string;
  dateRange?: string;
}

export const TimeSeriesLineChart: React.FC<TimeSeriesLineChartProps> = ({
  data,
  yLabel,
  dateRange,
}) => {
  // Determine if labels should be rotated (for 1d and 1m date ranges)
  const shouldRotateLabels = dateRange === "1d" || dateRange === "1m";
  
  // Create a Map for O(1) lookup of display labels by timestamp
  const displayLabelMap = new Map(
    data.map((item) => [item.date.getTime(), item.displayLabel])
  );
  
  // Convert dates to timestamps for x-axis
  const xAxisData = data.map((item) => item.date.getTime());
  const yAxisData = data.map((item) => item.value);

  return (
    <LineChart
      xAxis={[
        {
          data: xAxisData,
          label: "Date",
          scaleType: "time",
          valueFormatter: (value) => {
            // Use Map for O(1) lookup of display label
            return displayLabelMap.get(value) || new Date(value).toLocaleDateString("en-US", {
              month: "short",
              day: "numeric",
            });
          },
          tickLabelStyle: shouldRotateLabels ? {
            angle: -45,
            textAnchor: 'end',
            dominantBaseline: 'hanging',
            fontSize: 12,
          } : {
            fontSize: 12,
          },
        },
      ]}
      yAxis={[
        {
          label: yLabel,
          tickMinStep: 1,
        },
      ]}
      series={[
        {
          data: yAxisData,
          label: yLabel,
          color: "#2563EB",
          curve: "linear",
        },
      ]}
      height={400}
      margin={{ left: 40, right: 50, top: 20, bottom: 20 }}
      sx={{
        ".MuiChartsAxis-tick": {
          fontSize: "12px",
          fill: colors.grey[600],
        },
        ".MuiChartsAxis-label": {
          fontSize: "12px",
          fill: colors.grey[600],
        },
        ".MuiChartsAxis-tickLabel": {
          fontSize: "12px",
          fill: colors.grey[600],
        },
        ".MuiChartsTooltip-root": {
          backgroundColor: "white",
          border: `1px solid ${colors.grey[300]}`,
          borderRadius: "8px",
          fontSize: "14px",
        },
      }}
    />
  );
};
