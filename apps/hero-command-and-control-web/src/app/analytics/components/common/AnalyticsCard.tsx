import { Box, Card, CardContent } from "@mui/material";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import { Typography } from "../../../../design-system/components/Typography";
import { colors } from "../../../../design-system/tokens";

interface AnalyticsCardProps {
  title: string;
  currentValue: number | string;
  previousValue: number | string;
  isLoading?: boolean;
  comparisonText?: string;
  showPreviousValueForStrings?: boolean;
  formatValue?: (value: number | string) => string;
}

export const AnalyticsCard = ({
  title,
  currentValue,
  previousValue,
  isLoading,
  comparisonText = "from previous period",
  showPreviousValueForStrings = false,
  formatValue,
}: AnalyticsCardProps) => {
  // Don't calculate percentage for string values
  const isStringValue = typeof currentValue === "string";
  const numericCurrentValue = isStringValue ? 0 : currentValue;
  const numericPreviousValue =
    typeof previousValue === "string" ? 0 : previousValue;

  const percentageChange =
    !isStringValue && numericPreviousValue > 0
      ? ((numericCurrentValue - numericPreviousValue) / numericPreviousValue) *
        100
      : 0;
  const isPositive = percentageChange >= 0;

  const displayValue = formatValue 
    ? formatValue(currentValue)
    : isStringValue
    ? currentValue
    : currentValue.toLocaleString();

  return (
    <Card
      sx={{
        height: "140px",
        bgcolor: "white",
        border: `1px solid ${colors.grey[300]}`,
        borderRadius: "12px",
        boxShadow: "none",
        "&:hover": {
          borderColor: colors.grey[400],
        },
      }}
    >
      <CardContent
        sx={{
          p: "24px",
          height: "100%",
          display: "flex",
          flexDirection: "column",
          justifyContent: "space-between",
        }}
      >
        <Box>
          <Typography style="body4" color={colors.grey[500]}>
            <span style={{ fontSize: "14px", fontWeight: 500 }}>{title}</span>
          </Typography>

          <Box sx={{ mt: "12px" }}>
            {isLoading ? (
              <Skeleton
                height={32}
                width={100}
                borderRadius={4}
                baseColor="#f5f5f5"
                highlightColor="#e0e0e0"
              />
            ) : (
              <Typography style="h1" color={colors.grey[900]}>
                <span style={{ fontSize: "28px", fontWeight: 700 }}>
                  {displayValue}
                </span>
              </Typography>
            )}
          </Box>
        </Box>

        <Box>
          {isLoading ? (
            <Skeleton
              height={16}
              width={85}
              borderRadius={4}
              baseColor="#f5f5f5"
              highlightColor="#e0e0e0"
            />
          ) : (
            <Box sx={{ display: "flex", alignItems: "center", gap: "6px" }}>
              {/* Only show percentage change for numeric values */}
              {!isStringValue && (
                <>
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      gap: "4px",
                    }}
                  >
                    {/* SVG Arrow Icon */}
                    {isPositive ? (
                      // Up arrow (red for increase)
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="17"
                        height="17"
                        viewBox="0 0 17 17"
                        fill="none"
                      >
                        <g clipPath="url(#clip0_4261_110790)">
                          <path
                            d="M11 5.1665H15V9.1665"
                            stroke="#E60076"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                          <path
                            d="M15.0003 5.1665L9.33366 10.8332L6.00033 7.49984L1.66699 11.8332"
                            stroke="#E60076"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </g>
                        <defs>
                          <clipPath id="clip0_4261_110790">
                            <rect
                              width="16"
                              height="16"
                              fill="white"
                              transform="translate(0.333496 0.5)"
                            />
                          </clipPath>
                        </defs>
                      </svg>
                    ) : (
                      // Down arrow (green for decrease)
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="17"
                        viewBox="0 0 16 17"
                        fill="none"
                      >
                        <path
                          d="M10.6665 11.8335H14.6665V7.8335"
                          stroke="#00A63E"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                        <path
                          d="M14.6668 11.8332L9.00016 6.1665L5.66683 9.49984L1.3335 5.1665"
                          stroke="#00A63E"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                    )}

                    <Typography
                      style="body4"
                      color={isPositive ? "#E60076" : "#00A63E"}
                    >
                      <span style={{ fontSize: "14px", fontWeight: 500 }}>
                        {Math.abs(percentageChange).toFixed(0)}%
                      </span>
                    </Typography>
                  </Box>
                </>
              )}
              <Typography style="body4" color={colors.grey[500]}>
                <span style={{ fontSize: "14px" }}>
                  {!isStringValue ? (
                    <>
                      from{" "}
                      <span style={{ fontWeight: 600 }}>
                        {comparisonText.replace("from ", "")}
                      </span>
                    </>
                  ) : showPreviousValueForStrings ? (
                    <>
                      was{" "}
                      <span style={{ fontWeight: 600 }}>
                        {previousValue}
                      </span>{" "}
                      {comparisonText.replace("from ", "")}
                    </>
                  ) : (
                    <span style={{ fontWeight: 600 }}>
                      {comparisonText.replace("from ", "")}
                    </span>
                  )}
                </span>
              </Typography>
            </Box>
          )}
        </Box>
      </CardContent>
    </Card>
  );
};
