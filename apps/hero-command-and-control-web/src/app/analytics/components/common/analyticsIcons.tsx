// Common icon components for analytics

export const GraphIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
    <path d="M10.545 8.0175L9 7.3125L10.545 6.6075L11.25 5.0625L11.955 6.6075L13.5 7.3125L11.955 8.0175L11.25 9.5625L10.545 8.0175ZM3 11.0625L3.705 9.5175L5.25 8.8125L3.705 8.1075L3 6.5625L2.295 8.1075L0.75 8.8125L2.295 9.5175L3 11.0625ZM6.375 7.3125L7.1925 5.505L9 4.6875L7.1925 3.87L6.375 2.0625L5.5575 3.87L3.75 4.6875L5.5575 5.505L6.375 7.3125ZM3.375 15.9375L7.875 11.43L10.875 14.43L17.25 7.26L16.1925 6.2025L10.875 12.18L7.875 9.18L2.25 14.8125L3.375 15.9375Z" fill="#364153"/>
  </svg>
);

export const HeatViewIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="18"
    height="18"
    viewBox="0 0 18 18"
    fill="none"
  >
    {/* Map outline */}
    <path
      d="M2 3C2 2.44772 2.44772 2 3 2H15C15.5523 2 16 2.44772 16 3V13C16 13.5523 15.5523 14 15 14H3C2.44772 14 2 13.5523 2 13V3Z"
      stroke="#9CA3AF"
      strokeWidth="1"
      fill="none"
    />
    
    {/* Heat zones - organic blob shapes */}
    <ellipse cx="5" cy="5" rx="1.5" ry="1" fill="#FEF3C7" fillOpacity="0.8" />
    <ellipse cx="12" cy="6" rx="2" ry="1.5" fill="#FDE68A" fillOpacity="0.8" />
    <ellipse cx="8" cy="9" rx="1.8" ry="1.2" fill="#FBBF24" fillOpacity="0.8" />
    <ellipse cx="13" cy="10" rx="1.2" ry="0.8" fill="#F59E0B" fillOpacity="0.8" />
    <ellipse cx="6" cy="11" rx="1" ry="0.7" fill="#D97706" fillOpacity="0.8" />
    
    {/* Location markers */}
    <circle cx="5" cy="5" r="1" fill="#DC2626" />
    <circle cx="12" cy="6" r="1" fill="#DC2626" />
    <circle cx="8" cy="9" r="1" fill="#DC2626" />
  </svg>
);

export const BubbleViewIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="18"
    height="18"
    viewBox="0 0 18 18"
    fill="none"
  >
    <circle
      cx="4.5"
      cy="4.5"
      r="2.25"
      fill="#364153"
      fillOpacity="0.3"
    />
    <circle
      cx="13.5"
      cy="6"
      r="3"
      fill="#364153"
      fillOpacity="0.5"
    />
    <circle
      cx="7.5"
      cy="12"
      r="1.5"
      fill="#364153"
      fillOpacity="0.7"
    />
    <circle
      cx="12"
      cy="13.5"
      r="2.25"
      fill="#364153"
      fillOpacity="0.4"
    />
  </svg>
);

export const BarChartHorizontalIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
    <path d="M3 4.5C3 5.325 3.675 6 4.5 6L9.75 6C10.575 6 11.25 5.325 11.25 4.5C11.25 3.675 10.575 3 9.75 3L4.5 3C3.675 3 3 3.675 3 4.5Z" fill="black"/>
    <path d="M6.75 12L4.5 12C3.675 12 3 12.675 3 13.5C3 14.325 3.675 15 4.5 15L6.75 15C7.575 15 8.25 14.325 8.25 13.5C8.25 12.675 7.575 12 6.75 12Z" fill="black"/>
    <path d="M3 9C3 9.825 3.675 10.5 4.5 10.5L13.5 10.5C14.325 10.5 15 9.825 15 9C15 8.175 14.325 7.5 13.5 7.5L4.5 7.5C3.675 7.5 3 8.175 3 9Z" fill="black"/>
  </svg>
);

export const BarChartVerticalIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
    <path d="M4.5 15C5.325 15 6 14.325 6 13.5V8.25C6 7.425 5.325 6.75 4.5 6.75C3.675 6.75 3 7.425 3 8.25V13.5C3 14.325 3.675 15 4.5 15Z" fill="black"/>
    <path d="M12 11.25V13.5C12 14.325 12.675 15 13.5 15C14.325 15 15 14.325 15 13.5V11.25C15 10.425 14.325 9.75 13.5 9.75C12.675 9.75 12 10.425 12 11.25Z" fill="black"/>
    <path d="M9 15C9.825 15 10.5 14.325 10.5 13.5V4.5C10.5 3.675 9.825 3 9 3C8.175 3 7.5 3.675 7.5 4.5V13.5C7.5 14.325 8.175 15 9 15Z" fill="black"/>
  </svg>
);

export const PieChartIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
    <path d="M9 1.5C13.1421 1.5 16.5 4.85786 16.5 9C16.5 13.1421 13.1421 16.5 9 16.5C4.85786 16.5 1.5 13.1421 1.5 9C1.5 4.85786 4.85786 1.5 9 1.5Z" fill="#E5E7EB"/>
    <path d="M9 1.5V9H16.5C16.5 4.85786 13.1421 1.5 9 1.5Z" fill="#6B7280"/>
    <path d="M9 9L14.1213 3.87868C15.4645 5.22183 16.5 7.03553 16.5 9H9Z" fill="#374151"/>
  </svg>
);

export const ScatterplotIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="18"
    height="18"
    viewBox="0 0 18 18"
    fill="none"
  >
    <circle cx="4.5" cy="13.5" r="1.5" fill="#364153" />
    <circle cx="7.5" cy="9" r="1.5" fill="#364153" />
    <circle cx="10.5" cy="6" r="1.5" fill="#364153" />
    <circle cx="13.5" cy="4.5" r="1.5" fill="#364153" />
    <path
      d="M1.5 16.5V1.5M1.5 16.5H16.5"
      stroke="#364153"
      strokeWidth="1.5"
      strokeLinecap="round"
    />
  </svg>
);

export const LineChartIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="18"
    height="18"
    viewBox="0 0 18 18"
    fill="none"
  >
    <path
      d="M1.5 16.5V3H3V15H16.5V16.5H1.5Z"
      fill="#364153"
    />
    <path
      d="M4.5 12L7.5 9L10.5 10.5L15 6"
      stroke="#364153"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const TableIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
    <rect x="2.25" y="3.75" width="13.5" height="10.5" rx="1.5" stroke="#364153" strokeWidth="1.5" fill="none"/>
    <line x1="2.25" y1="7.5" x2="15.75" y2="7.5" stroke="#364153" strokeWidth="1.5"/>
    <line x1="2.25" y1="10.5" x2="15.75" y2="10.5" stroke="#364153" strokeWidth="1.5"/>
    <line x1="6.75" y1="3.75" x2="6.75" y2="14.25" stroke="#364153" strokeWidth="1.5"/>
    <line x1="11.25" y1="3.75" x2="11.25" y2="14.25" stroke="#364153" strokeWidth="1.5"/>
  </svg>
);

export const DownloadIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="18"
    height="18"
    viewBox="0 0 18 18"
    fill="none"
  >
    <path
      d="M13.5 11.25V13.5H4.5V11.25H3V13.5C3 14.325 3.675 15 4.5 15H13.5C14.325 15 15 14.325 15 13.5V11.25H13.5ZM12.75 8.25L11.6925 7.1925L9.75 9.1275V3H8.25V9.1275L6.3075 7.1925L5.25 8.25L9 12L12.75 8.25Z"
      fill="black"
    />
  </svg>
);

export const HeatMapIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="18"
    height="18"
    viewBox="0 0 18 18"
    fill="none"
  >
    <path
      d="M15.375 2.25L15.255 2.2725L11.25 3.825L6.75 2.25L2.52 3.675C2.3625 3.7275 2.25 3.8625 2.25 4.035V15.375C2.25 15.585 2.415 15.75 2.625 15.75L2.745 15.7275L6.75 14.175L11.25 15.75L15.48 14.325C15.6375 14.2725 15.75 14.1375 15.75 13.965V2.625C15.75 2.415 15.585 2.25 15.375 2.25ZM11.25 14.25L6.75 12.6675V3.75L11.25 5.3325V14.25Z"
      fill="#364153"
    />
  </svg>
);