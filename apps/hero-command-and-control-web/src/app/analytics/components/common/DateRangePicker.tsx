import { Box } from "@mui/material";
import { Typography } from "../../../../design-system/components/Typography";
import { colors } from "../../../../design-system/tokens";
import type { DateRangeOption } from "../../types";

interface DateRangePickerProps {
  value: DateRangeOption;
  onChange: (value: DateRangeOption) => void;
  options: { value: DateRangeOption; label: string }[];
}

export const DateRangePicker = ({
  value,
  onChange,
  options,
}: DateRangePickerProps) => {
  return (
    <Box
      sx={{
        display: "flex",
        bgcolor: "white",
        borderRadius: "8px",
        border: `1px solid ${colors.grey[200]}`,
        overflow: "hidden",
      }}
    >
      {options.map((option, index) => (
        <Box
          key={option.value}
          onClick={() => onChange(option.value)}
          sx={{
            px: "14px",
            py: "8px",
            cursor: "pointer",
            backgroundColor:
              value === option.value ? colors.blue[100] : "transparent",
            transition: "all 0.15s ease",
            userSelect: "none",
            borderRight:
              index < options.length - 1
                ? `1px solid ${colors.grey[200]}`
                : "none",
            "&:hover": {
              backgroundColor:
                value === option.value ? colors.blue[200] : colors.grey[50],
            },
          }}
        >
          <Typography
            style="body1"
            color={value === option.value ? colors.blue[600] : colors.grey[600]}
          >
            {option.label}
          </Typography>
        </Box>
      ))}
    </Box>
  );
};
