// Common utility functions for analytics components

import { useState } from "react";

/**
 * Hook for managing download loading state
 */
export const useDownloadState = () => {
  const [isDownloading, setIsDownloading] = useState(false);

  const withLoadingState = async (downloadFn: () => Promise<void>) => {
    if (isDownloading) return; // Prevent multiple simultaneous downloads

    try {
      setIsDownloading(true);
      await downloadFn();
    } catch (error) {
      console.error("Download failed:", error);
    } finally {
      setIsDownloading(false);
    }
  };

  return { isDownloading, withLoadingState };
};

/**
 * Exports data as CSV file
 */
export const exportToCSV = (data: any[], filename: string) => {
  if (data.length === 0) {
    alert("No data to export");
    return;
  }

  const headers = Object.keys(data[0]);
  const csvContent = [
    headers.join(","),
    ...data.map((row) =>
      headers
        .map((header) => {
          const value = row[header];
          // Escape commas and quotes in CSV values
          if (
            typeof value === "string" &&
            (value.includes(",") || value.includes('"'))
          ) {
            return `"${value.replace(/"/g, '""')}"`;
          }
          return value;
        })
        .join(",")
    ),
  ].join("\n");

  const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
  const link = document.createElement("a");
  const url = URL.createObjectURL(blob);
  link.setAttribute("href", url);
  link.setAttribute("download", filename);
  link.style.visibility = "hidden";
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

/**
 * Exports chart as PNG image with fallback to CSV
 */
export const exportChartAsImage = async (
  filename: string,
  csvData: any[],
  containerSelector: string = "[data-chart-container]"
) => {
  try {
    // Import html2canvas dynamically to avoid SSR issues
    const html2canvas = (await import("html2canvas")).default;

    // Find the chart container
    const chartContainer = document.querySelector(
      containerSelector
    ) as HTMLElement;

    if (!chartContainer) {
      console.warn("Chart container not found, exporting data as CSV instead");
      // Fallback: export data as CSV instead
      exportToCSV(csvData, filename.replace(".png", ".csv"));
      return;
    }

    const canvas = await html2canvas(chartContainer, {
      background: "#ffffff",
      useCORS: true,
      allowTaint: true,
      height: chartContainer.scrollHeight,
      width: chartContainer.scrollWidth,
    });

    const link = document.createElement("a");
    link.download = filename;
    link.href = canvas.toDataURL("image/png");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  } catch (error) {
    console.error("Error exporting chart:", error);
    // Fallback: export data as CSV instead
    exportToCSV(csvData, filename.replace(".png", ".csv"));
  }
};

/**
 * Advanced chart export with smart container detection and retry mechanism
 */
export const exportChartAsImageAdvanced = async (
  filename: string,
  csvData: any[],
  componentClass: string = ""
) => {
  try {
    // Import html2canvas dynamically to avoid SSR issues
    const html2canvas = (await import("html2canvas")).default;

    // Find the chart container with component-specific selector first
    let chartContainer: HTMLElement | null = null;

    if (componentClass) {
      chartContainer = document.querySelector(
        `.${componentClass} [data-chart-container]`
      ) as HTMLElement;
    }

    // If not found, fall back to any chart container
    if (!chartContainer) {
      chartContainer = document.querySelector(
        "[data-chart-container]"
      ) as HTMLElement;
    }

    if (!chartContainer) {
      // Fallback: export data as CSV instead
      exportToCSV(csvData, filename.replace(".png", ".csv"));
      return;
    }

    // If dimensions are zero, wait for the chart to render and retry
    let retryCount = 0;
    const maxRetries = 5;
    while (
      (chartContainer.offsetWidth === 0 || chartContainer.offsetHeight === 0) &&
      retryCount < maxRetries
    ) {
      await new Promise((resolve) => setTimeout(resolve, 200));
      retryCount++;
    }

    // If still zero dimensions, try using SVG element
    let targetElement = chartContainer;
    if (chartContainer.offsetWidth === 0) {
      const svgElements = chartContainer.querySelectorAll("svg");
      if (svgElements.length > 0) {
        // Find the largest SVG (usually the main chart)
        let largestSvg = svgElements[0] as unknown as HTMLElement;
        let maxArea = 0;

        svgElements.forEach((svg) => {
          const rect = svg.getBoundingClientRect();
          const area = rect.width * rect.height;
          if (area > maxArea) {
            maxArea = area;
            largestSvg = svg as unknown as HTMLElement;
          }
        });

        if (largestSvg.getBoundingClientRect().width > 0) {
          targetElement = largestSvg;
        }
      }
    }

    // Final check - if still no dimensions, export CSV
    if (
      targetElement.offsetWidth === 0 &&
      targetElement.getBoundingClientRect().width === 0
    ) {
      exportToCSV(csvData, filename.replace(".png", ".csv"));
      return;
    }

    const canvas = await html2canvas(targetElement, {
      background: "#ffffff",
      useCORS: true,
      allowTaint: true,
      height:
        targetElement.scrollHeight ||
        targetElement.getBoundingClientRect().height,
      width:
        targetElement.scrollWidth ||
        targetElement.getBoundingClientRect().width,
    });

    // Check if canvas has valid dimensions
    if (canvas.width === 0 || canvas.height === 0) {
      exportToCSV(csvData, filename.replace(".png", ".csv"));
      return;
    }

    const link = document.createElement("a");
    link.download = filename;
    link.href = canvas.toDataURL("image/png");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  } catch (error) {
    console.error("Error exporting chart:", error);
    // Fallback: export data as CSV instead
    exportToCSV(csvData, filename.replace(".png", ".csv"));
  }
};

/**
 * Exports heatmap as PNG image
 */
export const exportHeatmapAsImage = async (filename: string) => {
  try {
    // Import html2canvas dynamically to avoid SSR issues
    const html2canvas = (await import("html2canvas")).default;

    // Find the heatmap container
    const heatmapContainer = document.querySelector(
      "[data-heatmap-container]"
    ) as HTMLElement;
    if (!heatmapContainer) {
      alert("Heatmap not found for export");
      return;
    }

    // Wait a bit to ensure map is fully rendered
    await new Promise((resolve) => setTimeout(resolve, 500));

    const canvas = await html2canvas(heatmapContainer, {
      useCORS: true,
      allowTaint: true,
      width: heatmapContainer.offsetWidth,
      height: heatmapContainer.offsetHeight,
    });

    const link = document.createElement("a");
    link.download = filename;
    link.href = canvas.toDataURL("image/png");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  } catch (error) {
    console.error("Error exporting heatmap:", error);
    alert("Failed to export heatmap. Please try again.");
  }
};

/**
 * Check if download should be disabled based on view type and data
 */
export const isDownloadDisabled = (
  viewType: string,
  dataLength: number,
  selectedRowsSize: number = 0
) => {
  switch (viewType) {
    case "graph":
      return dataLength === 0;
    case "table":
      return selectedRowsSize === 0;
    case "heatmap":
      return true; // Always disabled for heatmap
    default:
      return true;
  }
};

/**
 * Check if download should be shown based on view type
 */
export const shouldShowDownload = (viewType: string) => {
  return viewType !== "heatmap";
};

// Overview Analytics Utilities

/**
 * Date range options for analytics components
 */
export const DATE_RANGE_OPTIONS = [
  { value: "1h" as const, label: "1h" },
  { value: "1d" as const, label: "1d" },
  { value: "7d" as const, label: "7d" },
  { value: "1m" as const, label: "1m" },
  { value: "3m" as const, label: "3m" },
  { value: "1y" as const, label: "1yr" },
];

/**
 * Calculate date range based on selected option
 */
export const getDateRange = (range: string, now: Date = new Date()) => {
  const end = new Date(now);
  const start = new Date(now);

  switch (range) {
    case "1h":
      start.setHours(start.getHours() - 1);
      break;
    case "1d":
      start.setDate(start.getDate() - 1);
      break;
    case "7d":
      start.setDate(start.getDate() - 7);
      break;
    case "1m":
      start.setMonth(start.getMonth() - 1);
      break;
    case "3m":
      start.setMonth(start.getMonth() - 3);
      break;
    case "1y":
      start.setFullYear(start.getFullYear() - 1);
      break;
  }

  return {
    from: start.toISOString(),
    to: end.toISOString(),
  };
};

/**
 * Calculate previous date range for comparison
 */
export const getPreviousDateRange = (range: string, now: Date = new Date()) => {
  const currentRange = getDateRange(range, now);
  const currentStart = new Date(currentRange.from);
  const currentEnd = new Date(currentRange.to);
  const duration = currentEnd.getTime() - currentStart.getTime();

  const previousEnd = new Date(currentStart);
  const previousStart = new Date(currentStart.getTime() - duration);

  return {
    from: previousStart.toISOString(),
    to: previousEnd.toISOString(),
  };
};

/**
 * Generate comparison text based on selected range
 */
export const getComparisonText = (range: string): string => {
  switch (range) {
    case "1h":
      return "from previous hour";
    case "1d":
      return "from yesterday";
    case "7d":
      return "from previous week";
    case "1m":
      return "from previous month";
    case "3m":
      return "from previous 3 months";
    case "1y":
      return "from previous year";
    default:
      return "from previous period";
  }
};

/**
 * Parse time string to number for comparison
 */
export const parseTimeString = (timeStr: string | number): number => {
  if (typeof timeStr === "number") return timeStr;
  if (timeStr === "N/A") return 0;
  const numStr = timeStr.replace(/[^\d.]/g, "");
  return parseFloat(numStr) || 0;
};

/**
 * Format minutes to readable time string
 */
export const formatTimeFromMinutes = (minutes: number | string): string => {
  if (typeof minutes === "string") return minutes;
  if (minutes === 0) return "N/A";

  // Show days if >= 1440 minutes (24 hours), hours if >= 60 minutes, seconds if < 1 minute
  if (minutes >= 1440) {
    const days = (minutes / 1440).toFixed(1);
    return `${days}d`;
  } else if (minutes >= 60) {
    const hours = (minutes / 60).toFixed(1);
    return `${hours}h`;
  } else if (minutes < 1) {
    const seconds = Math.round(minutes * 60);
    return `${seconds}s`;
  } else {
    return `${Math.round(minutes)}m`;
  }
};

/**
 * Format minutes to hours/minutes for incidents (different format than cases)
 */
export const formatIncidentTimeFromMinutes = (
  minutes: string | number
): string => {
  if (typeof minutes === "string") return minutes;
  if (minutes === 0) return "N/A";

  // Show hours if >= 60 minutes, seconds if < 1 minute
  if (minutes >= 60) {
    const hours = (minutes / 60).toFixed(1);
    return `${hours}h`;
  } else if (minutes < 1) {
    const seconds = Math.round(minutes * 60);
    return `${seconds}s`;
  } else {
    return `${Math.round(minutes)}m`;
  }
};

// Time Series Analytics Utilities

/**
 * Get the appropriate time scale based on selected date range
 */
export const getTimeScale = (selectedDateRange: string): string => {
  switch (selectedDateRange) {
    case "1d":
      return "hour";
    case "3m":
      return "week";
    case "1y":
      return "month";
    default:
      return "day"; // For 1h, 7d, 1m
  }
};

/**
 * Generate time periods based on the scale and date range
 */
export const generateTimeRange = (
  dateRange: { from: string; to: string },
  selectedDateRange: string
): string[] => {
  const periods: string[] = [];
  const start = new Date(dateRange.from);
  const end = new Date(dateRange.to);
  const timeScale = getTimeScale(selectedDateRange);

  if (timeScale === "hour") {
    // Generate hourly periods for 1d
    start.setMinutes(0, 0, 0);
    const current = new Date(start);
    while (current <= end) {
      periods.push(current.toISOString()); // Full timestamp for hours
      current.setHours(current.getHours() + 1);
    }
  } else if (timeScale === "week") {
    // Generate weekly periods for 3m
    const startOfWeek = new Date(start);
    startOfWeek.setDate(start.getDate() - start.getDay()); // Go to Sunday
    startOfWeek.setHours(0, 0, 0, 0);

    const current = new Date(startOfWeek);
    while (current <= end) {
      periods.push(current.toISOString().split("T")[0]); // YYYY-MM-DD format for weeks
      current.setDate(current.getDate() + 7);
    }
  } else if (timeScale === "month") {
    // Generate monthly periods for 1y
    const current = new Date(start);
    current.setDate(1); // First day of month
    current.setHours(0, 0, 0, 0);

    while (current <= end) {
      periods.push(
        `${current.getFullYear()}-${String(current.getMonth() + 1).padStart(
          2,
          "0"
        )}`
      ); // YYYY-MM format
      current.setMonth(current.getMonth() + 1);
    }
  } else {
    // Generate daily periods (default)
    start.setHours(0, 0, 0, 0);
    const current = new Date(start);
    while (current <= end) {
      periods.push(current.toISOString().split("T")[0]); // YYYY-MM-DD format
      current.setDate(current.getDate() + 1);
    }
  }

  return periods;
};

/**
 * Get the period key for a given date based on time scale
 */
export const getPeriodKey = (date: Date, timeScale: string): string => {
  switch (timeScale) {
    case "hour": {
      // Round to nearest hour
      const hourDate = new Date(date);
      hourDate.setMinutes(0, 0, 0);
      return hourDate.toISOString();
    }
    case "week": {
      // Get the Sunday of the week containing this date
      const weekDate = new Date(date);
      weekDate.setDate(date.getDate() - date.getDay());
      weekDate.setHours(0, 0, 0, 0);
      return weekDate.toISOString().split("T")[0];
    }
    case "month":
      // Get YYYY-MM format
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(
        2,
        "0"
      )}`;
    default:
      // Daily - YYYY-MM-DD format
      return date.toISOString().split("T")[0];
  }
};

/**
 * Format period for display based on time scale
 */
export const formatPeriodForDisplay = (
  periodKey: string,
  timeScale: string,
  dateRange?: string
): string => {
  switch (timeScale) {
    case "hour": {
      const hourDate = new Date(periodKey);
      return hourDate.toLocaleTimeString("en-US", {
        hour: "numeric",
        hour12: true,
      });
    }
    case "week": {
      const weekDate = new Date(periodKey);
      return `Week of ${weekDate.toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
      })}`;
    }
    case "month": {
      const [year, month] = periodKey.split("-");
      const monthDate = new Date(parseInt(year), parseInt(month) - 1);

      // For 1y date range, format as "Apr 25" (short year) instead of "Apr 2025"
      if (dateRange === "1y") {
        return monthDate.toLocaleDateString("en-US", {
          month: "short",
          year: "2-digit",
        });
      }

      return monthDate.toLocaleDateString("en-US", {
        month: "short",
        year: "numeric",
      });
    }
    default:
      return new Date(periodKey).toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
      });
  }
};
