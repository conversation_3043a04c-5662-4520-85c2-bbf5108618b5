export interface OrganizationData {
  informationSection?: {
    name?: string;
    type?: string;
    status?: string;
    phone?: string;
    website?: string;
    email?: string;
    address?: Array<{
      streetAddress1?: string;
      streetAddress2?: string;
      city?: string;
      state?: string;
      zipCode?: string;
    }>;
    primaryContactName?: string;
    primaryContactTitle?: string;
    primaryContactPhone?: string;
    campusOrganization?: string;
  };
  createTime?: string;
  updateTime?: string;
  internalTags?: Array<{
    value: string;
    label: string;
    color: string;
  }>;
}