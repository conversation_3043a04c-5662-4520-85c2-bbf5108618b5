import { colors } from "@/design-system/tokens";
import DeleteIcon from "@mui/icons-material/Delete";
import DownloadIcon from "@mui/icons-material/Download";
import PhotoIcon from "@mui/icons-material/Photo";
import VisibilityIcon from "@mui/icons-material/Visibility";
import {
    Box,
    Chip,
    IconButton,
    Paper,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Typography,
} from "@mui/material";
import { Entity } from "proto/hero/entity/v1/entity_pb";
import { SortField, SortOrder } from "proto/hero/filerepository/v1/filerepository_pb";
import { useState } from "react";
import {
    FaFile,
    FaFileAlt,
    FaFileAudio,
    FaFileImage,
    FaFilePdf,
    FaFileVideo,
} from "react-icons/fa";
import { useDeleteFile, useGetPresignedDownloadUrl, useSearchFiles } from "../../../apis/services/filerepository/hooks";
import { usePropertyFileAttachments, useRemovePropertyFileAttachment } from "../../../apis/services/workflow/property/hooks";

interface MediaTableCardProps {
    property: Entity | undefined;
}

// Helper function to get file type icon (matches MediaSection pattern)
const getFileTypeIcon = (mimeType: string) => {
    const fileType = (mimeType || '').toLowerCase();

    // Detection based ONLY on MIME type
    if (fileType.startsWith('image/')) {
        return <FaFileImage style={{ fontSize: 20, color: colors.blue[600] }} />;
    }
    if (fileType.startsWith('video/')) {
        return <FaFileVideo style={{ fontSize: 20, color: colors.blue[600] }} />;
    }
    if (fileType.startsWith('audio/')) {
        return <FaFileAudio style={{ fontSize: 20, color: colors.blue[600] }} />;
    }
    if (fileType === 'application/pdf') {
        return <FaFilePdf style={{ fontSize: 20, color: colors.blue[600] }} />;
    }
    if (fileType.includes('text/') || fileType.includes('document') || fileType.includes('word')) {
        return <FaFileAlt style={{ fontSize: 20, color: colors.blue[600] }} />;
    }

    // Default icon if MIME type is unknown or missing
    return <FaFile style={{ fontSize: 20, color: colors.grey[500] }} />;
};

// Helper function to format file size
const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

// Helper function to format date
const formatDate = (dateString: string) => {
    try {
        const date = new Date(dateString);
        return date.toLocaleDateString();
    } catch {
        return dateString;
    }
};

export default function MediaTableCard({ property }: MediaTableCardProps) {
    type DisplayFile = {
        id: string;
        fileName?: string;
        fileType?: string;
        caption?: string;
        fileCategory?: string;
        displayOrder?: number;
        extraMetadata?: any;
    };
    const [downloadingFileId, setDownloadingFileId] = useState<string | null>(null);
    const [deletingFileId, setDeletingFileId] = useState<string | null>(null);

    // Use property ID directly from the entity
    const propertyId = property?.id || "";

    // Get property file attachments using the new API
    const {
        data: propertyFileAttachments,
        isLoading: isPropertyFilesLoading,
        isError: isPropertyFilesError,
    } = usePropertyFileAttachments({
        propertyId: propertyId,
        pageSize: 50,
    });

    // Also search file repository for backward compatibility (for old properties)
    const {
        data: searchResults,
        isLoading: isSearchLoading,
        isError: isSearchError,
    } = useSearchFiles({
        query: propertyId || "",
        searchFields: ["extraMetadata"],
        ownerId: "",
        statuses: [],
        fileTypes: [],
        extensions: [],
        storageClasses: [],
        tagFilters: {
            "propertyId": propertyId || "",
        },
        pageSize: 50,
        publicOnly: false,
        privateOnly: false,
        tagKeys: [],
        hasThumbnail: false,
        sortField: SortField.UNSPECIFIED,
        sortOrder: SortOrder.UNSPECIFIED,
        pageToken: "",
        $typeName: "hero.filerepository.v1.SearchFilesRequest",
    }, {
        enabled: !!propertyId,
    });

    // Combine property file attachments with legacy search results
    const propertyAttachmentFiles = propertyFileAttachments?.fileAttachments || [];
    const searchResultFilesRaw = (searchResults?.results || []).map(result => result.metadata).filter(Boolean) as any[];
    const normalizedSearchFiles: DisplayFile[] = searchResultFilesRaw.map((m: any) => ({
        id: m.id,
        fileName: m.fileName,
        fileType: m.fileType,
        extraMetadata: m.extraMetadata,
    }));

    // Convert property file attachments to file metadata format for display (matching MediaSection format)
    const convertedPropertyFiles: DisplayFile[] = propertyAttachmentFiles.map(attachment => ({
        id: attachment.fileId,
        fileName: attachment.displayName || 'Unknown',
        fileType: attachment.metadata?.fileType || 'application/octet-stream',
        caption: attachment.caption || `Attached to property`,
        fileCategory: attachment.fileCategory || 'property_attachment',
        displayOrder: attachment.displayOrder,
        extraMetadata: {
            propertyId: propertyId,
            attachmentId: attachment.id,
            displayOrder: attachment.displayOrder,
            fileCategory: attachment.fileCategory,
            uploadContext: attachment.metadata?.uploadContext || 'property_form'
        }
    }));

    // Combine all file sources
    const allMediaFiles: DisplayFile[] = [...convertedPropertyFiles, ...normalizedSearchFiles];

    // Remove duplicates based on file ID
    const uniqueMediaFiles: DisplayFile[] = allMediaFiles.filter((file, index, self) =>
        index === self.findIndex(f => f.id === file.id)
    );

    // Only consider it loading if we're loading property files AND don't have any files yet
    // OR if we're loading both and have no files from either source
    const hasPropertyFiles = propertyAttachmentFiles.length > 0;
    const hasSearchFiles = normalizedSearchFiles.length > 0;
    const isLoading = (isPropertyFilesLoading && !hasPropertyFiles) ||
        (isSearchLoading && !hasPropertyFiles && !hasSearchFiles);
    // Only consider it an error if property files failed AND we have no files from any source
    // OR if both failed
    const isError = (isPropertyFilesError && !hasPropertyFiles && !hasSearchFiles) ||
        (isPropertyFilesError && isSearchError);


    // File repository hooks for operations (matches MediaSection pattern)
    const deleteFileMutation = useDeleteFile();

    // Property file attachment removal hook
    const removePropertyFileAttachmentMutation = useRemovePropertyFileAttachment();

    const getDownloadUrlMutation = useGetPresignedDownloadUrl();

    const handleOpenFile = async (file: DisplayFile) => {
        try {
            setDownloadingFileId(file.id);

            // Get presigned download URL with 5-minute expiration
            const response = await getDownloadUrlMutation.mutateAsync({
                id: file.id,
                expiresIn: 300 // 5 minutes
            } as any);

            // The backend automatically logs 'download' when GetPresignedDownloadUrl is called
            // Open the file in a new tab (like in MediaSection)
            window.open(response.presignedUrl, '_blank');
        } catch (error) {
            console.error('Failed to open file:', error);
            alert('Failed to open file. Please try again.');
        } finally {
            setDownloadingFileId(null);
        }
    };

    const handleDownloadFile = async (file: DisplayFile) => {
        try {
            setDownloadingFileId(file.id);

            // Get presigned download URL with 5-minute expiration
            const response = await getDownloadUrlMutation.mutateAsync({
                id: file.id,
                expiresIn: 300 // 5 minutes
            } as any);

            // The backend automatically logs 'download' when GetPresignedDownloadUrl is called
            // Fetch the file content and create a blob URL to force download
            const fileResponse = await fetch(response.presignedUrl);
            const blob = await fileResponse.blob();

            // Create a blob URL and trigger download
            const blobUrl = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = blobUrl;
            link.download = file.fileName || 'download'; // This forces download with the correct filename
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // Clean up the blob URL to free memory
            URL.revokeObjectURL(blobUrl);

        } catch (error) {
            console.error('Download failed:', error);
            alert('Failed to download file. Please try again.');
        } finally {
            setDownloadingFileId(null);
        }
    };

    const handleDeleteFile = async (file: DisplayFile) => {
        if (!confirm('Are you sure you want to delete this file? This will remove it from the property and delete it from the database.')) {
            return;
        }

        try {
            setDeletingFileId(file.id);

            // Check if this is a property file attachment (has attachmentId in extraMetadata)
            const attachmentId = file.extraMetadata?.attachmentId as unknown;

            if (typeof attachmentId === 'string') {
                // Remove the property file attachment relationship
                await removePropertyFileAttachmentMutation.mutateAsync({
                    propertyId: propertyId,
                    attachmentId: attachmentId
                });
            } else {
                // Soft delete the file in the file repository for legacy files
                await deleteFileMutation.mutateAsync({ id: file.id } as any);
            }
        } catch (error) {
            console.error('Error deleting file:', error);
            alert('Failed to delete file. Please try again.');
        } finally {
            setDeletingFileId(null);
        }
    };

    if (isLoading) {
        return (
            <Paper
                elevation={0}
                sx={{
                    borderRadius: 2,
                    border: `1px solid ${colors.grey[200]}`,
                    mb: 3,
                    overflow: "hidden",
                    maxWidth: "100%",
                }}
            >
                <Box sx={{ p: 2, bgcolor: "white" }}>
                    <Typography variant="body2" color={colors.grey[500]}>
                        Loading media files...
                    </Typography>
                </Box>
            </Paper>
        );
    }

    // 

    // Show "No media files found" for both error and empty states
    if (isError || !uniqueMediaFiles || uniqueMediaFiles.length === 0) {
        return (
            <Paper
                elevation={0}
                sx={{
                    borderRadius: 2,
                    border: `1px solid ${colors.grey[200]}`,
                    mb: 3,
                    overflow: "hidden",
                    maxWidth: "100%",
                }}
            >
                <Box sx={{ p: 2, bgcolor: "white" }}>
                    <Box sx={{ display: "flex", alignItems: "center", gap: 1, mb: 2 }}>
                        <PhotoIcon sx={{ color: colors.grey[500] }} />
                        <Typography variant="h6" sx={{ fontWeight: 600 }}>
                            Associated Media
                        </Typography>
                    </Box>
                    <Typography variant="body2" color={colors.grey[500]}>
                        No media files found
                    </Typography>
                </Box>
            </Paper>
        );
    }

    return (
        <Paper
            elevation={0}
            sx={{
                borderRadius: 2,
                border: `1px solid ${colors.grey[200]}`,
                mb: 3,
                overflow: "hidden",
                maxWidth: "100%",
            }}
        >
            <Box sx={{ p: 2, bgcolor: "white" }}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1, mb: 2 }}>
                    <PhotoIcon sx={{ color: colors.grey[500] }} />
                    <Typography variant="h6" sx={{ fontWeight: 600 }}>
                        Associated Media
                    </Typography>
                    <Chip label={uniqueMediaFiles.length} size="small" color="primary" />
                </Box>

                <TableContainer>
                    <Table size="small">
                        <TableHead>
                            <TableRow>
                                <TableCell sx={{ fontWeight: 600 }}>Preview</TableCell>
                                <TableCell sx={{ fontWeight: 600 }}>Title</TableCell>
                                <TableCell sx={{ fontWeight: 600 }}>Description</TableCell>
                                <TableCell sx={{ fontWeight: 600 }}>Category</TableCell>
                                <TableCell sx={{ fontWeight: 600 }}>Actions</TableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {uniqueMediaFiles.map((file) => file && (
                                <TableRow key={file.id} hover>
                                    {/* Preview Column */}
                                    <TableCell>
                                        <Box
                                            sx={{
                                                width: 48,
                                                height: 48,
                                                borderRadius: "8px",
                                                border: `1px solid ${colors.grey[200]}`,
                                                display: "flex",
                                                alignItems: "center",
                                                justifyContent: "center",
                                                backgroundColor: colors.grey[50],
                                            }}
                                        >
                                            {getFileTypeIcon(file.fileType || 'application/octet-stream')}
                                        </Box>
                                    </TableCell>
                                    {/* Title Column - Clickable to open file */}
                                    <TableCell>
                                        <Box
                                            onClick={() => handleOpenFile(file)}
                                            sx={{
                                                cursor: downloadingFileId === file.id ? 'wait' : 'pointer',
                                                display: 'flex',
                                                alignItems: 'center',
                                                gap: 1,
                                                '&:hover': {
                                                    textDecoration: 'underline',
                                                },
                                            }}
                                        >
                                            {downloadingFileId === file.id && (
                                                <Box
                                                    sx={{
                                                        width: 12,
                                                        height: 12,
                                                        border: `2px solid ${colors.blue[200]}`,
                                                        borderTop: `2px solid ${colors.blue[600]}`,
                                                        borderRadius: '50%',
                                                        animation: 'spin 1s linear infinite',
                                                        '@keyframes spin': {
                                                            '0%': { transform: 'rotate(0deg)' },
                                                            '100%': { transform: 'rotate(360deg)' },
                                                        },
                                                    }}
                                                />
                                            )}
                                            <Typography
                                                variant="body2"
                                                sx={{
                                                    fontWeight: 500,
                                                    color: downloadingFileId === file.id ? colors.blue[300] : colors.blue[600]
                                                }}
                                            >
                                                {file.fileName || "Unknown"}
                                            </Typography>
                                        </Box>
                                    </TableCell>
                                    {/* Description Column */}
                                    <TableCell>
                                        <Typography variant="body2" color={colors.grey[700]}>
                                            {file.caption || 'No description'}
                                        </Typography>
                                    </TableCell>
                                    {/* Category Column */}
                                    <TableCell>
                                        <Box
                                            sx={{
                                                px: 2,
                                                py: 0.5,
                                                borderRadius: "12px",
                                                backgroundColor: colors.grey[100],
                                                display: "inline-flex",
                                                alignItems: "center",
                                                justifyContent: "center",
                                                maxWidth: "100%",
                                            }}
                                        >
                                            <Typography variant="caption" color={colors.grey[700]}>
                                                {file.fileCategory || 'Unknown'}
                                            </Typography>
                                        </Box>
                                    </TableCell>
                                    <TableCell>
                                        <Box sx={{ display: "flex", gap: 1 }}>
                                            <IconButton
                                                size="small"
                                                onClick={() => handleOpenFile(file)}
                                                disabled={downloadingFileId === file.id}
                                                sx={{
                                                    color: downloadingFileId === file.id ? colors.blue[300] : colors.blue[600],
                                                    "&:hover": {
                                                        color: downloadingFileId === file.id ? colors.blue[300] : colors.blue[700],
                                                        backgroundColor: downloadingFileId === file.id ? 'transparent' : colors.blue[50],
                                                    },
                                                }}
                                            >
                                                {downloadingFileId === file.id ? (
                                                    <Box
                                                        sx={{
                                                            width: 16,
                                                            height: 16,
                                                            border: `2px solid ${colors.blue[200]}`,
                                                            borderTop: `2px solid ${colors.blue[600]}`,
                                                            borderRadius: '50%',
                                                            animation: 'spin 1s linear infinite',
                                                            '@keyframes spin': {
                                                                '0%': { transform: 'rotate(0deg)' },
                                                                '100%': { transform: 'rotate(360deg)' },
                                                            },
                                                        }}
                                                    />
                                                ) : (
                                                    <VisibilityIcon sx={{ fontSize: 16 }} />
                                                )}
                                            </IconButton>
                                            <IconButton
                                                size="small"
                                                onClick={() => handleDownloadFile(file)}
                                                disabled={downloadingFileId === file.id}
                                                sx={{
                                                    color: downloadingFileId === file.id ? colors.blue[300] : colors.blue[600],
                                                    "&:hover": {
                                                        color: downloadingFileId === file.id ? colors.blue[300] : colors.blue[700],
                                                        backgroundColor: downloadingFileId === file.id ? 'transparent' : colors.blue[50],
                                                    },
                                                }}
                                            >
                                                {downloadingFileId === file.id ? (
                                                    <Box
                                                        sx={{
                                                            width: 16,
                                                            height: 16,
                                                            border: `2px solid ${colors.blue[200]}`,
                                                            borderTop: `2px solid ${colors.blue[600]}`,
                                                            borderRadius: '50%',
                                                            animation: 'spin 1s linear infinite',
                                                            '@keyframes spin': {
                                                                '0%': { transform: 'rotate(0deg)' },
                                                                '100%': { transform: 'rotate(360deg)' },
                                                            },
                                                        }}
                                                    />
                                                ) : (
                                                    <DownloadIcon sx={{ fontSize: 16 }} />
                                                )}
                                            </IconButton>
                                            <IconButton
                                                size="small"
                                                onClick={() => handleDeleteFile(file)}
                                                disabled={deletingFileId === file.id}
                                                sx={{
                                                    color: deletingFileId === file.id ? colors.grey[300] : colors.grey[400],
                                                    "&:hover": {
                                                        color: deletingFileId === file.id ? colors.grey[300] : colors.grey[600],
                                                        backgroundColor: deletingFileId === file.id ? 'transparent' : colors.grey[100],
                                                    },
                                                }}
                                            >
                                                {deletingFileId === file.id ? (
                                                    <Box
                                                        sx={{
                                                            width: 16,
                                                            height: 16,
                                                            border: `2px solid ${colors.grey[300]}`,
                                                            borderTop: `2px solid ${colors.grey[600]}`,
                                                            borderRadius: '50%',
                                                            animation: 'spin 1s linear infinite',
                                                            '@keyframes spin': {
                                                                '0%': { transform: 'rotate(0deg)' },
                                                                '100%': { transform: 'rotate(360deg)' },
                                                            },
                                                        }}
                                                    />
                                                ) : (
                                                    <DeleteIcon sx={{ fontSize: 16 }} />
                                                )}
                                            </IconButton>
                                        </Box>
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </TableContainer>
            </Box>
        </Paper>
    );
} 