# Property Entity Components

This directory contains specialized components for displaying property entity information, including chain of custody tracking and media file management.

## Components

### ChainOfCustodyCard

Displays the complete chain of custody history for a property entity.

**Features:**
- Shows all custody events in chronological order
- Displays action types with color-coded chips (Collected, Transferred, Released, etc.)
- Shows transferring and receiving user information
- Includes location and notes for each custody event
- Handles loading and error states gracefully

**Props:**
- `property: Entity` - The property entity object

**Data Source:**
- Uses `useCustody<PERSON>hain` hook to fetch custody chain data from the property service
- Extracts property ID from entity data structure

**UI Elements:**
- Table format with columns: Date/Time, Action, From, To, Location, Notes
- Color-coded action type chips
- Icons for different data types (person, location, description, etc.)

### MediaTableCard

Displays media files associated with a property entity.

**Features:**
- Shows all media files in a table format
- Displays file type icons and metadata
- Provides view and download actions
- Shows file status and upload information
- Handles loading and error states

**Props:**
- `property: Entity` - The property entity object

**Data Source:**
- Uses `useSearchFiles` hook to search for files associated with the property
- Searches in `extraMetadata` field and uses `tagFilters` for property association

**UI Elements:**
- Table format with columns: Type, File Name, Size, Uploaded, Status, Actions
- File type icons (image, video, audio, PDF, document, etc.)
- Action buttons for view and download
- Status chips showing file lifecycle state

## Usage

These components are automatically included in the `PropertyInfoCard` component and will appear below the main property information section when viewing a property entity.

### Example Integration

```tsx
import ChainOfCustodyCard from './cards/ChainOfCustodyCard';
import MediaTableCard from './cards/MediaTableCard';

// In PropertyInfoCard component
return (
  <>
    {/* Main property info */}
    <Paper>
      {/* Property details */}
    </Paper>

    {/* Chain of Custody Section */}
    <ChainOfCustodyCard property={property} />

    {/* Media Table Section */}
    <MediaTableCard property={property} />
  </>
);
```

## Data Requirements

### Chain of Custody
- Property entity must have a valid property ID in its data structure
- Property service must be available and configured
- Custody events should follow the `CustodyEvent` interface structure

### Media Files
- Files must be associated with the property via `extraMetadata` or `storage_tags`
- File repository service must be available and configured
- Files should have proper metadata including file type, size, and status

## Styling

Both components follow the existing design system:
- Use Material-UI components for consistency
- Follow the color scheme defined in `@/design-system/tokens`
- Maintain consistent spacing and typography
- Use the same paper elevation and border styling as other entity cards

## Error Handling

Both components include comprehensive error handling:
- Graceful degradation when property ID is not available
- Loading states during data fetching
- Error states with user-friendly messages
- Fallback displays when no data is found

## Performance Considerations

- Components use React Query for efficient caching and data fetching
- Search queries are optimized with proper filtering
- Pagination is supported for large datasets
- Components are memoized to prevent unnecessary re-renders 