import { colors } from "@/design-system/tokens";
import {
    Box,
    Paper,
    Typography
} from "@mui/material";
import { Entity } from "proto/hero/entity/v1/entity_pb";
import { useAsset } from "../../../apis/services/workflow/assets/hooks";
import { hookCustodyActionTypeToString, stringToCustodyActionType } from "../../../apis/services/workflow/property/enumConverters";
import { useCustody<PERSON>hain } from "../../../apis/services/workflow/property/hooks";
import { CustodyActionType, CustodyEvent } from "../../../apis/services/workflow/property/types";

interface ChainOfCustodyCardProps {
    property: Entity | undefined;
}

// Helper function to get custody action display info
const getCustodyActionInfo = (action: CustodyActionType | string) => {
    // Handle both string and numeric enum values
    let actionType: CustodyActionType;

    if (typeof action === 'string') {
        actionType = stringToCustodyActionType(action);
    } else {
        actionType = action;
    }

    switch (actionType) {
        case CustodyActionType.COLLECTED:
            return { label: "Collected", color: "success" as const };
        case CustodyActionType.CHECKED_IN:
            return { label: "In Custody", color: "primary" as const };
        case CustodyActionType.CHECKED_OUT:
            return { label: "Checked Out", color: "warning" as const };
        case CustodyActionType.TRANSFERRED:
            return { label: "Transferred", color: "info" as const };
        case CustodyActionType.RELEASED:
            return { label: "Released", color: "success" as const };
            case CustodyActionType.DISPOSED:
            return { label: "Disposed", color: "error" as const };
        case CustodyActionType.LOGGED:
            return { label: "Logged", color: "default" as const };
        default:
            return { label: "Unknown", color: "default" as const };
    }
};

// Helper function to get custody action display info for hook data
const getCustodyActionInfoFromHook = (action: CustodyActionType) => {
    const actionString = hookCustodyActionTypeToString(action);
    return getCustodyActionInfo(actionString);
};

// Helper function to format date for grouping
const formatDateForGrouping = (timestamp: string) => {
    try {
        const date = new Date(timestamp);
        if (isNaN(date.getTime())) {
            return "Invalid Date";
        }
        return date.toLocaleDateString("en-US", {
            weekday: "long",
            month: "long",
            day: "numeric",
            year: "numeric"
        });
    } catch {
        return "Invalid Date";
    }
};

// Helper function to format time
const formatTime = (timestamp: string) => {
    try {
        const date = new Date(timestamp);
        if (isNaN(date.getTime())) {
            return "Invalid Time";
        }
        return date.toLocaleTimeString("en-US", {
            hour: "2-digit",
            minute: "2-digit",
            hour12: false
        });
    } catch {
        return "Invalid Time";
    }
};

// Helper function to get user initials
const getUserInitials = (name: string) => {
    if (!name) return "00";

    const parts = name.split(/[\s.-]/);
    if (parts.length >= 2) {
        return `${parts[0][0]}${parts[1][0]}`.toUpperCase();
    }
    return name.substring(0, 2).toUpperCase();
};

// Helper function to get display name
const getDisplayName = (userId: string) => {
    if (!userId) return "Unknown";

    // Extract initials from user ID (assuming format like "T. Kim" or "J. Pina")
    const parts = userId.split(/[\s.-]/);
    if (parts.length >= 2) {
        return `${parts[0]}. ${parts[1]}`;
    }
    return userId;
};

// Group events by date
const groupEventsByDate = (events: CustodyEvent[]) => {
    const groups: { [key: string]: CustodyEvent[] } = {};

    events.forEach(event => {
        const dateKey = formatDateForGrouping(event.timestamp);
        if (!groups[dateKey]) {
            groups[dateKey] = [];
        }
        groups[dateKey].push(event);
    });

    // Sort events within each group by timestamp (newest first)
    Object.keys(groups).forEach(dateKey => {
        groups[dateKey].sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
    });

    return groups;
};

// Component for individual custody event with asset lookup
const CustodyEventItem = ({ event }: { event: CustodyEvent }) => {
    const actionInfo = getCustodyActionInfoFromHook(event.actionType);

    // Look up the asset for the transferring user
    const { data: assetData } = useAsset(event.transferringUserId);
    const asset = assetData?.asset;

    // Look up the asset for the receiving user
    const { data: receivingAssetData } = useAsset(event.receivingUserId);
    const receivingAsset = receivingAssetData?.asset;

    // Get display name from asset or fallback to user ID
    const displayName = asset?.name || getDisplayName(event.transferringUserId);
    const userInitials = getUserInitials(asset?.name || event.transferringUserId);

    // Get receiving user display name
    const receivingDisplayName = receivingAsset?.name || getDisplayName(event.receivingUserId);

    // Create enhanced notes with transfer information
    const getEnhancedNotes = () => {
        let notes = event.notes || '';

        // Add transfer information if there's a receiving user different from transferring user
        if (event.receivingUserId && event.receivingUserId !== event.transferringUserId) {
            const transferNote = `(transferred to: ${receivingDisplayName})`;
            notes = notes ? `${notes} ${transferNote}` : transferNote;
        }

        return notes;
    };

    // Get location display
    const getLocationDisplay = () => {
        if (event.newLocation && event.newLocation.trim() !== '') {
            return event.newLocation;
        }
        return null; // Return null instead of 'N/A' to hide the location
    };

    return (
        <Box sx={{ mb: 2 }}>
            {/* Event Row */}
            <Box sx={{ display: "flex", alignItems: "flex-start", gap: 1.5 }}>
                {/* Time */}
                <Typography
                    sx={{
                        color: "#101828",
                        fontFamily: "Roboto",
                        fontSize: "12px",
                        fontStyle: "normal",
                        fontWeight: 500,
                        lineHeight: "140%",
                        letterSpacing: "0.17px",
                        minWidth: 50,
                    }}
                >
                    {formatTime(event.timestamp)}
                </Typography>

                {/* User Avatar */}
                <Box
                    sx={{
                        display: "flex",
                        width: "20px",
                        height: "20px",
                        padding: "1.818px 0",
                        justifyContent: "center",
                        alignItems: "center",
                        borderRadius: "14.545px",
                        background: "#0453CC",
                    }}
                >
                    <Typography
                        sx={{
                            color: "#FFF",
                            textAlign: "center",
                            fontFamily: "Roboto",
                            fontSize: "10.909px",
                            fontStyle: "normal",
                            fontWeight: 400,
                            lineHeight: "16.364px",
                        }}
                    >
                        {userInitials}
                    </Typography>
                </Box>

                {/* Event Details */}
                <Box sx={{ flex: 1, minWidth: 0 }}>
                    <Box sx={{ display: "flex", alignItems: "center", gap: 1, mb: 0.5 }}>
                        <Typography
                            sx={{
                                color: "#697282",
                                fontFamily: "Roboto",
                                fontSize: "12px",
                                fontStyle: "normal",
                                fontWeight: 500,
                                lineHeight: "140%",
                                letterSpacing: "0.17px",
                            }}
                        >
                            {displayName}
                        </Typography>
                        <Typography
                            sx={{
                                color: "#101828",
                                fontFamily: "Roboto",
                                fontSize: "12px",
                                fontStyle: "normal",
                                fontWeight: 500,
                                lineHeight: "140%",
                                letterSpacing: "0.17px",
                                overflow: "hidden",
                                textOverflow: "ellipsis",
                            }}
                        >
                            {actionInfo.label}
                        </Typography>
                        {getLocationDisplay() && (
                            <>
                                <Box
                                    sx={{
                                        width: 3,
                                        height: 3,
                                        borderRadius: "50%",
                                        bgcolor: colors.grey[500],
                                        mx: 1,
                                    }}
                                />
                                <Typography
                                    sx={{
                                        color: "#101828",
                                        fontFamily: "Roboto",
                                        fontSize: "12px",
                                        fontStyle: "normal",
                                        fontWeight: 400,
                                        lineHeight: "140%",
                                        letterSpacing: "0.17px",
                                        overflow: "hidden",
                                        textOverflow: "ellipsis",
                                        whiteSpace: "nowrap",
                                    }}
                                >
                                    {getLocationDisplay()}
                                </Typography>
                            </>
                        )}
                    </Box>

                    {/* Additional Notes */}
                    {getEnhancedNotes() && (
                        <Typography
                            sx={{
                                color: "#101828",
                                fontFamily: "Roboto",
                                fontSize: "12px",
                                fontStyle: "normal",
                                fontWeight: 400,
                                lineHeight: "140%",
                                letterSpacing: "0.17px",
                                ml: 0,
                            }}
                        >
                            {getEnhancedNotes()}
                        </Typography>
                    )}
                </Box>
            </Box>
        </Box>
    );
};

export default function ChainOfCustodyCard({ property }: ChainOfCustodyCardProps) {
    // Use property ID directly from the entity
    const propertyId = property?.id || "";

    const {
        data: custodyEvents,
        isLoading,
        isError,
    } = useCustodyChain(propertyId);

    const handleExport = () => {
        console.log("Export chain of custody");
        // TODO: Implement export functionality
    };

    if (isLoading) {
        return (
            <Paper
                elevation={0}
                sx={{
                    borderRadius: 2,
                    border: `1px solid ${colors.grey[200]}`,
                    mb: 3,
                    overflow: "hidden",
                    maxWidth: "100%",
                }}
            >
                <Box sx={{ p: 3, bgcolor: "white" }}>
                    <Typography variant="body2" color={colors.grey[500]}>
                        Loading chain of custody...
                    </Typography>
                </Box>
            </Paper>
        );
    }

    if (isError) {
        return (
            <Paper
                elevation={0}
                sx={{
                    borderRadius: 2,
                    border: `1px solid ${colors.grey[200]}`,
                    mb: 3,
                    overflow: "hidden",
                    maxWidth: "100%",
                }}
            >
                <Box sx={{ p: 3, bgcolor: "white" }}>
                    <Typography variant="body2" color={colors.grey[500]}>
                        Property does not have chain of custody yet. Chain of custody will begin on ingest.
                    </Typography>
                </Box>
            </Paper>
        );
    }

    if (!custodyEvents || custodyEvents.length === 0) {
        return (
            <Paper
                elevation={0}
                sx={{
                    borderRadius: 2,
                    border: `1px solid ${colors.grey[200]}`,
                    mb: 3,
                    overflow: "hidden",
                    maxWidth: "100%",
                }}
            >
                <Box sx={{ p: 3, bgcolor: "white" }}>
                    <Typography
                        sx={{
                            color: "#101828",
                            fontFamily: "Roboto",
                            fontSize: "20px",
                            fontStyle: "normal",
                            fontWeight: 700,
                            lineHeight: "140%",
                            letterSpacing: "0.25px",
                            mb: 3,
                        }}
                    >
                        Chain of Custody
                    </Typography>
                    <Typography variant="body2" color={colors.grey[500]}>
                        This object does not have a chain of custody yet as it has never been in police custody. Ingest the property to begin the chain of custody.
                    </Typography>
                </Box>
            </Paper>
        );
    }

    const groupedEvents = groupEventsByDate(custodyEvents);
    const sortedDates = Object.keys(groupedEvents).sort((a, b) =>
        new Date(b).getTime() - new Date(a).getTime()
    );

    return (
        <Paper
            elevation={0}
            sx={{
                borderRadius: 2,
                border: `1px solid ${colors.grey[200]}`,
                mb: 3,
                overflow: "hidden",
                maxWidth: "100%",
                maxHeight: 600,
                display: "flex",
                flexDirection: "column",
            }}
        >
            <Box sx={{ p: 3, bgcolor: "white" }}>
                {/* Header */}
                <Typography
                    sx={{
                        color: "#101828",
                        fontFamily: "Roboto",
                        fontSize: "20px",
                        fontStyle: "normal",
                        fontWeight: 700,
                        lineHeight: "140%",
                        letterSpacing: "0.25px",
                        mb: 3,
                    }}
                >
                    Chain of Custody
                </Typography>

                {/* Events Timeline */}
                <Box sx={{
                    overflowY: "auto",
                    maxHeight: 500,
                    '&::-webkit-scrollbar': {
                        width: 6,
                    },
                    '&::-webkit-scrollbar-track': {
                        backgroundColor: colors.grey[100],
                        borderRadius: 3,
                    },
                    '&::-webkit-scrollbar-thumb': {
                        backgroundColor: colors.grey[300],
                        borderRadius: 3,
                        '&:hover': {
                            backgroundColor: colors.grey[400],
                        },
                    },
                }}>
                    {sortedDates.map((dateKey, dateIndex) => (
                        <Box key={dateKey} sx={{ mb: dateIndex < sortedDates.length - 1 ? 4 : 0 }}>
                            {/* Date Header */}
                            <Typography
                                sx={{
                                    color: "#101828",
                                    fontFamily: "Roboto",
                                    fontSize: "12px",
                                    fontStyle: "normal",
                                    fontWeight: 500,
                                    lineHeight: "140%",
                                    letterSpacing: "0.17px",
                                    mb: 2,
                                }}
                            >
                                {dateKey}
                            </Typography>

                            {/* Events for this date */}
                            {groupedEvents[dateKey].map((event, eventIndex) => (
                                <CustodyEventItem key={eventIndex} event={event} />
                            ))}
                        </Box>
                    ))}
                </Box>
            </Box>
        </Paper>
    );
} 