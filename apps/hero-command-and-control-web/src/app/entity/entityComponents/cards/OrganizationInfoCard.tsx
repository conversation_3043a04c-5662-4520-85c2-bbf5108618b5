import React from "react";
import {
  Box,
  Paper,
  Typography,
  Divider,
} from "@mui/material";
import { colors } from "@/design-system/tokens";
import { Entity } from "proto/hero/entity/v1/entity_pb";
import { Label } from "@/design-system/components/Label";
import BusinessIcon from "@mui/icons-material/Business";
import LocationOnIcon from "@mui/icons-material/LocationOn";
import CategoryIcon from "@mui/icons-material/Category";
import DescriptionIcon from "@mui/icons-material/Description";
import WorkIcon from "@mui/icons-material/Work";
import AlternateEmailIcon from "@mui/icons-material/AlternateEmail";
import { twoLineClamp } from "../utils/constants";
import { OrganizationData } from "../utils/organizationTypes";

interface OrganizationInfoCardProps {
  organization: Entity | undefined;
}

interface InternalTag {
  value: string;
  label: string;
  color: string;
}

interface DisplayTag {
  label: string;
  color: string;
  prominence: boolean;
}

export default function OrganizationInfoCard({ organization }: OrganizationInfoCardProps) {
  // Extract data from the entity
  const extractOrganizationData = (): OrganizationData | null => {
    if (!organization?.data) {
      return null;
    }

    try {
      const data = typeof organization.data === 'string' ? JSON.parse(organization.data) : organization.data;
      
      const informationSection = data?.informationSection || {};
      const internalTags = data?.internalTags || [];

      return {
        informationSection: {
          name: informationSection.name || "",
          type: informationSection.type || "",
          status: informationSection.status || "",
          phone: informationSection.phone || "",
          website: informationSection.website || "",
          email: informationSection.email || "",
          address: informationSection.address || [],
          primaryContactName: informationSection.primaryContactName || "",
          primaryContactTitle: informationSection.primaryContactTitle || "",
          primaryContactPhone: informationSection.primaryContactPhone || "",
          campusOrganization: informationSection.campusOrganization || "",
        },
        createTime: data?.createTime || "",
        updateTime: data?.updateTime || "",
        internalTags: internalTags,
      };
    } catch (error) {
      console.warn("Failed to parse organization entity data:", error);
      return null;
    }
  };

  const organizationData = extractOrganizationData();

  // If we can't extract data, show a message
  if (!organizationData) {
    return (
      <Paper
        elevation={0}
        sx={{
          borderRadius: 2,
          border: `1px solid ${colors.grey[200]}`,
          mb: 3,
          overflow: "hidden",
          maxWidth: "100%",
        }}
      >
        <Box sx={{ p: 2, bgcolor: "white" }}>
          <Typography variant="body2" color={colors.grey[500]}>
            No organization data available
          </Typography>
        </Box>
      </Paper>
    );
  }

  // Helper function to get display name
  const getDisplayName = () => {
    return organizationData.informationSection?.name || 'Unknown Organization';
  };

  // Helper function to get organization type display
  const getTypeDisplay = () => {
    return organizationData.informationSection?.type || '---';
  };

  // Helper function to get status
  const getStatus = () => {
    return organizationData.informationSection?.status || '---';
  };

  // Helper function to get phone
  const getPhone = () => {
    return organizationData.informationSection?.phone || '---';
  };

  // Helper function to get website
  const getWebsite = () => {
    return organizationData.informationSection?.website || '---';
  };

  // Helper function to get email
  const getEmail = () => {
    return organizationData.informationSection?.email || '---';
  };

  // Helper function to get primary contact info
  const getPrimaryContactInfo = () => {
    const name = organizationData.informationSection?.primaryContactName || '';
    const title = organizationData.informationSection?.primaryContactTitle || '';
    const phone = organizationData.informationSection?.primaryContactPhone || '';
    
    const parts = [name, title, phone].filter(Boolean);
    return parts.length > 0 ? parts.join(' - ') : '---';
  };

  // Helper function to get campus organization status
  const getCampusOrganization = () => {
    return organizationData.informationSection?.campusOrganization || '---';
  };

  // Helper function to get full address
  const getFullAddress = () => {
    const addressArray = organizationData.informationSection?.address;
    if (!addressArray || !Array.isArray(addressArray) || addressArray.length === 0) return '---';
    
    const addressData = addressArray[0];
    if (!addressData) return '---';
    
    const { streetAddress1, streetAddress2, city, state, zipCode } = addressData;
    const parts = [streetAddress1, streetAddress2, city, state, zipCode].filter(Boolean);
    return parts.length > 0 ? parts.join(', ') : '---';
  };

  // Process internal tags for display
  const getTagsForDisplay = (): DisplayTag[] => {
    if (!organizationData.internalTags || organizationData.internalTags.length === 0) {
      return [{ label: "No tags", color: "grey", prominence: false }];
    }

    return organizationData.internalTags.map((tag: InternalTag) => ({
      label: tag.label || tag.value || 'Unknown',
      color: tag.color || 'grey',
      prominence: false,
    }));
  };

  return (
    <Paper
      elevation={0}
      sx={{
        borderRadius: 2,
        border: `1px solid ${colors.grey[200]}`,
        mb: 3,
        overflow: "hidden",
        maxWidth: "100%",
      }}
    >
      <Box sx={{ p: 2, bgcolor: "white" }}>
        {/* wrapper flex row */}
        <Box
          sx={{
            display: "flex",
            flexWrap: "wrap",
            gap: 2,
            width: "100%",
            alignItems: "stretch",
          }}
        >
          {/* Photo / placeholder */}
          <Box
            sx={{
              flexBasis: { xs: "100%", sm: "25%", md: "16.666%" },
              maxWidth: { xs: "100%", sm: "25%", md: "16.666%" },
            }}
          >
            <Box
              sx={{
                width: "100%",
                height: "100%",
                minHeight: 120,
                backgroundColor: colors.grey[100],
                border: `1px solid ${colors.grey[200]}`,
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                borderRadius: 1,
              }}
            >
              <BusinessIcon
                sx={{ color: colors.grey[300], fontSize: 40 }}
              />
            </Box>
          </Box>

          {/* Right‑hand info */}
          <Box
            sx={{
              flexBasis: {
                xs: "100%",
                sm: "calc(75% - 16px)",
                md: "calc(83.333% - 16px)",
              },
              maxWidth: { xs: "100%", sm: "75%", md: "83.333%" },
              display: "flex",
              flexDirection: "column",
              gap: 2,
            }}
          >
            {/* Risk labels */}
            <Box sx={{ display: "flex", gap: 1, flexWrap: "wrap" }}>
              {getTagsForDisplay().map((tag: DisplayTag, i: number) => (
                <Label
                  key={i}
                  label={tag.label}
                  color={tag.color as any}
                  prominence={tag.prominence}
                  size="small"
                />
              ))}
            </Box>

            <Divider />

            <Box
              sx={{
                display: "grid",
                gap: 1,
                gridTemplateColumns: {
                  xs: "repeat(auto-fill, minmax(160px, 1fr))",
                  sm: "repeat(auto-fill, minmax(200px, 1fr))",
                  md: "repeat(auto-fill, minmax(220px, 1fr))",
                },
                alignItems: "start",
              }}
            >
              {[
                {
                  icon: <CategoryIcon sx={{ color: colors.grey[500] }} />,
                  text: getTypeDisplay(),
                },
                {
                  icon: <AlternateEmailIcon sx={{ color: colors.grey[500] }} />,
                  text: getStatus(),
                },
                {
                  icon: <WorkIcon sx={{ color: colors.grey[500] }} />,
                  text: getPhone(),
                },
                {
                  icon: <LocationOnIcon sx={{ color: colors.grey[500] }} />,
                  text: getFullAddress(),
                  wide: true,
                },
                {
                  icon: <DescriptionIcon sx={{ color: colors.grey[500] }} />,
                  text: getPrimaryContactInfo(),
                  wide: true,
                },
              ].map(({ icon, text, wide }, i) => (
                <Box
                  key={i}
                  sx={{
                    display: "flex",
                    alignItems: "flex-start",
                    gap: 1,
                    gridColumn: wide ? { md: "span 2" } : undefined,
                  }}
                >
                  {icon}
                  <Typography
                    variant="body2"
                    sx={{
                      ...twoLineClamp,
                      minWidth: 0,
                      flex: 1,
                    }}
                  >
                    {text}
                  </Typography>
                </Box>
              ))}
            </Box>
          </Box>
        </Box>
      </Box>
    </Paper>
  );
}