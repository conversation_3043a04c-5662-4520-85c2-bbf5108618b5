"use client";

import { useUpdateEntity } from "@/app/apis/services/workflow/entity/hooks";
import { useBreadcrumbHeader } from "@/app/hooks/useBreadcrumbHeader";
import { useRecentlyViewedTracker } from "@/app/hooks/useRecentlyViewedTracker";
import { isArchivedStatus } from "@/app/utils/entityUtils";
import { Header } from "@/design-system/components/Header";
import ArchiveIcon from "@mui/icons-material/Archive";
import UnarchiveIcon from "@mui/icons-material/Unarchive";
import { useQueryClient } from "@tanstack/react-query";
import { Entity, RecordStatus, UpdateEntityRequest } from "proto/hero/entity/v1/entity_pb";
import { useState } from "react";
import { OrganizationData } from "../utils/organizationTypes";
import DeleteConfirmationDialog from "./DeleteConfirmationDialog";

interface OrganizationHeaderProps {
  organization: Entity | undefined;
  onClose: () => void;
}

export default function OrganizationHeader({ organization, onClose }: OrganizationHeaderProps) {
  // Extract organization data
  const extractOrganizationData = (): OrganizationData | null => {
    if (!organization?.data) return null;

    try {
      const data = typeof organization.data === 'string' ? JSON.parse(organization.data) : organization.data;
      return {
        informationSection: data?.informationSection || {},
        createTime: data?.createTime || "",
        updateTime: data?.updateTime || "",
      };
    } catch (error) {
      console.warn("Failed to parse organization entity data:", error);
      return null;
    }
  };

  const organizationData = extractOrganizationData();
  const organizationName = organizationData?.informationSection?.name || "Unknown Organization";

  // Get organization ID in display format
  const organizationId = organization?.id?.slice(0, 7) || "Unknown ID";

  const { breadcrumbs } = useBreadcrumbHeader({
    id: `entity-${organization?.id}`,
    label: organizationName,
    path: `/entity?entityId=${organization?.id}`,
  });

  useRecentlyViewedTracker({
    id: `entity-${organization?.id}`,
    title: organizationName,
    subtitle: "Organization Record",
    path: `/entity?entityId=${organization?.id}`,
  });

  // Format date from timestamp or ISO string
  const formatDate = (timestamp: string): string => {
    if (!timestamp) return "N/A";
    try {
      const date = new Date(timestamp);

      return date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "2-digit",
      });
    } catch (e) {
      return "N/A";
    }
  };

  const queryClient = useQueryClient();
  const isArchived = organization && isArchivedStatus(organization.status);
  const [showArchiveDialog, setShowArchiveDialog] = useState(false);

  // Use the updateEntity hook
  const updateEntityMutation = useUpdateEntity({
    onSuccess: (data) => {
      console.log("Organization status updated successfully:", data);
      // Force a refetch of the entity data to update the UI
      queryClient.invalidateQueries({ queryKey: ["entity", data.id] });
      queryClient.refetchQueries({ queryKey: ["entity", data.id] });
    },
    onError: (error) => {
      console.error("Error updating organization status:", error);
      alert(`Failed to update organization status: ${error.message}`);
    },
  });

  const handleArchive = () => {
    setShowArchiveDialog(true);
  };

  const handleArchiveConfirm = () => {
    if (!organization?.id) {
      console.error("Organization ID is missing");
      return;
    }

    const archiveRequest = {
      entity: {
        id: organization.id,
        status: RecordStatus.ARCHIVED,
      },
    } as UpdateEntityRequest;

    updateEntityMutation.mutate(archiveRequest);
    setShowArchiveDialog(false);
  };

  const handleUnarchive = () => {
    if (!organization?.id) {
      console.error("Organization ID is missing");
      return;
    }

    const unarchiveRequest = {
      entity: {
        id: organization.id,
        status: RecordStatus.ACTIVE,
      },
    } as UpdateEntityRequest;

    updateEntityMutation.mutate(unarchiveRequest);
  };

  return (
    <>
      <Header
        breadcrumbs={breadcrumbs}
        title={organizationName}
        metadata={[
          {
            label: "Date Created",
            value: formatDate(organization?.createTime || "")
          },
          {
            label: "Last Updated",
            value: formatDate(organization?.updateTime || "")
          },
          {
            label: "ID",
            value: organizationId
          }
        ]}
        actions={[
          {
            label: isArchived ? "Unarchive" : "Archive",
            leftIcon: isArchived ? <UnarchiveIcon /> : <ArchiveIcon />,
            size: "medium",
            color: "grey",
            prominence: false,
            onClick: isArchived ? handleUnarchive : handleArchive,
            disabled: updateEntityMutation.isPending
          }
        ]}
      />
      
      <DeleteConfirmationDialog
        open={showArchiveDialog}
        onClose={() => setShowArchiveDialog(false)}
        onConfirm={handleArchiveConfirm}
        entityName={organizationName}
        entityType="organization"
        isLoading={updateEntityMutation.isPending}
      />
  </>
  );
}