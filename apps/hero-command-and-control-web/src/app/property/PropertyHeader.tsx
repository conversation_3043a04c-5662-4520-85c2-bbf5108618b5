"use client";

import { useBreadcrumbs } from "@/app/contexts/Breadcrumb/BreadcrumbContext";
import { useRecentlyViewedTracker } from "@/app/hooks/useRecentlyViewedTracker";
import { Header } from "@/design-system/components/Header";
import { <PERSON><PERSON>, Snackbar } from "@mui/material";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

import { useAddCustodyEvent, useUpdateProperty } from "../apis/services/workflow/property/hooks";
import { propertyTypeToDisplayString } from "../apis/services/workflow/property/propertyTypeManager";
import { CustodyActionType, CustodyEvent, Property } from "../apis/services/workflow/property/types";
import CustodyUpdateModal from "./components/CustodyUpdateModal";
import UpdateButton from "./components/UpdateButton";

interface PropertyHeaderProps {
    propertyData: Property | undefined;
    propertyId: string;
    onClose: () => void;
}

export default function PropertyHeader({ propertyData, propertyId, _onClose }: PropertyHeaderProps) {
    const router = useRouter();

    // Get property name from workflow property data
    const getPropertyName = () => {
        if (!propertyData) return "Unknown Property";

        // Try to get from property schema first - prioritize description or make/model
        if (propertyData.propertySchema?.description) {
            return propertyData.propertySchema.description;
        }

        if (propertyData.propertySchema?.identifiers) {
            return propertyData.propertySchema.identifiers;
        }

        // Fallback to property number if available
        if (propertyData.propertyNumber) {
            return `Property ${propertyData.propertyNumber}`;
        }

        // Use property type as a last resort, but make it more readable
        // Check top-level nibrsPropertyType field first
        if (propertyData.nibrsPropertyType) {
            const displayString = propertyTypeToDisplayString(propertyData.nibrsPropertyType);
            return `${displayString} Property`;
        }

        // Fallback to legacy propertySchema propertyType field
        if (propertyData.propertySchema?.propertyType) {
            const displayString = propertyTypeToDisplayString(propertyData.propertySchema.propertyType);
            return `${displayString} Property`;
        }

        return "Property";
    };

    const propertyName = getPropertyName();

    // Get property ID in display format
    const displayPropertyId = propertyId?.slice(0, 7) || "Unknown ID";

    const { breadcrumbs, addBreadcrumb, clearBreadcrumbs } = useBreadcrumbs();

    // State for custody update modal
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [selectedActionType, setSelectedActionType] = useState<CustodyActionType | null>(null);
    const [showSuccessMessage, setShowSuccessMessage] = useState(false);
    const [showErrorMessage, setShowErrorMessage] = useState(false);
    const [errorMessage, setErrorMessage] = useState("");

    // Hooks for custody events and property updates
    const addCustodyEventMutation = useAddCustodyEvent();
    const _updatePropertyMutation = useUpdateProperty();

    // Set custom breadcrumbs for property page
    useEffect(() => {
        clearBreadcrumbs();
        addBreadcrumb({
            id: "search",
            label: "Back to Search",
            path: "/search"
        });
        addBreadcrumb({
            id: `property-${propertyId}`,
            label: propertyName,
            path: `/property?propertyId=${propertyId}`
        });
    }, [propertyId, propertyName, addBreadcrumb, clearBreadcrumbs]);

    useRecentlyViewedTracker({
        id: `property-${propertyId}`,
        title: propertyName,
        subtitle: "Property Record",
        path: `/property?propertyId=${propertyId}`,
    });

    // Format date from timestamp or ISO string
    const formatDate = (timestamp: string): string => {
        if (!timestamp) return "N/A";
        try {
            const date = new Date(timestamp);

            return date.toLocaleDateString("en-US", {
                year: "numeric",
                month: "short",
                day: "2-digit",
            });
        } catch (_e) {
            return "N/A";
        }
    };


    const handleUpdateCustody = (actionType: CustodyActionType, _propertyId: string) => {
        setSelectedActionType(actionType);
        setIsModalOpen(true);
    };

    const handleCustodyEventSubmit = async (custodyEvent: CustodyEvent, disposalType?: string, propertyNumber?: string) => {
        try {
            // Add the custody event - backend automatically updates property status
            await addCustodyEventMutation.mutateAsync({
                propertyId,
                custodyEvent,
            });

            // Property number update is now handled automatically by the backend AddCustodyEvent

            setShowSuccessMessage(true);
        } catch (error) {
            setShowErrorMessage(true);
            setErrorMessage(`Failed to update custody: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    };

    // Convert breadcrumbs to header format
    const headerBreadcrumbs = breadcrumbs.map((breadcrumb, index, array) => ({
        label: breadcrumb.label,
        path: breadcrumb.path,
        active: index === array.length - 1,
        onClick: index === array.length - 1 ? undefined : () => {
            // Navigate to the breadcrumb path
            router.push(breadcrumb.path);
        }
    }));

    return (
        <>
            <Header
                breadcrumbs={headerBreadcrumbs}
                title={propertyName}
                metadata={[
                    {
                        label: "Date Created",
                        value: formatDate(propertyData?.createTime || "")
                    },
                    {
                        label: "Last Updated",
                        value: formatDate(propertyData?.updateTime || "")
                    },
                    {
                        label: "ID",
                        value: displayPropertyId
                    }
                ]}
                actions={[]}
                statusIndicator={
                    <UpdateButton propertyId={propertyId} onUpdateCustody={handleUpdateCustody} />
                }
            />

            {/* Custody Update Modal */}
            {selectedActionType && (
                <CustodyUpdateModal
                    open={isModalOpen}
                    onClose={() => {
                        setIsModalOpen(false);
                        setSelectedActionType(null);
                    }}
                    onSubmit={handleCustodyEventSubmit}
                    actionType={selectedActionType}
                    propertyId={propertyId}
                />
            )}

            {/* Success Message */}
            <Snackbar
                open={showSuccessMessage}
                autoHideDuration={6000}
                onClose={() => setShowSuccessMessage(false)}
            >
                <Alert onClose={() => setShowSuccessMessage(false)} severity="success">
                    Custody event added successfully!
                </Alert>
            </Snackbar>

            {/* Error Message */}
            <Snackbar
                open={showErrorMessage}
                autoHideDuration={6000}
                onClose={() => setShowErrorMessage(false)}
            >
                <Alert onClose={() => setShowErrorMessage(false)} severity="error">
                    {errorMessage}
                </Alert>
            </Snackbar>
        </>
    );
} 