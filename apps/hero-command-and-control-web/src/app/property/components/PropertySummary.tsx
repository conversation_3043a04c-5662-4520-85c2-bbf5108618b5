"use client";

import { Typography } from "@/design-system/components/Typography";
import { colors } from "@/design-system/tokens";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import LocationOnIcon from "@mui/icons-material/LocationOn";
import PersonIcon from "@mui/icons-material/Person";
import ScheduleIcon from "@mui/icons-material/Schedule";
import { Box, Collapse, Paper } from "@mui/material";
import { useState } from "react";
import { Property } from "../../apis/services/workflow/property/types";
import { getPropertyStatusDisplay } from "../../utils/propertyHelpers";

// Format date helper function
function formatDate(date: Date): string {
    try {
        const datePart = date.toLocaleDateString("en-US", {
            month: "short",
            day: "numeric",
            year: "numeric",
        }).replace(/,/g, "");

        const timePart = date.toLocaleTimeString("en-US", {
            hour: "numeric",
            minute: "2-digit",
            hour12: true,
        });

        return `${datePart} • ${timePart}`;
    } catch (e) {
        console.error("Error formatting date:", e);
        return "Unknown date";
    }
}

interface SectionHeaderProps {
    title: string;
    isExpanded: boolean;
    onToggle: () => void;
    count?: number;
}

const SectionHeader = ({
    title,
    isExpanded,
    onToggle,
    count = 0,
}: SectionHeaderProps) => (
    <Box
        sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            py: 2,
            color: colors.grey[600],
            cursor: "pointer",
        }}
        onClick={onToggle}
    >
        <Box sx={{ display: "flex", alignItems: "center" }}>
            <Box sx={{ ml: 1, display: 'flex', alignItems: 'center' }}>
                <Typography style="caps3" color={colors.grey[600]}>
                    {title}
                </Typography>
                {count > 0 && (
                    <Box
                        sx={{
                            backgroundColor: colors.blue[100],
                            color: colors.blue[600],
                            borderRadius: '20px',
                            width: '20px',
                            height: '20px',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            fontSize: '10px',
                            fontWeight: 500,
                            ml: 1.5,
                        }}
                    >
                        {count}
                    </Box>
                )}
            </Box>
        </Box>
        {isExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
    </Box>
);

interface PropertySummaryProps {
    property?: Property;
    isLoading?: boolean;
}

export default function PropertySummary({
    property,
    isLoading = false
}: PropertySummaryProps) {
    const [statusExpanded, setStatusExpanded] = useState(true);
    const [custodyExpanded, setCustodyExpanded] = useState(false);

    // Format property status for display using shared function
    const getStatusDisplay = () => {
        if (!property?.propertyStatus) return "Unknown";
        return getPropertyStatusDisplay(property.propertyStatus);
    };

    // Get recent custody events
    const recentCustodyEvents = property?.custodyChain?.slice(-3) || [];

    return (
        <Paper
            elevation={3}
            sx={{
                borderRadius: 2,
                bgcolor: "white",
                overflow: "hidden",
                overflowY: "auto",
                height: "100%",
                display: "flex",
                flexDirection: "column",
                boxShadow: "none",
                border: `1px solid ${colors.grey[200]}`,
            }}
        >
            <Box sx={{ px: 2 }}>
                {/* Property Status Section */}
                <Box>
                    <SectionHeader
                        title="PROPERTY STATUS"
                        isExpanded={statusExpanded}
                        onToggle={() => setStatusExpanded(!statusExpanded)}
                    />
                    <Collapse in={statusExpanded}>
                        <Box sx={{ mb: 2 }}>
                            <Box sx={{ display: "flex", alignItems: "center", gap: 1, mb: 1 }}>
                                <Typography style="body3" color={colors.grey[800]} fontWeight={500}>
                                    Status:
                                </Typography>
                                <Typography style="body3" color={colors.grey[600]}>
                                    {getStatusDisplay()}
                                </Typography>
                            </Box>

                            {property?.currentLocation && (
                                <Box sx={{ display: "flex", alignItems: "center", gap: 1, mb: 1 }}>
                                    <LocationOnIcon sx={{ color: colors.grey[700], fontSize: 14 }} />
                                    <Typography style="body3" color={colors.grey[600]}>
                                        {property.currentLocation}
                                    </Typography>
                                </Box>
                            )}

                            {property?.currentCustodian && (
                                <Box sx={{ display: "flex", alignItems: "center", gap: 1, mb: 1 }}>
                                    <PersonIcon sx={{ color: colors.grey[700], fontSize: 14 }} />
                                    <Typography style="body3" color={colors.grey[600]}>
                                        {property.currentCustodian}
                                    </Typography>
                                </Box>
                            )}

                            {property?.propertyNumber && (
                                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                                    <Typography style="body3" color={colors.grey[800]} fontWeight={500}>
                                        Property #:
                                    </Typography>
                                    <Typography style="body3" color={colors.grey[600]}>
                                        {property.propertyNumber}
                                    </Typography>
                                </Box>
                            )}
                        </Box>
                    </Collapse>
                </Box>

                {/* Recent Custody Events Section */}
                <Box>
                    <SectionHeader
                        title="RECENT CUSTODY EVENTS"
                        isExpanded={custodyExpanded}
                        onToggle={() => setCustodyExpanded(!custodyExpanded)}
                        count={recentCustodyEvents.length}
                    />
                    <Collapse in={custodyExpanded}>
                        {recentCustodyEvents.length === 0 ? (
                            <Box sx={{ mb: 2 }}>
                                <Typography style="body3" color={colors.grey[500]}>
                                    No custody events
                                </Typography>
                            </Box>
                        ) : (
                            recentCustodyEvents.map((event, index) => (
                                <Box
                                    key={index}
                                    sx={{
                                        px: 1,
                                        py: 1,
                                        borderBottom: index < recentCustodyEvents.length - 1 ? `1px solid ${colors.grey[100]}` : 'none'
                                    }}
                                >
                                    <Box sx={{ mb: 1 }}>
                                        <Typography style="body3" color={colors.grey[800]}>
                                            {event.actionType || 'Custody Change'}
                                        </Typography>
                                    </Box>

                                    {event.notes && (
                                        <Box sx={{ mb: 1 }}>
                                            <Typography style="tag2" color={colors.grey[600]}>
                                                {event.notes}
                                            </Typography>
                                        </Box>
                                    )}

                                    {event.timestamp && (
                                        <Box
                                            sx={{
                                                display: "flex",
                                                alignItems: "center",
                                                gap: 1,
                                            }}
                                        >
                                            <ScheduleIcon sx={{ color: colors.grey[700], fontSize: 14 }} />
                                            <Typography style="tag2" color={colors.grey[700]}>
                                                {formatDate(new Date(event.timestamp))}
                                            </Typography>
                                        </Box>
                                    )}
                                </Box>
                            ))
                        )}
                    </Collapse>
                </Box>

                <Box sx={{ borderTop: 1, borderColor: "divider" }} />
            </Box>
        </Paper>
    );
}
