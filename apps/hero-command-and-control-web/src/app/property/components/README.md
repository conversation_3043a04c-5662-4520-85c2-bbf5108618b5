# Property Chain of Custody Update Components

This directory contains components for updating the chain of custody for property items.

## Components

### UpdateButton

A button component that displays a dropdown menu with chain of custody action options.

**Features:**
- Blue "Update" button with refresh icon
- Dropdown menu with custody action options
- Triggers modal for collecting custody event details

**Props:**
- `propertyId: string` - The property ID
- `onUpdateCustody: (actionType: CustodyActionType) => void` - Callback when an action is selected

**Available Actions:**
- Collected
- Check In
- Check Out
- Transfer
- Release
- Dispose

### CustodyUpdateModal

A modal dialog for collecting details about a custody event.

**Features:**
- Form for entering custody event details
- Required location field
- Optional notes, case number, evidence number
- Conditional receiving officer field for transfers
- Validation before submission

**Props:**
- `open: boolean` - Whether the modal is open
- `onClose: () => void` - Callback when modal is closed
- `onSubmit: (custodyEvent: CustodyEvent) => void` - Callback when form is submitted
- `actionType: CustodyActionType` - The type of custody action
- `propertyId: string` - The property ID

**Form Fields:**
- Location (required)
- Notes (multiline)
- Receiving Officer (only for transfers)
- Case Number
- Evidence Number

## Usage

The components are integrated into the `PropertyHeader` component and automatically appear on property detail pages. The Update button is positioned next to the entity actions menu in the header.

### Example Integration

```tsx
// In PropertyHeader.tsx
<UpdateButton 
    propertyId={propertyId} 
    onUpdateCustody={handleUpdateCustody} 
/>

<CustodyUpdateModal
    open={isModalOpen}
    onClose={() => setIsModalOpen(false)}
    onSubmit={handleCustodyEventSubmit}
    actionType={selectedActionType}
    propertyId={propertyId}
/>
```

## Data Flow

1. User clicks "Update" button
2. Dropdown menu appears with custody action options
3. User selects an action
4. Modal opens with form for that action type
5. User fills out form and submits
6. Custody event is sent to backend via `useAddCustodyEvent` hook
7. Success message is displayed
8. Chain of custody is automatically refreshed

## Styling

Components follow the existing design system:
- Use Material-UI components for consistency
- Follow the color scheme defined in `@/design-system/tokens`
- Maintain consistent spacing and typography
- Use the same button styling as other components

## Error Handling

- Form validation prevents submission without required fields
- API errors are logged to console
- Success messages are displayed via Snackbar
- Modal can be cancelled at any time

## Backend Integration

The components integrate with the property service API:
- Uses `useAddCustodyEvent` hook for API calls
- Automatically invalidates relevant queries after updates
- Follows the `CustodyEvent` interface structure
- Supports all defined `CustodyActionType` values 