import { Typography } from "@/design-system/components/Typography";
import { colors } from "@/design-system/tokens";
import SchemaIcon from "@mui/icons-material/Schema";
import { Box, Card, CardContent, Chip, Stack } from "@mui/material";
import { Property } from "../../apis/services/workflow/property/types";

interface SchemaMetadataCardProps {
    propertyData: Property;
}

export default function SchemaMetadataCard({ propertyData }: SchemaMetadataCardProps) {
    if (!propertyData.schemaId) {
        return null; // Don't render if no schema info
    }

    return (
        <Card sx={{ bgcolor: 'white', border: `1px solid ${colors.grey[200]}` }}>
            <CardContent>
                <Stack direction="row" spacing={2} alignItems="center" sx={{ mb: 2 }}>
                    <SchemaIcon sx={{ color: colors.blue[600] }} />
                    <Typography style="h6" color={colors.grey[900]}>
                        Schema Information
                    </Typography>
                    <Chip 
                        label="Schema" 
                        size="small" 
                        sx={{ 
                            bgcolor: colors.blue[100], 
                            color: colors.blue[800],
                            fontWeight: 600
                        }} 
                    />
                </Stack>
                
                <Stack spacing={2}>
                    <Box>
                        <Typography style="body2" color={colors.grey[600]} sx={{ mb: 0.5 }}>
                            Schema ID
                        </Typography>
                        <Typography style="body1" color={colors.grey[900]} sx={{ fontFamily: 'monospace' }}>
                            {propertyData.schemaId}
                        </Typography>
                    </Box>
                    
                    <Box>
                        <Typography style="body2" color={colors.grey[600]} sx={{ mb: 0.5 }}>
                            Schema Version
                        </Typography>
                        <Typography style="body1" color={colors.grey[900]}>
                            {propertyData.schemaVersion}
                        </Typography>
                    </Box>
                    
                    <Box>
                        <Typography style="body2" color={colors.grey[600]} sx={{ mb: 0.5 }}>
                            Form Data Fields
                        </Typography>
                        <Typography style="body2" color={colors.grey[700]}>
                            {propertyData.details ? Object.keys(propertyData.details).length : 0} dynamic fields stored
                        </Typography>
                    </Box>
                </Stack>
            </CardContent>
        </Card>
    );
}