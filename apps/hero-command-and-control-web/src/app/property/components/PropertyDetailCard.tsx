import { Label } from "@/design-system/components/Label";
import { Typography } from "@/design-system/components/Typography";
import { colors } from "@/design-system/tokens";
import { ColorToken } from "@/design-system/tokens/colors";
import AttachMoneyIcon from "@mui/icons-material/AttachMoney";
import BadgeIcon from "@mui/icons-material/Badge";
import CategoryIcon from "@mui/icons-material/Category";
import DescriptionIcon from "@mui/icons-material/Description";
import HandymanIcon from "@mui/icons-material/Handyman";
import InventoryIcon from "@mui/icons-material/Inventory";
import NoPhotographyOutlinedIcon from "@mui/icons-material/NoPhotographyOutlined";
import PersonIcon from "@mui/icons-material/Person";
import {
    Box,
    Divider,
    Typography as MuiTypography,
    Paper,
} from "@mui/material";
import React from "react";
import { Property } from "../../apis/services/workflow/property/types";
import { getPropertyStatusDisplay, getReadablePropertyCategory } from "../../utils/propertyHelpers";

interface PropertyDetailCardProps {
    propertyData: Property;
    isLoading?: boolean;
    isError?: boolean;
}

interface DisplayTag {
    label: string;
    color: ColorToken;
    prominence: boolean;
}

interface PropertyInfoItem {
    icon: React.ReactNode;
    text: string;
    wide?: boolean;
}

export default function PropertyDetailCard({
    propertyData,
    isLoading = false,
    isError = false
}: PropertyDetailCardProps) {




    // Using shared getPropertyStatusDisplay function from propertyHelpers

    // Process internal tags for display (now from details field)
    const getTagsForDisplay = (): DisplayTag[] => {
        // Only show the property status tag
        if (propertyData.propertyStatus) {
            return [{
                label: getPropertyStatusDisplay(propertyData.propertyStatus),
                color: 'grey',
                prominence: false
            }];
        }

        return [{ label: "No status", color: "grey", prominence: false }];
    };

    if (isLoading) {
        return (
            <Paper
                elevation={0}
                sx={{
                    borderRadius: 2,
                    border: `1px solid ${colors.grey[200]}`,
                    mb: 3,
                    overflow: "hidden",
                    maxWidth: "100%",
                }}
            >
                <Box sx={{ p: 3, bgcolor: "white" }}>
                    <Typography style="body2" color={colors.grey[500]}>
                        Loading property information...
                    </Typography>
                </Box>
            </Paper>
        );
    }

    if (isError || !propertyData) {
        return (
            <Paper
                elevation={0}
                sx={{
                    borderRadius: 2,
                    border: `1px solid ${colors.grey[200]}`,
                    mb: 3,
                    overflow: "hidden",
                    maxWidth: "100%",
                }}
            >
                <Box sx={{ p: 3, bgcolor: "white" }}>
                    <Typography style="body2" color={colors.grey[500]}>
                        No property data available
                    </Typography>
                </Box>
            </Paper>
        );
    }

    // Get property details from the dynamic schema (details field)
    const detailsMap = propertyData.details;

    // Create an array of property info items from details field
    const propertyInfoItems: PropertyInfoItem[] = [
        detailsMap?.category && {
            icon: <CategoryIcon sx={{ color: colors.grey[500], flexShrink: 0 }} />,
            text: getReadablePropertyCategory(detailsMap.category),
        },
        detailsMap?.serialNumber && {
            icon: <BadgeIcon sx={{ color: colors.grey[500], flexShrink: 0 }} />,
            text: `S/N: ${detailsMap.serialNumber}`,
        },
        detailsMap?.identifiers && {
            icon: <InventoryIcon sx={{ color: colors.grey[500], flexShrink: 0 }} />,
            text: detailsMap.identifiers,
        },
        detailsMap?.owner && {
            icon: <PersonIcon sx={{ color: colors.grey[500], flexShrink: 0 }} />,
            text: detailsMap.owner,
        },
        detailsMap?.condition && {
            icon: <HandymanIcon sx={{ color: colors.grey[500], flexShrink: 0 }} />,
            text: detailsMap.condition,
        },
        detailsMap?.value && {
            icon: <AttachMoneyIcon sx={{ color: colors.grey[500], flexShrink: 0 }} />,
            text: detailsMap.value.toString(),
        },
        detailsMap?.description && {
            icon: <DescriptionIcon sx={{ color: colors.grey[500], flexShrink: 0 }} />,
            text: detailsMap.description,
            wide: true,
        },
    ].filter(Boolean) as PropertyInfoItem[];

    return (
        <Paper
            elevation={0}
            sx={{
                borderRadius: 2,
                border: `1px solid ${colors.grey[200]}`,
                mb: 3,
                overflow: "hidden",
                maxWidth: "100%",
            }}
        >
            <Box sx={{ p: 3, bgcolor: "white" }}>
                {/* wrapper flex row */}
                <Box
                    sx={{
                        display: "flex",
                        flexWrap: "wrap",
                        gap: 3,
                        width: "100%",
                        alignItems: "stretch",
                    }}
                >
                    {/* Photo / placeholder */}
                    <Box
                        sx={{
                            flexBasis: { xs: "100%", sm: "25%", md: "20%" },
                            maxWidth: { xs: "100%", sm: "25%", md: "20%" },
                            minWidth: { xs: "100%", sm: "200px", md: "200px" },
                        }}
                    >
                        <Box
                            sx={{
                                width: "100%",
                                height: "100%",
                                minHeight: 120,
                                backgroundColor: colors.grey[100],
                                border: `1px solid ${colors.grey[200]}`,
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "center",
                                borderRadius: 1,
                            }}
                        >
                            <NoPhotographyOutlinedIcon
                                sx={{ color: colors.grey[300], fontSize: 40 }}
                            />
                        </Box>
                    </Box>

                    {/* Right‑hand info */}
                    <Box
                        sx={{
                            flex: 1,
                            minWidth: 0, // Allow flex item to shrink below content size
                            display: "flex",
                            flexDirection: "column",
                            gap: 3,
                        }}
                    >
                        {/* Tags section */}
                        <Box sx={{ display: "flex", gap: 1, flexWrap: "wrap" }}>
                            {getTagsForDisplay().map((tag: DisplayTag, i: number) => (
                                <Label
                                    key={i}
                                    label={tag.label}
                                    color={tag.color}
                                    prominence={tag.prominence}
                                    size="small"
                                />
                            ))}
                        </Box>

                        <Divider />

                        <Box
                            sx={{
                                display: "grid",
                                gap: 2,
                                gridTemplateColumns: {
                                    xs: "1fr",
                                    sm: "repeat(auto-fit, minmax(280px, 1fr))",
                                    md: "repeat(auto-fit, minmax(320px, 1fr))",
                                },
                                alignItems: "start",
                            }}
                        >
                            {propertyInfoItems.map(({ icon, text, wide }, i) => (
                                <Box
                                    key={i}
                                    sx={{
                                        display: "flex",
                                        alignItems: "flex-start",
                                        gap: 1.5,
                                        gridColumn: wide ? { sm: "span 2" } : undefined,
                                        minWidth: 0, // Allow grid item to shrink
                                    }}
                                >
                                    {icon}
                                    <MuiTypography
                                        variant="body2"
                                        sx={{
                                            minWidth: 0,
                                            flex: 1,
                                            overflow: "hidden",
                                            textOverflow: "ellipsis",
                                            whiteSpace: "nowrap",
                                            lineHeight: 1.4,
                                        }}
                                        title={text} // Show full text on hover
                                    >
                                        {text}
                                    </MuiTypography>
                                </Box>
                            ))}
                        </Box>
                    </Box>
                </Box>
            </Box>
        </Paper>
    );
}