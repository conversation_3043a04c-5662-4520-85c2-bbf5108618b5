"use client";

import { Button } from "@/design-system/components/Button";
import { colors } from "@/design-system/tokens";
import RefreshIcon from "@mui/icons-material/Refresh";
import { Box, Popover, Typography } from "@mui/material";
import React, { useState } from "react";
import { stringToPropertyStatus } from "../../apis/services/workflow/property/enumConverters";
import { useProperty } from "../../apis/services/workflow/property/hooks";
import { CustodyActionType, PropertyStatus } from "../../apis/services/workflow/property/types";

interface UpdateButtonProps {
    propertyId: string;
    onUpdateCustody: (actionType: CustodyActionType, propertyId: string) => void;
}

// Simple mapping: PropertyStatus -> allowed CustodyActionTypes
const getAllowedActionsForPropertyStatus = (status?: PropertyStatus | string): CustodyActionType[] => {
    if (!status) return [];

    // Normalize the status to ensure we're working with the numeric enum value
    let propertyStatus: PropertyStatus;
    if (typeof status === 'string') {
        propertyStatus = stringToPropertyStatus(status);
    } else {
        propertyStatus = status;
    }

    switch (propertyStatus) {
        case PropertyStatus.INTAKE_PENDING:
            // Logged items can only be checked in (the only allowable action from Logged)
            return [
                CustodyActionType.CHECKED_IN,
            ];
        case PropertyStatus.COLLECTED:
            return [
                CustodyActionType.TRANSFERRED,
                CustodyActionType.CHECKED_IN,
                CustodyActionType.RELEASED,
            ];

        case PropertyStatus.CHECKED_IN:
        case PropertyStatus.SAFEKEEPING:
        case PropertyStatus.AWAITING_DISPOSITION:
            return [
                CustodyActionType.MOVED,
                CustodyActionType.CHECKED_OUT,
                CustodyActionType.TRANSFERRED,
                CustodyActionType.DISPOSED,
                CustodyActionType.RELEASED,
            ];

        case PropertyStatus.CHECKED_OUT:
            return [
                CustodyActionType.CHECKED_IN,  // Key fix: Allow check back in!
                CustodyActionType.CHECKED_OUT, // Transfer to different person
                CustodyActionType.TRANSFERRED,
                CustodyActionType.DISPOSED,
                CustodyActionType.RELEASED,
            ];



        // Terminal/informational states - limited actions allowed
        case PropertyStatus.DISPOSED:
            return []; // Truly terminal

        case PropertyStatus.MISSING:
        case PropertyStatus.STOLEN:
            return [
                CustodyActionType.CHECKED_IN, // Bring item into custody
                CustodyActionType.LOGGED,    // Update status/info
            ];

        case PropertyStatus.FOUND:
        case PropertyStatus.RECOVERED:
            return [
                CustodyActionType.COLLECTED, // Bring into custody
                CustodyActionType.CHECKED_IN, // Direct check-in
                CustodyActionType.RELEASED,   // Return to owner
            ];

        default:
            return [
                CustodyActionType.TRANSFERRED,
                CustodyActionType.CHECKED_IN,
                CustodyActionType.RELEASED,
            ];
    }
};

// Define base labels for custody actions
const baseCustodyActions = [
    { value: CustodyActionType.COLLECTED, label: "Collected" },
    { value: CustodyActionType.CHECKED_IN, label: "Check In" },
    { value: CustodyActionType.CHECKED_OUT, label: "Check Out" },
    { value: CustodyActionType.TRANSFERRED, label: "Transfer" },
    { value: CustodyActionType.RELEASED, label: "Release" },
    { value: CustodyActionType.DISPOSED, label: "Dispose" },
    { value: CustodyActionType.MOVED, label: "Move" },
];

export default function UpdateButton({ propertyId, onUpdateCustody }: UpdateButtonProps) {
    const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);

    // Get property data to check status and last custody action
    const { data: propertyData } = useProperty(propertyId);


    const handleClick = (event: React.MouseEvent<HTMLElement>) => {
        setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
        setAnchorEl(null);
    };

    const handleActionSelect = (actionType: CustodyActionType) => {
        onUpdateCustody(actionType, propertyId);
        handleClose();
    };

    const open = Boolean(anchorEl);

    // Determine allowed actions based on property status (single source of truth)
    const propertyStatus = propertyData?.propertyStatus;
    const allowedFromState = new Set(getAllowedActionsForPropertyStatus(propertyStatus));


    // Get context-sensitive labels based on current state
    const getContextualLabel = (actionType: CustodyActionType): string => {
        const baseAction = baseCustodyActions.find(action => action.value === actionType);
        const baseLabel = baseAction?.label || "Unknown";

        // Provide context-sensitive labels for move action
        if (actionType === CustodyActionType.MOVED) {
            return "Update Location";
        }

        return baseLabel;
    };

    const availableActions = baseCustodyActions.filter(action => {
        return allowedFromState.has(action.value);
    }).map(action => ({
        ...action,
        label: getContextualLabel(action.value)
    }));

    return (
        <>
            <Button
                label="Update"
                leftIcon={<RefreshIcon />}
                color="blue"
                prominence={true}
                onClick={handleClick}
                disabled={!propertyId}
            />
            <Popover
                open={open}
                anchorEl={anchorEl}
                onClose={handleClose}
                anchorOrigin={{
                    vertical: "bottom",
                    horizontal: "left",
                }}
                transformOrigin={{
                    vertical: "top",
                    horizontal: "left",
                }}
                PaperProps={{
                    sx: {
                        mt: 1,
                        borderRadius: "8px",
                        boxShadow: "0px 4px 8px rgba(0, 0, 0, 0.1)",
                        border: `1px solid ${colors.grey[200]}`,
                        minWidth: 200,
                    },
                }}
            >
                <Box>
                    {availableActions.map((action) => (
                        <Box
                            key={action.value}
                            onClick={() => handleActionSelect(action.value)}
                            sx={{
                                px: 2,
                                py: 1.5,
                                cursor: "pointer",
                                "&:hover": {
                                    backgroundColor: colors.grey[100],
                                },
                                borderBottom: `1px solid ${colors.grey[100]}`,
                                "&:last-child": {
                                    borderBottom: "none",
                                },
                            }}
                        >
                            <Typography variant="body2" color={colors.grey[900]}>
                                {action.label}
                            </Typography>
                        </Box>
                    ))}
                </Box>
            </Popover>
        </>
    );
} 