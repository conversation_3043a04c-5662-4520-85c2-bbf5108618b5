"use client";

import { useListAssets } from "@/app/apis/services/workflow/assets/hooks";
// No longer need useUpdateProperty or stringToPropertyDisposalType since property updates are handled by parent
import { useUserAsset } from "@/app/contexts/User/UserAssetContext";
import { Button } from "@/design-system/components/Button";
import { Dropdown } from "@/design-system/components/Dropdown";
import { InputType, TextInput } from "@/design-system/components/TextInput";
import { Typography } from "@/design-system/components/Typography";
import { colors } from "@/design-system/tokens";
import { create } from "@bufbuild/protobuf";
import CloseIcon from "@mui/icons-material/Close";
import { Box, Dialog, DialogActions, DialogContent, DialogTitle, IconButton } from "@mui/material";
import Image from 'next/image';
import { AssetStatus, AssetType, ListAssetsRequest } from "proto/hero/assets/v2/assets_pb";
import { FileReferenceSchema } from "proto/hero/reports/v2/reports_pb";
import { useEffect, useMemo, useRef, useState } from "react";
import { useFileUpload } from "../../apis/services/filerepository/hooks";
import { useAddPropertyFileAttachment, useProperty } from "../../apis/services/workflow/property/hooks";
import { CustodyActionType, CustodyEvent } from "../../apis/services/workflow/property/types";
import { FileUploadDropZone, StagedFilesList, UploadProgressDashboard, type StagedFileReference } from "../../reports/ReportComponents/panels/uiComponents/MediaPanel";

interface CustodyUpdateModalProps {
    open: boolean;
    onClose: () => void;
    onSubmit: (custodyEvent: CustodyEvent, disposalType?: string, propertyNumber?: string) => void;
    actionType: CustodyActionType;
    propertyId: string;
}

const actionTypeLabels: Record<CustodyActionType, string> = {
    [CustodyActionType.UNSPECIFIED]: "Unknown Action",
    [CustodyActionType.COLLECTED]: "Collect Item",
    [CustodyActionType.CHECKED_IN]: "Check In Item",
    [CustodyActionType.CHECKED_OUT]: "Check Out Item",
    [CustodyActionType.TRANSFERRED]: "Transfer Item",
    [CustodyActionType.RELEASED]: "Release Item",
    [CustodyActionType.DISPOSED]: "Dispose Item",
    [CustodyActionType.LOGGED]: "Log Item",
    [CustodyActionType.MOVED]: "Move Item",
};

const disposalTypeOptions = [
    { value: "PROPERTY_DISPOSAL_TYPE_DESTROYED", label: "Destroyed" },
    { value: "PROPERTY_DISPOSAL_TYPE_AUCTIONED", label: "Auctioned" },
    { value: "PROPERTY_DISPOSAL_TYPE_AGENCY_RETAIN", label: "Agency Retain" },
];

const entityTypeOptions = [
    { value: "CUSTODY_ENTITY_TYPE_PERSON", label: "Person" },
    { value: "CUSTODY_ENTITY_TYPE_CRIME_LAB", label: "Crime Lab" },
    { value: "CUSTODY_ENTITY_TYPE_COURT", label: "Court" },
    { value: "CUSTODY_ENTITY_TYPE_SPECIALIST", label: "Specialist" },
    { value: "CUSTODY_ENTITY_TYPE_CSI", label: "CSI" },
    { value: "CUSTODY_ENTITY_TYPE_ORGANIZATION", label: "Organization" },
    { value: "CUSTODY_ENTITY_TYPE_OTHER", label: "Other" },
];

const agencyTypeOptions = [
    { value: "CUSTODY_AGENCY_TYPE_PD", label: "Police Department" },
    { value: "CUSTODY_AGENCY_TYPE_OTHER", label: "Other" },
];

// Helper functions to convert enum values to human-readable text
const getEntityTypeLabel = (entityType: string): string => {
    const option = entityTypeOptions.find(opt => opt.value === entityType);
    return option?.label || entityType.replace(/CUSTODY_ENTITY_TYPE_/, '').toLowerCase();
};

const getAgencyTypeLabel = (agencyType: string): string => {
    const option = agencyTypeOptions.find(opt => opt.value === agencyType);
    return option?.label || agencyType.replace(/CUSTODY_AGENCY_TYPE_/, '').toLowerCase();
};

const checkoutLengthOptions = [
    { value: "1 hour", label: "1 hour" },
    { value: "4 hours", label: "4 hours" },
    { value: "1 day", label: "1 day" },
    { value: "3 days", label: "3 days" },
    { value: "1 week", label: "1 week" },
    { value: "2 weeks", label: "2 weeks" },
    { value: "1 month", label: "1 month" },
    { value: "Until court date", label: "Until court date" },
    { value: "Indefinite", label: "Indefinite" },
    { value: "Custom", label: "Custom" },
];

export default function CustodyUpdateModal({
    open,
    onClose,
    onSubmit,
    actionType,
    propertyId
}: CustodyUpdateModalProps) {

    const [location, setLocation] = useState(""); // Only used for Move action
    const [comments, setComments] = useState("");
    const [_selectedAssetId, setSelectedAssetId] = useState<string>("");

    // Additional states for conditional fields
    const [_transferredFromId, setTransferredFromId] = useState<string>("");
    const [_transferredToId, setTransferredToId] = useState<string>("");
    const [disposalType, setDisposalType] = useState<string>("");

    // Enhanced fields for detailed custody information
    const [performingOfficerId, setPerformingOfficerId] = useState<string>("");
    const [receivingEntityType, setReceivingEntityType] = useState<string>("");
    const [receivingEntityName, setReceivingEntityName] = useState<string>("");
    const [checkoutLength, setCheckoutLength] = useState<string>("");
    const [customCheckoutLength, setCustomCheckoutLength] = useState<string>("");
    const [reason, setReason] = useState<string>("");
    const [receivingAgencyType, setReceivingAgencyType] = useState<string>("");
    const [collectionLocation, setCollectionLocation] = useState<string>("");
    const [storageLocation, setStorageLocation] = useState<string>("");

    // File upload state
    const [isDragging, setIsDragging] = useState(false);
    const [stagedFiles, setStagedFiles] = useState<StagedFileReference[]>([]);
    const [overallProgress, setOverallProgress] = useState(0);
    const [uploadingSummary, setUploadingSummary] = useState({
        totalFiles: 0,
        completedFiles: 0,
        failedFiles: 0,
        totalBytes: 0,
        uploadedBytes: 0,
        averageSpeed: 0,
        estimatedTimeRemaining: 0,
        isComplete: false
    });
    const fileInputRef = useRef<HTMLInputElement>(null);

    // Get current user's asset
    const { asset: currentUserAsset } = useUserAsset();

    // Hooks for file operations
    const { uploadFile, isLoading: _isUploadingFile } = useFileUpload();
    const addPropertyFileAttachmentMutation = useAddPropertyFileAttachment();

    // File upload event handlers
    const handleDragEnter = (event: React.DragEvent<HTMLDivElement>) => {
        event.preventDefault();
        event.stopPropagation();
        setIsDragging(true);
    };

    const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
        event.preventDefault();
        event.stopPropagation();
        setIsDragging(true);
    };

    const handleDragLeave = (event: React.DragEvent<HTMLDivElement>) => {
        event.preventDefault();
        event.stopPropagation();
        setIsDragging(false);
    };

    const handleDrop = async (event: React.DragEvent<HTMLDivElement>) => {
        event.preventDefault();
        event.stopPropagation();
        setIsDragging(false);

        if (event.dataTransfer.files) {
            const files = Array.from(event.dataTransfer.files);
            await handleFiles(files);
        }
    };

    const handleClickUpload = () => {
        fileInputRef.current?.click();
    };

    const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
        if (event.target.files) {
            const files = Array.from(event.target.files);
            await handleFiles(files);

            // Clear the input
            if (fileInputRef.current) {
                fileInputRef.current.value = '';
            }
        }
    };

    // Get assets for the dropdown
    const { data: assetsResponse } = useListAssets({
        pageSize: 100,
        pageToken: "",
        type: AssetType.UNSPECIFIED,
        status: AssetStatus.UNSPECIFIED,
        orderBy: "name",
    } as ListAssetsRequest);

    // Get current property data to show current location
    const { data: propertyData } = useProperty(propertyId);

    // Memoize the assets options to prevent recreation on every render
    const assetsOptions = useMemo(() => {
        return assetsResponse?.assets?.map((asset) => ({
            label: asset.id === currentUserAsset?.id ? `${asset.name} (You)` : asset.name,
            value: asset.id
        })) || [];
    }, [assetsResponse?.assets, currentUserAsset?.id]);

    // Property updates are now handled by the parent component


    // Set default performing officer to current user when modal opens
    useEffect(() => {
        if (open && currentUserAsset?.id && !performingOfficerId) {
            setPerformingOfficerId(currentUserAsset.id);
        }
    }, [open, currentUserAsset?.id, performingOfficerId]);

    // Helper functions for file upload UI
    const formatBytes = (bytes: number): string => {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
    };

    const formatDuration = (seconds: number): string => {
        if (seconds < 60) return `${Math.round(seconds)}s`;
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = Math.round(seconds % 60);
        return `${minutes}m ${remainingSeconds}s`;
    };

    const createPreview = (file: File): string => {
        // Create preview for supported file types
        if (file.type.startsWith('image/') || file.type.startsWith('video/') || file.type === 'application/pdf') {
            return URL.createObjectURL(file);
        }
        return '';
    };

    const getFileCategory = (file: File): string => {
        if (file.type.startsWith('image/')) return 'custody_image';
        if (file.type.startsWith('video/')) return 'custody_video';
        if (file.type === 'application/pdf') return 'custody_document';
        if (file.type.startsWith('audio/')) return 'custody_audio';
        return 'custody_media';
    };

    const renderFilePreview = (file: StagedFileReference) => {
        if (file.preview) {
            return (
                <Image
                    src={file.preview}
                    alt={file.displayName || 'Preview'}
                    width={100}
                    height={100}
                    style={{
                        width: '100%',
                        height: '100%',
                        objectFit: 'cover',
                    }}
                />
            );
        }
        return null;
    };

    const handleFiles = async (files: File[]) => {
        for (const file of files) {
            // Accept the same file types as MediaPanel: images, videos, PDFs, and audio files
            const isValidFileType = file.type.startsWith('image/') ||
                file.type.startsWith('video/') ||
                file.type === 'application/pdf' ||
                file.type.startsWith('audio/');

            if (!isValidFileType) {
                console.warn(`Skipping unsupported file type: ${file.type} (${file.name})`);
                continue;
            }

            const tempId = Date.now().toString() + Math.random().toString(36).substring(2, 9);
            const baseFileRef = create(FileReferenceSchema, {
                id: tempId,
                fileId: "", // Empty until uploaded
                caption: "",
                displayName: file.name,
                displayOrder: 0,
                fileCategory: getFileCategory(file),
                metadata: {
                    originalFilename: file.name,
                    fileSize: file.size,
                    fileType: file.type,
                },
            });

            const stagedFile: StagedFileReference = {
                ...baseFileRef,
                file,
                preview: createPreview(file),
                isUploading: false,
            };

            setStagedFiles(prev => [...prev, stagedFile]);
        }
    };

    // File metadata handlers
    const handleDisplayNameChange = (id: string, displayName: string) => {
        setStagedFiles(prev =>
            prev.map(file =>
                file.id === id ? { ...file, displayName } : file
            )
        );
    };

    const handleCaptionChange = (id: string, caption: string) => {
        setStagedFiles(prev =>
            prev.map(file =>
                file.id === id ? { ...file, caption } : file
            )
        );
    };

    const handleCategoryChange = (id: string, fileCategory: string) => {
        setStagedFiles(prev =>
            prev.map(file =>
                file.id === id ? { ...file, fileCategory } : file
            )
        );
    };

    const handleRemoveFile = (id: string) => {
        setStagedFiles(prev => {
            const file = prev.find(f => f.id === id);
            if (file?.preview) {
                URL.revokeObjectURL(file.preview);
            }
            return prev.filter(f => f.id !== id);
        });
    };

    const uploadStagedFiles = async (): Promise<Array<{ fileId: string; fileName: string; fileType: string }>> => {
        const results: Array<{ fileId: string; fileName: string; fileType: string }> = [];
        const filesToUpload = stagedFiles.filter(file =>
            file.file && !file.fileId && !file.isUploading && !file.uploadError
        );

        if (filesToUpload.length === 0) {
            // Return already uploaded files
            return stagedFiles
                .filter(file => file.fileId)
                .map(file => ({
                    fileId: file.fileId!,
                    fileName: String(file.displayName || file.metadata?.originalFilename || ''),
                    fileType: String(file.metadata?.fileType || '')
                }));
        }

        for (const stagedFile of filesToUpload) {
            if (!stagedFile.file) continue;

            try {
                // Mark file as uploading
                setStagedFiles(prev =>
                    prev.map(f =>
                        f.id === stagedFile.id
                            ? { ...f, isUploading: true, uploadStartTime: Date.now() }
                            : f
                    )
                );

                // Upload the file
                const result = await uploadFile(
                    stagedFile.file,
                    undefined,
                    {
                        propertyId: propertyId,
                        fileCategory: stagedFile.fileCategory || getFileCategory(stagedFile.file),
                        uploadContext: 'custody_update',
                        metadata: {
                            displayName: stagedFile.displayName,
                            caption: stagedFile.caption,
                            custodyActionType: actionType
                        }
                    }
                );

                if (result.success && result.fileId) {
                    // Update staged file with upload result
                    setStagedFiles(prev =>
                        prev.map(f =>
                            f.id === stagedFile.id
                                ? {
                                    ...f,
                                    fileId: result.fileId || '',
                                    isUploading: false,
                                    uploadProgress: 100
                                }
                                : f
                        )
                    );

                    results.push({
                        fileId: result.fileId,
                        fileName: String(stagedFile.displayName || stagedFile.metadata?.originalFilename || ''),
                        fileType: String(stagedFile.metadata?.fileType || '')
                    });
                } else {
                    throw new Error(result.error || 'Upload failed');
                }
            } catch (error) {
                // Mark file as failed
                setStagedFiles(prev =>
                    prev.map(f =>
                        f.id === stagedFile.id
                            ? {
                                ...f,
                                isUploading: false,
                                uploadError: error instanceof Error ? error.message : 'Upload failed'
                            }
                            : f
                    )
                );
                console.error(`Failed to upload file ${stagedFile.displayName}:`, error);
            }
        }

        return results;
    };

    const attachFilesToProperty = async (uploadedFiles: Array<{ fileId: string; fileName: string; fileType: string }>) => {
        if (uploadedFiles.length === 0) return;

        const attachmentPromises = uploadedFiles.map(async (file, index) => {
            const attachmentData = {
                propertyId: propertyId,
                fileAttachment: {
                    propertyId: propertyId,
                    fileId: file.fileId,
                    displayName: file.fileName,
                    fileCategory: file.fileType ? (file.fileType.startsWith('image/') ? 'custody_image' :
                        file.fileType.startsWith('video/') ? 'custody_video' :
                            file.fileType === 'application/pdf' ? 'custody_document' :
                                file.fileType.startsWith('audio/') ? 'custody_audio' : 'custody_media') : 'custody_media',
                    displayOrder: index,
                    metadata: {
                        uploadContext: 'custody_update',
                        fileType: file.fileType,
                        custodyActionType: actionType
                    }
                }
            };

            return await addPropertyFileAttachmentMutation.mutateAsync(attachmentData);
        });

        await Promise.all(attachmentPromises);
    };

    // Helper function to get location based on action type
    const getLocationForActionType = () => {
        switch (actionType) {
            case CustodyActionType.MOVED:
                return location || "Unknown location"; // Only Move action uses the location field
            case CustodyActionType.CHECKED_IN:
                return storageLocation || "Unknown location"; // Use storage location for check-ins
            default:
                return ""; // Other actions don't need location
        }
    };

    const handleSubmit = async () => {
        // Use current date and time
        const eventDate = new Date();

        // Generate property number for check-ins if needed
        const generatePropertyNumber = () => {
            // Generate 5 random numbers for now - will be replaced with sequential logic later
            const randomNumber = Math.floor(Math.random() * 90000) + 10000; // 5-digit number
            return `PROP-${randomNumber}`;
        };

        // Build enhanced custody event
        const custodyEvent: CustodyEvent = {
            timestamp: eventDate.toISOString(),
            transferringUserId: performingOfficerId || "",
            transferringAgency: "",
            receivingUserId: receivingEntityName || "",
            receivingAgency: receivingEntityName || "",
            newLocation: getLocationForActionType(),
            actionType: actionType,
            notes: comments || "",
            caseNumber: "",
            evidenceNumber: "",

            // Enhanced fields
            performingOfficerId: performingOfficerId || "",
            receivingEntityType: receivingEntityType || "",
            receivingEntityName: receivingEntityName || "",
            checkoutLength: checkoutLength === "Custom" ? customCheckoutLength : checkoutLength,
            reason: reason || "",
            receivingAgencyType: receivingAgencyType || "",
            confirmationReceived: false,
            collectionLocation: collectionLocation || "",
            disposalType: disposalType || "",
        };

        // Add property number for check-ins
        if (actionType === CustodyActionType.CHECKED_IN) {
            custodyEvent.evidenceNumber = generatePropertyNumber();
        }

        // Add action-specific notes based on the enhanced information
        let enhancedNotes = comments || "";

        switch (actionType) {
            case CustodyActionType.CHECKED_OUT:
                enhancedNotes = [
                    `Checked out to: ${receivingEntityName} (${getEntityTypeLabel(receivingEntityType)})`,
                    `Length: ${checkoutLength === "Custom" ? customCheckoutLength : checkoutLength}`,
                    `Reason: ${reason}`,
                    comments
                ].filter(Boolean).join('. ');
                break;
            case CustodyActionType.TRANSFERRED:
                enhancedNotes = [
                    `Transferred to: ${receivingEntityName} (${getAgencyTypeLabel(receivingAgencyType)})`,
                    `Reason: ${reason}`,
                    comments
                ].filter(Boolean).join('. ');
                break;
            case CustodyActionType.RELEASED:
                enhancedNotes = [
                    `Released to: ${receivingEntityName} (${getEntityTypeLabel(receivingEntityType)})`,
                    `Reason: ${reason}`,
                    comments
                ].filter(Boolean).join('. ');
                break;
            case CustodyActionType.DISPOSED:
                enhancedNotes = [
                    `Disposal Type: ${disposalTypeOptions.find(opt => opt.value === disposalType)?.label || disposalType}`,
                    `Reason: ${reason}`,
                    comments
                ].filter(Boolean).join('. ');
                break;
            case CustodyActionType.CHECKED_IN:
                enhancedNotes = [
                    collectionLocation ? `Collected from: ${collectionLocation}` : "",
                    storageLocation ? `Stored at: ${storageLocation}` : "",
                    comments
                ].filter(Boolean).join('. ');
                break;
            default:
                enhancedNotes = comments || "";
        }

        custodyEvent.notes = enhancedNotes;

        // Pass property number for check-ins
        const propertyNumberToPass = actionType === CustodyActionType.CHECKED_IN
            ? custodyEvent.evidenceNumber
            : undefined;

        try {
            // Upload any staged files and attach them to the property
            const uploadedFiles = await uploadStagedFiles();
            if (uploadedFiles.length > 0) {
                await attachFilesToProperty(uploadedFiles);
            }

            onSubmit(custodyEvent, disposalType || undefined, propertyNumberToPass);
            handleClose();
        } catch (error) {
            console.error('Error uploading files during custody update:', error);
            // You might want to show an error message to the user here
        }
    };

    const handleClose = () => {
        setLocation("");
        setComments("");
        setSelectedAssetId("");
        // Reset conditional fields
        setTransferredFromId("");
        setTransferredToId("");
        setDisposalType("");
        // Reset enhanced fields
        setPerformingOfficerId("");
        setReceivingEntityType("");
        setReceivingEntityName("");
        setCheckoutLength("");
        setCustomCheckoutLength("");
        setReason("");
        setReceivingAgencyType("");
        setCollectionLocation("");
        setStorageLocation("");
        // Reset file upload state
        setStagedFiles([]);
        setOverallProgress(0);
        setUploadingSummary({
            totalFiles: 0,
            completedFiles: 0,
            failedFiles: 0,
            totalBytes: 0,
            uploadedBytes: 0,
            averageSpeed: 0,
            estimatedTimeRemaining: 0,
            isComplete: false
        });
        onClose();
    }

    // Validation based on action type
    const isSubmitDisabled = (() => {
        if (!performingOfficerId.trim()) return true;

        // Check if any files are currently uploading
        const hasUploadingFiles = stagedFiles.some(f => f.isUploading);
        if (hasUploadingFiles) return true;

        switch (actionType) {
            case CustodyActionType.CHECKED_IN:
                return !storageLocation.trim(); // Storage location required for check-ins
            case CustodyActionType.CHECKED_OUT:
                return !receivingEntityType.trim() || !receivingEntityName.trim() ||
                    !checkoutLength.trim() || !reason.trim() ||
                    (checkoutLength === "Custom" && !customCheckoutLength.trim());
            case CustodyActionType.TRANSFERRED:
                return !receivingAgencyType.trim() || !receivingEntityName.trim() || !reason.trim();
            case CustodyActionType.RELEASED:
                return !receivingEntityType.trim() || !receivingEntityName.trim() || !reason.trim();
            case CustodyActionType.MOVED:
                return !location.trim(); // New location required
            case CustodyActionType.DISPOSED:
                return !reason.trim() || !disposalType.trim();
            default:
                return false;
        }
    })();

    return (
        <>
            <Dialog
                open={open}
                onClose={handleClose}
                maxWidth="sm"
                fullWidth
                PaperProps={{
                    sx: {
                        borderRadius: "12px",
                        boxShadow: "0px 8px 24px rgba(0, 0, 0, 0.15)",
                    }
                }}
            >
                <DialogTitle sx={{
                    pb: 1,
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    borderBottom: `1px solid ${colors.grey[200]}`
                }}>
                    <Typography style="body1" color={colors.grey[900]} className="font-semibold text-lg">
                        {actionTypeLabels[actionType]}
                    </Typography>
                    <IconButton
                        onClick={handleClose}
                        sx={{
                            color: colors.grey[600],
                            "&:hover": { backgroundColor: colors.grey[100] }
                        }}
                    >
                        <CloseIcon />
                    </IconButton>
                </DialogTitle>
                <DialogContent sx={{ pt: 4, pb: 3 }}>
                    <Box sx={{ display: "flex", flexDirection: "column", gap: 3, mt: 2 }}>
                        {/* Enhanced Fields Based on Action Type */}
                        {actionType === CustodyActionType.CHECKED_IN ? (
                            <>
                                {/* Check-In: Mirror the check-in form from property wizard */}
                                <Box>
                                    <Dropdown
                                        enableSearch
                                        onChange={(value) => setPerformingOfficerId(value || "")}
                                        options={assetsOptions}
                                        placeholder="Select officer"
                                        title="Officer Performing Action *"
                                        value={performingOfficerId}
                                    />
                                </Box>
                                <Box>
                                    <TextInput
                                        title="Collection Location"
                                        placeholder="Where was property collected from?"
                                        value={collectionLocation}
                                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => setCollectionLocation(e.target.value)}
                                        type={InputType.Text}
                                    />
                                </Box>
                                <Box>
                                    <TextInput
                                        title="Storage Location *"
                                        placeholder="Where are you storing this property?"
                                        value={storageLocation}
                                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => setStorageLocation(e.target.value)}
                                        type={InputType.Text}
                                    />
                                </Box>
                            </>
                        ) : actionType === CustodyActionType.CHECKED_OUT ? (
                            <>
                                {/* Check-Out Form */}
                                <Box>
                                    <Dropdown
                                        enableSearch
                                        onChange={(value) => setPerformingOfficerId(value || "")}
                                        options={assetsOptions}
                                        placeholder="Select officer"
                                        title="Officer Performing Action *"
                                        value={performingOfficerId}
                                    />
                                </Box>
                                <Box>
                                    <Dropdown
                                        onChange={(value) => setReceivingEntityType(value || "")}
                                        options={entityTypeOptions}
                                        placeholder="Select entity type"
                                        title="Receiving Entity Type *"
                                        value={receivingEntityType}
                                    />
                                </Box>
                                <Box>
                                    <TextInput
                                        title="Receiving Entity Name *"
                                        placeholder="Enter name of person/organization"
                                        value={receivingEntityName}
                                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => setReceivingEntityName(e.target.value)}
                                        type={InputType.Text}
                                    />
                                </Box>
                                <Box>
                                    <Dropdown
                                        onChange={(value) => setCheckoutLength(value || "")}
                                        options={checkoutLengthOptions}
                                        placeholder="Select checkout length"
                                        title="Expected Check Out Length *"
                                        value={checkoutLength}
                                    />
                                </Box>
                                {checkoutLength === "Custom" && (
                                    <Box>
                                        <TextInput
                                            title="Custom Checkout Length *"
                                            placeholder="Enter custom length"
                                            value={customCheckoutLength}
                                            onChange={(e: React.ChangeEvent<HTMLInputElement>) => setCustomCheckoutLength(e.target.value)}
                                            type={InputType.Text}
                                        />
                                    </Box>
                                )}
                                <Box>
                                    <TextInput
                                        title="Reason for Check Out *"
                                        placeholder="Enter reason"
                                        value={reason}
                                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => setReason(e.target.value)}
                                        type={InputType.Text}
                                    />
                                </Box>
                            </>
                        ) : actionType === CustodyActionType.TRANSFERRED ? (
                            <>
                                {/* Transfer Form */}
                                <Box>
                                    <Dropdown
                                        enableSearch
                                        onChange={(value) => setPerformingOfficerId(value || "")}
                                        options={assetsOptions}
                                        placeholder="Select officer"
                                        title="Officer Performing Action *"
                                        value={performingOfficerId}
                                    />
                                </Box>
                                <Box>
                                    <Dropdown
                                        onChange={(value) => setReceivingAgencyType(value || "")}
                                        options={agencyTypeOptions}
                                        placeholder="Select agency type"
                                        title="Receiving Agency Type *"
                                        value={receivingAgencyType}
                                    />
                                </Box>
                                <Box>
                                    <TextInput
                                        title="Receiving Agency Name *"
                                        placeholder="Enter agency name"
                                        value={receivingEntityName}
                                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => setReceivingEntityName(e.target.value)}
                                        type={InputType.Text}
                                    />
                                </Box>
                                <Box>
                                    <TextInput
                                        title="Reason for Transfer *"
                                        placeholder="Enter reason"
                                        value={reason}
                                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => setReason(e.target.value)}
                                        type={InputType.Text}
                                    />
                                </Box>
                            </>
                        ) : actionType === CustodyActionType.RELEASED ? (
                            <>
                                {/* Release Form */}
                                <Box>
                                    <Dropdown
                                        enableSearch
                                        onChange={(value) => setPerformingOfficerId(value || "")}
                                        options={assetsOptions}
                                        placeholder="Select officer"
                                        title="Officer Performing Action *"
                                        value={performingOfficerId}
                                    />
                                </Box>
                                <Box>
                                    <Dropdown
                                        onChange={(value) => setReceivingEntityType(value || "")}
                                        options={entityTypeOptions.filter(opt =>
                                            opt.value === "CUSTODY_ENTITY_TYPE_PERSON" ||
                                            opt.value === "CUSTODY_ENTITY_TYPE_ORGANIZATION" ||
                                            opt.value === "CUSTODY_ENTITY_TYPE_OTHER")}
                                        placeholder="Select entity type"
                                        title="Receiving Entity Type *"
                                        value={receivingEntityType}
                                    />
                                </Box>
                                <Box>
                                    <TextInput
                                        title="Receiving Entity Name *"
                                        placeholder="Enter name of person/organization"
                                        value={receivingEntityName}
                                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => setReceivingEntityName(e.target.value)}
                                        type={InputType.Text}
                                    />
                                </Box>
                                <Box>
                                    <TextInput
                                        title="Reason for Release *"
                                        placeholder="Enter reason"
                                        value={reason}
                                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => setReason(e.target.value)}
                                        type={InputType.Text}
                                    />
                                </Box>
                            </>
                        ) : actionType === CustodyActionType.MOVED ? (
                            <>
                                {/* Move Form */}
                                <Box>
                                    <Dropdown
                                        enableSearch
                                        onChange={(value) => setPerformingOfficerId(value || "")}
                                        options={assetsOptions}
                                        placeholder="Select officer"
                                        title="Officer Performing Action *"
                                        value={performingOfficerId}
                                    />
                                </Box>

                                {/* Current Location (Read-only) */}
                                <Box>
                                    <Typography style="body1" color={colors.grey[900]} className="mb-1">
                                        Current Location
                                    </Typography>
                                    <Box sx={{
                                        p: 2,
                                        backgroundColor: colors.grey[50],
                                        border: `1px solid ${colors.grey[200]}`,
                                        borderRadius: 1
                                    }}>
                                        <Typography style="body2" color={colors.grey[700]}>
                                            {propertyData?.currentLocation || "Unknown location"}
                                        </Typography>
                                    </Box>
                                </Box>

                                <Box>
                                    <TextInput
                                        title="New Location *"
                                        placeholder="Enter new storage location"
                                        value={location}
                                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => setLocation(e.target.value)}
                                        type={InputType.Text}
                                    />
                                </Box>
                            </>
                        ) : actionType === CustodyActionType.DISPOSED ? (
                            <>
                                {/* Dispose Form */}
                                <Box>
                                    <Dropdown
                                        enableSearch
                                        onChange={(value) => setPerformingOfficerId(value || "")}
                                        options={assetsOptions}
                                        placeholder="Select officer"
                                        title="Officer Performing Action *"
                                        value={performingOfficerId}
                                    />
                                </Box>
                                <Box>
                                    <TextInput
                                        title="Reason for Disposal *"
                                        placeholder="Enter reason"
                                        value={reason}
                                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => setReason(e.target.value)}
                                        type={InputType.Text}
                                    />
                                </Box>
                                <Box>
                                    <Dropdown
                                        onChange={(value) => setDisposalType(value || "")}
                                        options={disposalTypeOptions}
                                        placeholder="Select disposal method"
                                        title="Disposal Type *"
                                        value={disposalType}
                                    />
                                </Box>
                            </>
                        ) : (
                            /* Default form for other actions */
                            <Box>
                                <Dropdown
                                    enableSearch
                                    onChange={(value) => setPerformingOfficerId(value || "")}
                                    options={assetsOptions}
                                    placeholder="Select officer"
                                    title="Officer Performing Action *"
                                    value={performingOfficerId}
                                />
                            </Box>
                        )}

                        {/* Current Date and Time (Read Only) */}
                        <Box>
                            <Typography style="body1" color={colors.grey[900]} className="mb-1">
                                Date & Time
                            </Typography>
                            <Box sx={{
                                p: 2,
                                backgroundColor: colors.grey[50],
                                border: `1px solid ${colors.grey[200]}`,
                                borderRadius: 1
                            }}>
                                <Typography style="body2" color={colors.grey[700]}>
                                    {new Date().toLocaleString()}
                                </Typography>
                            </Box>
                        </Box>

                        {/* Comments */}
                        <Box>
                            <TextInput
                                title="Comments"
                                placeholder="Add any additional notes..."
                                value={comments}
                                onChange={(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => setComments(e.target.value)}
                                type={InputType.Multiline}
                            />
                        </Box>

                        {/* Media Section - Show for actions that previously had confirmation checkboxes */}
                        {(actionType === CustodyActionType.TRANSFERRED ||
                            actionType === CustodyActionType.RELEASED) && (
                                <Box sx={{ mt: 3, pt: 3, borderTop: `1px solid ${colors.grey[200]}` }}>
                                    <Box sx={{ mb: 3 }}>
                                        <Typography style="h3" color={colors.grey[900]}>
                                            Proof of Receipt (Optional)
                                        </Typography>
                                    </Box>
                                    <FileUploadDropZone
                                        isDragging={isDragging}
                                        onDragEnter={handleDragEnter}
                                        onDragOver={handleDragOver}
                                        onDragLeave={handleDragLeave}
                                        onDrop={handleDrop}
                                        onClick={handleClickUpload}
                                        fileInputRef={fileInputRef}
                                        onFileSelect={handleFileSelect}
                                    />

                                    {stagedFiles.length > 0 && (
                                        <Box sx={{ mt: 3 }}>
                                            <StagedFilesList
                                                stagedFiles={stagedFiles}
                                                readOnly={false}
                                                onDisplayNameChange={handleDisplayNameChange}
                                                onCaptionChange={handleCaptionChange}
                                                onCategoryChange={handleCategoryChange}
                                                onRemoveFile={handleRemoveFile}
                                                renderFilePreview={renderFilePreview}
                                                formatBytes={formatBytes}
                                                formatDuration={formatDuration}
                                            />
                                        </Box>
                                    )}

                                    {(stagedFiles.some(f => f.isUploading) || uploadingSummary.isComplete) && (
                                        <Box sx={{ mt: 3 }}>
                                            <UploadProgressDashboard
                                                overallProgress={overallProgress}
                                                uploadingSummary={uploadingSummary}
                                                formatBytes={formatBytes}
                                                formatDuration={formatDuration}
                                            />
                                        </Box>
                                    )}
                                </Box>
                            )}
                    </Box>
                </DialogContent>
                <DialogActions
                    sx={{
                        borderTop: `1px solid ${colors.grey[200]}`,
                        pt: 2,
                        px: 3,
                        pb: 3,
                        gap: 1,
                    }}
                >
                    <Button
                        label="Cancel"
                        prominence={false}
                        onClick={handleClose}
                    />
                    <Button
                        label="Submit"
                        prominence={true}
                        onClick={handleSubmit}
                        disabled={isSubmitDisabled}
                    />
                </DialogActions>
            </Dialog>
        </>
    );
} 