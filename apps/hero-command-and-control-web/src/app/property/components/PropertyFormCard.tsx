import {
  <PERSON><PERSON>onfig,
  <PERSON><PERSON><PERSON><PERSON>,
  FormRenderer<PERSON>ef,
  <PERSON>V<PERSON><PERSON>,
} from "@/design-system/form";
import { colors } from "@/design-system/tokens";
import {
  Box,
  CircularProgress,
  Paper,
} from "@mui/material";
import { EntityType } from "proto/hero/entity/v1/entity_pb";
import { useRef } from "react";
import { useListLatestEntitySchemas } from "../../apis/services/workflow/entity/hooks";
import { Property } from "../../apis/services/workflow/property/types";
import { schemaToFormConfig } from "../../reports/ReportComponents/core/utils/schemaToFormConfig";

interface PropertyFormCardProps {
  propertyData: Property;
  readOnly?: boolean;
}

export default function PropertyFormCard({
  propertyData,
  readOnly = true,
}: PropertyFormCardProps) {
  const formRef = useRef<FormRendererRef>(null);

  // Fetch property entity schemas like PropertyForm does
  const { data: propertySchemas, isLoading: isSchemasLoading } = useListLatestEntitySchemas({
    entityType: EntityType.PROPERTY,
    pageSize: 1,
    pageToken: "",
  } as any);

  // Use the first available property schema, or null if none exist
  const selectedSchema = propertySchemas?.schemas?.[0] || null;

  if (!propertyData || isSchemasLoading || !selectedSchema) {
    return (
      <Paper
        elevation={0}
        sx={{
          borderRadius: "12px",
          border: `1px solid ${colors.grey[200]}`,
          mb: 3,
          p: 3,
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          minHeight: 200,
          bgcolor: "white",
        }}
      >
        <CircularProgress size={30} />
      </Paper>
    );
  }

  // Use the same form configuration as PropertyForm (generated from entity schema)
  const formConfig = schemaToFormConfig(selectedSchema);

  // Filter out admin section for read-only display, keeping only property details
  const filteredConfig = {
    ...formConfig,
    sections: formConfig.sections.filter(section => section.id !== "admin")
  };

  // Convert property details to structured form values that FormRenderer expects
  // Match the section structure from the actual form configuration
  const formValues: FormValues = {};

  // Map property details and top-level fields to match the form structure
  filteredConfig.sections.forEach(section => {
    formValues[section.id] = {};

    section.fields.forEach(field => {
      let fieldValue = undefined;

      if (propertyData.details && propertyData.details[field.id] !== undefined) {
        fieldValue = propertyData.details[field.id];

        // Keep category as NIBRS enum for dropdown value matching (dropdown options have NIBRS values)
      } else {
        // Also check top-level property fields (like propertyType)
        switch (field.id) {
          case 'propertyType': {
            fieldValue = propertyData.nibrsPropertyType;
            break;
          }
          case 'propertyNumber': {
            fieldValue = propertyData.propertyNumber;
            break;
          }
          case 'propertyStatus': {
            fieldValue = propertyData.propertyStatus;
            break;
          }
          case 'currentCustodian': {
            fieldValue = propertyData.currentCustodian;
            break;
          }
          case 'currentLocation': {
            fieldValue = propertyData.currentLocation;
            break;
          }
          case 'notes': {
            fieldValue = propertyData.notes;
            break;
          }
          case 'isEvidence': {
            fieldValue = propertyData.isEvidence;
            break;
          }
          case 'retentionPeriod': {
            fieldValue = propertyData.retentionPeriod;
            break;
          }
          default: {
            // For unknown fields, try to access from the property object safely
            const propertyAsAny = propertyData as Record<string, any>;
            fieldValue = propertyAsAny[field.id];
            break;
          }
        }
      }

      if (fieldValue !== undefined) {
        formValues[section.id][field.id] = fieldValue;
      }
    });
  });

  return (
    <Box>
      <FormRenderer
        ref={formRef}
        config={filteredConfig as unknown as FormConfig}
        renderMode="consume"
        readOnly={readOnly}
        initialValues={formValues}
        showInternalTab={true}
        onSubmit={() => { }}
      />
    </Box>
  );
}