"use client";

import { Typography } from "@/design-system/components/Typography";
import { colors } from "@/design-system/tokens";
import { Box, CircularProgress } from "@mui/material";
import { useSearchParams } from "next/navigation";
import { Suspense } from "react";
import SidebarComponent from "../components/Sidebar";
import { useSidebar } from "../contexts/Sidebar/SidebarContext";
import PropertyDetailPage from "./PropertyDetailPage";

export default function Page() {
    const searchParams = useSearchParams();
    const propertyId = searchParams?.get("propertyId");
    const { collapsed } = useSidebar();

    return (
        <Box sx={{ bgcolor: '#F3F4F6', minHeight: '100vh' }}>
            <Suspense
                fallback={
                    <Box
                        sx={{
                            display: "flex",
                            justifyContent: "center",
                            alignItems: "center",
                            height: "100vh",
                        }}
                    >
                        <CircularProgress />
                    </Box>
                }
            >
                <div className="flex w-full">
                    <SidebarComponent />
                    <main
                        className={`flex-1 transition-all duration-300 ease-in-out ${collapsed ? 'ml-[40px]' : 'ml-[250px]'}`}
                        style={{
                            height: '100vh',
                            overflowY: 'auto',
                            overflowX: 'hidden'
                        }}
                    >
                        {propertyId ? (
                            <PropertyDetailPage propertyId={propertyId} />
                        ) : (
                            <Box sx={{ display: 'flex', height: '100vh', justifyContent: 'center', alignItems: 'center' }}>
                                <Typography style="h3" color={colors.grey[600]}>
                                    No property ID provided
                                </Typography>
                            </Box>
                        )}
                    </main>
                </div>
            </Suspense>
        </Box>
    );
} 