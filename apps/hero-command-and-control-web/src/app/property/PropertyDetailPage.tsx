"use client";

import { Typography } from "@/design-system/components/Typography";
import { JsonObject } from "@bufbuild/protobuf";
import { Al<PERSON>, Box, Stack } from "@mui/material";
import { useRouter } from "next/navigation";
import { Entity } from "proto/hero/entity/v1/entity_pb";
import { hookPropertyTypeToString } from "../apis/services/workflow/property/enumConverters";
import { useProperty } from "../apis/services/workflow/property/hooks";
import { migrateLegacyPropertyType } from "../apis/services/workflow/property/propertyTypeManager";
import ChainOfCustodyCard from "../entity/entityComponents/cards/ChainOfCustodyCard";
import MediaTableCard from "../entity/entityComponents/cards/MediaTableCard";
import { PROPERTY_FORM_CONFIG } from "../reports/ReportComponents/core/constants/propertyFormConfig";
import CustodyEvidenceCard from "./components/CustodyEvidenceCard";
import PropertyDetailCard from "./components/PropertyDetailCard";
import PropertyFormCard from "./components/PropertyFormCard";
import PropertyHeader from "./PropertyHeader";

interface PropertyDetailPageProps {
    propertyId: string;
}

// Interface for creating an Entity-compatible object from Property data
// Based on the Entity type from proto/hero/entity/v1/entity_pb.ts but tailored for Property use case
interface PropertyEntityAdapter extends Omit<Entity, 'references' | 'data'> {
    entityType: number; // 3 for PROPERTY
    status: number; // Entity status (1 for ACTIVE)  
    resourceType: string; // "PROPERTY"
    references: never[]; // Empty array for properties
    data?: JsonObject; // Use JsonObject type to match Entity interface
}

// Create a read-only version of the form config containing only PropertySchema fields
const SCHEMA_FIELD_IDS = new Set([
    "description",
    "propertyType",
    "category",
    "quantity",
    "identifiers",
    "owner",
    "condition",
    "serialNumber",
    "value",
]);

const _READ_ONLY_PROPERTY_FORM_CONFIG = {
    ...PROPERTY_FORM_CONFIG,
    sections: PROPERTY_FORM_CONFIG.sections
        // Keep only the basic info section
        .filter(section => section.id === "basicInfo")
        .map(section => ({
            ...section,
            // Keep only schema fields and exclude upload
            fields: section.fields.filter(field => SCHEMA_FIELD_IDS.has(field.id))
        })),
};

// Helper function to convert PropertyType enum to proper proto enum string
const _propertyTypeToFormValue = (propertyType: any): string => {
    if (!propertyType) return '';

    // Handle case where propertyType is already a string
    let enumString: string;
    if (typeof propertyType === 'string') {
        enumString = propertyType;
    } else {
        // Get the enum string representation for enum values
        enumString = hookPropertyTypeToString(propertyType);
    }

    // Use centralized migration for legacy values, otherwise return as-is
    return migrateLegacyPropertyType(enumString);
};

// Helper function to convert PropertyStatus enum to a form dropdown value for read-only display
const _propertyStatusToFormValue = (propertyStatus: any): string => {
    if (!propertyStatus) return '';

    // Handle case where propertyStatus is already a string
    let enumString: string;
    if (typeof propertyStatus === 'string') {
        enumString = propertyStatus;
    } else {
        // Get the enum string representation for enum values
        enumString = propertyStatus.toString();
    }

    // Map enum strings to dropdown values (lowercase to match the form config options)
    const statusToFormMap: Record<string, string> = {
        'PROPERTY_STATUS_INTAKE_PENDING': 'logged',
        'PROPERTY_STATUS_COLLECTED': 'collected',
        'PROPERTY_STATUS_CHECKED_IN': 'checked_in',
        'PROPERTY_STATUS_CHECKED_OUT': 'checked_out',
        'PROPERTY_STATUS_RECOVERED': 'recovered',
        'PROPERTY_STATUS_FOUND': 'found',
        'PROPERTY_STATUS_SAFEKEEPING': 'safekeeping',
        'PROPERTY_STATUS_AWAITING_DISPOSITION': 'awaiting_disposition',
        'PROPERTY_STATUS_DISPOSED': 'disposed',
        'PROPERTY_STATUS_MISSING': 'missing',
        'PROPERTY_STATUS_STOLEN': 'stolen',
    };

    const formValue = statusToFormMap[enumString] || '';
    return formValue;
};

export default function PropertyDetailPage({ propertyId }: PropertyDetailPageProps) {
    const router = useRouter();

    // Get property data
    const {
        data: propertyData,
        isLoading: isPropertyLoading,
        isError: isPropertyError,
    } = useProperty(propertyId);

    const handleClose = () => {
        router.back();
    };

    if (isPropertyLoading) {
        return (
            <Box sx={{ display: "flex", justifyContent: "center", alignItems: "center", height: "200px" }}>
                <Typography style="body1">Loading property...</Typography>
            </Box>
        );
    }

    if (isPropertyError || !propertyData) {
        return (
            <Box sx={{ p: 3 }}>
                <Alert severity="error">
                    Failed to load property data. Please try again.
                </Alert>
            </Box>
        );
    }



    // All properties now use dynamic schema (PropertySchema support removed)
    // Get initial values from details and include top-level propertyType field
    const _formInitialValues = {
        ...(propertyData.details || {}),
        // Include top-level property type field
        propertyType: propertyData.nibrsPropertyType
    };



    // Create a type-safe entity-like object for components that expect Entity type
    const entityForComponents: PropertyEntityAdapter = {
        id: propertyId,
        orgId: propertyData.orgId,
        entityType: 3, // PROPERTY
        status: 1, // ACTIVE
        createTime: propertyData.createTime,
        updateTime: propertyData.updateTime,
        version: propertyData.version,
        resourceType: "PROPERTY",
        data: propertyData as unknown as JsonObject, // Pass the actual property data so MediaTableCard can access metadata
        references: [], // Empty array for properties
        $typeName: "hero.entity.v1.Entity",
        createdBy: "",
        updatedBy: "",
        tags: [],
        schemaId: "", // Properties don't use schemaId
        schemaVersion: 0, // Properties don't use schemaVersion
    };

    return (
        <Box>
            <Box sx={{ bgcolor: 'white' }}>
                <PropertyHeader
                    propertyData={propertyData}
                    propertyId={propertyId}
                    onClose={handleClose}
                />
            </Box>
            <Box sx={{ bgcolor: '#F3F4F6', p: 3 }}>
                <Stack spacing={3}>
                    <PropertyDetailCard propertyData={propertyData} />

                    {/* Property Form with tabs for form info and additional info/tags */}
                    <PropertyFormCard propertyData={propertyData} readOnly={true} />

                    {/* Custody & Evidence */}
                    <CustodyEvidenceCard propertyData={propertyData} />

                    <ChainOfCustodyCard property={entityForComponents} />
                    <MediaTableCard property={entityForComponents} />
                </Stack>
            </Box>
        </Box>
    );
}