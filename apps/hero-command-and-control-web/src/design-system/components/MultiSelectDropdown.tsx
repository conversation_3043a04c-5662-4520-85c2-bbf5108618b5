import { Box, Popover } from "@mui/material";
import React, { useRef, useState } from "react";
import { colors } from "../tokens";
import { Label } from "./Label";
import { Typography } from "./Typography";

export type MultiSelectDropdownOption = {
    value: string;
    label: string;
};

export interface MultiSelectDropdownProps {
    title: string;
    placeholder: string;
    options: MultiSelectDropdownOption[];
    selectedValues: string[];
    onChange: (value: string) => void;
}

export const MultiSelectDropdown: React.FC<MultiSelectDropdownProps> = ({
    title,
    placeholder,
    options,
    selectedValues,
    onChange,
}) => {
    const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
    const [searchValue, setSearchValue] = useState<string>("");
    const [dropdownWidth, setDropdownWidth] = useState<number>(0);
    const inputRef = useRef<HTMLInputElement>(null);

    const filteredOptions = options.filter(option =>
        option.label.toLowerCase().includes(searchValue.toLowerCase())
    );

    const handleInputClick = (event: React.MouseEvent<HTMLElement>) => {
        const target = event.currentTarget;
        setAnchorEl(target);
        // Capture the width of the input field
        if (inputRef.current) {
            setDropdownWidth(inputRef.current.offsetWidth);
        }
        // Reset search when opening
        setSearchValue("");
    };

    const handleClose = () => {
        setAnchorEl(null);
        setSearchValue("");
    };

    const handleOptionClick = (optionValue: string) => {
        onChange(optionValue);
        // Don't close dropdown on selection to allow multiple selections
    };

    const open = Boolean(anchorEl);
    const selectedCount = selectedValues.length;

    return (
        <div style={{ width: "100%" }}>
            {title && (
                <div style={{ marginLeft: 12, marginBottom: 12 }}>
                    <Typography style="body4" color={colors.grey[500]}>
                        {title}
                    </Typography>
                </div>
            )}

            <div style={{ position: "relative" }}>
                <Box
                    ref={inputRef}
                    onClick={handleInputClick}
                    sx={{
                        display: "flex",
                        flexWrap: "wrap",
                        alignItems: "flex-start",
                        minHeight: "auto",
                        padding: "10px",
                        paddingRight: "30px", // Make room for the arrow
                        border: `1px solid ${open ? colors.blue[600] : colors.grey[200]}`,
                        borderRadius: "8px",
                        cursor: "pointer",
                        backgroundColor: "#fff",
                        "&:hover": {
                            borderColor: open ? colors.blue[600] : colors.grey[400],
                        },
                        gap: "6px",
                    }}
                >
                    {selectedCount > 0 ? (
                        <>
                            {/* Selected tags as Label components */}
                            {options
                                .filter(option => selectedValues.includes(option.value))
                                .map((option) => (
                                    <Label
                                        key={option.value}
                                        label={option.label}
                                        color="blue"
                                        size="small"
                                        rightIcon={
                                            <Box
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    onChange(option.value);
                                                }}
                                                sx={{
                                                    cursor: "pointer",
                                                    display: "flex",
                                                    alignItems: "center",
                                                    justifyContent: "center",
                                                    "&:hover": {
                                                        opacity: 0.7
                                                    }
                                                }}
                                            >
                                                <svg
                                                    width="12"
                                                    height="12"
                                                    viewBox="0 0 12 12"
                                                    fill="none"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                >
                                                    <path
                                                        d="M9 3L3 9M3 3L9 9"
                                                        stroke="currentColor"
                                                        strokeWidth="1.5"
                                                        strokeLinecap="round"
                                                        strokeLinejoin="round"
                                                    />
                                                </svg>
                                            </Box>
                                        }
                                    />
                                ))}
                        </>
                    ) : (
                        <Box
                            sx={{
                                flex: 1,
                                fontSize: "16px",
                                fontWeight: 400,
                                color: colors.grey[400],
                                fontFamily: "Roboto, sans-serif",
                            }}
                        >
                            {placeholder}
                        </Box>
                    )}

                </Box>

                {/* Arrow positioned absolutely to center vertically */}
                <svg
                    width="10"
                    height="6"
                    viewBox="0 0 10 6"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    style={{
                        position: "absolute",
                        top: "50%",
                        right: "12px",
                        transform: `translateY(-50%) ${open ? "rotate(180deg)" : "rotate(0deg)"}`,
                        transition: "transform 0.3s ease",
                        cursor: "pointer",
                        pointerEvents: "none", // Let clicks pass through to the container
                    }}
                >
                    <path
                        d="M1 1L5 5L9 1"
                        stroke={colors.grey[500]}
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                    />
                </svg>

                <Popover
                    open={open}
                    anchorEl={anchorEl}
                    onClose={handleClose}
                    anchorOrigin={{
                        vertical: "bottom",
                        horizontal: "left",
                    }}
                    transformOrigin={{
                        vertical: "top",
                        horizontal: "left",
                    }}
                    PaperProps={{
                        sx: {
                            mt: 1,
                            borderRadius: "8px",
                            boxShadow: "0px 4px 8px rgba(0, 0, 0, 0.1)",
                            border: `1px solid ${colors.grey[200]}`,
                            width: "511px",
                            maxWidth: "50%",
                            maxHeight: 400,
                            overflow: "auto",
                            "&::-webkit-scrollbar": {
                                width: "6px",
                            },
                            "&::-webkit-scrollbar-track": {
                                background: colors.grey[100],
                            },
                            "&::-webkit-scrollbar-thumb": {
                                backgroundColor: colors.grey[300],
                                borderRadius: "6px",
                            },
                            scrollbarWidth: "thin",
                            scrollbarColor: `${colors.grey[300]} ${colors.grey[100]}`,
                        },
                    }}
                >
                    {/* Search Input */}
                    <Box sx={{ p: 2, borderBottom: `1px solid ${colors.grey[100]}` }}>
                        <input
                            type="text"
                            placeholder="Search tags..."
                            value={searchValue}
                            onChange={(e) => setSearchValue(e.target.value)}
                            style={{
                                width: "100%",
                                padding: "8px 12px",
                                border: `1px solid ${colors.grey[200]}`,
                                borderRadius: "6px",
                                fontSize: "14px",
                                outline: "none",
                            }}
                            autoFocus
                        />
                    </Box>

                    {/* Options List */}
                    <Box>
                        {filteredOptions.length > 0 ? (
                            filteredOptions.map((option) => {
                                const isSelected = selectedValues.includes(option.value);
                                return (
                                    <Box
                                        key={option.value}
                                        onClick={() => handleOptionClick(option.value)}
                                        sx={{
                                            display: "flex",
                                            alignItems: "center",
                                            padding: "12px 16px",
                                            cursor: "pointer",
                                            "&:hover": {
                                                backgroundColor: colors.grey[100],
                                            },
                                            gap: "12px",
                                        }}
                                    >
                                        {/* Custom Checkbox */}
                                        <Box
                                            sx={{
                                                display: "flex",
                                                width: "20px",
                                                height: "20px",
                                                padding: "3.333px 4.167px 5px 4.167px",
                                                justifyContent: "center",
                                                alignItems: "center",
                                                borderRadius: "4px",
                                                backgroundColor: isSelected ? "#0060FF" : "transparent",
                                                border: isSelected ? "none" : `2px solid ${colors.grey[300]}`,
                                                transition: "all 0.2s ease",
                                            }}
                                        >
                                            {isSelected && (
                                                <svg
                                                    width="12"
                                                    height="9"
                                                    viewBox="0 0 12 9"
                                                    fill="none"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                >
                                                    <path
                                                        d="M10.5 1.5L4.5 7.5L1.5 4.5"
                                                        stroke="#fff"
                                                        strokeWidth="2"
                                                        strokeLinecap="round"
                                                        strokeLinejoin="round"
                                                    />
                                                </svg>
                                            )}
                                        </Box>

                                        <Typography
                                            style="body2"
                                            color={
                                                isSelected
                                                    ? colors.grey[900]
                                                    : colors.grey[500]
                                            }
                                        >
                                            {option.label}
                                        </Typography>
                                    </Box>
                                );
                            })
                        ) : (
                            <Box sx={{ px: 2, py: 2 }}>
                                <Typography style="body2" color={colors.grey[500]}>
                                    No tags found
                                </Typography>
                            </Box>
                        )}
                    </Box>
                </Popover>
            </div>
        </div>
    );
};