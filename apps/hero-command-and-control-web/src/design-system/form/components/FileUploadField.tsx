import { useFileUpload } from "@/app/apis/services/filerepository/hooks";
import { Typography } from "@/design-system/components/Typography";
import { colors } from "@/design-system/tokens";
import { Box } from "@mui/material";
import React, { useCallback, useRef, useState } from "react";
import { MdClose, MdOutlineFileUpload } from "react-icons/md";

interface FileUploadFieldProps {
    title?: string;
    placeholder?: string;
    value?: any;
    onChange?: (value: any) => void;
    disabled?: boolean;
    readOnly?: boolean;
    errorMessage?: string;
    required?: boolean;
    accept?: string;
    maxFiles?: number;
    maxFileSize?: number; // in bytes
}

interface UploadedFile {
    id: string;
    name: string;
    size: number;
    type: string;
    url?: string;
    uploadedAt: Date;
}

export const FileUploadField: React.FC<FileUploadFieldProps> = ({
    title,
    placeholder = "Drag and drop files here or click to browse",
    value = [],
    onChange,
    disabled = false,
    readOnly = false,
    errorMessage,
    required = false,
    accept = "image/*,video/*,application/pdf,audio/*",
    maxFiles = 5,
    maxFileSize = 250 * 1024 * 1024, // 250MB default
}) => {
    const [isDragging, setIsDragging] = useState(false);
    const [uploadingFiles, setUploadingFiles] = useState<Set<string>>(new Set());
    const [uploadErrors, setUploadErrors] = useState<string[]>([]);
    const fileInputRef = useRef<HTMLInputElement>(null);
    const { uploadFile, isLoading } = useFileUpload();

    // Parse the current value - it could be an array of file objects or a string
    const currentFiles: UploadedFile[] = Array.isArray(value) ? value : [];

    const handleFileSelect = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
        if (!event.target.files || disabled || readOnly) return;

        const files = Array.from(event.target.files);
        await processFiles(files);

        // Clear the input
        if (fileInputRef.current) {
            fileInputRef.current.value = '';
        }
    }, [disabled, readOnly]);

    const processFiles = useCallback(async (files: File[]) => {
        const validFiles = files.filter(file => {
            // Check file type against accept pattern
            if (accept) {
                const isValidType = accept.split(',').some(acceptType => {
                    const trimmedType = acceptType.trim();
                    if (trimmedType === 'image/*') {
                        return file.type.startsWith('image/');
                    } else if (trimmedType === 'video/*') {
                        return file.type.startsWith('video/');
                    } else if (trimmedType === 'audio/*') {
                        return file.type.startsWith('audio/');
                    } else if (trimmedType.includes('/')) {
                        return file.type === trimmedType;
                    } else if (trimmedType.startsWith('.')) {
                        return file.name.toLowerCase().endsWith(trimmedType.toLowerCase());
                    }
                    return false;
                });

                if (!isValidType) {
                    setUploadErrors(prev => [...prev, `${file.name} is not a supported file type. Accepted types: ${accept}`]);
                    return false;
                }
            }

            // Check file size
            if (file.size > maxFileSize) {
                setUploadErrors(prev => [...prev, `${file.name} exceeds the maximum file size of ${(maxFileSize / 1024 / 1024).toFixed(0)}MB`]);
                return false;
            }
            return true;
        });

        if (validFiles.length === 0) return;

        // Check if adding these files would exceed maxFiles
        if (currentFiles.length + validFiles.length > maxFiles) {
            setUploadErrors(prev => [...prev, `Maximum ${maxFiles} files allowed`]);
            return;
        }

        // Clear previous errors
        setUploadErrors([]);

        // Keep track of successfully uploaded files in this batch
        const newlyUploadedFiles: UploadedFile[] = [];

        for (const file of validFiles) {
            const tempId = `temp-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

            // Add to uploading state
            setUploadingFiles(prev => new Set(prev).add(tempId));

            try {
                const result = await uploadFile(file);

                if (result.success && result.fileId) {
                    const uploadedFile: UploadedFile = {
                        id: result.fileId,
                        name: file.name,
                        size: file.size,
                        type: file.type,
                        uploadedAt: new Date(),
                    };

                    // Add to our batch of newly uploaded files
                    newlyUploadedFiles.push(uploadedFile);
                } else {
                    setUploadErrors(prev => [...prev, `Failed to upload ${file.name}: ${result.error}`]);
                }
            } catch (error) {
                setUploadErrors(prev => [...prev, `Failed to upload ${file.name}: ${error instanceof Error ? error.message : 'Unknown error'}`]);
            } finally {
                setUploadingFiles(prev => {
                    const newSet = new Set(prev);
                    newSet.delete(tempId);
                    return newSet;
                });
            }
        }

        // Only call onChange once with all newly uploaded files
        if (newlyUploadedFiles.length > 0) {
            const allFiles = [...currentFiles, ...newlyUploadedFiles];
            onChange?.(allFiles);
        }
    }, [currentFiles, maxFiles, maxFileSize, uploadFile, onChange, accept]);

    const handleDragEnter = useCallback((event: React.DragEvent<HTMLDivElement>) => {
        event.preventDefault();
        event.stopPropagation();
        if (!disabled && !readOnly) {
            setIsDragging(true);
        }
    }, [disabled, readOnly]);

    const handleDragOver = useCallback((event: React.DragEvent<HTMLDivElement>) => {
        event.preventDefault();
        event.stopPropagation();
        if (!disabled && !readOnly) {
            setIsDragging(true);
        }
    }, [disabled, readOnly]);

    const handleDragLeave = useCallback((event: React.DragEvent<HTMLDivElement>) => {
        event.preventDefault();
        event.stopPropagation();
        setIsDragging(false);
    }, []);

    const handleDrop = useCallback(async (event: React.DragEvent<HTMLDivElement>) => {
        event.preventDefault();
        event.stopPropagation();
        setIsDragging(false);

        if (disabled || readOnly) return;

        const files = Array.from(event.dataTransfer.files);
        await processFiles(files);
    }, [disabled, readOnly, processFiles]);

    const handleClick = useCallback(() => {
        if (!disabled && !readOnly) {
            fileInputRef.current?.click();
        }
    }, [disabled, readOnly]);

    const handleRemoveFile = useCallback((fileId: string) => {
        if (readOnly) return;

        const newFiles = currentFiles.filter(file => file.id !== fileId);
        onChange?.(newFiles);
    }, [currentFiles, onChange, readOnly]);

    const formatFileSize = (bytes: number): string => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    const isUploading = uploadingFiles.size > 0 || isLoading;

    return (
        <Box>
                  {/* Field Title */}
      {title && (
        <Typography style="caps1" color={colors.grey[500]} className="mb-1">
          {required ? `${title} *` : title}
        </Typography>
      )}

            {/* Upload Zone */}
            <Box
                sx={{
                    border: isDragging
                        ? `2px dashed ${colors.blue[600]}`
                        : `2px dashed ${colors.grey[300]}`,
                    borderRadius: "8px",
                    padding: "24px",
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    justifyContent: "center",
                    cursor: disabled || readOnly ? "not-allowed" : "pointer",
                    backgroundColor: isDragging
                        ? colors.blue[50]
                        : colors.grey[50],
                    opacity: disabled || readOnly ? 0.5 : 1,
                    transition: "all 0.2s ease-in-out",
                    "&:hover": !disabled && !readOnly ? {
                        borderColor: colors.grey[400],
                        backgroundColor: colors.grey[100],
                    } : {},
                }}
                onClick={handleClick}
                onDragEnter={handleDragEnter}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
            >
                {/* Hidden file input */}
                <input
                    type="file"
                    ref={fileInputRef}
                    style={{ display: "none" }}
                    multiple
                    accept={accept}
                    onChange={handleFileSelect}
                    disabled={disabled || readOnly}
                />

                {/* Upload zone content */}
                <MdOutlineFileUpload
                    style={{
                        fontSize: 48,
                        color: isUploading ? colors.blue[600] : colors.grey[400]
                    }}
                />
                <Box sx={{ mt: 1, textAlign: 'center' }}>
                    <Typography style="body1" color={colors.grey[900]}>
                        {isUploading ? "Uploading..." : "Add Files"}
                    </Typography>
                </Box>
                <Box sx={{ mt: 1.5 }}>
                    <Typography style="body3" color={colors.grey[500]}>
                        {placeholder}
                    </Typography>
                </Box>
                <Box sx={{ mt: 1 }}>
                    <Typography style="body4" color={colors.grey[400]}>
                        Max {maxFiles} files, {formatFileSize(maxFileSize)} per file
                    </Typography>
                </Box>
            </Box>

            {/* Uploaded Files List */}
            {currentFiles.length > 0 && (
                <Box sx={{ mt: 2 }}>
                    <Typography style="body2" color={colors.grey[700]} className="mb-1">
                        Uploaded Files ({currentFiles.length}/{maxFiles})
                    </Typography>
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                        {currentFiles.map((file) => (
                            <Box
                                key={file.id}
                                sx={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'space-between',
                                    p: 1.5,
                                    backgroundColor: colors.grey[50],
                                    borderRadius: 1,
                                    border: `1px solid ${colors.grey[200]}`,
                                }}
                            >
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flex: 1 }}>
                                    <Typography style="body3" color={colors.grey[900]}>
                                        {file.name}
                                    </Typography>
                                    <Typography style="body4" color={colors.grey[500]}>
                                        ({formatFileSize(file.size)})
                                    </Typography>
                                </Box>
                                {!readOnly && (
                                    <Box
                                        sx={{
                                            cursor: 'pointer',
                                            p: 0.5,
                                            borderRadius: 0.5,
                                            '&:hover': {
                                                backgroundColor: colors.grey[200],
                                            },
                                        }}
                                        onClick={() => handleRemoveFile(file.id)}
                                    >
                                        <MdClose size={16} color={colors.grey[500]} />
                                    </Box>
                                )}
                            </Box>
                        ))}
                    </Box>
                </Box>
            )}

            {/* Uploading Files */}
            {uploadingFiles.size > 0 && (
                <Box sx={{ mt: 2 }}>
                    <Typography style="body2" color={colors.grey[700]} className="mb-1">
                        Uploading...
                    </Typography>
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                        {Array.from(uploadingFiles).map((tempId) => (
                            <Box
                                key={tempId}
                                sx={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    p: 1.5,
                                    backgroundColor: colors.blue[50],
                                    borderRadius: 1,
                                    border: `1px solid ${colors.blue[200]}`,
                                }}
                            >
                                <Typography style="body3" color={colors.blue[700]}>
                                    Uploading file...
                                </Typography>
                            </Box>
                        ))}
                    </Box>
                </Box>
            )}

            {/* Error Messages */}
            {uploadErrors.length > 0 && (
                <Box sx={{ mt: 2 }}>
                    {uploadErrors.map((error, index) => (
                        <Box
                            key={index}
                            sx={{
                                p: 1.5,
                                backgroundColor: colors.rose[50],
                                border: `1px solid ${colors.rose[200]}`,
                                borderRadius: 1,
                                mb: 1,
                            }}
                        >
                            <Typography style="body3" color={colors.rose[600]}>
                                {error}
                            </Typography>
                        </Box>
                    ))}
                </Box>
            )}

            {/* Field Error Message */}
            {errorMessage && (
                <Box sx={{ mt: 1 }}>
                    <Typography style="body4" color={colors.rose[600]}>
                        {errorMessage}
                    </Typography>
                </Box>
            )}
        </Box>
    );
}; 