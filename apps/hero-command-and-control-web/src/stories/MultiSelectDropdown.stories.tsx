import { Meta, StoryObj } from '@storybook/react';
import { useState } from 'react';
import { MultiSelectDropdown, MultiSelectDropdownOption } from '../design-system/components/MultiSelectDropdown';

const meta: Meta<typeof MultiSelectDropdown> = {
    title: 'Design System/Components/MultiSelectDropdown',
    component: MultiSelectDropdown,
    parameters: {
        layout: 'centered',
        docs: {
            description: {
                component: 'A multi-select dropdown component with search functionality, checkboxes, and label display for selected items.',
            },
        },
    },
    tags: ['autodocs'],
    argTypes: {
        title: {
            control: 'text',
            description: 'Title displayed above the dropdown',
        },
        placeholder: {
            control: 'text',
            description: 'Placeholder text shown when no items are selected',
        },
        options: {
            control: 'object',
            description: 'Array of options available for selection',
        },
        selectedValues: {
            control: 'object',
            description: 'Array of currently selected option values',
        },
        onChange: {
            action: 'changed',
            description: 'Callback function called when an option is selected/deselected',
        },
    },
};

export default meta;

type Story = StoryObj<typeof MultiSelectDropdown>;

// Sample options data
const tagOptions: MultiSelectDropdownOption[] = [
    { value: "CLERY", label: "CLERY" },
    { value: "NIBRS", label: "NIBRS" },
    { value: "Title IX", label: "Title IX" },
    { value: "Use of Force", label: "Use of Force" },
    { value: "Pursuit", label: "Pursuit" },
    { value: "Juvenile Involved", label: "Juvenile Involved" },
    { value: "Confidential", label: "Confidential" },
    { value: "Repeat Offender", label: "Repeat Offender" },
    { value: "Pattern Crime", label: "Pattern Crime" },
    { value: "FSL", label: "FSL" },
    { value: "Transient Involved", label: "Transient Involved" },
];

const categoryOptions: MultiSelectDropdownOption[] = [
    { value: "frontend", label: "Frontend" },
    { value: "backend", label: "Backend" },
    { value: "database", label: "Database" },
    { value: "api", label: "API" },
    { value: "ui-ux", label: "UI/UX" },
    { value: "testing", label: "Testing" },
    { value: "documentation", label: "Documentation" },
    { value: "security", label: "Security" },
];

// Basic MultiSelectDropdown story
export const Basic: Story = {
    args: {
        title: 'Select Tags',
        placeholder: 'Search and select tags',
        options: tagOptions,
    },
    render: (args) => {
        const [selectedValues, setSelectedValues] = useState<string[]>([]);

        const handleChange = (value: string) => {
            setSelectedValues(prev => {
                if (prev.includes(value)) {
                    return prev.filter(v => v !== value);
                } else {
                    return [...prev, value];
                }
            });
        };

        return (
            <div style={{ width: '400px' }}>
                <MultiSelectDropdown
                    {...args}
                    selectedValues={selectedValues}
                    onChange={handleChange}
                />
            </div>
        );
    },
};

// MultiSelectDropdown with pre-selected values
export const WithPreselectedValues: Story = {
    args: {
        title: 'Case Tags',
        placeholder: 'Search and select tags',
        options: tagOptions,
    },
    render: (args) => {
        const [selectedValues, setSelectedValues] = useState<string[]>(['CLERY', 'NIBRS', 'Title IX']);

        const handleChange = (value: string) => {
            setSelectedValues(prev => {
                if (prev.includes(value)) {
                    return prev.filter(v => v !== value);
                } else {
                    return [...prev, value];
                }
            });
        };

        return (
            <div style={{ width: '400px' }}>
                <MultiSelectDropdown
                    {...args}
                    selectedValues={selectedValues}
                    onChange={handleChange}
                />
            </div>
        );
    },
};

// MultiSelectDropdown with different options
export const DifferentOptions: Story = {
    args: {
        title: 'Project Categories',
        placeholder: 'Select project categories',
        options: categoryOptions,
    },
    render: (args) => {
        const [selectedValues, setSelectedValues] = useState<string[]>(['frontend', 'ui-ux']);

        const handleChange = (value: string) => {
            setSelectedValues(prev => {
                if (prev.includes(value)) {
                    return prev.filter(v => v !== value);
                } else {
                    return [...prev, value];
                }
            });
        };

        return (
            <div style={{ width: '400px' }}>
                <MultiSelectDropdown
                    {...args}
                    selectedValues={selectedValues}
                    onChange={handleChange}
                />
            </div>
        );
    },
};

// MultiSelectDropdown without title
export const WithoutTitle: Story = {
    args: {
        title: '',
        placeholder: 'Choose options',
        options: tagOptions,
    },
    render: (args) => {
        const [selectedValues, setSelectedValues] = useState<string[]>([]);

        const handleChange = (value: string) => {
            setSelectedValues(prev => {
                if (prev.includes(value)) {
                    return prev.filter(v => v !== value);
                } else {
                    return [...prev, value];
                }
            });
        };

        return (
            <div style={{ width: '400px' }}>
                <MultiSelectDropdown
                    {...args}
                    selectedValues={selectedValues}
                    onChange={handleChange}
                />
            </div>
        );
    },
};

// MultiSelectDropdown with many selections to show wrapping
export const ManySelections: Story = {
    args: {
        title: 'All Available Tags',
        placeholder: 'Search and select tags',
        options: tagOptions,
    },
    render: (args) => {
        const [selectedValues, setSelectedValues] = useState<string[]>([
            'CLERY', 'NIBRS', 'Title IX', 'Use of Force', 'Pursuit', 'Juvenile Involved', 'Confidential'
        ]);

        const handleChange = (value: string) => {
            setSelectedValues(prev => {
                if (prev.includes(value)) {
                    return prev.filter(v => v !== value);
                } else {
                    return [...prev, value];
                }
            });
        };

        return (
            <div style={{ width: '400px' }}>
                <MultiSelectDropdown
                    {...args}
                    selectedValues={selectedValues}
                    onChange={handleChange}
                />
            </div>
        );
    },
};

// MultiSelectDropdown in a narrow container
export const NarrowContainer: Story = {
    args: {
        title: 'Tags',
        placeholder: 'Select tags',
        options: tagOptions,
    },
    render: (args) => {
        const [selectedValues, setSelectedValues] = useState<string[]>(['CLERY', 'Use of Force']);

        const handleChange = (value: string) => {
            setSelectedValues(prev => {
                if (prev.includes(value)) {
                    return prev.filter(v => v !== value);
                } else {
                    return [...prev, value];
                }
            });
        };

        return (
            <div style={{ width: '250px' }}>
                <MultiSelectDropdown
                    {...args}
                    selectedValues={selectedValues}
                    onChange={handleChange}
                />
            </div>
        );
    },
};