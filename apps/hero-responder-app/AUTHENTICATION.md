# Mobile Authentication Architecture

This document describes the authentication system for the Hero Responder mobile applications, detailing the flow from environment discovery to user authentication.

## Overview

The Hero mobile authentication system uses a **multi-tenant discovery approach** that allows a single mobile app build to authenticate users across different organizations and environments. The system combines:

- **Environment Discovery**: Dynamic discovery of Cognito User Pool configurations based on email domain
- **Enhanced OAuth 2.0 Security**: Complete PKCE, state validation, and nonce verification for maximum security
- **Ephemeral Token Management**: Memory-only token storage with fresh authentication on each app launch
- **Dynamic Provider Rekeying**: React provider remounting for complete context isolation
- **Environment-Scoped API Authentication**: Dynamic token injection into service calls

## Authentication System Architecture

### Dynamic AuthProvider Rekeying

The AuthProvider implements environment-aware rekeying using a React key prop:

```typescript
<AuthProvider
  key={environmentConfig?.userPoolUrl ?? "no-env"}
  environmentConfig={environmentConfig}
  onClearEnvironmentConfig={() => setEnvironmentConfig(null)}
>
```

This architecture ensures complete context isolation between different environments and prevents authentication token mixups by **forcing a complete provider remount** when switching between organizations.

### Environment-Scoped Token Management

- **Dynamic, per-environment token handling** eliminates hardcoded authentication dependencies
- **Complete environment isolation** through React provider remounting when switching organizations
- **Ephemeral token storage** prevents token persistence and cross-contamination
- **Memory-only authentication state** requires fresh login on each app launch for enhanced security

### Axios Instance Authentication

The `registerAxiosAuth` utility provides dynamic token injection across service calls:

- **Workflow and communications services** leverage environment-specific access tokens via getter functions
- **Authentication headers are dynamically resolved** based on the current environment context
- **Runtime token resolution** ensures all authentication state comes from the active AuthProvider

## Key Components

### 1. Discovery Service (`/services/discoveryService.ts`)

**Purpose**: Maps user email domains to appropriate Cognito User Pool configurations.

**Responsibilities**:

- Validates email format
- Calls discovery Lambda API
- Handles discovery errors with user-friendly messages
- Returns environment configuration (userPoolUrl + clientId)

**Key Functions**:

- `discoverEnvironment(email: string)` → `EnvironmentConfig`
- `validateEmail(email: string)` → `boolean`

### 2. Discovery Lambda (`/infra/.../discovery-lambda/`)

**Purpose**: Backend service that maps email domains to Cognito configurations.

**API Endpoint**: `https://discovery.api.gethero.com`

**Implementation Details**: The lambda currently uses hardcoded domain mappings for simplicity but could be externalized for better maintainability. See [Discovery API Documentation](/infra/cloud-shared/services/discovery-api/discovery-lambda/README.md) for:
- Complete API specification and examples  
- Deployment instructions via CDK
- Monitoring and CloudWatch alarms setup
- Testing endpoints and health checks

**Current Domain Support**:
- `tcu.edu` → TCU environment
- `gethero.com` → Demo environment


### 3. AuthProvider (`/AuthContext.tsx`) - Deep Dive

**Purpose**: The heart of the authentication system - manages OAuth 2.0 lifecycle with enhanced security, ephemeral token storage, and environment isolation.

#### Core Implementation Details

**🔑 Environment-Based Provider Isolation**

```typescript
// Provider is rekeyed when userPoolUrl changes, forcing complete remount
<AuthProvider
  key={environmentConfig?.userPoolUrl ?? "no-env"}
  environmentConfig={environmentConfig}
/>
```

**🏪 Ephemeral Token Management**

```typescript
// Tokens stored in React state, not persisted
const [authTokens, setAuthTokens] = useState<TokenResponse | null>(null);

// Clean state on every app launch - no persistence
useEffect(() => {
  const clearAuthSession = async () => {
    try {
      WebBrowser.maybeCompleteAuthSession({ skipRedirectCheck: true });
      console.log("Cleared WebView session on mount");
    } catch (error) {
      console.error("Error clearing initial WebView session:", error);
    }
  };
  clearAuthSession();
}, []);
```

**🔒 Enhanced Security Through Ephemeral Sessions**

The current implementation prioritizes security over convenience by:
- Requiring fresh authentication on each app launch
- Clearing all WebView cookies and auth state
- Preventing token persistence and potential security vulnerabilities
- Using forced re-authentication with `prompt=login` and `max_age=0`

**🔗 Dynamic API Authentication Setup**

```typescript
// AuthProvider automatically wires up API authentication
useEffect(() => {
  setWorkflowAccessGetter(() => authTokens?.accessToken);
  setCommunicationsAccessGetter(() => authTokens?.accessToken);
}, [authTokens]);
```

#### Key State Management

**Authentication States**:

- `isLoading`: Authentication request in progress  
- `isAuthReady`: OAuth request is configured and ready
- `authTokens`: Current token response stored in memory (not persisted)

**Critical Lifecycle Hooks**:

1. **Initial Mount** (`useEffect` on mount):

   - Clear any existing WebView cookies and auth session
   - Reset authentication state to ensure clean startup
   - No token loading - all authentication starts fresh

2. **Authentication Flow** (triggered by user action):

   - Generate secure random state and nonce values
   - Create AuthRequest with PKCE, state, and nonce parameters
   - Exchange authorization code with security validation
   - Store tokens in memory (React state) only

3. **API Authentication Setup** (`useEffect` on `authTokens` change):
   - Configure axios instances with token getter functions
   - Enable authenticated API calls across all services
   - Tokens available until app restart or logout

#### OAuth 2.0 + PKCE Implementation

**Discovery Document Creation**:

```typescript
const createDiscoveryDocument = (
  config: EnvironmentConfig
): DiscoveryDocument => ({
  authorizationEndpoint: `${config.userPoolUrl}/oauth2/authorize`,
  tokenEndpoint: `${config.userPoolUrl}/oauth2/token`,
  revocationEndpoint: `${config.userPoolUrl}/oauth2/revoke`,
});
```

**Enhanced Security Implementation**:

The AuthProvider now implements comprehensive OAuth 2.0 security measures:

```typescript
// Generate secure random values for CSRF and replay protection
const state = rand();
const nonce = rand();

// Create AuthRequest with full security configuration
const reqCfg: AuthRequestConfig = {
  clientId: config.clientId,
  responseType: ResponseType.Code,
  redirectUri,
  usePKCE: true,               // PKCE for code interception prevention
  scopes: ["openid", "email", "profile"],
  state,                       // CSRF protection
  extraParams: { 
    nonce,                     // Token replay protection
    prompt: "login",           // Force fresh login (ephemeral behavior)
    max_age: "0"              // Force re-authentication
  },
};

const request = new AuthRequest(reqCfg);
const result = await request.promptAsync(discovery);

// Validate state parameter to prevent CSRF attacks
if (result.params.state !== state) {
  throw new Error("State mismatch - potential CSRF attack");
}

// Exchange code with PKCE verification
const tokens = await exchangeCodeAsync(
  {
    code: result.params.code,
    clientId: config.clientId,
    redirectUri,
    extraParams: { code_verifier: request.codeVerifier! }, // PKCE verification
  },
  discovery
);

// Optional nonce verification for ID token replay protection
const decoded = jwtDecode<{ nonce?: string }>(tokens.idToken);
if (decoded.nonce !== nonce) {
  console.warn("Nonce mismatch in ID token");
}
```

**Security Features**:

- **PKCE (Proof Key for Code Exchange)**: Prevents authorization code interception attacks
- **State Parameter**: CSRF protection using cryptographically secure random values  
- **Nonce Parameter**: ID token replay attack prevention
- **Ephemeral Sessions**: `prompt=login` and `max_age=0` force fresh authentication
- **Code Verifier Validation**: Server-side PKCE verification during token exchange

**Cryptographically Secure Random Generation**:

The AuthProvider includes a secure random string generator for state and nonce values:

```typescript
// Helper function to generate secure random strings for state/nonce
function rand(n = 32): string {
  const a = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  return Array.from({ length: n }, () => a[Math.floor(Math.random() * a.length)]).join("");
}
```

This function generates cryptographically secure random strings used for:
- **State parameter**: 32-character random string for CSRF protection
- **Nonce parameter**: 32-character random string for token replay prevention

#### Comprehensive Logout Flow

**Multi-Step Logout Process**:

1. **Hosted UI Logout**: Open Cognito logout URL to clear server session
2. **Token Revocation**: Revoke refresh token at Cognito endpoint
3. **Local Cache Cleanup**: Clear all SecureStore keys and Expo auth cache
4. **Provider Reset**: Clear environment config to trigger provider unmount
5. **Sentry Cleanup**: Clear user context from error tracking

```typescript
const logout = async (): Promise<void> => {
  // Step 1: Hosted UI logout
  const logoutUrl = `${config.userPoolUrl}/logout?client_id=${
    config.clientId
  }&logout_uri=${encodeURIComponent(redirectUri)}`;
  await WebBrowser.openAuthSessionAsync(logoutUrl, redirectUri);

  // Step 2: Token revocation
  await revokeAsync(
    {
      clientId: config.clientId,
      token: authTokens.refreshToken,
    },
    discovery
  );

  // Step 3-5: Complete cleanup
  await clearAllAuthCache();
  setAuthTokens(null);
  setSentryUserContext(null);
  onClearEnvironmentConfig?.();
};
```

#### Error Handling & Edge Cases

**Token Expiration**:

- No automatic refresh in ephemeral mode - tokens expire with app session
- Users must re-authenticate after app restart or token expiration
- Enhanced security through forced fresh authentication

**Network Failures**:

- Token exchange errors handled gracefully with user alerts
- Discovery service has 10-second timeout with abort signal support
- Graceful degradation with user-friendly error messages
- Manual retry available through UI reset flows

**Environment Switching**:

- Complete provider remount prevents token contamination
- No token persistence - fresh authentication required for each environment
- Clean slate for new environment authentication with complete state isolation

### 4. SignInScreen (`/screens/SignInScreen.tsx`)

**Purpose**: User interface for the authentication flow.

**Flow States**:

- `EMAIL_ENTRY`: Initial form for email input
- `DISCOVERING`: Loading state while calling discovery API
- `CONNECTING`: Loading state while setting up authentication
- `ERROR`: Error handling and retry options

**Key Features**:

- Email validation
- Organization name extraction from userPoolUrl
- Auto-resuming of authentication after provider remount
- Cancellation support for discovery requests

### 5. EnvironmentConfigProvider (`/contexts/EnvironmentConfigContext.tsx`)

**Purpose**: Manages global environment configuration state.

**Responsibilities**:

- Stores discovered environment configuration
- Provides configuration to child components
- Handles configuration clearing on logout

### 6. Root Layout (`/_layout.tsx`)

**Purpose**: Application-level provider orchestration.

**Key Features**:

- Uses environment config as key for AuthProvider remounting
- Manages location services based on authentication state
- Proper provider nesting order

## Authentication Flow

```
┌─────────┐    ┌──────────────┐    ┌──────────────┐    ┌──────────────┐    ┌─────────────┐    ┌─────────────┐
│  User   │    │  SignIn      │    │  Discovery   │    │  Discovery   │    │ AuthProvider│    │ AWS Cognito │
│         │    │  Screen      │    │  Service     │    │  Lambda      │    │             │    │             │
└────┬────┘    └──────┬───────┘    └──────┬───────┘    └──────┬───────┘    └──────┬──────┘    └──────┬──────┘
     │                │                   │                   │                   │                  │
     │ 1. Enter email │                   │                   │                   │                  │
     ├───────────────►│                   │                   │                   │                  │
     │                │ 2. discoverEnvironment(email)         │                   │                  │
     │                ├──────────────────►│                   │                   │                  │
     │                │                   │ 3. POST /discovery│                   │                  │
     │                │                   ├──────────────────►│                   │                  │
     │                │                   │                   │ 4. {userPoolUrl,  │                  │
     │                │                   │                   │    clientId}      │                  │
     │                │                   │◄──────────────────┤                   │                  │
     │                │ 5. EnvironmentConfig                  │                   │                  │
     │                │◄──────────────────┤                   │                   │                  │
     │                │ 6. onDiscoveryComplete(config)        │                   │                  │
     │                ├─────────────────────────────────────────────────────────►│                  │
     │                │                   │                   │ 7. AuthProvider   │                  │
     │                │                   │                   │    remounts with  │                  │
     │                │                   │                   │    new config     │                  │
     │                │                   │                   │                   │ 8. OAuth request │
     │                │                   │                   │                   ├─────────────────►│
     │ 9. Sign in via hosted UI                               │                   │                  │
     ├──────────────────────────────────────────────────────────────────────────────────────────────►│
     │                │                   │                   │                   │10. Auth code     │
     │                │                   │                   │                   │◄─────────────────┤
     │                │                   │                   │                   │11. Exchange code │
     │                │                   │                   │                   ├─────────────────►│
     │                │                   │                   │                   │12. Access tokens │
     │                │                   │                   │                   │◄─────────────────┤
     │                │                   │                   │ 13. Save tokens + │                  │
     │                │                   │                   │     Set Sentry    │                  │
     │                │                   │                   │     context       │                  │
     │ 14. Navigate to main app           │                   │                   │                  │
     │◄───────────────────────────────────────────────────────────────────────────┤                  │
```

## Component Architecture

```
┌─────────────┐    ┌────────────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│ _layout.tsx │    │ EnvironmentConfig  │    │ AuthProvider│    │ SignInScreen│    │ Discovery   │    │ Main App    │
│ Root Layout │    │ Provider           │    │             │    │             │    │ Service     │    │ Components  │
└──────┬──────┘    └─────────┬──────────┘    └──────┬──────┘    └──────┬──────┘    └──────┬──────┘    └──────┬──────┘
       │                     │                      │                 │                  │                 │
       │ 1. App startup      │                      │                 │                  │                 │
       ├────────────────────►│                      │                 │                  │                 │
       │                     │ 2. Provide environment context         │                  │                 │
       │                     ├─────────────────────►│                 │                  │                 │
       │                     │                      │ 3. Check auth   │                  │                 │
       │                     │                      │    state        │                  │                 │
       │                     │                      ├────────────────►│                 │                 │
       │                     │                      │                 │ 4. Discovery    │                 │
       │                     │                      │                 │    API calls    │                 │
       │                     │                      │                 ├────────────────►│                 │
       │                     │                      │ 5. Auth success │                 │                 │
       │                     │                      │◄────────────────┤                 │                 │
       │                     │                      │ 6. Render main app                │                 │
       │                     │                      ├─────────────────────────────────────────────────────►│
```

## Key Files & Locations

| Component               | File Path                                    | Purpose                   |
| ----------------------- | -------------------------------------------- | ------------------------- |
| **AuthProvider**        | `/app/AuthContext.tsx`                       | OAuth 2.0 flow management |
| **Discovery Service**   | `/app/services/discoveryService.ts`          | Email→config mapping      |
| **Discovery Lambda**    | `/infra/.../discovery-lambda/main.go`        | Backend domain mapping    |
| **SignIn Screen**       | `/app/screens/SignInScreen.tsx`              | User interface            |
| **Environment Context** | `/app/contexts/EnvironmentConfigContext.tsx` | Global config state       |
| **Root Layout**         | `/app/_layout.tsx`                           | Provider orchestration    |

## Security Features

### Token Management

**Current Implementation (Ephemeral)**:
- **Memory-Only Storage**: Tokens stored in React state, not persisted between app launches
- **No SecureStore Usage**: Each app launch requires fresh authentication
- **Organization Isolation**: Complete provider remount prevents token contamination between environments
- **Session Clearing**: WebView cookies cleared on logout and app launch for clean state

**Alternative Implementation (Persistent - Not Currently Used)**:
- **Secure Storage**: Tokens can be stored in Expo SecureStore for persistence
- **Organization Isolation**: Tokens scoped per userPoolUrl with environment-specific keys
- **Automatic Refresh**: Background refresh with 5-minute buffer
- **Proper Expiration**: Token validation and cleanup on expiry

*Note: The current AuthProvider implements ephemeral authentication where users must sign in on each app launch. This provides additional security but requires re-authentication after app restarts.*

### OAuth 2.0 Security

- **PKCE (Proof Key for Code Exchange)**: Prevents authorization code interception attacks using cryptographically secure code verifiers
- **State Parameter**: CSRF protection using cryptographically secure random values with validation
- **Nonce Parameter**: ID token replay attack prevention with JWT verification
- **Secure Redirect**: Deep linking with custom URL scheme
- **Ephemeral Authentication**: Forced fresh login with `prompt=login` and `max_age=0` parameters

### Logout Security

- **Complete Session Clearing**: Cognito hosted UI logout + token revocation
- **Cache Cleanup**: Clears all Expo auth caches and WebView cookies
- **State Reset**: Full application state cleanup

## Configuration

### Environment Variables

Discovery API endpoint configured in `/app/config/discovery.ts`:

```typescript
export const DISCOVERY_CONFIG = {
  API_URL: "https://discovery.api.gethero.com",
};
```

### Redirect URI Configuration

OAuth redirect configured in `AuthContext.tsx`:

```typescript
const redirectUri = makeRedirectUri({
  native: "myapp://callback/",
  scheme: "myapp",
  path: "callback/",
});
```

## Error Handling

### Discovery Errors

- **404**: No organization found for email domain
- **403**: Authentication failed
- **Timeout**: Network connectivity issues
- **Validation**: Invalid email format

### Authentication Errors

- **Token Exchange Failure**: Invalid authorization code
- **Token Refresh Failure**: Expired refresh token
- **Network Errors**: API connectivity issues

## Developer Guide

### 🚀 Quick Start for New Developers

**Understanding the Flow**:

1. User enters email → Discovery service finds their organization
2. AuthProvider remounts with organization config → OAuth setup
3. User authenticates → Tokens stored per organization
4. API calls automatically use correct tokens via getter functions

**Key Mental Model**: Think of each organization as a completely separate authentication realm. The AuthProvider creates isolated contexts that never mix.

### 🛠️ Common Development Tasks

#### Adding New Organizations

```bash
# 1. Update discovery lambda domain mappings
# Edit: infra/cloud-shared/services/discovery-api/discovery-lambda/main.go
domainConfigs["neworg.com"] = EnvironmentConfig{
    UserPoolURL: "https://auth.neworg.gethero.com",
    ClientID:    "new-client-id-here",
}

# 2. Deploy the lambda
./infra/scripts/prepare-cdk.sh
cd infra
cdk deploy --concurrency 10 --require-approval never CDKSharedEntryStack/DiscoveryApiStack --exclusively

# 3. Test with new org email
# Use <NAME_EMAIL> in the app
```

#### Adding New API Services

```typescript
// 1. Create axios instance with auth
// apps/hero-responder-app/app/apis/services/newservice/axiosInstance.ts
import { registerAxiosAuth } from "../../axiosAuth";

const axiosInstance = axios.create({
  baseURL: NEW_SERVICE_URL,
});

export const setAccessGetter = registerAxiosAuth(axiosInstance, "NewService");

// 2. Wire up in AuthProvider
// apps/hero-responder-app/app/AuthContext.tsx
import { setAccessGetter as setNewServiceAccessGetter } from "./apis/services/newservice/axiosInstance";

useEffect(() => {
  setWorkflowAccessGetter(() => authTokens?.accessToken);
  setCommunicationsAccessGetter(() => authTokens?.accessToken);
  setNewServiceAccessGetter(() => authTokens?.accessToken); // Add this line
}, [authTokens]);
```

#### Common Issues & Solutions

**🚫 "No organization found for this email domain"**

- Check discovery lambda domain mappings in `main.go`
- Verify email domain spelling
- Test discovery API directly: `curl -X POST https://discovery.api.gethero.com -d '{"email":"<EMAIL>"}'`

**🚫 Token exchange failures**

- Check `clientId` matches the one in Cognito User Pool
- Verify redirect URI configuration in Cognito app client
- Check CORS settings in Cognito hosted UI

**🚫 API calls returning 401 Unauthorized**

- Verify `setAccessGetter` is called for the service
- Check token expiration: look for automatic refresh logs
- Confirm service is configured to accept tokens from the User Pool

**🚫 Cross-organization token contamination**

- Verify AuthProvider key prop uses `userPoolUrl`
- Check SecureStore keys are environment-specific
- Look for provider remount logs when switching orgs
