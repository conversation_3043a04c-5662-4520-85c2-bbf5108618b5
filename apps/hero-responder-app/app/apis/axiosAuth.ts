import { AxiosInstance } from 'axios';

/**
 * Registers dynamic authentication for an axios instance.
 * Allows setting a getter function that provides the current access token.
 * 
 * @param axiosInstance - The axios instance to add auth to
 * @param name - Name for debugging (optional)
 * @returns object with setAccessGetter and setRefreshCallback functions
 */
export const registerAxiosAuth = (axiosInstance: AxiosInstance, _name?: string) => {
  let accessGetter = (): string | undefined => undefined;
  let refreshCallback: (() => Promise<boolean>) | null = null;
  let isRefreshing = false;
  let failedQueue: Array<{
    resolve: (value: any) => void;
    reject: (error: any) => void;
  }> = [];
  
  const processQueue = (error: any, token: string | null = null) => {
    failedQueue.forEach(prom => {
      if (error) {
        prom.reject(error);
      } else {
        prom.resolve(token);
      }
    });
    failedQueue = [];
  };
  
  const setAccessGetter = (fn: () => string | undefined) => { 
    accessGetter = fn; 
  };
  
  const setRefreshCallback = (fn: () => Promise<boolean>) => {
    refreshCallback = fn;
  };
  
  axiosInstance.interceptors.request.use((config) => {
    const token = accessGetter();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  }, (error) => Promise.reject(error));
  
  // Response interceptor to handle 401 errors and refresh token
  axiosInstance.interceptors.response.use(
    (response) => response,
    async (error) => {
      const originalRequest = error.config;
      
      if (error.response?.status === 401 && !originalRequest._retry && refreshCallback) {
        if (isRefreshing) {
          // If already refreshing, queue this request
          return new Promise((resolve, reject) => {
            failedQueue.push({ resolve, reject });
          }).then(token => {
            originalRequest.headers.Authorization = `Bearer ${token}`;
            return axiosInstance(originalRequest);
          }).catch(err => {
            return Promise.reject(err);
          });
        }
        
        originalRequest._retry = true;
        isRefreshing = true;
        
        try {
          const refreshSuccess = await refreshCallback();
          
          if (refreshSuccess) {
            const newToken = accessGetter();
            processQueue(null, newToken);
            originalRequest.headers.Authorization = `Bearer ${newToken}`;
            return axiosInstance(originalRequest);
          } else {
            processQueue(error, null);
            return Promise.reject(error);
          }
        } catch (refreshError) {
          processQueue(refreshError, null);
          return Promise.reject(refreshError);
        } finally {
          isRefreshing = false;
        }
      }
      
      return Promise.reject(error);
    }
  );
  
  return { setAccessGetter, setRefreshCallback };
};