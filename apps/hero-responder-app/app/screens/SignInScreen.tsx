import { router } from "expo-router";
import React, { useEffect, useRef, useState } from "react";
import {
  ActivityIndicator,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
  useWindowDimensions,
} from "react-native";
import Svg, { Defs, LinearGradient, Path, Stop } from "react-native-svg";
import { getColor } from "../(app)/V2/constants/colors";
import { useAuth } from "../AuthContext";
import { useDarkMode } from "../DarkModeContext";
import { useEnvironmentConfig } from "../contexts/EnvironmentConfigContext";
import {
  discoverEnvironment,
  DiscoveryError,
  EnvironmentConfig,
  validateEmail,
} from "../services/discoveryService";

const HeroIcon = () => (
  <Svg width={42.667} height={48} viewBox="0 0 27 33" fill="none">
    <Path
      d="M11.707 0.279863C12.6445 -0.0932459 13.6915 -0.0933297 14.6289 0.279863L24.4941 4.2076C25.6063 4.65055 26.3349 5.71799 26.335 6.90486V13.3238C26.3349 19.9367 22.93 26.0935 17.3027 29.6558L13.8535 31.8394C13.4356 32.104 12.9004 32.1039 12.4824 31.8394L9.03223 29.6558C3.40493 26.0935 8.29607e-05 19.9367 0 13.3238V6.90486C7.00484e-05 5.71803 0.728737 4.65058 1.84082 4.2076L11.707 0.279863ZM22.8135 8.29451C22.8133 7.78109 22.2898 7.43343 21.8164 7.6324L15.2285 10.4019C13.9111 10.9557 12.4258 10.9557 11.1084 10.4019L4.52051 7.6324C4.04715 7.43343 3.52361 7.78109 3.52344 8.29451V13.3267C3.52358 18.8277 6.31642 23.9493 10.9316 26.9127L13.1689 28.3492L15.4053 26.9127C20.0205 23.9493 22.8133 18.8277 22.8135 13.3267V8.29451ZM13.0693 2.62068C11.6611 2.62069 10.5195 3.76227 10.5195 5.17049C10.5196 6.57867 11.6611 7.72028 13.0693 7.72029C14.4775 7.72029 15.6191 6.57867 15.6191 5.17049C15.6191 3.76226 14.4776 2.62068 13.0693 2.62068Z"
      fill="url(#paint0_linear_3216_34853)"
    />
    <Defs>
      <LinearGradient
        id="paint0_linear_3216_34853"
        x1="13.1675"
        y1="32.0378"
        x2="13.1675"
        y2="0"
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#0148BE" />
        <Stop offset="0.434672" stopColor="#005FDD" />
        <Stop offset="1" stopColor="#01B9FF" />
      </LinearGradient>
    </Defs>
  </Svg>
);

interface EmailEntryFormProps {
  email: string;
  error: string;
  onEmailChange: (text: string) => void;
  onContinue: () => void;
}

function EmailEntryForm({
  email,
  error,
  onEmailChange,
  onContinue,
}: EmailEntryFormProps) {
  const { isDarkMode } = useDarkMode();

  return (
    <>
      <Text style={[styles.title, { color: getColor("gray.900", isDarkMode) }]}>
        Welcome to Hero
      </Text>
      <Text
        style={[styles.subtitle, { color: getColor("gray.500", isDarkMode) }]}
      >
        Enter your email to get started
      </Text>

      <View style={styles.inputContainer}>
        <Text
          style={[
            styles.inputLabel,
            { color: getColor("gray.900", isDarkMode) },
          ]}
        >
          Email
        </Text>
        <TextInput
          style={[
            styles.emailInput,
            {
              backgroundColor: getColor("background", isDarkMode),
              borderColor: error
                ? getColor("rose.600", isDarkMode)
                : getColor("blue.600", isDarkMode),
              color: getColor("gray.900", isDarkMode),
            },
          ]}
          placeholder=""
          placeholderTextColor={getColor("gray.500", isDarkMode)}
          value={email}
          onChangeText={onEmailChange}
          keyboardType="email-address"
          autoCapitalize="none"
          autoCorrect={false}
        />

        {error && (
          <Text
            style={[
              styles.errorText,
              { color: getColor("rose.600", isDarkMode) },
            ]}
          >
            {error}
          </Text>
        )}
      </View>

      <TouchableOpacity
        style={[
          styles.nextButton,
          { backgroundColor: getColor("blue.600", isDarkMode) },
        ]}
        onPress={onContinue}
      >
        <Text style={styles.nextButtonText}>Next</Text>
      </TouchableOpacity>
    </>
  );
}

interface LoadingStateProps {
  title: string;
  subtitle: string;
  buttonText?: string;
  onButtonPress?: () => void;
}

function LoadingState({
  title,
  subtitle,
  buttonText,
  onButtonPress,
}: LoadingStateProps) {
  const { isDarkMode } = useDarkMode();

  return (
    <>
      <Text style={[styles.title, { color: getColor("gray.900", isDarkMode) }]}>
        {title}
      </Text>
      <Text
        style={[styles.subtitle, { color: getColor("gray.600", isDarkMode) }]}
      >
        {subtitle}
      </Text>

      <View style={styles.loadingContainer}>
        <ActivityIndicator
          size="large"
          color={getColor("blue.600", isDarkMode)}
        />
      </View>

      {buttonText && onButtonPress && (
        <TouchableOpacity
          style={
            buttonText === "Cancel"
              ? [
                  styles.cancelButton,
                  { borderColor: getColor("gray.300", isDarkMode) },
                ]
              : styles.linkButton
          }
          onPress={onButtonPress}
        >
          <Text
            style={
              buttonText === "Cancel"
                ? [
                    styles.cancelButtonText,
                    { color: getColor("gray.700", isDarkMode) },
                  ]
                : [
                    styles.linkButtonText,
                    { color: getColor("blue.600", isDarkMode) },
                  ]
            }
          >
            {buttonText}
          </Text>
        </TouchableOpacity>
      )}
    </>
  );
}

interface SignInScreenProps {
  onDiscoveryComplete: (config: EnvironmentConfig) => void;
  initialEmail?: string;
}

// Loading states for the discovery flow
enum FlowState {
  EMAIL_ENTRY = "email_entry",
  DISCOVERING = "discovering",
  CONNECTING = "connecting",
  ERROR = "error",
}


const spacing = {
  xxs: 4,
  xs: 8,
  s: 12,
  m: 16,
  l: 24,
  xl: 32,
} as const;

export default function SignInScreen({
  onDiscoveryComplete,
  initialEmail = "",
}: SignInScreenProps) {
  const { isDarkMode } = useDarkMode();
  const { environmentConfig, setEnvironmentConfig } = useEnvironmentConfig();
  const { promptAsync, authTokens, isAuthReady, isLoading } = useAuth();
  const { width } = useWindowDimensions();

  // Responsive logo size - 10% of screen width, min 64px, max 80px
  const logoSize = Math.min(Math.max(width * 0.1, 64), 80);

  const [email, setEmail] = useState(initialEmail);
  const [flowState, setFlowState] = useState<FlowState>(FlowState.EMAIL_ENTRY);
  const [error, setError] = useState<string>("");
  const [userCancelled, setUserCancelled] = useState(false);

  // Track current discovery request for cancellation
  const discoveryAbortController = useRef<AbortController | null>(null);

  // Auto-switch to CONNECTING if we have config but no tokens (app remount scenario)
  // Only auto-resume if user hasn't explicitly cancelled
  useEffect(() => {
    if (environmentConfig && !authTokens && flowState === FlowState.EMAIL_ENTRY && !userCancelled) {
      console.log("Auto-resuming CONNECTING flow after provider remount");
      setFlowState(FlowState.CONNECTING);
    }
  }, [environmentConfig, authTokens, flowState, userCancelled]);

  // Navigate to main app when authentication succeeds
  useEffect(() => {
    if (authTokens) {
      console.log("Authentication successful, navigating to main app");
      setUserCancelled(false); // Reset cancellation flag on success
      router.replace("/(app)/V2/MapScreen");
    }
  }, [authTokens]);

  // Trigger authentication when ready and in CONNECTING state
  useEffect(() => {
    if (flowState === FlowState.CONNECTING && isAuthReady && !isLoading && !authTokens) {
      const triggerAuth = async () => {
        try {
          console.log("Auth is ready, triggering promptAsync");
          const result = await promptAsync();
          
          // Handle different result types
          if (result.type === "cancel") {
            console.log("User cancelled authentication, returning to email entry");
            setUserCancelled(true);
            setFlowState(FlowState.EMAIL_ENTRY);
          } else if (result.type === "error") {
            console.log("Authentication failed with error");
            setError("Authentication failed. Please try again.");
            setFlowState(FlowState.ERROR);
          }
          // Success case is handled automatically by token change effect
        } catch (authError) {
          console.error("Auth prompt failed:", authError);
          setError("Failed to start authentication. Please try again.");
          setFlowState(FlowState.ERROR);
        }
      };

      triggerAuth();
    }
  }, [flowState, isAuthReady, isLoading, authTokens, promptAsync]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (discoveryAbortController.current) {
        discoveryAbortController.current.abort();
      }
    };
  }, []);

  /**
   * Reset the flow back to email entry
   */
  const resetFlow = () => {
    // Cancel any ongoing discovery request
    if (discoveryAbortController.current) {
      discoveryAbortController.current.abort();
      discoveryAbortController.current = null;
    }

    setFlowState(FlowState.EMAIL_ENTRY);
    setError("");
    setEmail("");
    setUserCancelled(false);
    setEnvironmentConfig(null);
  };

  /**
   * Handle continue button press
   */
  const handleContinue = async () => {
    // Validate email first
    if (!validateEmail(email)) {
      setError("Please enter a valid email address");
      return;
    }

    // Clear previous error and start discovery
    setError("");
    setUserCancelled(false); 
    setFlowState(FlowState.DISCOVERING);

    // Create new abort controller for this request
    discoveryAbortController.current = new AbortController();

    try {
      // Discover environment configuration
      const config = await discoverEnvironment(
        email,
        discoveryAbortController.current.signal
      );
      console.log("Discovery found config:", config);

      // Transition to connecting state
      setFlowState(FlowState.CONNECTING);

      // Complete discovery and set up for auth
      console.log("Calling onDiscoveryComplete with:", config);
      onDiscoveryComplete(config);
    } catch (error) {
      // Check if request was cancelled
      if (discoveryAbortController.current?.signal.aborted) {
        return;
      }

      console.error("Discovery error:", error);

      // Handle DiscoveryError with user-friendly messages
      if (error instanceof DiscoveryError) {
        setError(error.message);
      } else {
        setError("An unexpected error occurred. Please try again.");
      }
      setFlowState(FlowState.ERROR);
    } finally {
      discoveryAbortController.current = null;
    }
  };

  /**
   * Cancel the current discovery/auth flow
   */
  const handleCancel = () => {
    resetFlow();
  };

  const renderContent = () => {
    switch (flowState) {
      case FlowState.EMAIL_ENTRY:
      case FlowState.ERROR:
        return (
          <EmailEntryForm
            email={email}
            error={error}
            onEmailChange={(text) => {
              setEmail(text);
              if (error) setError("");
            }}
            onContinue={handleContinue}
          />
        );

      case FlowState.DISCOVERING:
        return (
          <LoadingState
            title="Finding your organization..."
            subtitle={email}
            buttonText="Cancel"
            onButtonPress={handleCancel}
          />
        );

      case FlowState.CONNECTING:
        return (
          <LoadingState
            title="Connecting..."
            subtitle={email}
            buttonText="Use different email"
            onButtonPress={resetFlow}
          />
        );

      default:
        return null;
    }
  };

  return (
    <View
      style={[
        styles.container,
        { backgroundColor: getColor("gray.50", isDarkMode) },
      ]}
    >
      <View style={styles.content}>
        <View
          style={[styles.logoContainer, { width: logoSize, height: logoSize }]}
        >
          <HeroIcon />
        </View>

        {renderContent()}
      </View>
    </View>
  );
}

const baseText = {
  fontFamily: "Roboto",
};

const baseButton = {
  alignItems: "center" as const,
  justifyContent: "center" as const,
  borderRadius: 12,
  width: "100%" as const,
};

const baseShadow = {
  shadowColor: "#000",
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
  shadowRadius: 4,
  elevation: 3,
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: spacing.l,
    paddingTop: 100, // Component-specific, stays hardcoded
    alignItems: "stretch",
  },
  logoContainer: {
    alignSelf: "flex-start",
    justifyContent: "center",
    alignItems: "center",
    width: 64,
    height: 64,
    marginBottom: spacing.xl,
  },
  title: {
    ...baseText,
    fontSize: 26,
    fontWeight: "700",
    lineHeight: 36.4,
    letterSpacing: 0.5,
  },
  subtitle: {
    ...baseText,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 22.4,
    marginBottom: 74, // Component-specific, stays hardcoded
  },
  inputContainer: {
    width: "100%",
    marginBottom: spacing.l,
  },
  inputLabel: {
    ...baseText,
    fontSize: 14,
    fontWeight: "500",
    lineHeight: 19.6,
    marginBottom: spacing.xs,
  },
  emailInput: {
    ...baseText,
    width: "100%",
    height: 48,
    paddingHorizontal: spacing.s,
    paddingVertical: 0,
    borderRadius: 8,
    borderWidth: 2,
    fontSize: 16,
    fontWeight: "500",
    letterSpacing: 0.17,
    textAlignVertical: "center",
    includeFontPadding: false,
  },
  errorText: {
    ...baseText,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 19.6,
    marginTop: spacing.xs,
  },
  loadingContainer: {
    marginVertical: spacing.xl,
    alignItems: "center",
  },
  nextButton: {
    ...baseButton,
    ...baseShadow,
    paddingVertical: spacing.m,
    paddingHorizontal: spacing.l,
  },
  nextButtonText: {
    ...baseText,
    color: "#fff",
    fontSize: 18,
    fontWeight: "500",
    lineHeight: 22.4,
  },
  cancelButton: {
    ...baseButton,
    paddingVertical: spacing.s,
    paddingHorizontal: spacing.l,
    borderWidth: 1,
  },
  cancelButtonText: {
    ...baseText,
    fontSize: 16,
    fontWeight: "500",
    lineHeight: 22.4,
  },
  linkButton: {
    paddingVertical: spacing.s,
    paddingHorizontal: spacing.m,
  },
  linkButtonText: {
    ...baseText,
    fontSize: 16,
    fontWeight: "500",
    lineHeight: 22.4,
    textAlign: "center",
  },
});