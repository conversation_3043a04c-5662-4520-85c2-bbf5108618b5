import { ActionSheetProvider } from "@expo/react-native-action-sheet";
import { BottomSheetModalProvider } from "@gorhom/bottom-sheet";
import * as Sentry from "@sentry/react-native";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { Slot, useNavigationContainerRef } from "expo-router";
import React, { useEffect, useState } from "react";
import { LogBox } from "react-native";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { NavigationProvider } from "./(app)/V2/contexts/NavigationContext";
import { AuthProvider } from "./AuthContext";
import ErrorBoundary from "./ErrorBoundary";
import { ReportsProvider } from "./ReportContext";
import useLocationStore from "./store/useLocationStore";
import { DarkModeProvider } from "./DarkModeContext";
import { EnvironmentConfig } from "./services/discoveryService";
import { EnvironmentConfigProvider } from "./contexts/EnvironmentConfigContext";

// Create navigation integration for user interaction tracking
const navigationIntegration = Sentry.reactNavigationIntegration({
  enableTimeToInitialDisplay: true, // Enable Time To Initial Display tracking (native build)
  routeChangeTimeoutMs: 1000, // Timeout for route change transactions
  ignoreEmptyBackNavigationTransactions: true, // Reduce noise from empty back navigations
});

Sentry.init({
  dsn: "https://<EMAIL>/4509527654793216",

  // Debug mode - remove in production
  debug: __DEV__, // Only enable in development

  // Adds more context data to events (IP address, cookies, user, etc.)
  // For more information, visit: https://docs.sentry.io/platforms/react-native/data-management/data-collected/
  sendDefaultPii: true,

  tracesSampleRate: 1.0, // Enable performance monitoring (100% sampling)
  profilesSampleRate: 1.0, // Enable profiling (100% sampling)

  // Native frames tracking (enabled for ejected/native builds)
  enableNativeFramesTracking: true,

  // Auto instrumentation
  enableAutoPerformanceTracing: true, // Automatically trace app starts and screen loads
  enableUserInteractionTracing: true, // Enable user interaction tracing

  // Configure Session Replay
  replaysSessionSampleRate: 0.1,
  replaysOnErrorSampleRate: 1,

  // Enable logging
  _experiments: {
    enableLogs: true,
  },

  integrations: [
    // Default integrations for automatic error capture
    Sentry.reactNativeErrorHandlersIntegration({
      patchGlobalPromise: true,
    }),

    // React Native Tracing (automatically instruments Expo Router)
    Sentry.reactNativeTracingIntegration({
      idleTimeoutMs: 3000,
      finalTimeoutMs: 30000,
    }),

    // Navigation integration for user interaction tracking
    navigationIntegration,

    // HTTP client integration for network request tracking
    Sentry.httpClientIntegration({
      failedRequestStatusCodes: [[400, 599]],
    }),

    // User interaction tracking
    Sentry.userInteractionIntegration(),

    // Mobile replay integration
    Sentry.mobileReplayIntegration({
      maskAllText: false,
      maskAllImages: false,
      maskAllVectors: false,
    }),

    // Screenshot integration for better debugging
    Sentry.screenshotIntegration(),

    // ViewHierarchy integration for UI debugging
    Sentry.viewHierarchyIntegration(),
  ],

  // uncomment the line below to enable Spotlight (https://spotlightjs.com)
  // spotlight: __DEV__,
});

const queryClient = new QueryClient();

export default Sentry.wrap(function RootLayout() {
  LogBox.ignoreAllLogs();

  // Navigation container ref for Sentry tracing
  const ref = useNavigationContainerRef();

  // Environment configuration state - lifted above AuthProvider
  const [environmentConfig, setEnvironmentConfig] =
    useState<EnvironmentConfig | null>(null);

  const startLocationUpdates = useLocationStore(
    (state) => state.startLocationUpdates
  );
  const stopLocationUpdates = useLocationStore(
    (state) => state.stopLocationUpdates
  );

  // Register navigation container with Sentry for tracing
  useEffect(() => {
    if (ref) {
      navigationIntegration.registerNavigationContainer(ref);
    }
  }, [ref]);

  // Manage location services based on authentication state
  useEffect(() => {
    if (environmentConfig) {
      // User has discovered their organization and is in auth flow
      console.log("Starting location services for authenticated session");
      startLocationUpdates();
    } else {
      // User is logged out or not yet discovered
      console.log("Stopping location services - no active session");
      stopLocationUpdates();
    }

    return () => {
      stopLocationUpdates();
    };
  }, [environmentConfig, startLocationUpdates, stopLocationUpdates]);

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      {/* Wrap the entire app in the ErrorBoundary */}
      <ErrorBoundary>
        <DarkModeProvider>
          <QueryClientProvider client={queryClient}>
            <BottomSheetModalProvider>
              <ActionSheetProvider>
                <EnvironmentConfigProvider
                  environmentConfig={environmentConfig}
                  setEnvironmentConfig={setEnvironmentConfig}
                >
                  <AuthProvider
                    key={environmentConfig?.userPoolUrl ?? "no-env"}
                    environmentConfig={environmentConfig}
                    onClearEnvironmentConfig={() => setEnvironmentConfig(null)}
                  >
                    <ReportsProvider>
                      <NavigationProvider>
                        <Slot />
                      </NavigationProvider>
                    </ReportsProvider>
                  </AuthProvider>
                </EnvironmentConfigProvider>
              </ActionSheetProvider>
            </BottomSheetModalProvider>
          </QueryClientProvider>
        </DarkModeProvider>
      </ErrorBoundary>
    </GestureHandlerRootView>
  );
});
