import * as Sentry from "@sentry/react-native";
import {
  AuthRequest,
  AuthRequestConfig,
  AuthSessionResult,
  DiscoveryDocument,
  exchangeCodeAsync,
  makeRedirectUri,
  refreshAsync,
  ResponseType,
  revokeAsync,
  TokenResponse,
} from "expo-auth-session";
import * as WebBrowser from "expo-web-browser";
import { jwtDecode } from "jwt-decode";
import React, {
  createContext,
  PropsWithChildren,
  useContext,
  useEffect,
  useMemo,
  useState,
} from "react";
import { Alert } from "react-native";
import { getServiceUrl, setUserPoolUrlForApis } from "./apis/config";
import {
  setAccessGetter as setCommunicationsAccessGetter,
  setRefreshCallback as setCommunicationsRefreshCallback,
  setCommunicationsBaseUrl,
} from "./apis/services/communications/axiosInstance";
import {
  setAccessGetter as setWorkflowAccessGetter,
  setRefreshCallback as setWorkflowRefreshCallback,
  setWorkflowBaseUrl,
} from "./apis/services/workflow/axiosInstance";
import { EnvironmentConfig } from "./services/discoveryService";

WebBrowser.maybeCompleteAuthSession();

const redirectUri = makeRedirectUri({
  native: "myapp://callback/",
  scheme: "myapp",
  path: "callback/",
});

const scopes = ["openid", "email", "profile"];

/**
 * Create discovery document from environment configuration
 */
const createDiscoveryDocument = (
  config: EnvironmentConfig
): DiscoveryDocument => ({
  authorizationEndpoint: `${config.userPoolUrl}/oauth2/authorize`,
  tokenEndpoint: `${config.userPoolUrl}/oauth2/token`,
  revocationEndpoint: `${config.userPoolUrl}/oauth2/revoke`,
});

interface AuthContextValue {
  authTokens: TokenResponse | null;
  promptAsync: () => Promise<AuthSessionResult>;
  logout: () => Promise<void>;
  refreshAccessToken: () => Promise<boolean>;
  isLoading: boolean;
  isAuthReady: boolean;
  isAuthenticated: boolean; // Stable auth state that doesn't flicker during refresh
}

interface DecodedToken {
  sub: string;
  email?: string;
  username?: string;
}

const AuthContext = createContext<AuthContextValue>({} as AuthContextValue);

// Helper function to set Sentry user context
const setSentryUserContext = (tokens: TokenResponse | null) => {
  if (tokens?.idToken) {
    try {
      const decoded = jwtDecode<DecodedToken>(tokens.idToken);
      Sentry.setUser({
        id: decoded.sub,
        email: decoded.email,
        username: decoded.username,
      });
    } catch (error) {
      console.error("Error decoding token for Sentry context:", error);
      Sentry.setUser(null);
    }
  } else {
    Sentry.setUser(null);
  }
};

// Helper function to generate secure random strings for state/nonce
function rand(n = 32): string {
  const a = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  return Array.from({ length: n }, () => a[Math.floor(Math.random() * a.length)]).join("");
}

interface AuthProviderProps extends PropsWithChildren {
  environmentConfig?: EnvironmentConfig | null;
  onClearEnvironmentConfig?: () => void;
}

/**
 * EPHEMERAL AUTH PROVIDER
 *
 * This provider implements ephemeral authentication:
 * - Tokens are only stored in memory (not persisted to SecureStore)
 * - Sessions do not persist between app launches
 * - Each app launch requires fresh authentication
 * - WebView cookies are cleared on logout and app launch
 */
export function AuthProvider({
  children,
  environmentConfig = null,
  onClearEnvironmentConfig,
}: AuthProviderProps) {
  const [authTokens, setAuthTokensState] = useState<TokenResponse | null>(null);
  const [isAuthInProgress, setIsAuthInProgress] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const refreshPromiseRef = React.useRef<Promise<boolean> | null>(null);
  const authTokensRef = React.useRef<TokenResponse | null>(null);
  const isRefreshingRef = React.useRef(false); // Use ref instead of state to avoid re-renders
  
  // Update both ref and state - use ref for immediate access, state for React updates
  const setAuthTokens = React.useCallback((tokens: TokenResponse | null, skipStateUpdate = false) => {
    authTokensRef.current = tokens;
    
    if (!skipStateUpdate) {
      setAuthTokensState(tokens);
      // Update stable authentication state
      setIsAuthenticated(!!tokens?.accessToken);
    }
  }, []);
  
  const config = environmentConfig;
  const discovery = config ? createDiscoveryDocument(config) : null;
  const isAuthReady = !!config && !!discovery;


  // Configure API base URLs when environment changes
  useEffect(() => {
    if (!config) {
      setUserPoolUrlForApis(null);
      setWorkflowBaseUrl(null);
      setCommunicationsBaseUrl(null);
      return;
    }

    setUserPoolUrlForApis(config.userPoolUrl);
    setWorkflowBaseUrl(getServiceUrl("workflow"));
    setCommunicationsBaseUrl(getServiceUrl("communications"));
  }, [config?.userPoolUrl]);

  // Clear WebView cookies on mount to ensure clean state
  useEffect(() => {
    const clearAuthSession = async () => {
      try {
        WebBrowser.maybeCompleteAuthSession({ skipRedirectCheck: true });
      } catch (error) {
        console.error("Error clearing initial WebView session:", error);
      }
    };

    clearAuthSession();
  }, []);

  // Ephemeral promptAsync with PKCE, state, and nonce security
  const promptAsync = async (): Promise<AuthSessionResult> => {
    if (!config || !discovery) {
      throw new Error("Environment not configured");
    }

    if (isAuthInProgress) {
      return { type: "cancel" } as AuthSessionResult;
    }

    setIsAuthInProgress(true);
    try {
      // Generate secure random values for CSRF protection
      const state = rand();
      const nonce = rand();

      // Create AuthRequest with PKCE and security parameters
      const reqCfg: AuthRequestConfig = {
        clientId: config.clientId,
        responseType: ResponseType.Code,
        redirectUri,
        usePKCE: true, // Enables PKCE for security
        scopes,
        state, // CSRF protection
        extraParams: {
          nonce, // Token replay protection
          prompt: "login", // Force fresh login
          max_age: "0" // Force re-authentication
        },
      };

      const request = new AuthRequest(reqCfg);

      // Clear any existing WebView cookies before prompting
      WebBrowser.maybeCompleteAuthSession({ skipRedirectCheck: true });

      const result = await request.promptAsync(discovery, {
        // @ts-expect-error
        preferEphemeralSession: true // CRITICAL: This prevents state from carrying over between login attempts
      });

      if (result.type !== "success") {
        return { type: result.type } as AuthSessionResult;
      }

      // Validate state parameter to prevent CSRF attacks
      if (result.params.state !== state) {
        console.error("State mismatch - potential CSRF attack");
        throw new Error("State mismatch");
      }

      const tokens = await exchangeCodeAsync(
        {
          code: result.params.code,
          clientId: config.clientId,
          redirectUri,
          extraParams: { code_verifier: request.codeVerifier! }, // PKCE verification
        },
        discovery
      );

      // Optional: Verify nonce in ID token to prevent replay attacks
      if (tokens.idToken) {
        try {
          const decoded = jwtDecode<{ nonce?: string }>(tokens.idToken);
          if (decoded.nonce !== nonce) {
            console.warn("Nonce mismatch in ID token");
          }
        } catch (error) {
          console.error("Error verifying nonce:", error);
        }
      }

      // Add issuedAt timestamp for tracking refresh token expiry
      const enhancedTokens = {
        ...tokens,
        issuedAt: Date.now(),
      } as TokenResponse & { issuedAt: number };
      
      setAuthTokens(enhancedTokens);
      setSentryUserContext(enhancedTokens);
      
      return { type: "success" } as AuthSessionResult;
    } catch (error) {
      console.error("Error in ephemeral prompt with PKCE:", error);
      Alert.alert("Error", "Authentication failed. Please try again.");
      return { type: "error" } as AuthSessionResult;
    } finally {
      setIsAuthInProgress(false);
    }
  };

  // Logout - clear everything
  const logout = React.useCallback(async (): Promise<void> => {
    try {
      const currentTokens = authTokensRef.current;
      if (config && discovery && currentTokens?.refreshToken) {
        // Open Cognito logout URL
        const logoutUrl = `${config.userPoolUrl}/logout?client_id=${config.clientId
          }&logout_uri=${encodeURIComponent(redirectUri)}`;

        const logoutResult = await WebBrowser.openAuthSessionAsync(
          logoutUrl,
          redirectUri,
          { preferEphemeralSession: true }
        );

        if (logoutResult.type === "success" || logoutResult.type === "cancel") {
          try {
            await revokeAsync(
              {
                clientId: config.clientId,
                token: currentTokens.refreshToken,
              },
              discovery
            );
          } catch (revokeError) {
            console.error("Token revocation failed:", revokeError);
          }
        }
      }

      // Clear WebView cookies and local state
      WebBrowser.maybeCompleteAuthSession({ skipRedirectCheck: true });
      setAuthTokens(null);
      setSentryUserContext(null);
      onClearEnvironmentConfig?.();

    } catch (error) {
      console.error("Logout error:", error);
      // Always clear local state
      setAuthTokens(null);
      setSentryUserContext(null);
      onClearEnvironmentConfig?.();
    }
  }, [config, discovery, onClearEnvironmentConfig]);

  // Refresh access token using refresh token
  const refreshAccessToken = React.useCallback(async (): Promise<boolean> => {
    // Prevent multiple simultaneous refresh attempts
    if (refreshPromiseRef.current) {
      return refreshPromiseRef.current;
    }

    const currentTokens = authTokensRef.current;
    if (!config || !discovery || !currentTokens?.refreshToken) {
      return false;
    }


    const refreshPromise = (async () => {
      try {
        isRefreshingRef.current = true;
        
        const newTokens = await refreshAsync(
          {
            clientId: config.clientId,
            refreshToken: currentTokens.refreshToken,
          },
          discovery
        );

        
        // Preserve the original issuedAt time and refresh token
        // refreshAsync may not return a new refresh token - preserve the existing one
        const enhancedTokens = {
          ...newTokens,
          refreshToken: newTokens.refreshToken || currentTokens.refreshToken, // Preserve refresh token if not returned
          issuedAt: currentTokens.issuedAt || Date.now(),
        };
        
        // Update ref immediately for axios interceptors (doesn't trigger re-renders)
        authTokensRef.current = enhancedTokens as TokenResponse;
        
        // Only update Sentry context, don't update React state to avoid re-renders
        setSentryUserContext(enhancedTokens as TokenResponse);
        
        // Keep isAuthenticated true during silent refresh - don't flicker
        // The user is still authenticated, just with a new token
        
        return true;
      } catch (error) {
        // If refresh fails, log the user out
        await logout();
        
        return false;
      } finally {
        isRefreshingRef.current = false;
        refreshPromiseRef.current = null;
      }
    })();

    refreshPromiseRef.current = refreshPromise;
    return refreshPromise;
  }, [config, discovery, logout]);

  // Set access getter functions and refresh callbacks for axios instances
  useEffect(() => {
    // Use ref for token getters to avoid stale closures
    setWorkflowAccessGetter(() => authTokensRef.current?.accessToken);
    setCommunicationsAccessGetter(() => authTokensRef.current?.accessToken);
    
    // Set refresh callbacks once on mount
    setWorkflowRefreshCallback(refreshAccessToken);
    setCommunicationsRefreshCallback(refreshAccessToken);
  }, []); // Empty deps - only run once on mount

  // Proactive token refresh - refresh 4.5 minutes before expiry
  // Only schedule the initial refresh when isAuthenticated changes to true
  useEffect(() => {
    // Only start refresh cycle when user becomes authenticated
    if (!isAuthenticated || !authTokensRef.current?.expiresIn) {
      return;
    }

    // Calculate when to refresh (1 minute before expiry)
    const expiresInMs = authTokensRef.current.expiresIn * 1000;
    const refreshTime = expiresInMs - 60000; // 60 seconds (1 minute) before expiry

    if (refreshTime <= 0) {
      // Token already expired or about to expire
      refreshAccessToken();
      return;
    }

    
    const scheduleNextRefresh = () => {
      const timer = setTimeout(() => {
        refreshAccessToken().then(success => {
          if (success) {
            // Schedule the next refresh based on the new token
            const updatedTokens = authTokensRef.current;
            if (updatedTokens?.expiresIn) {
              const nextExpiresInMs = updatedTokens.expiresIn * 1000;
              const nextRefreshTime = nextExpiresInMs - 60000;
              if (nextRefreshTime > 0) {
                setTimeout(scheduleNextRefresh, nextRefreshTime);
              }
            }
          }
        });
      }, refreshTime);
      return timer;
    };
    
    const timer = scheduleNextRefresh();
    return () => clearTimeout(timer);
  }, [isAuthenticated, refreshAccessToken]); // Only re-run when user logs in/out

  // Memoize context value (fixed dependencies)
  const value = useMemo<AuthContextValue>(
    () => ({
      authTokens,
      promptAsync,
      logout,
      refreshAccessToken,
      isLoading: isAuthInProgress, // Don't include isRefreshing - it causes app to show loading during token refresh
      isAuthReady,
      isAuthenticated,
    }),
    [authTokens, isAuthReady, isAuthInProgress, isAuthenticated, logout, refreshAccessToken]
  );

  if (!environmentConfig) {
    return (
      <AuthContext.Provider
        value={{
          authTokens: null,
          promptAsync: () =>
            Promise.reject(new Error("Environment not configured")),
          logout: async () => {},
          refreshAccessToken: async () => false,
          isLoading: false,
          isAuthReady: false,
          isAuthenticated: false,
        }}
      >
        {children}
      </AuthContext.Provider>
    );
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

/**
 * Custom hook to consume the AuthContext
 */
export function useAuth(): AuthContextValue {
  return useContext(AuthContext);
}