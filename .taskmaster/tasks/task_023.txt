# Task ID: 23
# Title: Backend: Extend ForwardingConfig API Validation
# Status: pending
# Dependencies: None
# Priority: high
# Description: Ensure backend enforces validation rules for enabling/disabling call forwarding per PRD, including phone normalization and required fields.
# Details:
Update hero.orgs.v1 service to validate: forward_to_number required when enabling (unless already stored); assigned_asset_id optional; normalize phone to E.164. On disable, preserve forward_to_number in DB and return latest config without nulling it. Ensure UpdateForwardingConfig upserts into org_call_forwarding_config and returns latest config.

# Test Strategy:
Write unit tests for validation logic using Go’s testing package. Cover all edge cases: missing number, invalid format, disabling, and upsert behavior. Use integration tests to verify DB writes and API responses.

# Subtasks:
## 1. Implement Validation Logic for Enabling/Disabling Forwarding [pending]
### Dependencies: None
### Description: Develop backend logic to enforce PRD validation rules for enabling and disabling call forwarding, including required and optional fields.
### Details:
Ensure that 'forward_to_number' is required when enabling forwarding (unless already stored), and 'assigned_asset_id' is optional. On disabling, preserve 'forward_to_number' in the database and ensure the latest config is returned without nulling the field.

## 2. Normalize Phone Numbers to E.164 Format [pending]
### Dependencies: 23.1
### Description: Integrate phone number normalization to E.164 standard for all relevant API inputs and database writes.
### Details:
Use a library (e.g., Google's libphonenumber) to parse and format phone numbers into E.164 format before validation and storage. Ensure all phone numbers in the system conform to this format.

## 3. Implement Upsert Logic for org_call_forwarding_config [pending]
### Dependencies: 23.1, 23.2
### Description: Ensure UpdateForwardingConfig performs an upsert operation on org_call_forwarding_config and returns the latest configuration.
### Details:
Design the upsert logic to insert or update the forwarding config as needed, preserving existing data where required and ensuring atomicity.

## 4. Shape API Responses and Handle Errors [pending]
### Dependencies: 23.1, 23.2, 23.3
### Description: Define and implement API response structures, including error handling for all validation and persistence scenarios.
### Details:
Ensure the API returns the latest config after updates, provides clear error messages for validation failures, and does not null 'forward_to_number' on disable. Document response formats and error codes.

## 5. Develop Unit and Integration Tests for Validation Paths [pending]
### Dependencies: 23.1, 23.2, 23.3, 23.4
### Description: Write comprehensive unit and integration tests covering all validation, normalization, upsert, and API response scenarios.
### Details:
Test all edge cases: missing or invalid numbers, enabling/disabling logic, upsert behavior, and API error handling. Use Go’s testing package and include integration tests for database and API layers.

