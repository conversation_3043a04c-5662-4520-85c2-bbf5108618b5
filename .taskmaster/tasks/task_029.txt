# Task ID: 29
# Title: Backend: Real-Time Sync & Polling Support
# Status: pending
# Dependencies: 23
# Priority: medium
# Description: Implement conditional polling support for GetForwardingConfig with ETag/Last-Modified and 304/200 responses for efficient client sync.
# Details:
Extend GetForwardingConfig to include ETag (e.g., hash or updated_at) and Last-Modified headers. If If-None-Match/If-Modified-Since match current state, return 304; otherwise 200 with config. Define 'meaningful change' fields (enabled, assigned_asset_id, forward_to_number) for client toast logic. Document header usage and recommended ~3s polling interval.

# Test Strategy:
Unit test header logic and response codes. Integration test polling behavior and config change detection.

# Subtasks:
## 1. Implement ETag and Last-Modified Header Generation [pending]
### Dependencies: None
### Description: Extend GetForwardingConfig to generate and include ETag (e.g., hash or updated_at) and Last-Modified headers in the response, ensuring they accurately reflect the current state of meaningful change fields.
### Details:
Determine the appropriate method for generating ETag (such as hashing enabled, assigned_asset_id, forward_to_number) and set Last-Modified based on the latest update timestamp. Ensure headers conform to HTTP standards.

## 2. Implement Conditional 304/200 Response Logic [pending]
### Dependencies: 29.1
### Description: Add logic to GetForwardingConfig to evaluate If-None-Match and If-Modified-Since request headers and return 304 Not Modified if both match the current resource state, otherwise return 200 OK with the config.
### Details:
Check incoming request headers against current ETag and Last-Modified values. If both match, respond with 304; otherwise, respond with 200 and the latest config payload.

## 3. Document Header Usage and Polling Interval Recommendations [pending]
### Dependencies: 29.1, 29.2
### Description: Create documentation detailing how ETag and Last-Modified headers are used, how clients should interpret 304/200 responses, and recommend a ~3s polling interval for efficient sync.
### Details:
Include examples of request/response headers, explain the logic for meaningful change detection, and provide guidance for client polling strategies.

## 4. Develop Test Coverage for Polling and Change Detection [pending]
### Dependencies: 29.1, 29.2, 29.3
### Description: Write unit and integration tests to verify correct header generation, conditional response logic, and client polling behavior, including detection of meaningful changes.
### Details:
Ensure tests cover scenarios for both 304 and 200 responses, header accuracy, and client-side handling of polling intervals and change notifications.

