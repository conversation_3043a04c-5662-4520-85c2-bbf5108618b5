# Task ID: 19
# Title: Communications Service Call Queue Repository Updates
# Status: deferred
# Dependencies: 18
# Priority: medium
# Description: Update call queue repository methods to capture and preserve phone line context throughout the call lifecycle
# Details:
Update `services/communications/internal/cellularcall/data/postgres_callqueue_repo.go` to include org_phone_line_id in all call creation and query methods. Modify CreateCall() method to accept and store phone line ID. Update GetCallsByOrg() and similar query methods to JOIN with org_phone_lines table and return phone line information. Add GetCallsByPhoneLine() method for line-specific call filtering. Update webhook handling methods to identify which phone line received the call based on Twilio receiving number. Modify call status update methods to preserve phone line context. Add helper methods for phone line lookup by Twi<PERSON> number. Ensure all database queries use proper indexes for performance.

# Test Strategy:
Unit tests for updated repository methods, integration tests with real database, test call creation with phone line context, validate query methods return correct phone line information, test webhook handling identifies correct phone lines, performance testing for JOIN queries, test backward compatibility with existing calls
