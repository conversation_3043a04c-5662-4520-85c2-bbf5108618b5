# Task ID: 21
# Title: Frontend: Implement Call Settings Panel UI
# Status: pending
# Dependencies: None
# Priority: high
# Description: Develop the gear icon entrypoint and modal/panel for 'Active Calls – Settings' in the Call module, including all controls and tooltips per PRD.
# Details:
Implement the 'Active Calls – Settings' panel per PRD. Controls: global toggle; Assigned Dispatcher selector that couples asset id/name/number and, when changed with Override OFF, automatically sets the number to that asset’s contact_no; Override Default Number checkbox; when checked, show number dropdown of unique org numbers; Confirm/Cancel buttons. If the assigned asset has no number, show '(---) --- ----' and keep <PERSON><PERSON>rm disabled until Override is enabled and a number is chosen. Disable settings button with tooltip while the viewing asset has any non-ended call.

# Test Strategy:
Unit test each UI component with Jest and React Testing Library. Validate control states, tooltips, and modal behavior. Use Cypress for end-to-end tests covering entrypoint, modal open/close, control interactivity, and disabled states.

# Subtasks:
## 1. Implement Gear Icon Entrypoint and Modal/Panel Shell [pending]
### Dependencies: None
### Description: Create the gear icon button as the entrypoint for the Call Settings Panel. Implement the modal or side panel shell that opens when the gear icon is clicked, ensuring accessibility and focus management.
### Details:
Use a modal or panel component (e.g., Material UI Dialog or custom modal). Ensure the modal overlays the main content, traps focus, and can be closed via UI or keyboard. The gear icon should be visible and clickable unless disabled by business logic.

## 2. Add Global Toggle Control [pending]
### Dependencies: 21.1
### Description: Implement the global toggle switch for enabling/disabling call forwarding within the settings panel, as specified in the PRD.
### Details:
The toggle should reflect and control the enabled state. Ensure proper labeling, accessibility, and state synchronization with the rest of the panel.

## 3. Implement Assigned Dispatcher Selector with Asset Coupling Logic [pending]
### Dependencies: 21.2
### Description: Add the Assigned Dispatcher selector, coupling asset id/name/number. Implement logic so that when the asset is changed and Override is OFF, the number is set to the asset’s contact_no.
### Details:
Selector should display asset options with id, name, and number. On change, update the number field automatically if Override is not enabled. Handle cases where the assigned asset has no number.

## 4. Implement Override Default Number Checkbox and Number Dropdown [pending]
### Dependencies: 21.3
### Description: Add the Override Default Number checkbox. When checked, display a dropdown of unique organization numbers for selection.
### Details:
Checkbox toggles the override state. When enabled, show a dropdown with all unique org numbers. Ensure the dropdown is only interactive when Override is checked.

## 5. Implement Confirm/Cancel Button Logic and Validation [pending]
### Dependencies: 21.4
### Description: Add Confirm and Cancel buttons with validation logic. Disable Confirm if the assigned asset has no number and Override is not enabled or no number is selected.
### Details:
Confirm should only be enabled when a valid number is selected or Override is enabled with a chosen number. Cancel closes the panel without saving. Show '(---) --- ----' placeholder if no number is available.

## 6. Implement Disabled State and Tooltip Logic for Settings Button [pending]
### Dependencies: 21.1
### Description: Disable the gear/settings button and show a tooltip when the viewing asset has any non-ended call, per PRD requirements.
### Details:
Check asset call state before enabling the settings button. If disabled, display a tooltip explaining why. Ensure tooltip is accessible and appears on hover/focus.

