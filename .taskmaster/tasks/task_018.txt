# Task ID: 18
# Title: Call Queue Schema Integration
# Status: deferred
# Dependencies: 17
# Priority: high
# Description: Update the call_queue table to reference phone lines and backfill existing call data with primary line associations
# Details:
Create new migration to add org_phone_line_id column to call_queue table with foreign key constraint to org_phone_lines(id). Add index on org_phone_line_id for query performance: `CREATE INDEX idx_call_queue_org_phone_line_id ON call_queue(org_phone_line_id)`. Implement backfill query to associate existing calls with their organization's primary phone line: `UPDATE call_queue SET org_phone_line_id = (SELECT id FROM org_phone_lines WHERE org_phone_lines.org_id = call_queue.org_id AND is_primary_line = true)`. Add NOT NULL constraint after backfill is complete. Update any existing queries in communications service that reference call_queue to handle the new phone line relationship. Ensure proper cascade behavior when phone lines are deleted.

# Test Strategy:
Test migration on staging data to ensure no data loss, validate foreign key constraints work correctly, test backfill query performance and accuracy, verify all existing calls get associated with correct primary lines, test cascade deletion behavior, validate index improves query performance

# Subtasks:
## 1. Create Database Migration for org_phone_line_id Column [pending]
### Dependencies: None
### Description: Create and execute a database migration to add the org_phone_line_id column to the call_queue table with proper foreign key constraints and indexing
### Details:
Create a new migration file that adds org_phone_line_id column to call_queue table as INTEGER type, initially nullable. Add foreign key constraint referencing org_phone_lines(id) with ON DELETE CASCADE behavior. Create performance index: CREATE INDEX idx_call_queue_org_phone_line_id ON call_queue(org_phone_line_id). Test the migration on a copy of production data to ensure no issues with existing records.

## 2. Implement Data Backfill for Existing Call Records [pending]
### Dependencies: 18.1
### Description: Execute a backfill operation to associate all existing call_queue records with their organization's primary phone line
### Details:
Implement and execute the backfill query: UPDATE call_queue SET org_phone_line_id = (SELECT id FROM org_phone_lines WHERE org_phone_lines.org_id = call_queue.org_id AND is_primary_line = true) WHERE org_phone_line_id IS NULL. Monitor query performance and consider batching for large datasets. Verify all records are successfully updated and no calls are left without phone line associations. After successful backfill, add NOT NULL constraint to org_phone_line_id column.

## 3. Update Communications Service Queries and Cascade Behavior [pending]
### Dependencies: 18.2
### Description: Update all existing queries in the communications service to handle the new phone line relationship and ensure proper cascade deletion behavior
### Details:
Identify and update all queries in communications service that reference call_queue table to include org_phone_line_id joins where appropriate. Update call creation logic to require org_phone_line_id parameter. Modify call retrieval queries to include phone line information. Test cascade deletion behavior when phone lines are deleted to ensure call_queue records are properly handled. Update any ORM models or query builders to reflect the new schema relationship.

