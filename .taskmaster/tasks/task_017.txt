# Task ID: 17
# Title: Bootstrap Script Updates for Multi-Line Support
# Status: deferred
# Dependencies: 16
# Priority: medium
# Description: Update organization bootstrap scripts to create phone lines through the new API endpoints instead of direct database insertion
# Details:
Update `bootstrap/config/recipes/org_bootstrap_local.yaml` to use the new phone line creation workflow. Remove phone line fields from organization creation payload (twilio_number, twilio_number_sid, twiml_app_sid). Add new step 'create_primary_phone_line' that calls hero.orgs.v1.OrgsService/CreateOrgPhoneLine with org_id from previous step, line_type='general', twilio_receiving_number='+18556976298', twiml_app_sid='AP367659bb9684ba033a80685ee537ffcc', is_primary_line=true. Update dependencies to ensure phone line creation happens after organization creation. Test with local development environment to ensure bootstrap process works end-to-end. Update any other bootstrap recipes that create organizations with phone lines.

# Test Strategy:
Test bootstrap process in local development environment, verify organizations are created successfully with primary phone lines, validate phone line data is correctly populated, test bootstrap rollback procedures, confirm existing bootstrap data remains compatible
