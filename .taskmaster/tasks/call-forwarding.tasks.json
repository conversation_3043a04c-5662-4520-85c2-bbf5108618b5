{"master": {"tasks": [{"id": 1, "title": "Database Migration - Column <PERSON>ame and New Table", "description": "Create database migration to rename primary_phone_number to forward_to_number and create call_forward_events table", "details": "Create staged migration files:\n1. Add forward_to_number column (nullable)\n2. Backfill data from primary_phone_number\n3. Create call_forward_events table with schema:\n```sql\nCREATE TABLE call_forward_events (\n    id SERIAL PRIMARY KEY,\n    call_sid TEXT NOT NULL,\n    org_id INTEGER NOT NULL REFERENCES orgs(id),\n    from_number TEXT NOT NULL,\n    to_number TEXT NOT NULL,\n    status TEXT NOT NULL DEFAULT 'initiated',\n    dial_call_status TEXT,\n    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()\n);\n```\n4. Add indexes on org_id and created_at for performance\n5. Plan removal of primary_phone_number column for later phase", "testStrategy": "Test migration rollback capability, verify data integrity after backfill, test index performance with sample data, validate foreign key constraints", "priority": "high", "dependencies": [], "status": "pending", "subtasks": []}, {"id": 2, "title": "Environment Configuration Setup", "description": "Add environment variables for call forwarding configuration", "details": "Add the following environment variables:\n- CALL_FORWARD_TIMEOUT_SECONDS (default: 20)\n- CALL_FORWARD_ENABLED (default: true)\n\nUpdate configuration loading in the communications service to read these values. Create validation for timeout values (must be positive integer between 5-60 seconds). Document configuration options in service README.", "testStrategy": "Test default value loading, validate configuration parsing with various input types, test service startup with missing/invalid config values", "priority": "medium", "dependencies": [], "status": "pending", "subtasks": []}, {"id": 3, "title": "Repository Layer for Call Forward Events", "description": "Implement repository layer for call_forward_events table CRUD operations", "details": "Create repository interface and implementation in /services/communications/internal/cellularcall/data/:\n```go\ntype CallForwardEventRepository interface {\n    Create(ctx context.Context, event *CallForwardEvent) error\n    UpdateStatus(ctx context.Context, callSid string, status string, dialCallStatus string) error\n    GetByCallSid(ctx context.Context, callSid string) (*CallForwardEvent, error)\n    GetByOrgId(ctx context.Context, orgId int, limit int, offset int) ([]*CallForwardEvent, error)\n}\n```\nImplement with proper error handling, context cancellation, and database connection management. Include proper logging for audit trail.", "testStrategy": "Unit tests for all CRUD operations, integration tests with test database, test error handling for database connection issues, validate proper context cancellation", "priority": "medium", "dependencies": [1], "status": "pending", "subtasks": []}, {"id": 4, "title": "TwiML Builder Enhancement", "description": "Add ForwardDialResponse function to twiml/builder.go for generating call forwarding TwiML", "details": "Implement ForwardDialResponse function in /services/communications/internal/cellularcall/twiml/builder.go:\n```go\nfunc ForwardDialResponse(toNumber string, callerId string, forwardActionURL string) (string, error) {\n    dial := &twiml.VoiceDial{\n        Timeout:        20, // Read from env: CALL_FORWARD_TIMEOUT_SECONDS\n        Action:         forwardActionURL,\n        CallerId:       callerId,\n        AnswerOnBridge: true,\n    }\n    numberElement := twiml.VoiceNumber{\n        PhoneNumber: toNumber,\n    }\n    dial.InnerElements = []twiml.Element{&numberElement}\n    return twiml.Voice([]twiml.Element{dial})\n}\n```\nInclude phone number validation using existing Hero Core utility utils.StandardizeUSPhoneNumber(). Handle caller ID validation and fallback logic.", "testStrategy": "Unit tests for TwiML generation with various input combinations, test phone number validation, verify generated XML structure matches Twilio requirements, test caller ID fallback scenarios", "priority": "high", "dependencies": [2], "status": "pending", "subtasks": []}, {"id": 5, "title": "Caller ID Selection Logic", "description": "Implement caller ID selection and validation logic for call forwarding", "details": "Create utility functions for caller ID selection:\n1. If forwarded_from_number is present and verified by <PERSON><PERSON><PERSON> → use as CallerId\n2. <PERSON><PERSON> → omit CallerId to pass through original caller ID\n3. If pass-through not permitted, fallback to org's TwilioNumber\n\nImplement validation using existing Hero Core utilities and Twilio verification. Include proper error handling and logging for caller ID issues. Create helper function to determine best caller ID for forwarding context.", "testStrategy": "Test caller ID validation with verified/unverified numbers, test fallback logic for different account configurations, validate phone number formatting, test edge cases with invalid caller IDs", "priority": "medium", "dependencies": [4], "status": "pending", "subtasks": []}, {"id": 6, "title": "Forward Action Handler Implementation", "description": "Implement HandleForwardActionRequest handler for processing Dial action callbacks", "details": "Create HandleForwardActionRequest in cellularcall_usecase.go:\n1. Parse DialCallStatus from Twilio webhook\n2. Log forward attempt result to call_forward_events\n3. On failure states (no-answer, busy, failed, canceled), trigger existing QueueCall flow\n4. On success (completed), return empty TwiML response\n5. Include infinite loop prevention with forwardAttempted flag\n6. Implement proper error handling and status logging\n\nWebhook parameters to handle: DialCallStatus, CallSid, From, To, forwardAttempted", "testStrategy": "Unit tests for all DialCallStatus scenarios, test fallback to queue logic, verify infinite loop prevention, test webhook parameter parsing, integration tests with mock Twilio webhooks", "priority": "high", "dependencies": [3, 4, 5], "status": "pending", "subtasks": []}, {"id": 7, "title": "Enhanced HandleVoiceRequest Logic", "description": "Modify existing HandleVoiceRequest to check forwarding flag and return forward TwiML before queue logic", "details": "Enhance HandleVoiceRequest in cellularcall_usecase.go:\n1. Check is_call_forwarding_enabled flag for organization\n2. If enabled and forward_to_number exists, return ForwardDialResponse TwiML\n3. Include forwardAttempted=true in action URL to prevent loops\n4. Log forward initiation to call_forward_events table\n5. Maintain existing queue logic as fallback\n6. Add proper error handling for missing/invalid forward numbers\n\nEnsure backward compatibility with existing call flow when forwarding is disabled.", "testStrategy": "Test forwarding enabled/disabled scenarios, verify existing queue logic remains intact, test with missing forward_to_number, validate TwiML response format, integration tests with full call flow", "priority": "high", "dependencies": [6], "status": "pending", "subtasks": []}, {"id": 8, "title": "Route Registration for Forward Action Endpoint", "description": "Register new /twiml/forward-action endpoint in main.go server setup", "details": "Add route registration in /services/communications/cmd/server/main.go:\n1. Register /twiml/forward-action endpoint pointing to HandleForwardActionRequest\n2. Include proper middleware for Twilio signature validation\n3. Add request logging and monitoring\n4. Ensure proper error handling and response formatting\n5. Configure appropriate timeouts for webhook processing\n\nFollow existing patterns for Twilio webhook endpoints in the service.", "testStrategy": "Test route registration and accessibility, verify middleware application, test Twilio signature validation, validate response format compliance, test endpoint under load", "priority": "medium", "dependencies": [6], "status": "pending", "subtasks": []}, {"id": 9, "title": "Enhanced Call Status Logging", "description": "Enhance existing /callstatus endpoint to handle forward event logging with eventType parameter", "details": "Modify existing /callstatus handler to:\n1. Accept eventType=forward query parameter\n2. Log forward-specific events to call_forward_events table\n3. Handle webhook parameters: DialCallSid, DialCallStatus, ParentCallSid, CallStatus, From, To\n4. Maintain existing call status logging functionality\n5. Add proper error handling for forward event logging\n6. Ensure no TwiML response (200 OK only)\n\nReuse existing infrastructure while adding forward-specific logging capability.", "testStrategy": "Test eventType parameter handling, verify forward event logging, ensure existing call status logging unaffected, test webhook parameter parsing, validate database insertions", "priority": "medium", "dependencies": [3, 8], "status": "pending", "subtasks": []}, {"id": 10, "title": "Orgs Service Integration", "description": "Update orgs service to handle forward_to_number field and forwarding configuration", "details": "Update services/orgs to:\n1. Switch reads/writes from primary_phone_number to forward_to_number\n2. Update API endpoints to accept forwarding configuration\n3. Add validation for forward_to_number format\n4. Update bootstrap recipes and configurations\n5. Maintain backward compatibility during transition\n6. Add proper error handling for invalid phone numbers\n\nEnsure seamless transition from old column name to new one.", "testStrategy": "Test API endpoints with new field names, verify phone number validation, test backward compatibility, validate bootstrap recipe updates, integration tests with communications service", "priority": "medium", "dependencies": [1], "status": "pending", "subtasks": []}, {"id": 11, "title": "Security and Validation Implementation", "description": "Implement comprehensive security measures and validation for call forwarding", "details": "Implement security measures:\n1. <PERSON>idate Twi<PERSON> signatures on /twiml/forward-action and /callstatus endpoints\n2. Add rate limiting for forward attempts per organization\n3. Implement phone number validation using Hero Core utilities\n4. Add input sanitization for all webhook parameters\n5. Implement proper authentication for configuration endpoints\n6. Add logging for security events and validation failures\n\nEnsure all security best practices are followed for webhook handling.", "testStrategy": "Test Twilio signature validation with valid/invalid signatures, verify rate limiting functionality, test phone number validation edge cases, validate input sanitization, security penetration testing", "priority": "high", "dependencies": [8, 9], "status": "pending", "subtasks": []}, {"id": 12, "title": "Comprehensive Testing and Documentation", "description": "Implement comprehensive test suite and documentation for call forwarding feature", "details": "Create comprehensive testing:\n1. Unit tests for all new functions and handlers\n2. Integration tests with mock Twilio webhooks\n3. End-to-end tests for complete call forwarding flow\n4. Performance tests for database operations\n5. Load testing for webhook endpoints\n6. Create API documentation for configuration endpoints\n7. Update service README with forwarding feature details\n8. Create troubleshooting guide for common issues\n\nEnsure 90%+ code coverage for all new functionality.", "testStrategy": "Automated test execution in CI/CD pipeline, code coverage reporting, performance benchmarking, manual testing with real Twilio webhooks, documentation review and validation", "priority": "medium", "dependencies": [7, 9, 10, 11], "status": "pending", "subtasks": []}], "metadata": {"created": "2025-08-07T20:42:34.939Z", "updated": "2025-08-07T20:42:34.939Z", "description": "Tasks for master context"}}}