# Task ID: 14
# Title: Protocol Buffer Definitions and Code Generation
# Status: done
# Dependencies: 13
# Priority: high
# Description: Update the orgs.proto file with OrgPhoneLine message definitions and all CRUD service methods, then regenerate Go and TypeScript bindings
# Details:
Update `lib/proto/hero/orgs/v1/orgs.proto` with: OrgPhoneLine message containing all fields (id, org_id, line_type, external_forwarding_number, twilio_receiving_number, twilio_number_sid, twiml_app_sid, is_primary_line, created_at, updated_at). Add service methods to OrgsService: CreateOrgPhoneLine, GetOrgPhoneLineByTwilioNumber, ListOrgPhoneLines, UpdateOrgPhoneLine, DeleteOrgPhoneLine, SetPrimaryOrgPhoneLine. Define all corresponding request/response message types with proper field definitions and validation. Use google.protobuf.Timestamp for timestamp fields. Regenerate Go bindings for services and TypeScript bindings for frontend integration.

# Test Strategy:
Validate proto file compiles without errors, verify all message fields have correct types and numbers, test generated Go code compiles and integrates with existing codebase, confirm TypeScript bindings generate correctly, validate service method signatures match requirements

# Subtasks:
## 1. Define OrgPhoneLine Message and Request/Response Types [done]
### Dependencies: None
### Description: Update the orgs.proto file to define the OrgPhoneLine message with all required fields and create all necessary request/response message types for CRUD operations
### Details:
In `lib/proto/hero/orgs/v1/orgs.proto`, define the OrgPhoneLine message with fields: string id, string org_id, string line_type, string external_forwarding_number, string twilio_receiving_number, string twilio_number_sid, string twiml_app_sid, bool is_primary_line, google.protobuf.Timestamp created_at, google.protobuf.Timestamp updated_at. Create request/response message types: CreateOrgPhoneLineRequest/Response, GetOrgPhoneLineByTwilioNumberRequest/Response, ListOrgPhoneLinesRequest/Response, UpdateOrgPhoneLineRequest/Response, DeleteOrgPhoneLineRequest/Response, SetPrimaryOrgPhoneLineRequest/Response. Use proper field numbering and include validation annotations where appropriate.

## 2. Add CRUD Service Methods to OrgsService [done]
### Dependencies: 14.1
### Description: Extend the OrgsService in the proto file with all required phone line management service methods
### Details:
In the existing OrgsService definition within `lib/proto/hero/orgs/v1/orgs.proto`, add the following RPC methods: CreateOrgPhoneLine(CreateOrgPhoneLineRequest) returns (CreateOrgPhoneLineResponse), GetOrgPhoneLineByTwilioNumber(GetOrgPhoneLineByTwilioNumberRequest) returns (GetOrgPhoneLineByTwilioNumberResponse), ListOrgPhoneLines(ListOrgPhoneLinesRequest) returns (ListOrgPhoneLinesResponse), UpdateOrgPhoneLine(UpdateOrgPhoneLineRequest) returns (UpdateOrgPhoneLineResponse), DeleteOrgPhoneLine(DeleteOrgPhoneLineRequest) returns (DeleteOrgPhoneLineResponse), SetPrimaryOrgPhoneLine(SetPrimaryOrgPhoneLineRequest) returns (SetPrimaryOrgPhoneLineResponse). Ensure each method uses the corresponding request/response types defined in the previous subtask.

## 3. Regenerate Go and TypeScript Bindings [done]
### Dependencies: 14.2
### Description: Generate updated Go and TypeScript code bindings from the modified proto file to enable integration with backend services and frontend applications
### Details:
Run the proto code generation pipeline to create updated bindings. For Go bindings, use protoc with go_out and grpc_out plugins to generate service interfaces and message structs that can be imported by the orgs service implementation. For TypeScript bindings, use protoc with ts_proto or similar plugin to generate TypeScript definitions and client stubs for frontend integration. Ensure the generated code includes all new OrgPhoneLine message types and service method signatures. Update any import paths or package references as needed to maintain compatibility with existing codebase structure.

