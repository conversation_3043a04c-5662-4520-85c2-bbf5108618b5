# Task ID: 28
# Title: Frontend: Phone Number Formatting & Display
# Status: pending
# Dependencies: 21, 24
# Priority: medium
# Description: Ensure all phone numbers in UI are normalized to E.164 and displayed correctly, including placeholders for missing numbers.
# Details:
Use google-libphonenumber (latest) for client-side formatting. Display numbers as '{AssetName} • ({number})' or '(---) --- ----' if missing. Validate formatting after save and on dropdown selection. Prevent manual entry; only allow selection from dropdown.

# Test Strategy:
Unit test formatting logic and display. Integration test with various asset data to verify correct rendering and placeholder usage.

# Subtasks:
## 1. Integrate google-libphonenumber for Phone Number Formatting [pending]
### Dependencies: None
### Description: Install and configure the latest google-libphonenumber (or a compatible JS port) in the frontend codebase. Implement utility functions to normalize all phone numbers to E.164 format and validate them on save and dropdown selection.
### Details:
Ensure the library is properly imported and available in all relevant components. Use the library's parsing and formatting methods to convert user-selected numbers to E.164 before display and storage. Handle errors for invalid or incomplete numbers gracefully.

## 2. Implement UI Rendering Logic for Formatted Numbers and Placeholders [pending]
### Dependencies: 28.1
### Description: Develop UI logic to display phone numbers in the format '{AssetName} • ({number})' when present, or '(---) --- ----' as a placeholder when missing. Prevent manual entry and enforce dropdown-only selection.
### Details:
Update relevant UI components to use the formatting utility. Ensure placeholders are shown for missing numbers and that the dropdown enforces selection from valid, formatted numbers only. Validate formatting after save and on dropdown selection.

## 3. Create Test Coverage for Formatting and Display Scenarios [pending]
### Dependencies: 28.1, 28.2
### Description: Write unit and integration tests to verify correct phone number formatting, placeholder rendering, and validation logic across all relevant UI states.
### Details:
Develop tests for various scenarios, including valid numbers, missing numbers, and edge cases. Ensure tests cover both formatting utilities and UI rendering, including dropdown selection and save validation.

