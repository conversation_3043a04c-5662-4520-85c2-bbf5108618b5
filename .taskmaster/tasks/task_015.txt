# Task ID: 15
# Title: Orgs Service Repository Layer Implementation
# Status: deferred
# Dependencies: 14
# Priority: high
# Description: Implement all database operations for phone line management in the PostgreSQL repository layer with proper error handling and transaction management
# Details:
Update `services/orgs/internal/data/postgres_orgs_repo.go` with complete CRUD operations: CreateOrgPhoneLine() with SQL INSERT and proper error handling for unique constraint violations, GetOrgPhoneLineByTwilioNumber() with optimized query using twilio_receiving_number index, ListOrgPhoneLines() with org_id filtering and optional line_type filtering, UpdateOrgPhoneLine() with SQL UPDATE and optimistic locking, DeleteOrgPhoneLine() with CASCADE handling, SetPrimaryOrgPhoneLine() with transaction to unset other primary lines and set new one. Implement proper SQL transactions for data consistency, comprehensive error handling with specific error types, row-level security integration, and connection pooling optimization. Add helper methods for validation and data transformation.

# Test Strategy:
Unit tests for each repository method with mock database, integration tests with real PostgreSQL instance, test error scenarios including constraint violations and concurrent access, validate transaction rollback on failures, test RLS policy enforcement, performance testing for query optimization
