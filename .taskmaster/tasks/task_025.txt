# Task ID: 25
# Title: Frontend: Call Module Inactive State Logic
# Status: pending
# Dependencies: 22
# Priority: high
# Description: Implement per-asset logic to 'brick' the Call module UI when the asset has no active calls, per PRD requirements.
# Details:
Query call_queue for asset_id and state != 'ended' via backend API. If no active calls, hide call button, remove panes, and show 'Call Module Inactive' banner and nav chip. Gear icon remains enabled for toggling off Mobile Dispatch. Use React conditional rendering and context for state management.

# Test Strategy:
Unit test state transitions and UI rendering. End-to-end test with simulated call_queue data to verify correct bricking/unbricking behavior.

# Subtasks:
## 1. Integrate Backend API for call_queue State [pending]
### Dependencies: None
### Description: Implement logic to query the backend API for call_queue data, filtering by asset_id and state != 'ended', to determine if there are any active calls for the asset.
### Details:
Use React Query or equivalent data-fetching mechanism to poll the backend for call_queue status. Ensure efficient polling and error handling. Store the result in context or component state for downstream UI logic.

## 2. Implement Conditional Rendering for Inactive State [pending]
### Dependencies: 25.1
### Description: Develop React conditional rendering logic to detect when an asset has no active calls and trigger the inactive state UI.
### Details:
Use context or props to pass call_queue state. Apply conditional rendering patterns (if/else, ternary, switch, etc.) to determine when to 'brick' the Call module UI based on the absence of active calls.

## 3. Update UI to Hide/Remove Controls in Inactive State [pending]
### Dependencies: 25.2
### Description: Modify the UI to hide the call button, remove call panes, and ensure the gear icon remains enabled when the Call module is inactive.
### Details:
Refactor component structure so that controls are only rendered when the module is active. Ensure the gear icon is always accessible for toggling Mobile Dispatch, regardless of call state.

## 4. Display 'Call Module Inactive' Banner and Nav Chip [pending]
### Dependencies: 25.2
### Description: Implement logic to show the 'Call Module Inactive' banner and navigation chip when there are no active calls for the asset.
### Details:
Add banner and nav chip components that are conditionally rendered based on the inactive state. Ensure correct styling and placement per PRD requirements.

## 5. Develop Unit and End-to-End Tests for State Transitions [pending]
### Dependencies: 25.1, 25.2, 25.3, 25.4
### Description: Write comprehensive unit and E2E tests to verify correct UI state transitions and rendering for both active and inactive call scenarios.
### Details:
Test all state transitions, including bricking and unbricking the module, UI updates, and banner/nav chip display. Use mocked API responses and simulate user interactions to ensure robust coverage.

