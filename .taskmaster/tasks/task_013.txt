# Task ID: 13
# Title: Database Migration and Schema Setup
# Status: done
# Dependencies: None
# Priority: high
# Description: Create the org_phone_lines table with proper constraints, indexes, and Row Level Security policies, then migrate existing phone line data from the orgs table
# Details:
Create migration file `migrations/20250709134932_add_org_phone_lines_table.sql` with the complete schema including: SERIAL PRIMARY KEY, foreign key to orgs table, line_type field, Twilio integration fields (twilio_receiving_number, twilio_number_sid, twiml_app_sid), is_primary_line boolean, timestamps, unique constraints on twilio_receiving_number and org_id+external_forwarding_number combination. Add performance indexes on twilio_receiving_number, org_id, org_id+line_type, and org_id+is_primary_line. Implement RLS policies following Hero Core patterns with org_phone_lines_organization_access and org_phone_lines_meta_org_access policies. Execute data migration to move existing phone line data from orgs table to org_phone_lines as 'general' type with is_primary_line=true. Clean up orgs table by dropping migrated columns: twilio_number, twilio_number_sid, twiml_app_sid, forwarded_from_number, call_forwarding_type, is_call_forwarding_enabled, sip_uri.

# Test Strategy:
Verify table creation with correct schema, validate all constraints and indexes are properly created, test RLS policies with different org contexts, confirm data migration preserves all existing phone line data without loss, validate cleanup removes only intended columns from orgs table, test rollback procedures work correctly

# Subtasks:
## 1. Create org_phone_lines table schema and constraints [done]
### Dependencies: None
### Description: Create the complete database schema for the org_phone_lines table with all required fields, constraints, and indexes
### Details:
Create migration file `migrations/20250709134932_add_org_phone_lines_table.sql` with: CREATE TABLE org_phone_lines with SERIAL PRIMARY KEY id, org_id INTEGER REFERENCES orgs(id) ON DELETE CASCADE, line_type VARCHAR(50) NOT NULL DEFAULT 'general', external_forwarding_number VARCHAR(20), twilio_receiving_number VARCHAR(20), twilio_number_sid VARCHAR(100), twiml_app_sid VARCHAR(100), is_primary_line BOOLEAN DEFAULT false, created_at TIMESTAMPTZ DEFAULT NOW(), updated_at TIMESTAMPTZ DEFAULT NOW(). Add UNIQUE constraints on twilio_receiving_number and (org_id, external_forwarding_number). Create indexes on twilio_receiving_number, org_id, (org_id, line_type), and (org_id, is_primary_line) for performance optimization.
<info added on 2025-08-18T21:49:11.917Z>
Implementation plan:
- Create new migration file: `migrations/20250819090000_add_org_phone_lines_table.sql` (via `make db-new` / goose create) with Up/Down blocks.
- Up: `CREATE TABLE org_phone_lines (...)` with:
  - id SERIAL PRIMARY KEY
  - org_id INTEGER NOT NULL REFERENCES orgs(id) ON DELETE CASCADE
  - line_type TEXT NOT NULL DEFAULT 'general'
  - external_forwarding_number TEXT
  - twilio_receiving_number TEXT
  - twilio_number_sid TEXT
  - twiml_app_sid TEXT
  - is_primary_line BOOLEAN NOT NULL DEFAULT FALSE
  - created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
  - updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
  - Constraints:
    - UNIQUE (twilio_receiving_number)
    - UNIQUE (org_id, external_forwarding_number)
  - Indexes:
    - CREATE INDEX idx_org_phone_lines_twilio_receiving_number ON org_phone_lines(twilio_receiving_number)
    - CREATE INDEX idx_org_phone_lines_org_id ON org_phone_lines(org_id)
    - CREATE INDEX idx_org_phone_lines_org_id_line_type ON org_phone_lines(org_id, line_type)
    - CREATE INDEX idx_org_phone_lines_org_id_is_primary ON org_phone_lines(org_id, is_primary_line)
- Down: Drop indexes, policies (if any created in this file), then DROP TABLE IF EXISTS org_phone_lines.
- Notes:
  - Keep twilio_receiving_number nullable initially to support gradual backfill; we can tighten later if needed.
  - Use plain CREATEs (not IF NOT EXISTS) so Goose properly fails on drift; idempotency handled by Down.
</info added on 2025-08-18T21:49:11.917Z>

## 2. Implement Row Level Security policies [done]
### Dependencies: 13.1
### Description: Add RLS policies to the org_phone_lines table following Hero Core security patterns
### Details:
Enable RLS on org_phone_lines table with ALTER TABLE org_phone_lines ENABLE ROW LEVEL SECURITY. Create org_phone_lines_organization_access policy allowing users to access phone lines for their organization using org_id matching current user's org context. Create org_phone_lines_meta_org_access policy for meta-organization access patterns. Follow existing Hero Core RLS policy patterns from other tables, ensuring proper security isolation between organizations while allowing necessary administrative access.
<info added on 2025-08-18T21:49:17.811Z>
Implementation plan details:
- Execute RLS enablement and policy creation in the same migration file after table creation to maintain consistency with existing Hero Core patterns (following call_forward_events table approach)
- Use exact SQL commands: ALTER TABLE org_phone_lines ENABLE ROW LEVEL SECURITY followed by CREATE POLICY statements
- org_phone_lines_organization_access policy: USING (org_id::text = current_setting('app.org_id')) WITH CHECK (org_id::text = current_setting('app.org_id')) for standard organization access
- org_phone_lines_meta_org_access policy: USING (current_setting('app.org_id') = '-1') WITH CHECK (current_setting('app.org_id') = '-1') for meta-organization administrative access
- Migration rollback: Include DROP POLICY IF EXISTS statements for both policies in down migration (RLS automatically disabled when table is dropped)
- Testing requirements: Verify read/write operations work correctly after setting app.org_id context variable, test both organization-level and meta-organization access patterns
</info added on 2025-08-18T21:49:17.811Z>

## 3. Migrate existing phone line data from orgs table [done]
### Dependencies: 13.2
### Description: Execute data migration to transfer existing phone line data from the orgs table to the new org_phone_lines table
### Details:
Create INSERT INTO org_phone_lines SELECT statement to migrate data from orgs table where phone line fields are not null. Map orgs.twilio_number to twilio_receiving_number, orgs.twilio_number_sid to twilio_number_sid, orgs.twiml_app_sid to twiml_app_sid, orgs.forwarded_from_number to external_forwarding_number. Set line_type to 'general' and is_primary_line to true for all migrated records. Handle NULL values appropriately and ensure data integrity during migration. Add validation queries to confirm all data was migrated correctly.
<info added on 2025-08-18T21:49:22.310Z>
Implementation plan:
- Backfill existing data from `orgs` to `org_phone_lines` with a single INSERT…SELECT:
```
INSERT INTO org_phone_lines (
  org_id, line_type, external_forwarding_number, twilio_receiving_number,
  twilio_number_sid, twiml_app_sid, is_primary_line
)
SELECT
  o.id,
  'general' AS line_type,
  NULLIF(o.forwarded_from_number, '') AS external_forwarding_number,
  NULLIF(o.twilio_number, '') AS twilio_receiving_number,
  NULLIF(o.twilio_number_sid, '') AS twilio_number_sid,
  NULLIF(o.twiml_app_sid, '') AS twiml_app_sid,
  TRUE AS is_primary_line
FROM orgs o
WHERE (o.twilio_number IS NOT NULL AND o.twilio_number <> '')
   OR (o.forwarded_from_number IS NOT NULL AND o.forwarded_from_number <> '');
```
- Post-backfill validation:
  - Compare counts where `orgs.twilio_number` not null vs inserted rows.
  - Spot check a few orgs.
- Do NOT add NOT NULL constraints yet; leave flexibility for orgs without numbers.
- Down: DELETE FROM org_phone_lines where created via backfill (heuristic: those with is_primary_line=true AND line_type='general').
</info added on 2025-08-18T21:49:22.310Z>

## 4. Clean up orgs table by removing migrated columns [done]
### Dependencies: 13.3
### Description: Remove the phone line related columns from the orgs table after successful data migration
### Details:
Execute ALTER TABLE orgs DROP COLUMN statements to remove: twilio_number, twilio_number_sid, twiml_app_sid, forwarded_from_number, call_forwarding_type, is_call_forwarding_enabled, sip_uri. Ensure all dependent views, functions, or triggers are updated before dropping columns. Add rollback instructions in case migration needs to be reversed. Verify no application code references these columns before removal.
<info added on 2025-08-18T21:49:25.865Z>
Implementation plan:
- Defer column drops until after Tasks 14–17 are complete and services are switched to use `org_phone_lines`.
- Prepare a separate migration for cleanup later:
  - ALTER TABLE orgs DROP COLUMN IF EXISTS twilio_number;
  - ALTER TABLE orgs DROP COLUMN IF EXISTS twilio_number_sid;
  - ALTER TABLE orgs DROP COLUMN IF EXISTS twiml_app_sid;
  - ALTER TABLE orgs DROP COLUMN IF EXISTS forwarded_from_number;
  - ALTER TABLE orgs DROP COLUMN IF EXISTS call_forwarding_type;
  - ALTER TABLE orgs DROP COLUMN IF EXISTS is_call_forwarding_enabled;
  - ALTER TABLE orgs DROP COLUMN IF EXISTS sip_uri;
- Include a reversible Down block that re-adds columns (nullable) to allow rollback.
- Preconditions: ensure no application code reads these columns (grep and remove usages), deploy API changes, and run smoke tests before dropping.
</info added on 2025-08-18T21:49:25.865Z>

