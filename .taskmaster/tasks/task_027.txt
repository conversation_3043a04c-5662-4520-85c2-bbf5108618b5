# Task ID: 27
# Title: Backend: Observability & Logging
# Status: pending
# Dependencies: 23
# Priority: medium
# Description: Add logging for config changes and client metrics for toggles and time-in-state per non-functional requirements.
# Details:
Instrument hero.orgs.v1: log org_id, updated_by_asset_id, old→new diff (enabled flag, assigned_asset_id, forward_to_number), and timestamps. Add Prometheus counters for enable/disable, override on/off, dispatcher changed; gauge/histogram for time-in-state if trivial.

# Test Strategy:
Unit test logging hooks and metric increments. Integration test log output and Prometheus scrape endpoints.

# Subtasks:
## 1. Implement Logging for Config Changes [pending]
### Dependencies: None
### Description: Add structured logging to hero.orgs.v1 for configuration changes, capturing org_id, updated_by_asset_id, old→new diff (enabled flag, assigned_asset_id, forward_to_number), and timestamps. Ensure logs are machine and human-readable, include necessary context, and avoid sensitive data.
### Details:
Follow best practices for structured logging (e.g., JSON or key=value), include all required metadata, and ensure logs are centralized and standardized across services. Redact or mask any sensitive information as needed.

## 2. Instrument Prometheus Metrics [pending]
### Dependencies: 27.1
### Description: Add Prometheus counters for enable/disable, override on/off, and dispatcher changed events in hero.orgs.v1. Implement gauge or histogram metrics for time-in-state if feasible.
### Details:
Expose metrics endpoints for Prometheus scraping. Ensure metrics are incremented or updated at appropriate points in the codebase and follow naming conventions for clarity and consistency.

## 3. Test Coverage for Logs and Metrics [pending]
### Dependencies: 27.1, 27.2
### Description: Develop unit and integration tests to verify correct logging of config changes and accurate Prometheus metric increments and values.
### Details:
Unit tests should validate that logs contain all required fields and that metrics are updated as expected. Integration tests should check log output and Prometheus scrape endpoints for correctness and completeness.

