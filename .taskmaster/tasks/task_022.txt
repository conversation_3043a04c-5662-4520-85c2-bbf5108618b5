# Task ID: 22
# Title: Frontend: State Management & API Integration
# Status: pending
# Dependencies: 21
# Priority: high
# Description: Integrate frontend with backend APIs for reading and updating call forwarding config, and manage local/global state.
# Details:
Use React Query for fetching/caching. Wire UpdateForwardingConfig and GetForwardingConfig. Implement conditional polling (~3s) using ETag/Last-Modified; on 304 keep cache, on 200 diff against previous config and show toast only for meaningful changes (enabled state, assigned asset, forward_to_number), never on initial load. Maintain previous config in memory; normalize phones to E.164 before send. Store config in shared context.

# Test Strategy:
Mock API responses in unit tests. Validate polling, config refresh, error handling, and optimistic updates. Use MSW for integration tests simulating backend responses.

# Subtasks:
## 1. React Query Setup for GetForwardingConfig [pending]
### Dependencies: None
### Description: Implement React Query's useQuery to fetch call forwarding configuration from the backend API, ensuring proper caching and error handling.
### Details:
Set up QueryClientProvider at the app root. Create a useQuery hook for GetForwardingConfig, specifying queryKey and queryFn. Handle loading and error states appropriately.

## 2. React Query Setup for UpdateForwardingConfig [pending]
### Dependencies: 22.1
### Description: Implement React Query's useMutation to update call forwarding configuration via backend API, with cache invalidation and optimistic updates.
### Details:
Create a useMutation hook for UpdateForwardingConfig. On success, invalidate GetForwardingConfig query to refetch updated data. Handle mutation errors and loading states.

## 3. Conditional Polling Implementation with ETag/Last-Modified [pending]
### Dependencies: 22.1
### Description: Integrate conditional polling (~3s) for GetForwardingConfig using ETag/Last-Modified headers to minimize unnecessary data fetching.
### Details:
Configure polling interval in useQuery. On each poll, send ETag/Last-Modified headers; if response is 304, retain cache; if 200, proceed to diff logic. Ensure polling does not occur on initial load.

## 4. In-Memory Previous Config Management and Diff Logic [pending]
### Dependencies: 22.1, 22.3
### Description: Maintain previous call forwarding config in memory and implement logic to diff against new config for meaningful changes.
### Details:
Store previous config in a React ref or context. On successful fetch (200), compare enabled state, assigned asset, and forward_to_number fields. Trigger downstream logic only for meaningful changes.

## 5. Toast Notification Logic for Meaningful Changes [pending]
### Dependencies: 22.4
### Description: Show toast notifications only when meaningful changes are detected in enabled state, assigned asset, or forward_to_number, excluding initial load.
### Details:
Integrate a toast notification system (e.g., Material UI Snackbar). Ensure notifications are triggered only when diff logic detects relevant changes, and suppress notifications on initial load.

## 6. E.164 Normalization Before API Send [pending]
### Dependencies: 22.2
### Description: Normalize phone numbers to E.164 format before sending updates to the backend API.
### Details:
Use a library (e.g., google-libphonenumber) to format phone numbers to E.164. Validate and transform numbers in the update mutation before API call.

## 7. Shared Context Integration for Config State [pending]
### Dependencies: 22.1, 22.2, 22.4
### Description: Store and provide call forwarding config state via a shared React context for access across relevant components.
### Details:
Create a React context to hold config state and previous config. Ensure context is updated on successful fetch and mutation, and consumed by child components as needed.

