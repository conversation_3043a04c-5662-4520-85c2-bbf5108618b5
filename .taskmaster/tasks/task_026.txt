# Task ID: 26
# Title: Frontend: Confirmation Dialogs & Error Handling
# Status: pending
# Dependencies: 21, 22
# Priority: medium
# Description: Implement confirmation dialogs for disabling Mobile Dispatch and toggling Override, and robust inline error handling for API failures.
# Details:
Use Material UI Dialog components for confirmations. Show dialog when disabling Mobile Dispatch (with targeted asset/number) and when turning off Override with a custom number. Inline error display for failed API calls with retry option. Ensure dialogs block destructive actions until confirmed.

# Test Strategy:
Unit test dialog open/close logic and error display. End-to-end test disabling flows and error scenarios with Cypress.

# Subtasks:
## 1. Implement Confirmation Dialog for Disabling Mobile Dispatch [pending]
### Dependencies: None
### Description: Create a Material UI confirmation dialog that appears when a user attempts to disable Mobile Dispatch. The dialog should display the targeted asset and/or number, block the action until confirmed, and handle cancel/confirm flows.
### Details:
Use Material UI Dialog components. Ensure the dialog interrupts the flow and requires explicit user confirmation before proceeding. Integrate with the relevant state and API logic to block destructive actions until confirmed.

## 2. Implement Confirmation Dialog for Toggling Override [pending]
### Dependencies: 26.1
### Description: Develop a Material UI confirmation dialog for toggling the Override setting, specifically when turning off Override with a custom number. The dialog should require user confirmation before committing the change.
### Details:
Utilize Material UI Dialog components. Display relevant information about the custom number being affected. Ensure the dialog blocks the action until the user confirms or cancels.

## 3. Implement Inline Error Handling and Retry Logic for API Failures [pending]
### Dependencies: 26.1, 26.2
### Description: Add robust inline error handling for failed API calls related to Mobile Dispatch and Override actions. Display error messages inline and provide a retry option for users.
### Details:
Show error messages directly in the UI near the affected controls. Implement a retry button that re-attempts the failed API call. Ensure errors are clearly communicated and do not block unrelated actions.

## 4. Develop Test Coverage for Dialog Flows and Error Scenarios [pending]
### Dependencies: 26.1, 26.2, 26.3
### Description: Create unit and end-to-end tests to validate dialog open/close logic, confirmation flows, error display, and retry functionality for all dialog and error scenarios.
### Details:
Use Jest and React Testing Library for unit tests. Use Cypress for end-to-end tests covering dialog interactions, error handling, and retry logic. Ensure all edge cases and user flows are tested.

