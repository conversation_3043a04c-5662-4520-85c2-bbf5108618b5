# Task ID: 24
# Title: Backend: Asset & Phone Number Source APIs
# Status: pending
# Dependencies: None
# Priority: high
# Description: Provide APIs to list assets and their contact numbers for populating selectors and dropdowns in the UI.
# Details:
Expose assets listing that returns {asset_id, asset_name, contact_no}. Normalize contact_no to E.164 and dedupe numbers per org (server-side) to power the override dropdown; sort by asset_name. Document response shape.

# Test Strategy:
Unit test asset queries and normalization logic. Integration test API responses for various orgs. Validate uniqueness and E.164 formatting in returned data.

# Subtasks:
## 1. Implement Asset Listing API Endpoint [pending]
### Dependencies: None
### Description: Develop a backend API endpoint that returns a list of assets with their associated contact numbers, exposing the fields {asset_id, asset_name, contact_no}.
### Details:
Design the API route, request/response schema, and integrate with the data source to fetch asset records. Ensure the endpoint is accessible and returns the required fields for each asset.

## 2. Server-Side E.164 Normalization and Deduplication [pending]
### Dependencies: 24.1
### Description: Normalize all contact_no values to E.164 format and deduplicate phone numbers per organization before returning results from the API.
### Details:
Integrate a phone number normalization library (e.g., Google's libphonenumber) to convert all numbers to E.164 format. Implement logic to remove duplicate numbers within each organization, ensuring only unique, normalized numbers are included in the response.

## 3. Sorting and Response Shaping [pending]
### Dependencies: 24.2
### Description: Sort the asset list by asset_name and ensure the API response matches the documented shape, including only the required fields.
### Details:
Apply server-side sorting by asset_name after normalization and deduplication. Structure the response to strictly adhere to the specified schema, omitting any extraneous fields.

## 4. API Documentation and Test Coverage [pending]
### Dependencies: 24.3
### Description: Document the API endpoint, response shape, and normalization logic. Implement unit and integration tests to validate correct formatting, deduplication, and sorting.
### Details:
Write clear API documentation covering request/response formats and normalization rules. Develop unit tests for normalization and deduplication logic, and integration tests to verify end-to-end API behavior for various organizations and data scenarios.

