Call Forwarding (Mobile Dispatch) – PRD

Status (current branch)
- Backend refactor complete: configuration now persists in org_call_forwarding_config; orgs table no longer stores forwarding fields.
- UpdateForwardingConfig returns ForwardingConfig; new read RPC GetForwardingConfig added.
- Validation simplified: enabling requires a forward_to_number (either in request or already stored). Assigned_asset_id is optional. Phone is normalized to E.164.
- Event emission for config changes is not implemented in this refactor (deferred).
- Breaking change: UpdateForwardingConfigResponse now returns { config } instead of { org }.

Overview
- Goal: Enable a per-asset UI control within the Call module that toggles a global org-level call forwarding setting ("Mobile Dispatch"). Any asset can enable/disable; the effect is global. Settings selection includes an assigned asset (for labeling/events) and a forwarded phone number (from org assets’ numbers). The Call module UI becomes inactive per-asset when that asset has no active calls.
- Primary Users: Dispatch supervisors/admins and dispatchers using the CAD web application.
- Platforms: Web Command & Control only (for now).

Key Outcomes
- Operators can enable/disable Mobile Dispatch from the Call module via a gear icon settings panel.
- On enable: global toast announces “Dispatch Forwarded to {AssetName} • {Number}” to all users currently in CAD (5–10s). [Deferred until eventing lands]
- The Call module UI “bricks” (shows “Call Module Inactive”) per-asset when that asset has no active calls in call_queue (state != ended). Assets with active calls remain fully functional until they no longer have active calls.

In Scope
- UI entrypoint: gear icon next to the phone icon in Call module opens a dedicated Call Settings panel.
- Settings panel controls:
  - Toggle: "Enable mobile dispatch" (global effect).
  - Dropdown: "Assigned Dispatcher" -> asset selector. Default: current viewing asset. The selection couples asset id, name, and contact number for display as "{AssetName} • ({number})"; when the asset has no number, show placeholder "(---) --- ----".
  - Checkbox + Dropdown: "Override Default Number". When unchecked, the effective number equals the selected asset’s contact_no and is read-only. When checked, show an "Assigned Phone Number" dropdown listing unique values from assets.contact_no for the org; manual entry is not allowed.
  - Confirm/Cancel buttons. Confirm is enabled only when the toggle is on and an effective phone number is determined (from assigned asset or override dropdown). [UI]
- Save flow:
  - Use orgs service UpdateForwardingConfig to set: is_call_forwarding_enabled (BoolValue), forward_to_number (StringValue), assigned_asset_id (StringValue).
  - On success: clients refresh local state; toast/eventing is deferred.
- Inactive (“bricked”) Call module state:
  - Call button hidden; on-hold/inbound panes removed; banner shows “Call Module Inactive”.
  - Gear icon remains; settings panel allows toggling off Mobile Dispatch. When toggled off, Call module returns to normal behavior.

Validation rules (current backend)
- forward_to_number required when enabling; may be omitted only if one already exists in stored config.
- assigned_asset_id optional.
- Phone number is normalized to E.164 on write; no enforcement that it matches assets.contact_no (UI should enforce selection list).

Eventing/real-time sync
- Planned: emit call_forwarding.config.updated on change including: orgId, isEnabled, assignedAssetId, forwardToNumber, assignedAssetName. [Deferred]
  - Global toast note: Global toasts require broadcasting this event and client subscription via WS/polling. If eventing remains deferred, only the initiating client shows a local success toast; cross-client toast is deferred.
  - Conditional polling: Clients MAY poll GetForwardingConfig every ~3s using ETag or Last-Modified (based on updated_at). Server SHOULD return 304 when unchanged; on 200 with changed config, clients compare to prior and show a toast only when a meaningful field changed (enabled state, assigned asset, or number).

Conditional Polling (optional but recommended now)
- Server behavior:
  - Include ETag (e.g., hash of {is_enabled, assigned_asset_id, forward_to_number} or a strong value derived from updated_at) and Last-Modified (from updated_at) headers in GetForwardingConfig responses.
  - If If-None-Match/If-Modified-Since match current state, respond 304 (no body). Otherwise respond 200 with the latest config and updated headers.
- Client behavior:
  - Poll every ~3s while CAD is open (per-tab); use If-None-Match and/or If-Modified-Since with last received values.
  - Maintain previous config in memory. On 200, compute a diff and only show a toast for “meaningful changes”: enabled flag, assigned_asset_id, or forward_to_number. Suppress toast on initial load.
  - Update local UI state to reflect the latest config; do not block user interactions while polling.

Out of Scope (now)
- Permissions and role gating (assume any dispatcher/admin in CAD can toggle).
- Multi-platform support (responder/member apps).
- Phone number conflict resolution or provisioning. Numbers are sourced from assets only; conflicts not anticipated.
- Remembering last dispatcher selection in org config (only persisted in events for now).

Functional Details
- Global vs per-asset behavior:
  - Toggle is global (org-level setting). Any asset can toggle.
  - UI bricking is evaluated per asset: an asset’s Call module becomes inactive only when it has no active calls.
- Activation logic (per asset):
  - A call is “active” if any row exists in call_queue with asset_id == <asset> and state != "ended".
  - States (reference): waiting, active, pending_selective_assignment, hold, ended.
  - On enable:
    - If the viewing asset still has any non-ended calls, their Call module stays normal until all calls end, then transitions to inactive.
    - If no calls are active, transition to inactive immediately.
  - On disable: Call module returns to normal state.
- Defaults:
  - assigned_asset_id defaults to the current viewing asset.
  - forward_to_number defaults to the selected assigned asset’s contact_no. If the assigned asset has no number, the UI shows placeholder "(---) --- ----" and the user must enable "Override Default Number" and choose a number before enabling.

Data Model (current)
- org_call_forwarding_config table (source of truth):
  - org_id (int, PK, FK orgs.id)
  - is_call_forwarding_enabled (boolean, default false)
  - forward_to_number (text, nullable)
  - assigned_asset_id (text, nullable, FK assets.id)
  - updated_at (timestamptz)
- orgs table: forwarding columns removed; still has forwarded_from_number (unchanged) if present.
- assets table: source for asset list and phone numbers (unique contact_no values per org).

API (current)
- UpdateForwardingConfig (hero.orgs.v1)
  - Request: { org_id, is_call_forwarding_enabled?: BoolValue, forward_to_number?: StringValue, assigned_asset_id?: StringValue }
  - Response: { config: ForwardingConfig }
  - Behavior: upsert into org_call_forwarding_config; normalizes phone; assigned_asset_id persisted.
- GetForwardingConfig (hero.orgs.v1)
  - Request: { org_id }
  - Response: { config: ForwardingConfig }
- ForwardingConfig fields: { org_id, is_call_forwarding_enabled: BoolValue, forward_to_number: string, assigned_asset_id?: StringValue }

Eventing
- On update: planned broadcast (toast), see "Eventing/real-time sync". [Deferred]

UX Requirements
- Gear icon opens a right-side or panel modal titled “Active Calls – Settings”.
- Controls and tooltips per provided mocks (info tooltip near toggle; descriptive helper text under toggle when enabled).
- Settings access: while the viewing asset has any non-ended call, the settings button is disabled and shows tooltip “End call to access settings.”
- Save button:
  - Enabled only when (toggle on) AND assigned_asset_id selected AND an effective forward_to_number is determined (from assigned asset or override dropdown) (UI-level rule).
  - Disabled when toggle off and no changes.
- Error handling:
  - Inline error if API fails; retry option.
  - Backend prevents enabling without a number (unless stored from prior state).
- State display:
  - When inactive: banner and nav chip show “Call Module Inactive”.
  - When toggled off: remove banner and restore normal components.
 - Confirmation dialogs:
  - When disabling Mobile Dispatch, show confirmation dialog indicating the currently targeted asset and number.
  - When Override Default Number is ON with a custom number and the user turns it OFF, confirm the number will revert to the assigned asset’s default.

Non-Functional Requirements
- Real-time reflection: median < 1s after event for toast display and state refresh (assuming WS). Polling fallback acceptable. [Deferred]
- Reliability: last-write-wins; subsequent clients render the latest server config.
- Observability: log config changes and (future) event emissions; add client metrics for toggles and time-in-state.

Acceptance Criteria (updated)
1) UpdateForwardingConfig updates org_call_forwarding_config and returns the latest ForwardingConfig.
2) GetForwardingConfig returns the current ForwardingConfig for the org.
3) Enabling without a number fails unless a number already exists in stored config.
4) Disabling returns enabled=false; number is preserved.
5) UI enforces both assigned_asset_id and forward_to_number selection when enabling; backend only requires forward_to_number.
6) Phone number shown in UI is normalized to E.164 after save.
7) Settings button is disabled while the viewing asset has any non-ended call and shows the tooltip “End call to access settings.”
8) Default number derives from the selected assigned asset; if that asset has no number, the UI shows placeholder “(---) --- ----” and requires choosing a number via Override before enabling.
9) Override Default Number presents a dropdown of unique assets.contact_no values; manual entry is not allowed.
10) Disabling Mobile Dispatch and turning OFF Override (when a custom number exists) show confirmation dialogs and behave as described.

Open Questions (tracked but not blocking)
- Permissions/roles allowed to toggle (admin-only vs dispatcher leads).
- Persisting the last selected assigned_asset_id beyond events.
- Exact toast copy and styling; final UX polish for the inactive banner and icons.
