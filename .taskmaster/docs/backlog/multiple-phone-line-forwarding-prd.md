# Multiple Phone Line Forwarding - Product Requirements Document

## Executive Summary

This PRD outlines the implementation of multiple phone line support for Hero Core organizations, enabling each organization to manage multiple distinct phone lines (general, emergency, parking, etc.) instead of the current single-line limitation. This feature will provide enhanced call routing, better operational efficiency, and improved customer experience while maintaining full backward compatibility.

**Reference Implementation**: A proof-of-concept was completed on branch `f/call-multiple-line-forwarding` (commits d8169d785 through b2d575689) that demonstrates the full architecture and provides implementation blueprints for all components.

## Problem Statement

### Current Architecture Limitations
- Organizations are restricted to a single phone line stored directly in the `orgs` table
- All calls route through the same Twilio number with no context differentiation
- No ability to identify which service line (emergency vs. general) received a call
- Limited operational flexibility for multi-department organizations

### Business Impact
- Emergency calls cannot be distinguished from general inquiries
- Poor caller experience due to lack of direct department routing
- Operational inefficiency requiring manual call classification
- Scalability limitations for growing organizations

## Solution Architecture

### Database Design

#### New `org_phone_lines` Table
Based on the reference implementation, create a new table to replace the phone line fields currently in the `orgs` table:

```sql
CREATE TABLE org_phone_lines (
    id SERIAL PRIMARY KEY,
    org_id INTEGER NOT NULL REFERENCES orgs(id) ON DELETE CASCADE,
    line_type TEXT NOT NULL,                           -- "general", "emergency", "parking", etc.
    external_forwarding_number TEXT,                   -- Customer's number that forwards to us (optional)
    twilio_receiving_number TEXT NOT NULL,             -- Our Twilio number that receives calls
    twilio_number_sid TEXT,                            -- Twilio's SID for API operations
    twiml_app_sid TEXT,                                -- TwiML application SID for call handling
    is_primary_line BOOLEAN DEFAULT FALSE,             -- Primary line for backwards compatibility
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(twilio_receiving_number),                   -- Each Twilio number used once globally
    UNIQUE(org_id, external_forwarding_number)        -- Each org's external number used once (if provided)
);

-- Performance indexes
CREATE INDEX idx_org_phone_lines_twilio_receiving_number ON org_phone_lines(twilio_receiving_number);
CREATE INDEX idx_org_phone_lines_org_id ON org_phone_lines(org_id);
CREATE INDEX idx_org_phone_lines_org_id_line_type ON org_phone_lines(org_id, line_type);
CREATE INDEX idx_org_phone_lines_org_id_is_primary ON org_phone_lines(org_id, is_primary_line);

-- Row Level Security (following existing Hero Core patterns)
ALTER TABLE org_phone_lines ENABLE ROW LEVEL SECURITY;
CREATE POLICY "org_phone_lines_organization_access" ON org_phone_lines
    USING (org_id::text = current_setting('app.org_id'))
    WITH CHECK (org_id::text = current_setting('app.org_id'));
CREATE POLICY "org_phone_lines_meta_org_access" ON org_phone_lines
    USING (current_setting('app.org_id') = '-1')
    WITH CHECK (current_setting('app.org_id') = '-1');
```

#### Data Migration Strategy
The reference implementation shows how to seamlessly migrate existing data:

```sql
-- Migrate existing data from orgs table to org_phone_lines
INSERT INTO org_phone_lines (org_id, line_type, twilio_receiving_number, twilio_number_sid, twiml_app_sid, is_primary_line)
SELECT id, 'general', twilio_number, twilio_number_sid, twiml_app_sid, true
FROM orgs
WHERE twilio_number IS NOT NULL AND twilio_number != '';

-- Clean up orgs table by removing moved columns
ALTER TABLE orgs DROP COLUMN IF EXISTS twilio_number;
ALTER TABLE orgs DROP COLUMN IF EXISTS twilio_number_sid;
ALTER TABLE orgs DROP COLUMN IF EXISTS twiml_app_sid;
-- Also remove deprecated columns
ALTER TABLE orgs DROP COLUMN IF EXISTS forwarded_from_number;
ALTER TABLE orgs DROP COLUMN IF EXISTS call_forwarding_type;
ALTER TABLE orgs DROP COLUMN IF EXISTS is_call_forwarding_enabled;
ALTER TABLE orgs DROP COLUMN IF EXISTS sip_uri;
```

### Protocol Buffer Definitions

Based on the reference implementation in `lib/proto/hero/orgs/v1/orgs.proto`, add the following definitions:

#### Data Models
```protobuf
message OrgPhoneLine {
    int32 id = 1;
    int32 org_id = 2;
    string line_type = 3;
    string external_forwarding_number = 4;
    string twilio_receiving_number = 5;
    string twilio_number_sid = 6;
    string twiml_app_sid = 7;
    bool is_primary_line = 8;
    google.protobuf.Timestamp created_at = 9;
    google.protobuf.Timestamp updated_at = 10;
}
```

#### Service Methods
```protobuf
service OrgsService {
    // Add these methods to existing service
    rpc CreateOrgPhoneLine(CreateOrgPhoneLineRequest) returns (CreateOrgPhoneLineResponse) {}
    rpc GetOrgPhoneLineByTwilioNumber(GetOrgPhoneLineByTwilioNumberRequest) returns (GetOrgPhoneLineByTwilioNumberResponse) {}
    rpc ListOrgPhoneLines(ListOrgPhoneLinesRequest) returns (ListOrgPhoneLinesResponse) {}
    rpc UpdateOrgPhoneLine(UpdateOrgPhoneLineRequest) returns (UpdateOrgPhoneLineResponse) {}
    rpc DeleteOrgPhoneLine(DeleteOrgPhoneLineRequest) returns (DeleteOrgPhoneLineResponse) {}
    rpc SetPrimaryOrgPhoneLine(SetPrimaryOrgPhoneLineRequest) returns (SetPrimaryOrgPhoneLineResponse) {}
}
```

#### Request/Response Messages
```protobuf
message CreateOrgPhoneLineRequest {
    int32 org_id = 1;
    string line_type = 2;
    string external_forwarding_number = 3;
    string twilio_receiving_number = 4;
    string twilio_number_sid = 5;
    string twiml_app_sid = 6;
    bool is_primary_line = 7;
}

message CreateOrgPhoneLineResponse {
    OrgPhoneLine org_phone_line = 1;
}

message GetOrgPhoneLineByTwilioNumberRequest {
    string twilio_receiving_number = 1;
}

message GetOrgPhoneLineByTwilioNumberResponse {
    OrgPhoneLine org_phone_line = 1;
    bool found = 2;
}

// Additional request/response messages for other operations...
```

## Implementation Plan

### Phase 1: Foundation & Data Layer (Orgs Service)

#### 1.1 Database Migration
- **File**: `migrations/20250709134932_add_org_phone_lines_table.sql`
- Create `org_phone_lines` table with full schema
- Implement data migration from existing `orgs` table
- Set up proper indexes and RLS policies
- Remove deprecated columns from `orgs` table

#### 1.2 Protocol Buffer Updates
- **File**: `lib/proto/hero/orgs/v1/orgs.proto`
- Add `OrgPhoneLine` message definition
- Add all CRUD service methods for phone line management
- Add corresponding request/response messages
- Regenerate Go and TypeScript bindings

#### 1.3 Repository Layer Implementation
- **File**: `services/orgs/internal/data/postgres_orgs_repo.go`
- Implement `CreateOrgPhoneLine()` method
- Implement `GetOrgPhoneLineByTwilioNumber()` method
- Implement `ListOrgPhoneLines()` method
- Implement `UpdateOrgPhoneLine()` method
- Implement `DeleteOrgPhoneLine()` method
- Implement `SetPrimaryOrgPhoneLine()` method

**Key Implementation Notes from Reference**:
- Use proper SQL transactions for data consistency
- Include comprehensive error handling
- Implement row-level security checks
- Handle unique constraint violations gracefully

#### 1.4 Use Case Layer Implementation
- **File**: `services/orgs/internal/usecase/orgs_usecase.go`
- Implement business logic for all phone line operations
- Add validation for line types ("general", "emergency", "parking", etc.)
- Implement primary line management logic
- Handle organization existence validation

**Key Validation Logic from Reference**:
```go
// Validate line type
func ValidateLineType(lineType string) error {
    validTypes := []string{"general", "emergency", "parking", "dispatch", "admin"}
    for _, validType := range validTypes {
        if lineType == validType {
            return nil
        }
    }
    return fmt.Errorf("invalid line type: %s", lineType)
}
```

#### 1.5 Bootstrap Script Updates
- **File**: `bootstrap/config/recipes/org_bootstrap_local.yaml`
- Update organization creation to use phone line endpoints
- Create primary phone line after organization creation
- Remove direct phone line fields from organization creation

**Reference Implementation Pattern**:
```yaml
- name: create_primary_phone_line
  description: Create the primary phone line for the organization
  rpc: hero.orgs.v1.OrgsService/CreateOrgPhoneLine
  payload:
    org_id: "${create_org.org.id}"
    line_type: "general"
    twilio_receiving_number: "+18556976298"
    twiml_app_sid: "AP367659bb9684ba033a80685ee537ffcc"
    is_primary_line: true
  depends_on:
    - create_org
```

### Phase 2: Communications Service Integration

#### 2.1 Call Queue Schema Updates
The reference implementation identified this as the next critical step:

- **File**: Need new migration for `call_queue` table
- Add `org_phone_line_id` column with foreign key to `org_phone_lines`
- Create index on the new column for query performance
- Backfill existing calls with primary line references

```sql
-- New migration needed
ALTER TABLE call_queue 
ADD COLUMN org_phone_line_id INTEGER 
REFERENCES org_phone_lines(id);

CREATE INDEX idx_call_queue_org_phone_line_id ON call_queue(org_phone_line_id);

-- Backfill existing calls with primary line
UPDATE call_queue 
SET org_phone_line_id = (
    SELECT id FROM org_phone_lines 
    WHERE org_phone_lines.org_id = call_queue.org_id 
    AND is_primary_line = true
);
```

#### 2.2 Call Queue Repository Updates
- **File**: `services/communications/internal/cellularcall/data/postgres_callqueue_repo.go`
- Update all call creation methods to capture phone line context
- Modify query methods to include phone line information
- Update webhook handling to identify which line received the call

**Reference Implementation Notes**:
The branch shows a comment "Delete this comment" indicating work was started but not completed on this file.

#### 2.3 Webhook Handler Updates
- Update Twilio webhook handlers to identify receiving phone line
- Modify call creation logic to preserve line context throughout call lifecycle
- Ensure backward compatibility for existing single-line setups

### Phase 3: Development Environment & Testing

#### 3.1 Local Development Setup
- **Files**: `scripts/twilio-webhook-dev/test-setup.sh`, `scripts/twilio-webhook-dev/update-twilio-webhooks.sh`
- Update scripts to handle multiple phone lines
- Modify webhook configuration for testing
- Update development documentation

#### 3.2 Testing Strategy
- End-to-end testing with multiple phone lines
- Verify call routing maintains line context
- Test backward compatibility with existing single-line organizations
- Validate data migration integrity

## Business Requirements

### Functional Requirements

#### Multi-Line Management
- Organizations can create multiple phone lines with distinct purposes
- Each line has a configurable type (general, emergency, parking, etc.)
- Support for external forwarding numbers (customer's number that forwards to Hero)
- Primary line designation for backward compatibility

#### Call Context Preservation
- Every incoming call tracks which specific phone line received it
- Call queue maintains phone line context throughout call lifecycle
- APIs return phone line information with call data

#### Backward Compatibility
- Existing single-line organizations continue working without changes
- Migration automatically creates primary "general" line from existing data
- All existing APIs remain functional

### Non-Functional Requirements

#### Performance
- Database queries optimized with proper indexing
- Phone line lookups should be sub-100ms
- Migration should complete without significant downtime

#### Security
- Row-level security policies protect multi-tenant data
- Phone line management respects organization boundaries
- Audit trail for all phone line configuration changes

#### Scalability
- Support for unlimited phone lines per organization
- Efficient querying even with large numbers of lines
- Proper database constraints prevent data inconsistencies

## Success Metrics

### Technical Metrics
- Zero data loss during migration
- All existing API endpoints continue working
- New phone line operations complete within SLA
- Database migration completes in under 5 minutes

### Business Metrics
- Organizations can successfully configure multiple phone lines
- Call routing accuracy improves (measured by reduced manual routing)
- Customer satisfaction increases due to direct department access
- Zero downtime during feature deployment

## Risk Assessment & Mitigation

### High Risk: Data Migration Complexity
- **Mitigation**: Thorough testing on staging environment
- **Rollback Plan**: Migration includes complete rollback procedures
- **Testing**: Validate data integrity before and after migration

### Medium Risk: Communications Service Integration
- **Mitigation**: Phase implementation to isolate changes
- **Testing**: Comprehensive end-to-end call flow testing
- **Monitoring**: Enhanced observability during rollout

### Low Risk: API Breaking Changes
- **Mitigation**: All changes are additive, maintaining backward compatibility
- **Validation**: Existing API contracts remain unchanged

## Documentation Requirements

### Technical Documentation
- Updated API documentation for new phone line endpoints
- Database schema documentation updates
- Operations runbook for phone line management

### User Documentation  
- Admin guide for configuring multiple phone lines
- Best practices for line type organization
- Troubleshooting guide for call routing issues

## Appendix: Reference Implementation Analysis

The proof-of-concept branch `f/call-multiple-line-forwarding` provides complete implementation blueprints:

### Completed Components (Reference)
✅ **Database Schema**: Complete `org_phone_lines` table with proper constraints and RLS  
✅ **Protocol Buffers**: Full API definition with all CRUD operations  
✅ **Orgs Service**: Complete business logic and data layer implementation  
✅ **Bootstrap Scripts**: Updated for multi-line organization creation  
✅ **Local Development**: Working configuration for testing  

### Identified Next Steps (From Branch Notes)
🔄 **Communications Service**: Schema updates and webhook integration  
⏳ **Frontend Integration**: Admin UI and call queue enhancements  
⏳ **Advanced Features**: Line-specific routing and analytics  

### Key Implementation Insights
- Migration strategy successfully preserves backward compatibility
- RLS policies follow existing Hero Core security patterns  
- API design supports both single and multi-line use cases
- Performance considerations addressed through strategic indexing

---

*This PRD is based on a complete proof-of-concept implementation that validates the technical feasibility and provides detailed implementation blueprints for all components. The reference branch demonstrates that this architecture successfully maintains backward compatibility while enabling advanced multi-line functionality.*