Problem Statement: Authentication & Logout Issues

  🎯 Current System Architecture

  Frontend:
  - Next.js SPA with output: "export" (static files)
  - Deployed to S3 + CloudFront
  - Uses AWS Amplify SDK for auth client-side

  Authentication Flow:
  - <PERSON><PERSON>@Edge (cognito-at-edge library) intercepts all requests
  - Redirects unauthenticated users to Cognito Hosted UI
  - Sets HttpOnly cookies with JWT tokens after successful auth
  - Client-side code reads cookies and copies to localStorage

  Infrastructure:
  - CloudFront serves static files from S3
  - Lambda@Edge runs on VIEWER_REQUEST events
  - Custom domain: command.gethero.com
  - Auth domain: auth.gethero.com

  🐛 Observed Issues

  Issue #1: Logout Cookies Not Clearing
  - Symptom: After logout, users can still access protected pages
  - Root Cause: Cookies set at different domain levels not being cleared
  - Evidence: removeTokensFromCookies() was only clearing cookies without explicit domain attributes
  - Status: Fixed by adding domain-specific cookie cleanup

  Issue #2: OAuth Redirect URL Corruption
  - Symptom: URL https://command.gethero.com/login.txt?from=%2Fcad appears after logout
  - Expected: https://command.gethero.com/login?from=%2Fcad
  - Impact: "Failed to fetch RLS payload" error, white page with text content

  Issue #3: Lambda@Edge URL Rewriting Conflict
  - Code Location: authorizationLambda/index.js line 16
  - Logic: Adds .html extension to URLs without file extensions
  - Conflict: OAuth redirects to /login get rewritten to /login.html
  - Mystery: .html somehow becomes .txt (unconfirmed mechanism)

  🔍 Technical Root Causes

  1. Static Export + OAuth Mismatch
  - Next.js static export creates individual .html files
  - OAuth redirects expect dynamic routing capability
  - Lambda@Edge tries to bridge this gap with URL rewriting

  2. Multi-Layer Cookie Management
  - cognito-at-edge sets cookies server-side
  - Amplify SDK manages auth state client-side
  - Cookie domain attributes not explicitly controlled

  3. URL Rewriting Logic Gap
  - nextJSDynamicRouting function assumes all extension-less URLs should get .html
  - OAuth callback URLs (/login?params) don't fit this pattern
  - No special handling for auth-related routes

  📊 System Complexity Assessment

  Components Involved in Auth Flow:
  1. AWS Cognito User Pool
  2. Cognito Hosted UI
  3. CloudFront Distribution
  4. Lambda@Edge Function (cognito-at-edge)
  5. S3 Static Files
  6. Next.js Client App
  7. Amplify SDK
  8. Custom cookie management utilities

  Failure Points:
  - Any of these 8 components can cause auth to break
  - Debugging requires understanding all layers
  - Changes to one component can affect others unexpectedly

  🎯 Core Architectural Question

  The fundamental tension:
  - Static deployment (S3 + CloudFront) for performance/cost
  - Dynamic auth features (OAuth redirects, server-side cookie management)

  Current approach: Bridge the gap with Lambda@Edge complexity
  Alternative approaches: Choose one paradigm (fully static or fully dynamic)

  📈 Impact Assessment

  User Experience:
  - Intermittent auth failures
  - Confusing error messages
  - Manual page refreshes required

  Developer Experience:
  - Complex debugging across multiple AWS services
  - Non-obvious error sources
  - Time spent on infrastructure instead of features

  Operational Risk:
  - Auth issues are high-severity incidents
  - Multiple single points of failure
  - Difficult to reproduce issues locally

  This is a classic case of architectural complexity debt - the system works but has accumulated complexity that makes it fragile and hard to
  maintain.

    Lambda@Edge (Current)

  User Request → CloudFront → Lambda@Edge Auth Check → S3 Static Files
                                ↓
                           If not auth: redirect to Cognito
                           If auth: serve content

  Protection Level: Server-side (bulletproof)
  When auth runs: Before any content loads
  User experience: Seamless (if working)

  Amplify Built-in (Alternative)

  User Request → CloudFront → S3 Static Files → React App Loads → Amplify Auth Check
                                                      ↓
                                                If not auth: redirect to Cognito
                                                If auth: show content

  Protection Level: Client-side (user can disable JavaScript)
  When auth runs: After page loads and React hydrates
  User experience: Brief loading screen, then auth redirect

  https://claude.ai/public/artifacts/98200cad-f144-4d7c-85e6-a4d3e4ad0804