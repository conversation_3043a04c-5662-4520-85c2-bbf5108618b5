# Call Forwarding for Inbound Dispatch Line

## Overview

Certain customers don't use <PERSON>'s browser-based queue during off-hours. They need every inbound call to ring a designated duty phone first, but still want the safety net of the existing agent queue if that phone is busy or unanswered.

This feature implements per-organization instant call forwarding with automatic fallback to existing queue logic and comprehensive observability.

## Core Features

| #   | Feature                | What it does                                                                  | Why it's important                                | Implementation                                                                            |
| --- | ---------------------- | ----------------------------------------------------------------------------- | ------------------------------------------------- | ----------------------------------------------------------------------------------------- |
| 1   | Forwarding Toggle      | Uses existing `is_call_forwarding_enabled` boolean on orgs table              | Enables/disables behavior without code deployment | Simple UPDATE via orgs service                                                            |
| 2   | Forward Destination    | Uses `forward_to_number` field (rename from `primary_phone_number`)           | Clearer naming for forward destination            | Simple column rename migration                                                            |
| 3   | Entry-point Forwarding | `HandleVoiceRequest` checks flag and returns forward TwiML before queue logic | Callers reach real person faster during off-hours | New `ForwardDialResponse` in `twiml/builder.go`                                           |
| 4   | Timeout + Fallback     | Failed forwards automatically trigger existing QueueCall flow                 | Guarantees no dead-ends for callers               | New `/twiml/forward-action` endpoint for `<Dial action>`; reuse `/callstatus` for logging |
| 5   | Caller-ID Control      | Uses existing `forwarded_from_number` field for caller ID override            | Compliance & branding requirements                | TwiML callerId attribute                                                                  |
| 6   | Forward Event Logging  | New call_forward_events table for audit trail                                 | SLA metrics & troubleshooting                     | Separate from call_queue table                                                            |

## User Experience

### Personas

- **Duty Dispatcher** – Needs after-hours calls forwarded to their mobile device
- **Internal Admin** – Manages forwarding configuration via API (this can be masked via a very simple toggle in the call module or something for now, for PoC)
- **End Callers** – Experience seamless routing with automatic fallback

### Key User Flows

1. **Forward Enabled** → Next inbound call rings duty phone first
2. **Forward Answered** → Call connects directly; bypass queue entirely
3. **Forward Failed** (no answer/busy/failed after 20s) → System automatically queues caller using existing flow
4. **Forward Disabled** → Behavior returns to normal queue-first operation

### System Components

- **PostgreSQL** – New call_forward_events table; existing orgs table columns
- **Communications Service** – Enhance `HandleVoiceRequest`; add forward action endpoint; reuse `/callstatus` for status logging
- **Twilio Programmable Voice** – Leverages `<Dial>` verb for PSTN forwarding
- **Existing Services** – Orgs service for configuration management

### Data Models

#### orgs table (modify existing column + existing columns)

| Column                       | Type    | Notes                                          |
| ---------------------------- | ------- | ---------------------------------------------- |
| `is_call_forwarding_enabled` | boolean | Already exists; controls feature toggle        |
| `forward_to_number`          | text    | Rename from `primary_phone_number` for clarity |
| `forwarded_from_number`      | text    | Already exists; caller ID override (nullable)  |

#### call_forward_events (new table)

```sql
CREATE TABLE call_forward_events (
    id SERIAL PRIMARY KEY,
    call_sid TEXT NOT NULL,
    org_id INTEGER NOT NULL REFERENCES orgs(id),
    from_number TEXT NOT NULL,
    to_number TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'initiated',
    dial_call_status TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Column Rename Strategy (primary_phone_number → forward_to_number)

- Preferred staged migration (zero-downtime):
  1. Add `forward_to_number` column nullable
  2. Backfill from `primary_phone_number`
  3. Switch reads/writes in `services/orgs` to `forward_to_number`
  4. Update bootstrap recipes and any configs
  5. Remove `primary_phone_number` once traffic fully migrated
- Alternatively (simpler, requires coordination): single rename + refactor across `services/orgs` and bootstrap recipes if downtime/compatibility is acceptable

### TwiML Implementation

#### New TwiML Builder Function (twiml/builder.go)

```go
// ForwardDialResponse generates TwiML to forward a call with `<Dial action>` for fallback
func ForwardDialResponse(toNumber string, callerId string, forwardActionURL string) (string, error) {
    dial := &twiml.VoiceDial{
        Timeout:        20,            // Read from env: CALL_FORWARD_TIMEOUT_SECONDS
        Action:         forwardActionURL, // Points to /twiml/forward-action
        CallerId:       callerId,
        AnswerOnBridge: true,
    }
    numberElement := twiml.VoiceNumber{
        PhoneNumber: toNumber,
    }
    dial.InnerElements = []twiml.Element{&numberElement}

    return twiml.Voice([]twiml.Element{dial})
}
```

#### Integration with Existing Infrastructure

- **Action URL**: New `/hero.communications.v1.TwilioWebhookService/twiml/forward-action` endpoint (returns TwiML)
- **Status logging**: Reuse existing `/hero.communications.v1.TwilioWebhookService/callstatus` (no TwiML). Tag requests with `eventType=forward` via query param
- **Fallback logic**: Implemented in new `HandleForwardActionRequest` (reads `DialCallStatus` and queues on failure)

#### Twilio Behavior Contract

- `<Dial action>` endpoint must return TwiML immediately to continue the call graph
- `statusCallback` endpoints should not return TwiML; respond `200 OK` quickly and log/audit

#### Webhook Parameters to Read

- Forward action (`/twiml/forward-action`): `DialCallStatus`, `CallSid` (parent), optional `From`, `To`, `forwardAttempted`
- Status logging (`/callstatus?eventType=forward`): `DialCallSid`, `DialCallStatus`, `ParentCallSid`, `CallStatus`, `From`, `To`

### API Integration Points

| Endpoint                        | Purpose                                                                  | Implementation                                           |
| ------------------------------- | ------------------------------------------------------------------------ | -------------------------------------------------------- |
| `HandleVoiceRequest` (existing) | Enhanced to check forwarding flag before queue logic                     | Modify `cellularcall_usecase.go`                         |
| `/twiml/forward-action` (new)   | `<Dial action>` handler to decide fallback and return next TwiML         | Add `HandleForwardActionRequest` + register in `main.go` |
| `/callstatus` (existing)        | Status logging (no TwiML). Use `eventType=forward` to log forward events | Optionally insert into `call_forward_events`             |
| Orgs Service                    | Update forwarding settings via existing API                              | Leverage existing org management                         |

#### Service Flow Integration

1. `HandleVoiceRequest` checks `is_call_forwarding_enabled` for org
2. If enabled, returns `ForwardDialResponse` TwiML with `Action=/twiml/forward-action?forwardAttempted=true`
3. Twilio calls `/twiml/forward-action` with `DialCallStatus`
4. `HandleForwardActionRequest` queues caller on failure states and returns queue TwiML
5. `/callstatus` receives `statusCallback` events for auditing (`eventType=forward`)

## Development Roadmap

### Phase 1 – MVP Implementation

#### Core Components (Priority Order)

1. **Database Migration** – Rename column + create `call_forward_events` table
2. **TwiML Enhancement** – Add `ForwardDialResponse` to `builder.go`
3. **Entry Logic** – Enhance `HandleVoiceRequest` to check forwarding before queue
4. **Action Handler** – Add `HandleForwardActionRequest` for forward fallback logic; register route
5. **Repository Layer** – Add call forward event CRUD operations
6. **Event Logging** – Reuse `/callstatus` to log forward events (`eventType=forward`)
7. **Testing** – Unit tests + Twilio webhook emulation

#### File Changes Required

- `/migrations/` – Rename column + new migration for call_forward_events table
- `/services/communications/internal/cellularcall/twiml/builder.go` – Add `ForwardDialResponse`
- `/services/communications/internal/cellularcall/usecase/cellularcall_usecase.go` – Enhance `HandleVoiceRequest`; add `HandleForwardActionRequest`
- `/services/communications/cmd/server/main.go` – Register `/twiml/forward-action`
- `/services/communications/internal/cellularcall/data/` – Add forward events repository

## Risk Mitigation

| Risk                                | Impact                    | Mitigation Strategy                                                 |
| ----------------------------------- | ------------------------- | ------------------------------------------------------------------- |
| Invalid callerId rejected by Twilio | Forward fails immediately | Validate caller ID format; fallback to original caller if invalid   |
| Infinite fallback loops             | System overload           | Add `forward_attempted` flag to call attributes; check before retry |
| Timeout duration issues             | Poor user experience      | Make timeout configurable via environment variable (default: 20s)   |
| Forward destination unreachable     | Caller abandoned          | Robust fallback to existing queue system                            |
| Database performance impact         | Service degradation       | Index call_forward_events table on org_id and created_at            |

## Configuration & Validation

### Environment Variables

- `CALL_FORWARD_TIMEOUT_SECONDS` – Dial timeout duration (default: 20)
- `CALL_FORWARD_ENABLED` – Global feature flag (default: true)

### Phone Number Validation

- **Hero Core Utility**: Use existing `utils.StandardizeUSPhoneNumber()` from `/lib/common/utils/validators.go`
- **Twilio Validation**: Caller ID must be verified Twilio number or empty

### Security

- Validate Twilio signatures on `/twiml/forward-action` and `/callstatus`

### Caller ID Selection Logic

- If `forwarded_from_number` is present and verified by Twilio → use as `CallerId`
- Else → omit `CallerId` to pass through the original caller ID (Twilio default)
- If pass-through is not permitted in a given account/region, fallback to the org's `TwilioNumber`

### Status Handling

- **completed** → Mark forward event as successful
- **no-answer|busy|failed|canceled** → Log failure and trigger QueueCall fallback

## References

### Twilio Documentation

- [Dial Verb Documentation](https://www.twilio.com/docs/voice/twiml/dial)
- [CallerId Configuration](https://www.twilio.com/docs/voice/twiml/dial#attributes-callerid)
- [DialCallStatus Values](https://www.twilio.com/docs/voice/api/call-resource#create-a-call-resource)

### Hero Core Integration Points

- Entry flow in `cellularcall_usecase.go:HandleVoiceRequest`
- TwiML patterns in `twiml/builder.go`
- Organization management via orgs service
- Call queue fallback via existing `QueueCall` method
