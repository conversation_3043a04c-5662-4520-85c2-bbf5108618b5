package repository

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	cmncontext "common/context"
	"common/database"
	"common/herosentry"
	filerepository "proto/hero/filerepository/v1"

	_ "github.com/lib/pq"
	"google.golang.org/protobuf/types/known/structpb"
)

// Constants for common values
const (
	SystemOwnerID = "SYSTEM"
)

// SQL Injection Prevention - Security Validation Helpers
// These helpers provide centralized validation against allowlists to prevent SQL injection attacks.

// validateSQLIdentifier validates that a string is a safe SQL identifier (column/table name).
// Only allows alphanumeric characters and underscores, must start with letter/underscore.
// This prevents SQL injection through column/table name injection.
func validateSQLIdentifier(identifier string) error {
	if identifier == "" {
		return fmt.Errorf("identifier cannot be empty")
	}

	// Check first character (must be letter or underscore)
	if !((identifier[0] >= 'a' && identifier[0] <= 'z') ||
		(identifier[0] >= 'A' && identifier[0] <= 'Z') ||
		identifier[0] == '_') {
		return fmt.Errorf("invalid identifier: must start with letter or underscore")
	}

	// Check remaining characters (alphanumeric or underscore only)
	for i := 1; i < len(identifier); i++ {
		char := identifier[i]
		if !((char >= 'a' && char <= 'z') ||
			(char >= 'A' && char <= 'Z') ||
			(char >= '0' && char <= '9') ||
			char == '_') {
			return fmt.Errorf("invalid identifier: contains illegal character '%c'", char)
		}
	}

	return nil
}

// allowedSortColumns defines the complete list of safe column names for ORDER BY clauses.
// This prevents SQL injection in dynamic sorting operations.
var allowedSortColumns = map[string]bool{
	"id":             true,
	"created_at":     true,
	"updated_at":     true,
	"file_name":      true,
	"file_size":      true,
	"download_count": true,
	"last_accessed":  true,
	"status":         true,
}

// allowedFilterColumns defines the complete list of safe column names for WHERE clauses.
// This prevents SQL injection in dynamic filtering operations.
var allowedFilterColumns = map[string]bool{
	"id":             true,
	"org_id":         true,
	"owner_id":       true,
	"file_name":      true,
	"file_type":      true,
	"file_size":      true,
	"status":         true,
	"provider":       true,
	"storage_class":  true,
	"created_at":     true,
	"updated_at":     true,
	"last_accessed":  true,
	"download_count": true,
	"is_public":      true,
}

// validateColumnName validates that a column name is safe for use in SQL queries.
// Uses allowlists to prevent SQL injection through column name manipulation.
func validateColumnName(columnName string, operation string) error {
	// First validate it's a proper SQL identifier
	if err := validateSQLIdentifier(columnName); err != nil {
		return fmt.Errorf("invalid column name: %w", err)
	}

	// Then check against operation-specific allowlists
	switch operation {
	case "sort", "order":
		if !allowedSortColumns[columnName] {
			return fmt.Errorf("column '%s' not allowed for sorting operations", columnName)
		}
	case "filter", "where":
		if !allowedFilterColumns[columnName] {
			return fmt.Errorf("column '%s' not allowed for filtering operations", columnName)
		}
	default:
		return fmt.Errorf("unknown operation type: %s", operation)
	}

	return nil
}

// PostgresFileRepositoryRepository implements the FileRepositoryRepository interface using PostgreSQL.
type PostgresFileRepositoryRepository struct {
	database *sql.DB
}

// NewPostgresFileRepositoryRepository creates a new PostgreSQL file repository.
func NewPostgresFileRepositoryRepository(database *sql.DB) *PostgresFileRepositoryRepository {
	return &PostgresFileRepositoryRepository{
		database: database,
	}
}

// nullIfEmpty returns nil if the string is empty, otherwise returns the string.
func nullIfEmpty(s string) interface{} {
	if s == "" {
		return nil
	}
	return s
}

// validateStatusTransition validates that a status transition is allowed according to business rules.
// Valid transitions:
//   - PENDING → ACTIVE: Upload confirmed
//   - ACTIVE → ARCHIVED: File archived
//   - ACTIVE → DELETED: File soft-deleted
//   - ARCHIVED → DELETED: Archived file deleted
//   - DELETED → ACTIVE: File undeleted (restore)
//   - PENDING → DELETED: Failed upload cleanup
func validateStatusTransition(currentStatus, newStatus filerepository.FileStatus) error {
	// No validation needed if status isn't changing
	if currentStatus == newStatus {
		return nil
	}

	// Valid transitions using proto-generated enums for better visibility
	validTransitions := map[filerepository.FileStatus][]filerepository.FileStatus{
		filerepository.FileStatus_FILE_STATUS_PENDING: {
			filerepository.FileStatus_FILE_STATUS_ACTIVE,  // Upload confirmed
			filerepository.FileStatus_FILE_STATUS_DELETED, // Failed upload cleanup
		},
		filerepository.FileStatus_FILE_STATUS_ACTIVE: {
			filerepository.FileStatus_FILE_STATUS_ARCHIVED, // File archived
			filerepository.FileStatus_FILE_STATUS_DELETED,  // File soft-deleted
		},
		filerepository.FileStatus_FILE_STATUS_ARCHIVED: {
			filerepository.FileStatus_FILE_STATUS_DELETED, // Archived file deleted
		},
		filerepository.FileStatus_FILE_STATUS_DELETED: {
			filerepository.FileStatus_FILE_STATUS_ACTIVE, // File undeleted (restore)
		},
	}

	allowedTargets, exists := validTransitions[currentStatus]
	if !exists {
		return fmt.Errorf("invalid current status: %v", currentStatus)
	}

	for _, allowed := range allowedTargets {
		if newStatus == allowed {
			return nil // Valid transition
		}
	}

	return fmt.Errorf("invalid status transition from %v to %v", currentStatus, newStatus)
}

// scanFileMetadata scans a database row into a FileMetadata proto.
func scanFileMetadata(rows *sql.Rows) (*filerepository.FileMetadata, error) {
	var file filerepository.FileMetadata
	var createdAt, updatedAt, lastAccessed sql.NullTime
	var storageTagsJSON, providerMetadataJSON, extraMetadataJSON sql.NullString
	var thumbnailURL sql.NullString
	var fileType, storageKey, bucketName, objectVersion, integrityHash, encryptionKeyID, checksum sql.NullString

	err := rows.Scan(
		&file.Id,
		&file.OrgId,
		&file.OwnerId,
		&file.FileName,
		&fileType,
		&file.FileSize,
		&createdAt,
		&updatedAt,
		&lastAccessed,
		&file.Status,
		&file.Provider,
		&storageKey,
		&bucketName,
		&objectVersion,
		&integrityHash,
		&file.StorageClass,
		&encryptionKeyID,
		&checksum,
		&storageTagsJSON,
		&providerMetadataJSON,
		&extraMetadataJSON,
		&file.DownloadCount,
		&file.IsPublic,
		&thumbnailURL,
	)
	if err != nil {
		return nil, err
	}

	// Handle nullable fields
	if fileType.Valid {
		file.FileType = fileType.String
	}
	if storageKey.Valid {
		file.StorageKey = storageKey.String
	}
	if bucketName.Valid {
		file.BucketName = bucketName.String
	}
	if objectVersion.Valid {
		file.ObjectVersion = objectVersion.String
	}
	if integrityHash.Valid {
		file.IntegrityHash = integrityHash.String
	}
	if encryptionKeyID.Valid {
		file.EncryptionKeyId = encryptionKeyID.String
	}
	if checksum.Valid {
		file.Checksum = checksum.String
	}
	if thumbnailURL.Valid {
		file.ThumbnailUrl = thumbnailURL.String
	}

	// Handle timestamps
	if createdAt.Valid {
		file.CreatedAt = createdAt.Time.Format(time.RFC3339)
	}
	if updatedAt.Valid {
		file.UpdatedAt = updatedAt.Time.Format(time.RFC3339)
	}
	if lastAccessed.Valid {
		file.LastAccessed = lastAccessed.Time.Format(time.RFC3339)
	}

	// Handle JSONB fields
	if storageTagsJSON.Valid && storageTagsJSON.String != "" {
		if err := json.Unmarshal([]byte(storageTagsJSON.String), &file.StorageTags); err != nil {
			return nil, fmt.Errorf("failed to unmarshal storage_tags: %w", err)
		}
	}
	if providerMetadataJSON.Valid && providerMetadataJSON.String != "" {
		if err := json.Unmarshal([]byte(providerMetadataJSON.String), &file.ProviderMetadata); err != nil {
			return nil, fmt.Errorf("failed to unmarshal provider_metadata: %w", err)
		}
	}
	if extraMetadataJSON.Valid && extraMetadataJSON.String != "" {
		if err := json.Unmarshal([]byte(extraMetadataJSON.String), &file.ExtraMetadata); err != nil {
			return nil, fmt.Errorf("failed to unmarshal extra_metadata: %w", err)
		}
	}

	return &file, nil
}

// CreateFileMetadata stores a new file metadata record.
func (repo *PostgresFileRepositoryRepository) CreateFileMetadata(ctx context.Context, tx *sql.Tx, metadata *filerepository.FileMetadata) error {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PostgresFileRepository.CreateFileMetadata")
	defer finish()
	span.SetTag("file.id", metadata.Id)
	span.SetTag("org.id", fmt.Sprintf("%d", metadata.OrgId))
	span.SetTag("file.status", metadata.Status.String())

	return database.WithSessionErr(repo.database, spanContext, tx, func(sessionTx *sql.Tx) error {
		// Repository-level validation: ensure org_id is valid and authorized
		if metadata.OrgId == 0 {
			return fmt.Errorf("repository validation failed: org_id cannot be zero")
		}

		// Repository-level validation: ensure owner_id is properly set from context
		if metadata.OwnerId == "" {
			return fmt.Errorf("repository validation failed: owner_id cannot be empty")
		}

		// Validate owner_id matches authentication context (security check)
		// GetUsername returns different formats based on authentication method:
		// - Cognito users: "cognito:<user_id>" (e.g., "cognito:abc123-def456")
		// - Bot services: "bot:<service_name>" (e.g., "bot:post-confirmation-lambda")
		// - Basic auth: "actual-username" (plain username from org API user)
		// - System operations: "SYSTEM" (default when no authenticated user)
		contextUsername := cmncontext.GetUsername(spanContext)
		expectedOwnerID := contextUsername
		if contextUsername == "" {
			expectedOwnerID = SystemOwnerID
		}

		if metadata.OwnerId != expectedOwnerID {
			return fmt.Errorf("repository validation failed: owner_id mismatch - expected %s, got %s", expectedOwnerID, metadata.OwnerId)
		}

		// Validate that the org_id is in the user's allowed organizations
		allowedOrgIDs := cmncontext.GetAllowedOrgIDs(spanContext)
		if len(allowedOrgIDs) == 0 {
			return fmt.Errorf("repository validation failed: no allowed organizations found in context")
		}

		orgAllowed := false
		for _, allowedOrgID := range allowedOrgIDs {
			if allowedOrgID == metadata.OrgId {
				orgAllowed = true
				break
			}
		}
		if !orgAllowed {
			return fmt.Errorf("repository validation failed: org_id %d is not in allowed organizations", metadata.OrgId)
		}
		query := `
			INSERT INTO files (
				id, org_id, owner_id, file_name, file_type, file_size,
				status, provider, storage_key, bucket_name, object_version,
				integrity_hash, storage_class, encryption_key_id, checksum,
				storage_tags, provider_metadata, extra_metadata,
				download_count, is_public, thumbnail_url
			) VALUES (
				$1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21
			)`

		// Marshal JSONB fields
		var storageTagsJSON, providerMetadataJSON, extraMetadataJSON interface{}
		if metadata.StorageTags != nil {
			tags, err := json.Marshal(metadata.StorageTags)
			if err != nil {
				return fmt.Errorf("failed to marshal storage_tags: %w", err)
			}
			storageTagsJSON = string(tags)
		}
		if metadata.ProviderMetadata != nil {
			providerMeta, err := json.Marshal(metadata.ProviderMetadata)
			if err != nil {
				return fmt.Errorf("failed to marshal provider_metadata: %w", err)
			}
			providerMetadataJSON = string(providerMeta)
		}
		if metadata.ExtraMetadata != nil {
			extraMeta, err := json.Marshal(metadata.ExtraMetadata)
			if err != nil {
				err := fmt.Errorf("failed to marshal extra_metadata: %w", err)
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal)
				return err
			}
			extraMetadataJSON = string(extraMeta)
		}

		_, err := sessionTx.ExecContext(spanContext, query,
			metadata.Id,
			metadata.OrgId,
			metadata.OwnerId,
			metadata.FileName,
			nullIfEmpty(metadata.FileType),
			metadata.FileSize,
			metadata.Status,
			metadata.Provider,
			nullIfEmpty(metadata.StorageKey),
			nullIfEmpty(metadata.BucketName),
			nullIfEmpty(metadata.ObjectVersion),
			nullIfEmpty(metadata.IntegrityHash),
			metadata.StorageClass,
			nullIfEmpty(metadata.EncryptionKeyId),
			nullIfEmpty(metadata.Checksum),
			storageTagsJSON,
			providerMetadataJSON,
			extraMetadataJSON,
			metadata.DownloadCount,
			metadata.IsPublic,
			nullIfEmpty(metadata.ThumbnailUrl),
		)
		return err
	})
}

// GetFileMetadata returns file metadata by its ID.
func (repo *PostgresFileRepositoryRepository) GetFileMetadata(ctx context.Context, tx *sql.Tx, fileID string) (*filerepository.FileMetadata, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PostgresFileRepository.GetFileMetadata")
	defer finish()
	span.SetTag("file.id", fileID)

	var result *filerepository.FileMetadata
	err := database.WithSessionErr(repo.database, spanContext, tx, func(sessionTx *sql.Tx) error {
		query := `
			SELECT id, org_id, owner_id, file_name, file_type, file_size,
				   created_at, updated_at, last_accessed, status, provider,
				   storage_key, bucket_name, object_version, integrity_hash,
				   storage_class, encryption_key_id, checksum, storage_tags,
				   provider_metadata, extra_metadata, download_count, is_public, thumbnail_url
			FROM files
			WHERE id = $1`

		rows, err := sessionTx.QueryContext(spanContext, query, fileID)
		if err != nil {
			return err
		}
		defer rows.Close()

		if !rows.Next() {
			return ErrFileNotFound
		}

		result, err = scanFileMetadata(rows)
		return err
	})
	return result, err
}

// UpdateFileMetadata updates the file metadata fields.
func (repo *PostgresFileRepositoryRepository) UpdateFileMetadata(ctx context.Context, tx *sql.Tx, fileID string, req *filerepository.UpdateFileMetadataRequest) (*filerepository.FileMetadata, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PostgresFileRepository.UpdateFileMetadata")
	defer finish()
	span.SetTag("file.id", fileID)

	var result *filerepository.FileMetadata
	err := database.WithSessionErr(repo.database, spanContext, tx, func(sessionTx *sql.Tx) error {
		setParts := []string{}
		args := []interface{}{}
		argIndex := 1

		if req.FileName != "" {
			setParts = append(setParts, fmt.Sprintf("file_name = $%d", argIndex))
			args = append(args, req.FileName)
			argIndex++
		}
		// Security: owner_id cannot be modified to prevent ownership transfer attacks
		if req.OwnerId != "" {
			return fmt.Errorf("security violation: owner_id cannot be modified")
		}
		if req.ExtraMetadata != nil {
			extraMeta, err := json.Marshal(req.ExtraMetadata)
			if err != nil {
				err := fmt.Errorf("failed to marshal extra_metadata: %w", err)
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal)
				return err
			}
			setParts = append(setParts, fmt.Sprintf("extra_metadata = $%d", argIndex))
			args = append(args, string(extraMeta))
			argIndex++
		}

		if len(setParts) == 0 {
			// No updates to make, just return current metadata
			return repo.getFileMetadataInternal(spanContext, sessionTx, fileID, &result)
		}

		// Add file ID to args
		args = append(args, fileID)

		setClause := strings.Join(setParts, ", ")
		// Build parameterized UPDATE query safely
		var queryBuilder strings.Builder
		queryBuilder.WriteString("UPDATE files SET ")
		queryBuilder.WriteString(setClause)
		queryBuilder.WriteString(" WHERE id = $")
		queryBuilder.WriteString(strconv.Itoa(argIndex))
		query := queryBuilder.String()

		_, err := sessionTx.ExecContext(spanContext, query, args...)
		if err != nil {
			return err
		}

		return repo.getFileMetadataInternal(spanContext, sessionTx, fileID, &result)
	})
	return result, err
}

// getFileMetadataInternal is a helper function that doesn't apply session wrapper (used internally)
func (repo *PostgresFileRepositoryRepository) getFileMetadataInternal(ctx context.Context, tx *sql.Tx, fileID string, result **filerepository.FileMetadata) error {
	query := `
		SELECT id, org_id, owner_id, file_name, file_type, file_size,
			   created_at, updated_at, last_accessed, status, provider,
			   storage_key, bucket_name, object_version, integrity_hash,
			   storage_class, encryption_key_id, checksum, storage_tags,
			   provider_metadata, extra_metadata, download_count, is_public, thumbnail_url
		FROM files
		WHERE id = $1`

	rows, err := tx.QueryContext(ctx, query, fileID)
	if err != nil {
		return err
	}
	defer rows.Close()

	if !rows.Next() {
		return ErrFileNotFound
	}

	*result, err = scanFileMetadata(rows)
	return err
}

// ListFiles returns a paginated list of files with optional filtering.
func (repo *PostgresFileRepositoryRepository) ListFiles(ctx context.Context, tx *sql.Tx, req *filerepository.ListFilesRequest) (*filerepository.ListFilesResponse, error) {
	var result *filerepository.ListFilesResponse
	err := database.WithSessionErr(repo.database, ctx, tx, func(sessionTx *sql.Tx) error {
		whereConditions := []string{}
		args := []interface{}{}
		argIndex := 1

		if req.OwnerId != "" {
			whereConditions = append(whereConditions, fmt.Sprintf("owner_id = $%d", argIndex))
			args = append(args, req.OwnerId)
			argIndex++
		}
		if req.Status != filerepository.FileStatus_FILE_STATUS_UNSPECIFIED {
			whereConditions = append(whereConditions, fmt.Sprintf("status = $%d", argIndex))
			args = append(args, int32(req.Status))
			argIndex++
		}

		whereClause := ""
		if len(whereConditions) > 0 {
			whereClause = "WHERE " + strings.Join(whereConditions, " AND ")
		}

		// Handle pagination
		offset := 0
		if req.PageToken != "" {
			if parsedOffset, err := strconv.Atoi(req.PageToken); err == nil {
				offset = parsedOffset
			}
		}

		pageSize := int(req.PageSize)
		if pageSize <= 0 || pageSize > 100 {
			pageSize = 20 // Default page size
		}

		baseQuery := `
			SELECT id, org_id, owner_id, file_name, file_type, file_size,
				   created_at, updated_at, last_accessed, status, provider,
				   storage_key, bucket_name, object_version, integrity_hash,
				   storage_class, encryption_key_id, checksum, storage_tags,
				   provider_metadata, extra_metadata, download_count, is_public, thumbnail_url
			FROM files`

		var query string
		if whereClause != "" {
			query = baseQuery + " " + whereClause + " ORDER BY created_at DESC LIMIT $" + strconv.Itoa(argIndex) + " OFFSET $" + strconv.Itoa(argIndex+1)
		} else {
			query = baseQuery + " ORDER BY created_at DESC LIMIT $" + strconv.Itoa(argIndex) + " OFFSET $" + strconv.Itoa(argIndex+1)
		}

		args = append(args, pageSize+1, offset) // +1 to check if there are more results

		rows, err := sessionTx.QueryContext(ctx, query, args...)
		if err != nil {
			return err
		}
		defer rows.Close()

		files := []*filerepository.FileMetadata{}
		for rows.Next() {
			file, err := scanFileMetadata(rows)
			if err != nil {
				return err
			}
			files = append(files, file)
		}

		// Determine next page token
		nextPageToken := ""
		if len(files) > pageSize {
			files = files[:pageSize] // Remove the extra record
			nextPageToken = strconv.Itoa(offset + pageSize)
		}

		result = &filerepository.ListFilesResponse{
			Files:         files,
			NextPageToken: nextPageToken,
		}
		return nil
	})
	return result, err
}

// SearchFiles performs advanced search on files with comprehensive filtering and highlighting.
// This function implements a sophisticated search architecture following enterprise patterns
// used across the Hero platform, providing rich search capabilities with optimal performance.
//
// Key Features:
//   - Multi-field text search with highlighting
//   - Advanced filtering (type, status, size, dates, tags)
//   - Flexible sorting with multiple criteria
//   - Efficient pagination with consistent results
//   - Search result highlighting for UI display
//   - Single-query optimization (no N+1 patterns)
//
// Search Capabilities:
//   - General text search across filename and ID fields
//   - Field-specific searches for targeted queries
//   - File type and extension filtering
//   - File size range filtering
//   - Date range filtering (created, updated, accessed)
//   - Download count range filtering
//   - Tag-based filtering (key-value and key-only)
//   - Public/private visibility filtering
//   - Storage class and provider filtering
//
// Performance Optimizations:
//   - Window functions for single-query count retrieval
//   - Parameterized queries for security and performance
//   - Efficient pagination with offset-based tokens
//   - Indexed field searches for fast lookups
//
// Security Features:
//   - Organization-scoped queries for multi-tenant isolation
//   - Field validation to prevent injection attacks
//   - Parameterized query construction
//   - Input sanitization and bounds checking
func (repo *PostgresFileRepositoryRepository) SearchFiles(ctx context.Context, tx *sql.Tx, req *filerepository.SearchFilesRequest) (*filerepository.SearchFilesResponse, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PostgresFileRepository.SearchFiles")
	defer finish()
	span.SetTag("search.page_size", fmt.Sprintf("%d", req.PageSize))
	if req.Query != "" {
		span.SetTag("search.has_query", "true")
	}

	var result *filerepository.SearchFilesResponse
	err := database.WithSessionErr(repo.database, spanContext, tx, func(sessionTx *sql.Tx) error {
		// Step 1: Validate search request parameters
		if err := repo.validateSearchFilesRequest(req); err != nil {
			return fmt.Errorf("search validation failed: %w", err)
		}

		// Step 2: Build comprehensive search query with all filters
		searchTermsMap := make(map[string][]string)
		_, whereConditions, queryArgs := repo.buildFileSearchQuery(req, &searchTermsMap)

		// Step 3: Set pagination defaults and validate bounds
		pageSize := int(req.PageSize)
		if pageSize <= 0 {
			pageSize = 20 // Default page size for reasonable response times
		}
		if pageSize > 100 {
			pageSize = 100 // Maximum page size to prevent resource exhaustion
		}

		// Step 4: Calculate offset from page token
		offset := 0
		if req.PageToken != "" {
			if parsedOffset, parseErr := strconv.Atoi(req.PageToken); parseErr == nil {
				offset = parsedOffset
			}
			// Invalid tokens are silently ignored, defaulting to offset 0
		}

		// Step 5: Build ORDER BY clause with consistent sorting
		orderByClause := repo.buildFileOrderByClause(req.SortField, req.SortOrder == filerepository.SortOrder_SORT_ORDER_ASC)

		// Step 6: Complete query with pagination and window function for total count
		whereClause := ""
		if len(whereConditions) > 0 {
			whereClause = "WHERE " + strings.Join(whereConditions, " AND ")
		}

		// Use window function to get total count in single query (eliminates N+1 pattern)
		baseSearchQuery := `
			SELECT 
				id, org_id, owner_id, file_name, file_type, file_size,
				created_at, updated_at, last_accessed, status, provider,
				storage_key, bucket_name, object_version, integrity_hash,
				storage_class, encryption_key_id, checksum, storage_tags,
				provider_metadata, extra_metadata, download_count, is_public, thumbnail_url,
				COUNT(*) OVER() as total_count
			FROM files`

		var fullQuery string
		limitOffsetClause := " LIMIT $" + strconv.Itoa(len(queryArgs)+1) + " OFFSET $" + strconv.Itoa(len(queryArgs)+2)

		if whereClause != "" {
			fullQuery = baseSearchQuery + " " + whereClause + " " + orderByClause + limitOffsetClause
		} else {
			fullQuery = baseSearchQuery + " " + orderByClause + limitOffsetClause
		}

		queryArgs = append(queryArgs, pageSize+1, offset) // +1 to check for more results

		// Step 7: Execute search query
		rows, err := sessionTx.QueryContext(ctx, fullQuery, queryArgs...)
		if err != nil {
			return fmt.Errorf("failed to execute file search query: %w", err)
		}
		defer rows.Close()

		// Step 8: Scan results with proper NULL handling
		searchResults := []*filerepository.SearchResult{}
		var totalCount int32 = 0

		for rows.Next() {
			fileMetadata, rowTotalCount, err := repo.scanFileSearchRow(rows)
			if err != nil {
				return fmt.Errorf("failed to scan file search row: %w", err)
			}

			// Set total count from first row (window function provides same value for all rows)
			if totalCount == 0 {
				totalCount = rowTotalCount
			}

			// Create search result with basic relevance scoring
			searchResult := &filerepository.SearchResult{
				Metadata: fileMetadata,
				Score:    repo.calculateRelevanceScore(fileMetadata, req),
			}
			searchResults = append(searchResults, searchResult)
		}

		// Step 9: Check for row iteration errors
		if rowsErr := rows.Err(); rowsErr != nil {
			return fmt.Errorf("error iterating file search results: %w", rowsErr)
		}

		// Step 10: Generate search highlights for UI display
		searchHighlights := repo.generateFileSearchHighlights(searchResults, searchTermsMap)

		// Step 11: Calculate next page token
		nextPageToken := ""
		if len(searchResults) > pageSize {
			searchResults = searchResults[:pageSize] // Remove extra record
			nextPageToken = strconv.Itoa(offset + pageSize)
		}

		// Step 12: Return complete search response directly as protobuf
		result = &filerepository.SearchFilesResponse{
			Results:          searchResults,
			NextPageToken:    nextPageToken,
			TotalCount:       totalCount,
			HighlightResults: searchHighlights,
		}
		return nil
	})
	return result, err
}

// validateSearchFilesRequest validates search request parameters for security and correctness.
// This function implements comprehensive input validation following security best practices
// to prevent injection attacks, ensure data integrity, and provide clear error messages.
//
// Validation Rules:
//   - Page size bounds (1-100) to prevent resource exhaustion
//   - Date format validation for range filters
//   - Numeric range validation (size, download count)
//   - Field name validation for security
//   - Tag key/value sanitization
//
// Security Features:
//   - Input sanitization to prevent injection attacks
//   - Bounds checking to prevent resource exhaustion
//   - Field name validation against allowed list
//   - Error messages that don't leak sensitive information
func (repo *PostgresFileRepositoryRepository) validateSearchFilesRequest(req *filerepository.SearchFilesRequest) error {
	// Validate page size bounds
	if req.PageSize < 0 {
		return errors.New("page_size cannot be negative")
	}
	if req.PageSize > 100 {
		return errors.New("page_size cannot exceed 100")
	}

	// Validate date ranges if provided
	if req.CreatedRange != nil {
		if req.CreatedRange.From != "" {
			if _, err := time.Parse(time.RFC3339, req.CreatedRange.From); err != nil {
				return fmt.Errorf("invalid created_range.from format, expected ISO-8601: %w", err)
			}
		}
		if req.CreatedRange.To != "" {
			if _, err := time.Parse(time.RFC3339, req.CreatedRange.To); err != nil {
				return fmt.Errorf("invalid created_range.to format, expected ISO-8601: %w", err)
			}
		}
	}

	if req.UpdatedRange != nil {
		if req.UpdatedRange.From != "" {
			if _, err := time.Parse(time.RFC3339, req.UpdatedRange.From); err != nil {
				return fmt.Errorf("invalid updated_range.from format, expected ISO-8601: %w", err)
			}
		}
		if req.UpdatedRange.To != "" {
			if _, err := time.Parse(time.RFC3339, req.UpdatedRange.To); err != nil {
				return fmt.Errorf("invalid updated_range.to format, expected ISO-8601: %w", err)
			}
		}
	}

	// Validate size range bounds
	if req.SizeRange != nil {
		if req.SizeRange.MinBytes < 0 {
			return errors.New("size_range.min_bytes cannot be negative")
		}
		if req.SizeRange.MaxBytes < 0 {
			return errors.New("size_range.max_bytes cannot be negative")
		}
		if req.SizeRange.MinBytes > 0 && req.SizeRange.MaxBytes > 0 && req.SizeRange.MinBytes > req.SizeRange.MaxBytes {
			return errors.New("size_range.min_bytes cannot be greater than max_bytes")
		}
	}

	// Validate download count range bounds
	if req.DownloadRange != nil {
		if req.DownloadRange.MinCount < 0 {
			return errors.New("download_range.min_count cannot be negative")
		}
		if req.DownloadRange.MaxCount < 0 {
			return errors.New("download_range.max_count cannot be negative")
		}
		if req.DownloadRange.MinCount > 0 && req.DownloadRange.MaxCount > 0 && req.DownloadRange.MinCount > req.DownloadRange.MaxCount {
			return errors.New("download_range.min_count cannot be greater than max_count")
		}
	}

	// Validate search fields against allowed list
	allowedSearchFields := map[string]bool{
		"filename": true,
		"id":       true,
	}
	for _, field := range req.SearchFields {
		if !allowedSearchFields[field] {
			return fmt.Errorf("unsupported search field: %s", field)
		}
	}

	// Validate tag filters to prevent DoS attacks
	if len(req.TagFilters) > 50 {
		return errors.New("too many tag filters: maximum 50 allowed")
	}
	for key, value := range req.TagFilters {
		if len(key) > 100 {
			return errors.New("tag key too long: maximum 100 characters allowed")
		}
		if len(value) > 500 {
			return errors.New("tag value too long: maximum 500 characters allowed")
		}
	}

	// Validate tag keys to prevent DoS attacks
	if len(req.TagKeys) > 50 {
		return errors.New("too many tag keys: maximum 50 allowed")
	}
	for _, tagKey := range req.TagKeys {
		if len(tagKey) > 100 {
			return errors.New("tag key too long: maximum 100 characters allowed")
		}
	}

	// Validate file types and extensions to prevent DoS attacks
	if len(req.FileTypes) > 20 {
		return errors.New("too many file types: maximum 20 allowed")
	}
	if len(req.Extensions) > 20 {
		return errors.New("too many extensions: maximum 20 allowed")
	}
	if len(req.StorageClasses) > 10 {
		return errors.New("too many storage classes: maximum 10 allowed")
	}

	return nil
}

// buildFileSearchQuery constructs the comprehensive SQL query for file search operations.
// This function implements a modular query building approach that supports all search
// features while maintaining security, performance, and maintainability.
//
// Query Building Strategy:
//   - Start with base SELECT clause
//   - Add WHERE conditions dynamically based on request
//   - Use parameterized queries for security
//   - Track search terms for highlighting
//   - Maintain argument index consistency
//
// Supported Search Types:
//   - General text search across multiple fields
//   - Field-specific targeted searches
//   - Exact match filters (status, type, owner)
//   - Range filters (size, dates, download count)
//   - Tag-based filtering with JSON operations
//   - Boolean filters (public/private)
//
// Returns:
//   - baseQuery: SELECT clause with field list
//   - whereConditions: Array of WHERE conditions for AND joining
//   - queryArgs: Parameterized arguments for secure execution
//   - searchTermsMap: Field-to-terms mapping for highlighting (via pointer)
func (repo *PostgresFileRepositoryRepository) buildFileSearchQuery(req *filerepository.SearchFilesRequest, searchTermsMap *map[string][]string) (baseQuery string, whereConditions []string, queryArgs []interface{}) {
	// Initialize search terms map for highlighting
	*searchTermsMap = make(map[string][]string)

	// Base SELECT query with all file fields
	baseQuery = `SELECT id, org_id, owner_id, file_name, file_type, file_size,
		created_at, updated_at, last_accessed, status, provider,
		storage_key, bucket_name, object_version, integrity_hash,
		storage_class, encryption_key_id, checksum, storage_tags,
		provider_metadata, extra_metadata, download_count, is_public, thumbnail_url
		FROM files`

	whereConditions = []string{}
	queryArgs = []interface{}{}
	paramIndex := 1

	// Add general text search conditions
	if req.Query != "" {
		textCondition, textArgs := repo.buildFileTextSearchCondition(req.Query, req.SearchFields, paramIndex, searchTermsMap)
		if textCondition != "" {
			whereConditions = append(whereConditions, "("+textCondition+")")
			queryArgs = append(queryArgs, textArgs...)
			paramIndex += len(textArgs)
		}
	}

	// Add owner filter
	if req.OwnerId != "" {
		whereConditions = append(whereConditions, fmt.Sprintf("owner_id = $%d", paramIndex))
		queryArgs = append(queryArgs, req.OwnerId)
		paramIndex++
	}

	// Add status filters
	if len(req.Statuses) > 0 {
		statusValues := make([]interface{}, len(req.Statuses))
		statusPlaceholders := make([]string, len(req.Statuses))
		for i, status := range req.Statuses {
			statusValues[i] = int32(status)
			statusPlaceholders[i] = fmt.Sprintf("$%d", paramIndex)
			paramIndex++
		}
		whereConditions = append(whereConditions, "status IN ("+strings.Join(statusPlaceholders, ", ")+")")
		queryArgs = append(queryArgs, statusValues...)
	}

	// Add file type filters
	if len(req.FileTypes) > 0 {
		typeConditions := []string{}
		for _, fileType := range req.FileTypes {
			typeConditions = append(typeConditions, fmt.Sprintf("file_type LIKE $%d", paramIndex))
			queryArgs = append(queryArgs, fileType)
			paramIndex++
		}
		whereConditions = append(whereConditions, "("+strings.Join(typeConditions, " OR ")+")")
	}

	// Add extension filters
	if len(req.Extensions) > 0 {
		extensionConditions := []string{}
		for _, ext := range req.Extensions {
			extensionConditions = append(extensionConditions, fmt.Sprintf("file_name ILIKE $%d", paramIndex))
			// Normalize extension to lowercase for consistent matching
			normalizedExt := strings.ToLower(ext)
			if !strings.HasPrefix(normalizedExt, ".") {
				normalizedExt = "." + normalizedExt
			}
			queryArgs = append(queryArgs, "%"+normalizedExt)
			paramIndex++
		}
		whereConditions = append(whereConditions, "("+strings.Join(extensionConditions, " OR ")+")")
	}

	// Add storage class filters
	if len(req.StorageClasses) > 0 {
		storageClassValues := make([]interface{}, len(req.StorageClasses))
		storageClassPlaceholders := make([]string, len(req.StorageClasses))
		for i, sc := range req.StorageClasses {
			storageClassValues[i] = int32(sc)
			storageClassPlaceholders[i] = fmt.Sprintf("$%d", paramIndex)
			paramIndex++
		}
		whereConditions = append(whereConditions, "storage_class IN ("+strings.Join(storageClassPlaceholders, ", ")+")")
		queryArgs = append(queryArgs, storageClassValues...)
	}

	// Add date range filters
	paramIndex = repo.addFileDateRangeFilter(&whereConditions, &queryArgs, "created_at", req.CreatedRange, paramIndex)
	paramIndex = repo.addFileDateRangeFilter(&whereConditions, &queryArgs, "updated_at", req.UpdatedRange, paramIndex)

	// Add size range filter
	if req.SizeRange != nil {
		if req.SizeRange.MinBytes > 0 {
			whereConditions = append(whereConditions, fmt.Sprintf("file_size >= $%d", paramIndex))
			queryArgs = append(queryArgs, req.SizeRange.MinBytes)
			paramIndex++
		}
		if req.SizeRange.MaxBytes > 0 {
			whereConditions = append(whereConditions, fmt.Sprintf("file_size <= $%d", paramIndex))
			queryArgs = append(queryArgs, req.SizeRange.MaxBytes)
			paramIndex++
		}
	}

	// Add download count range filter
	if req.DownloadRange != nil {
		if req.DownloadRange.MinCount > 0 {
			whereConditions = append(whereConditions, fmt.Sprintf("download_count >= $%d", paramIndex))
			queryArgs = append(queryArgs, req.DownloadRange.MinCount)
			paramIndex++
		}
		if req.DownloadRange.MaxCount > 0 {
			whereConditions = append(whereConditions, fmt.Sprintf("download_count <= $%d", paramIndex))
			queryArgs = append(queryArgs, req.DownloadRange.MaxCount)
			paramIndex++
		}
	}

	// Add public/private filters
	if req.PublicOnly {
		whereConditions = append(whereConditions, "is_public = true")
	}
	if req.PrivateOnly {
		whereConditions = append(whereConditions, "is_public = false")
	}

	// Add tag filters
	if len(req.TagFilters) > 0 {
		for key, value := range req.TagFilters {
			whereConditions = append(whereConditions, fmt.Sprintf("storage_tags->>$%d = $%d", paramIndex, paramIndex+1))
			queryArgs = append(queryArgs, key, value)
			paramIndex += 2
		}
	}

	// Add tag key filters
	if len(req.TagKeys) > 0 {
		tagKeyConditions := []string{}
		for _, tagKey := range req.TagKeys {
			tagKeyConditions = append(tagKeyConditions, fmt.Sprintf("storage_tags ? $%d", paramIndex))
			queryArgs = append(queryArgs, tagKey)
			paramIndex++
		}
		whereConditions = append(whereConditions, "("+strings.Join(tagKeyConditions, " OR ")+")")
	}

	return baseQuery, whereConditions, queryArgs
}

// buildFileTextSearchCondition creates text search conditions for general query searches.
// This function implements flexible text search across multiple file fields using
// case-insensitive pattern matching with proper security and performance considerations.
//
// SECURITY: Uses allowlisted field names to prevent SQL injection attacks.
// All field names are validated against predefined safe lists before use in queries.
//
// Search Strategy:
//   - Default to filename and ID if no fields specified
//   - Use ILIKE for case-insensitive partial matching
//   - Join multiple field conditions with OR for broad matching
//   - Track search terms for result highlighting
//   - Validate field names against allowed list for security
//
// Supported Search Fields:
//   - filename: Primary search field for user-friendly queries
//   - id: Useful for finding files by partial UUID
//
// Security Features:
//   - Field name validation against allowed list
//   - Parameterized queries to prevent SQL injection
//   - Input sanitization for search terms
func (repo *PostgresFileRepositoryRepository) buildFileTextSearchCondition(query string, searchFields []string, startParamIndex int, searchTermsMap *map[string][]string) (condition string, args []interface{}) {
	if query == "" {
		return "", nil
	}

	// Default to filename and ID if no fields specified
	if len(searchFields) == 0 {
		searchFields = []string{"filename", "id"}
	}

	// SECURITY: Allowed search fields mapping to safe database column names
	// This prevents SQL injection through field name manipulation
	allowedFields := map[string]string{
		"filename": "file_name",
		"id":       "id",
	}

	conditions := []string{}
	args = []interface{}{}
	paramIndex := startParamIndex

	for _, field := range searchFields {
		dbField, allowed := allowedFields[field]
		if !allowed {
			// Log security violation attempt
			fmt.Printf("SECURITY WARNING: Invalid search field attempted: %s\n", field)
			continue // Skip unsupported fields for security
		}

		// Additional validation using our security helpers
		if err := validateColumnName(dbField, "filter"); err != nil {
			fmt.Printf("SECURITY WARNING: Column validation failed for field '%s': %v\n", dbField, err)
			continue
		}

		conditions = append(conditions, fmt.Sprintf("%s ILIKE $%d", dbField, paramIndex))
		args = append(args, "%"+query+"%")
		paramIndex++

		// Track search terms for highlighting
		(*searchTermsMap)[field] = append((*searchTermsMap)[field], query)
	}

	if len(conditions) > 0 {
		condition = strings.Join(conditions, " OR ")
	}

	return condition, args
}

// addFileDateRangeFilter adds date range filtering conditions for timestamp fields.
// This function provides flexible date range filtering supporting both open and closed
// ranges with proper parameter management and SQL generation.
//
// SECURITY: Validates field names against an allowlist to prevent SQL injection.
// Only predefined, safe column names are allowed in date range filters.
//
// Date Range Types:
//   - Open ranges: Only 'from' or only 'to' specified
//   - Closed ranges: Both 'from' and 'to' specified
//   - Inclusive bounds: Both endpoints included in results
//
// Supported Fields:
//   - created_at: File creation timestamp
//   - updated_at: File modification timestamp
//   - last_accessed: File access timestamp
//
// Parameter Management:
//   - Maintains parameter index consistency
//   - Returns updated parameter index for subsequent filters
//   - Uses parameterized queries for security
func (repo *PostgresFileRepositoryRepository) addFileDateRangeFilter(whereConditions *[]string, queryArgs *[]interface{}, fieldName string, dateRange *filerepository.DateRange, startParamIndex int) int {
	paramIndex := startParamIndex

	if dateRange == nil {
		return paramIndex
	}

	// SECURITY: Validate field name against allowlist to prevent SQL injection
	allowedDateFields := map[string]bool{
		"created_at":    true,
		"updated_at":    true,
		"last_accessed": true,
	}

	if !allowedDateFields[fieldName] {
		// Log security violation but don't fail the query
		fmt.Printf("SECURITY WARNING: Invalid date field name attempted: %s\n", fieldName)
		return paramIndex
	}

	// Add 'from' date condition (inclusive) - field name is now validated
	if dateRange.From != "" {
		*whereConditions = append(*whereConditions, fmt.Sprintf("%s >= $%d", fieldName, paramIndex))
		*queryArgs = append(*queryArgs, dateRange.From)
		paramIndex++
	}

	// Add 'to' date condition (inclusive) - field name is now validated
	if dateRange.To != "" {
		*whereConditions = append(*whereConditions, fmt.Sprintf("%s <= $%d", fieldName, paramIndex))
		*queryArgs = append(*queryArgs, dateRange.To)
		paramIndex++
	}

	return paramIndex
}

// buildFileOrderByClause constructs ORDER BY clause for consistent result sorting.
// This function creates appropriate sorting strategies for file search results,
// ensuring consistent pagination and user-friendly result ordering.
//
// SECURITY: Uses allowlisted column names to prevent SQL injection attacks.
// All column names are validated against a predefined safe list before use.
//
// Sorting Strategy:
//   - Always includes ID as secondary sort for pagination consistency
//   - Supports both ascending and descending directions
//   - Uses database indexes for optimal performance
//   - Provides meaningful default ordering
//
// Supported Sort Fields:
//   - CREATED_AT: Chronological by creation time
//   - UPDATED_AT: Chronological by modification time
//   - FILE_NAME: Alphabetical by filename
//   - FILE_SIZE: Numerical by file size
//   - DOWNLOAD_COUNT: Numerical by popularity
//   - LAST_ACCESSED: Chronological by access time
//
// Default Behavior:
//   - Defaults to created_at DESC for newest-first ordering
//   - Always includes id ASC as tiebreaker for consistent pagination
func (repo *PostgresFileRepositoryRepository) buildFileOrderByClause(sortField filerepository.SortField, ascending bool) string {
	// Safe direction mapping (enum-based, injection-proof)
	direction := "DESC"
	if ascending {
		direction = "ASC"
	}

	// SECURITY: Use allowlisted column names to prevent SQL injection
	// This maps proto enums to safe, validated database column names
	var primaryColumn string
	switch sortField {
	case filerepository.SortField_SORT_FIELD_CREATED_AT:
		primaryColumn = "created_at"
	case filerepository.SortField_SORT_FIELD_UPDATED_AT:
		primaryColumn = "updated_at"
	case filerepository.SortField_SORT_FIELD_FILE_NAME:
		primaryColumn = "file_name"
	case filerepository.SortField_SORT_FIELD_FILE_SIZE:
		primaryColumn = "file_size"
	case filerepository.SortField_SORT_FIELD_DOWNLOAD_COUNT:
		primaryColumn = "download_count"
	case filerepository.SortField_SORT_FIELD_LAST_ACCESSED:
		primaryColumn = "last_accessed"
	default:
		// Default to creation time descending (newest first)
		return "ORDER BY created_at DESC, id ASC"
	}

	// Build safe ORDER BY clause using validated column names
	// Both primaryColumn and direction are from controlled enums/allowlists
	return fmt.Sprintf("ORDER BY %s %s, id ASC", primaryColumn, direction)
}

// scanFileSearchRow scans a database row into FileMetadata with total count.
// This function handles the complex row scanning required for search results,
// including proper NULL handling and the window function total count.
//
// Row Structure:
//   - All FileMetadata fields with proper NULL handling
//   - Additional total_count field from window function
//   - Proper type conversion and validation
//
// NULL Handling:
//   - Uses sql.Null* types for nullable database fields
//   - Provides sensible defaults for NULL values
//   - Maintains data integrity during conversion
//
// Returns:
//   - FileMetadata: Complete file metadata object
//   - totalCount: Total result count from window function
//   - error: Any scanning or conversion errors
func (repo *PostgresFileRepositoryRepository) scanFileSearchRow(rows *sql.Rows) (*filerepository.FileMetadata, int32, error) {
	// Scan all file metadata fields plus total_count from window function
	var id, ownerID, fileName, fileType, storageKey, bucketName, objectVersion, integrityHash, encryptionKeyID, checksum, thumbnailURL sql.NullString
	var orgID sql.NullInt32
	var fileSize, status, provider, storageClass, downloadCount int32
	var createdAt, updatedAt, lastAccessed sql.NullTime
	var storageTags, providerMetadata, extraMetadata sql.NullString
	var isPublic bool
	var totalCount int32

	err := rows.Scan(
		&id, &orgID, &ownerID, &fileName, &fileType, &fileSize,
		&createdAt, &updatedAt, &lastAccessed, &status, &provider,
		&storageKey, &bucketName, &objectVersion, &integrityHash,
		&storageClass, &encryptionKeyID, &checksum, &storageTags,
		&providerMetadata, &extraMetadata, &downloadCount, &isPublic, &thumbnailURL,
		&totalCount,
	)
	if err != nil {
		return nil, 0, err
	}

	// Build FileMetadata with proper NULL handling
	file := &filerepository.FileMetadata{
		FileSize:      fileSize,
		Status:        filerepository.FileStatus(status),
		Provider:      filerepository.StorageProvider(provider),
		StorageClass:  filerepository.StorageClass(storageClass),
		DownloadCount: downloadCount,
		IsPublic:      isPublic,
	}

	// Handle nullable string fields
	if id.Valid {
		file.Id = id.String
	}
	if ownerID.Valid {
		file.OwnerId = ownerID.String
	}
	if fileName.Valid {
		file.FileName = fileName.String
	}
	if fileType.Valid {
		file.FileType = fileType.String
	}
	if storageKey.Valid {
		file.StorageKey = storageKey.String
	}
	if bucketName.Valid {
		file.BucketName = bucketName.String
	}
	if objectVersion.Valid {
		file.ObjectVersion = objectVersion.String
	}
	if integrityHash.Valid {
		file.IntegrityHash = integrityHash.String
	}
	if encryptionKeyID.Valid {
		file.EncryptionKeyId = encryptionKeyID.String
	}
	if checksum.Valid {
		file.Checksum = checksum.String
	}
	if thumbnailURL.Valid {
		file.ThumbnailUrl = thumbnailURL.String
	}

	// Handle nullable timestamp fields
	if createdAt.Valid {
		file.CreatedAt = createdAt.Time.Format(time.RFC3339)
	}
	if updatedAt.Valid {
		file.UpdatedAt = updatedAt.Time.Format(time.RFC3339)
	}
	if lastAccessed.Valid {
		file.LastAccessed = lastAccessed.Time.Format(time.RFC3339)
	}

	// Handle orgID
	if orgID.Valid {
		file.OrgId = orgID.Int32
	}

	// Parse JSON fields with error handling
	if storageTags.Valid && storageTags.String != "" {
		var tags map[string]string
		if err := json.Unmarshal([]byte(storageTags.String), &tags); err == nil {
			file.StorageTags = tags
		}
	}

	if providerMetadata.Valid && providerMetadata.String != "" {
		var metadata map[string]interface{}
		if err := json.Unmarshal([]byte(providerMetadata.String), &metadata); err == nil {
			if structValue, err := structpb.NewStruct(metadata); err == nil {
				file.ProviderMetadata = structValue
			}
		}
	}

	if extraMetadata.Valid && extraMetadata.String != "" {
		var metadata map[string]interface{}
		if err := json.Unmarshal([]byte(extraMetadata.String), &metadata); err == nil {
			if structValue, err := structpb.NewStruct(metadata); err == nil {
				file.ExtraMetadata = structValue
			}
		}
	}

	return file, totalCount, nil
}

// calculateRelevanceScore computes a relevance score for search results.
// This function implements basic relevance scoring to help users identify
// the most relevant files for their search queries.
//
// Scoring Factors:
//   - Exact filename matches get higher scores
//   - Recent files get slight score boost
//   - Frequently downloaded files get score boost
//   - File size considerations for relevance
//
// Score Range:
//   - Base score: 1.0
//   - Maximum score: ~3.0
//   - Minimum score: 0.1
//
// Future Enhancements:
//   - Full-text search scoring
//   - User behavior-based scoring
//   - Machine learning relevance models
func (repo *PostgresFileRepositoryRepository) calculateRelevanceScore(file *filerepository.FileMetadata, req *filerepository.SearchFilesRequest) float32 {
	score := float32(1.0) // Base score

	// Boost score for exact filename matches
	if req.Query != "" && strings.EqualFold(file.FileName, req.Query) {
		score += 1.0
	}

	// Boost score for frequently downloaded files
	switch {
	case file.DownloadCount > 10:
		score += 0.3
	case file.DownloadCount > 5:
		score += 0.2
	case file.DownloadCount > 0:
		score += 0.1
	}

	// Slight boost for recently created files
	if file.CreatedAt != "" {
		if createdTime, err := time.Parse(time.RFC3339, file.CreatedAt); err == nil {
			daysSinceCreation := time.Since(createdTime).Hours() / 24
			if daysSinceCreation < 7 {
				score += 0.2
			} else if daysSinceCreation < 30 {
				score += 0.1
			}
		}
	}

	return score
}

// generateFileSearchHighlights creates search result highlights for UI display.
// This function implements sophisticated text highlighting that shows users
// exactly where their search terms were found within file metadata.
//
// Highlighting Features:
//   - Context-aware text fragments
//   - Multiple search term support
//   - Field-specific highlighting
//   - Deduplication of identical fragments
//   - Ellipsis for truncated content
//
// Fragment Generation:
//   - 30 characters of context before and after matches
//   - Ellipsis indicators for truncated content
//   - Case-insensitive matching with original case preservation
//   - Multiple fragments per field supported
//
// Returns:
//   - Map of file ID to HighlightResult for efficient lookup
//   - Empty map if no search terms or no matches found
//   - Deduplicated fragments for clean UI display
func (repo *PostgresFileRepositoryRepository) generateFileSearchHighlights(results []*filerepository.SearchResult, searchTermsMap map[string][]string) map[string]*filerepository.HighlightResult {
	highlights := make(map[string]*filerepository.HighlightResult)

	for _, result := range results {
		file := result.Metadata
		var allFragments []string
		var highlightedFields []string

		// Create field-to-value mapping for efficient access
		fieldValueMap := map[string]string{
			"filename": file.FileName,
			"id":       file.Id,
		}

		// Check each searchable field against all search terms
		for fieldName, searchTerms := range searchTermsMap {
			fieldValue, exists := fieldValueMap[fieldName]
			if !exists || fieldValue == "" {
				continue
			}

			// Check each search term against current field value
			for _, searchTerm := range searchTerms {
				if repo.containsTermIgnoreCase(fieldValue, searchTerm) {
					fragment := repo.createHighlightFragment(fieldValue, searchTerm)
					if fragment != "" {
						allFragments = append(allFragments, fragment)
						if !repo.containsString(highlightedFields, fieldName) {
							highlightedFields = append(highlightedFields, fieldName)
						}
					}
				}
			}
		}

		// Create highlight result if matches found
		if len(allFragments) > 0 {
			highlights[file.Id] = &filerepository.HighlightResult{
				Field:     strings.Join(highlightedFields, ", "),
				Fragments: repo.deduplicateFragments(allFragments),
			}
		}
	}

	return highlights
}

// containsTermIgnoreCase checks if text contains search term (case-insensitive).
// This utility function provides consistent case-insensitive substring matching
// for the highlighting system.
func (repo *PostgresFileRepositoryRepository) containsTermIgnoreCase(textContent, searchTerm string) bool {
	return strings.Contains(strings.ToLower(textContent), strings.ToLower(searchTerm))
}

// createHighlightFragment creates a highlighted text fragment with context.
// This function generates readable text snippets showing where search terms
// were found, with surrounding context for user comprehension.
func (repo *PostgresFileRepositoryRepository) createHighlightFragment(textContent, searchTerm string) string {
	lowerText := strings.ToLower(textContent)
	lowerTerm := strings.ToLower(searchTerm)

	termIndex := strings.Index(lowerText, lowerTerm)
	if termIndex == -1 {
		return ""
	}

	// Calculate fragment boundaries with context
	contextLength := 30
	startIndex := repo.maxInt(0, termIndex-contextLength)
	endIndex := repo.minInt(len(textContent), termIndex+len(searchTerm)+contextLength)

	// Extract fragment from original text (preserving case)
	fragment := textContent[startIndex:endIndex]

	// Add ellipsis indicators for truncated content
	if startIndex > 0 {
		fragment = "…" + fragment
	}
	if endIndex < len(textContent) {
		fragment += "…"
	}

	return fragment
}

// deduplicateFragments removes duplicate fragments from the list.
// This utility function ensures clean UI display by removing identical
// text fragments while preserving original order.
func (repo *PostgresFileRepositoryRepository) deduplicateFragments(fragments []string) []string {
	seen := make(map[string]bool)
	var unique []string

	for _, fragment := range fragments {
		if !seen[fragment] {
			seen[fragment] = true
			unique = append(unique, fragment)
		}
	}

	return unique
}

// containsString checks if string slice contains target string.
// This utility function provides efficient string slice membership testing.
func (repo *PostgresFileRepositoryRepository) containsString(slice []string, target string) bool {
	for _, item := range slice {
		if item == target {
			return true
		}
	}
	return false
}

// minInt returns the minimum of two integers.
// This utility function provides safe integer minimum calculation.
func (repo *PostgresFileRepositoryRepository) minInt(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// maxInt returns the maximum of two integers.
// This utility function provides safe integer maximum calculation.
func (repo *PostgresFileRepositoryRepository) maxInt(a, b int) int {
	if a > b {
		return a
	}
	return b
}

// ConfirmUpload marks a PENDING file as ACTIVE after successful upload.
func (repo *PostgresFileRepositoryRepository) ConfirmUpload(ctx context.Context, tx *sql.Tx, pendingID string, actualFileSize int32) (*filerepository.FileMetadata, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PostgresFileRepository.ConfirmUpload")
	defer finish()
	span.SetTag("file.id", pendingID)
	span.SetTag("file.size", fmt.Sprintf("%d", actualFileSize))

	var result *filerepository.FileMetadata
	err := database.WithSessionErr(repo.database, spanContext, tx, func(sessionTx *sql.Tx) error {
		// First get current metadata to validate status transition
		var currentMetadata *filerepository.FileMetadata
		if err := repo.getFileMetadataInternal(spanContext, sessionTx, pendingID, &currentMetadata); err != nil {
			return fmt.Errorf("failed to get current file metadata: %w", err)
		}

		// Validate status transition
		if err := validateStatusTransition(currentMetadata.Status, filerepository.FileStatus_FILE_STATUS_ACTIVE); err != nil {
			return fmt.Errorf("status transition validation failed: %w", err)
		}

		query := `
			UPDATE files 
			SET status = $1, file_size = $2 
			WHERE id = $3 AND status = $4`

		_, err := sessionTx.ExecContext(spanContext, query,
			int32(filerepository.FileStatus_FILE_STATUS_ACTIVE),
			actualFileSize,
			pendingID,
			int32(filerepository.FileStatus_FILE_STATUS_PENDING))
		if err != nil {
			return err
		}

		return repo.getFileMetadataInternal(spanContext, sessionTx, pendingID, &result)
	})
	return result, err
}

// CleanupPendingUploads removes stale PENDING records older than the specified timestamp.
func (repo *PostgresFileRepositoryRepository) CleanupPendingUploads(ctx context.Context, tx *sql.Tx, olderThan string) (int32, error) {
	var rowsAffected int32
	err := database.WithSessionErr(repo.database, ctx, tx, func(sessionTx *sql.Tx) error {
		// Note: We validate the status transition conceptually here, but since we're doing a bulk DELETE
		// of PENDING records, we don't need to validate each individual transition.
		// The business rule allows PENDING(1) → DELETED(4) for cleanup operations.

		query := `
			DELETE FROM files 
			WHERE status = $1 AND created_at < $2`

		result, err := sessionTx.ExecContext(ctx, query,
			int32(filerepository.FileStatus_FILE_STATUS_PENDING),
			olderThan)
		if err != nil {
			return err
		}

		affected, err := result.RowsAffected()
		if err != nil {
			return err
		}
		// Safe conversion with bounds checking
		if affected > 2147483647 { // int32 max value
			return fmt.Errorf("rows affected count too large: %d", affected)
		}
		if affected < 0 {
			return fmt.Errorf("negative rows affected count: %d", affected)
		}
		//nolint:gosec // G115: Safe after bounds checking above
		rowsAffected = int32(affected)
		return nil
	})
	return rowsAffected, err
}

// DeleteFile soft-deletes a file by setting status to DELETED.
func (repo *PostgresFileRepositoryRepository) DeleteFile(ctx context.Context, tx *sql.Tx, fileID string) error {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PostgresFileRepository.DeleteFile")
	defer finish()
	span.SetTag("file.id", fileID)

	return database.WithSessionErr(repo.database, spanContext, tx, func(sessionTx *sql.Tx) error {
		// First get current metadata to validate status transition
		var currentMetadata *filerepository.FileMetadata
		if err := repo.getFileMetadataInternal(spanContext, sessionTx, fileID, &currentMetadata); err != nil {
			return fmt.Errorf("failed to get current file metadata: %w", err)
		}

		// Validate status transition
		if err := validateStatusTransition(currentMetadata.Status, filerepository.FileStatus_FILE_STATUS_DELETED); err != nil {
			return fmt.Errorf("status transition validation failed: %w", err)
		}

		query := `
			UPDATE files 
			SET status = $1 
			WHERE id = $2`

		_, err := sessionTx.ExecContext(spanContext, query,
			int32(filerepository.FileStatus_FILE_STATUS_DELETED),
			fileID)
		return err
	})
}

// UndeleteFile restores a soft-deleted file back to ACTIVE status.
func (repo *PostgresFileRepositoryRepository) UndeleteFile(ctx context.Context, tx *sql.Tx, fileID string) (*filerepository.FileMetadata, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PostgresFileRepository.UndeleteFile")
	defer finish()
	span.SetTag("file.id", fileID)

	var result *filerepository.FileMetadata
	err := database.WithSessionErr(repo.database, spanContext, tx, func(sessionTx *sql.Tx) error {
		// First get current metadata to validate status transition
		var currentMetadata *filerepository.FileMetadata
		if err := repo.getFileMetadataInternal(spanContext, sessionTx, fileID, &currentMetadata); err != nil {
			err := fmt.Errorf("failed to get current file metadata: %w", err)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return err
		}

		// Validate status transition
		if err := validateStatusTransition(currentMetadata.Status, filerepository.FileStatus_FILE_STATUS_ACTIVE); err != nil {
			err := fmt.Errorf("status transition validation failed: %w", err)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation)
			return err
		}

		query := `
			UPDATE files 
			SET status = $1 
			WHERE id = $2 AND status = $3`

		_, err := sessionTx.ExecContext(spanContext, query,
			int32(filerepository.FileStatus_FILE_STATUS_ACTIVE),
			fileID,
			int32(filerepository.FileStatus_FILE_STATUS_DELETED))
		if err != nil {
			return err
		}

		return repo.getFileMetadataInternal(spanContext, sessionTx, fileID, &result)
	})
	return result, err
}

// PurgeFile permanently removes file metadata.
func (repo *PostgresFileRepositoryRepository) PurgeFile(ctx context.Context, tx *sql.Tx, fileID string) error {
	return database.WithSessionErr(repo.database, ctx, tx, func(sessionTx *sql.Tx) error {
		query := `DELETE FROM files WHERE id = $1`
		_, err := sessionTx.ExecContext(ctx, query, fileID)
		return err
	})
}

// RecordAccess logs a file access event.
func (repo *PostgresFileRepositoryRepository) RecordAccess(ctx context.Context, tx *sql.Tx, req *filerepository.RecordAccessRequest) (string, error) {
	var entryID string
	err := database.WithSessionErr(repo.database, ctx, tx, func(sessionTx *sql.Tx) error {
		query := `
			INSERT INTO file_access_logs (
				file_id, user_id, action, ip_address, user_agent, 
				referrer, session_id, metadata, org_id
			) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, 
				(SELECT org_id FROM files WHERE id = $1))
			RETURNING id`

		var metadataJSON interface{}
		if req.Metadata != nil {
			metadata, err := json.Marshal(req.Metadata)
			if err != nil {
				return fmt.Errorf("failed to marshal metadata: %w", err)
			}
			metadataJSON = string(metadata)
		}

		err := sessionTx.QueryRowContext(ctx, query,
			req.FileId,
			req.UserId,
			req.Action,
			nullIfEmpty(req.IpAddress),
			nullIfEmpty(req.UserAgent),
			nullIfEmpty(req.Referrer),
			nullIfEmpty(req.SessionId),
			metadataJSON,
		).Scan(&entryID)
		return err
	})
	return entryID, err
}

// GetAccessLog retrieves paginated access logs for a file.
func (repo *PostgresFileRepositoryRepository) GetAccessLog(ctx context.Context, tx *sql.Tx, req *filerepository.GetAccessLogRequest) (*filerepository.GetAccessLogResponse, error) {
	var result *filerepository.GetAccessLogResponse
	err := database.WithSessionErr(repo.database, ctx, tx, func(sessionTx *sql.Tx) error {
		whereConditions := []string{"file_id = $1"}
		args := []interface{}{req.FileId}
		argIndex := 2

		if req.UserId != "" {
			whereConditions = append(whereConditions, fmt.Sprintf("user_id = $%d", argIndex))
			args = append(args, req.UserId)
			argIndex++
		}
		if req.Action != "" {
			whereConditions = append(whereConditions, fmt.Sprintf("action = $%d", argIndex))
			args = append(args, req.Action)
			argIndex++
		}
		if req.TimeRange != nil {
			if req.TimeRange.From != "" {
				whereConditions = append(whereConditions, fmt.Sprintf("timestamp >= $%d", argIndex))
				args = append(args, req.TimeRange.From)
				argIndex++
			}
			if req.TimeRange.To != "" {
				whereConditions = append(whereConditions, fmt.Sprintf("timestamp <= $%d", argIndex))
				args = append(args, req.TimeRange.To)
				argIndex++
			}
		}

		whereClause := "WHERE " + strings.Join(whereConditions, " AND ")

		// Handle pagination
		offset := 0
		if req.PageToken != "" {
			if parsedOffset, err := strconv.Atoi(req.PageToken); err == nil {
				offset = parsedOffset
			}
		}

		pageSize := int(req.PageSize)
		if pageSize <= 0 || pageSize > 100 {
			pageSize = 20
		}

		// Build optimized query with window function to get total count in single query
		// This eliminates the N+1 query pattern by combining data and count queries
		baseAccessQuery := `
			SELECT id, file_id, user_id, action, timestamp, ip_address, 
				   user_agent, referrer, file_size, session_id, metadata,
				   COUNT(*) OVER() as total_count
			FROM file_access_logs`

		limitOffsetClause := fmt.Sprintf(" ORDER BY timestamp DESC LIMIT $%d OFFSET $%d", argIndex, argIndex+1)
		query := baseAccessQuery + " " + whereClause + limitOffsetClause

		args = append(args, pageSize+1, offset) // +1 to check if there are more results

		rows, err := sessionTx.QueryContext(ctx, query, args...)
		if err != nil {
			return err
		}
		defer rows.Close()

		entries := []*filerepository.AccessLogEntry{}
		var totalCount int32 = 0

		for rows.Next() {
			// Scan access log fields plus total_count
			var id, fileID, userID, action, ipAddress, userAgent, referrer, sessionID sql.NullString
			var timestamp time.Time
			var fileSize sql.NullInt32 // Changed to handle NULL values
			var metadata sql.NullString
			var rowTotalCount int32

			err := rows.Scan(
				&id, &fileID, &userID, &action, &timestamp, &ipAddress,
				&userAgent, &referrer, &fileSize, &sessionID, &metadata,
				&rowTotalCount, // Window function result
			)
			if err != nil {
				return err
			}

			// Set total count from first row (all rows will have same value due to window function)
			if totalCount == 0 {
				totalCount = rowTotalCount
			}

			// Build AccessLogEntry proto
			entry := &filerepository.AccessLogEntry{
				Id:        id.String,
				FileId:    fileID.String,
				UserId:    userID.String,
				Action:    action.String,
				Timestamp: timestamp.Format(time.RFC3339),
				IpAddress: ipAddress.String,
				UserAgent: userAgent.String,
				Referrer:  referrer.String,
				FileSize:  0, // Default to 0, will be set below if valid
				SessionId: sessionID.String,
			}

			// Handle nullable file_size
			if fileSize.Valid {
				entry.FileSize = fileSize.Int32
			}

			// Parse metadata JSON
			if metadata.Valid && metadata.String != "" {
				var metadataMap map[string]string
				if err := json.Unmarshal([]byte(metadata.String), &metadataMap); err == nil {
					entry.Metadata = metadataMap
				}
			}

			entries = append(entries, entry)
		}

		// Determine next page token
		nextPageToken := ""
		if len(entries) > pageSize {
			entries = entries[:pageSize] // Remove the extra record
			nextPageToken = strconv.Itoa(offset + pageSize)
		}

		result = &filerepository.GetAccessLogResponse{
			Entries:       entries,
			NextPageToken: nextPageToken,
			TotalCount:    totalCount,
		}
		return nil
	})
	return result, err
}

// IncrementDownloadCount increments the download counter for a file.
func (repo *PostgresFileRepositoryRepository) IncrementDownloadCount(ctx context.Context, tx *sql.Tx, fileID string) error {
	return database.WithSessionErr(repo.database, ctx, tx, func(sessionTx *sql.Tx) error {
		query := `
			UPDATE files 
			SET download_count = download_count + 1 
			WHERE id = $1`
		_, err := sessionTx.ExecContext(ctx, query, fileID)
		return err
	})
}

// UpdateLastAccessed updates the last_accessed timestamp for a file.
func (repo *PostgresFileRepositoryRepository) UpdateLastAccessed(ctx context.Context, tx *sql.Tx, fileID string) error {
	return database.WithSessionErr(repo.database, ctx, tx, func(sessionTx *sql.Tx) error {
		query := `
			UPDATE files 
			SET last_accessed = CURRENT_TIMESTAMP 
			WHERE id = $1`
		_, err := sessionTx.ExecContext(ctx, query, fileID)
		return err
	})
}
