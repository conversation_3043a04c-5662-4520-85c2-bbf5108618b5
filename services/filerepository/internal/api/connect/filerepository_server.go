package connect

import (
	"context"

	connecthelper "common/connect"
	"filerepository/internal/usecase"
	filerepository "proto/hero/filerepository/v1"

	"connectrpc.com/connect"
)

// FileRepositoryServer implements the FileRepositoryService RPCs.
type FileRepositoryServer struct {
	fileRepositoryUseCase *usecase.FileRepositoryUseCase
}

// NewFileRepositoryServer creates a new FileRepositoryServer.
func NewFileRepositoryServer(fileRepositoryUseCase *usecase.FileRepositoryUseCase) *FileRepositoryServer {
	return &FileRepositoryServer{
		fileRepositoryUseCase: fileRepositoryUseCase,
	}
}

// GetPresignedUploadUrl generates a presigned URL for file upload.
func (server *FileRepositoryServer) GetPresignedUploadUrl(
	requestContext context.Context,
	request *connect.Request[filerepository.PresignedUploadRequest],
) (*connect.Response[filerepository.PresignedUploadResponse], error) {
	response, err := server.fileRepositoryUseCase.GetPresignedUploadUrl(requestContext, request.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "GetPresignedUploadUrl")
	}
	return connect.NewResponse(response), nil
}

// GetPresignedDownloadUrl generates a presigned URL for file download.
func (server *FileRepositoryServer) GetPresignedDownloadUrl(
	requestContext context.Context,
	request *connect.Request[filerepository.PresignedDownloadRequest],
) (*connect.Response[filerepository.PresignedDownloadResponse], error) {
	response, err := server.fileRepositoryUseCase.GetPresignedDownloadUrl(requestContext, request.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "GetPresignedDownloadUrl")
	}
	return connect.NewResponse(response), nil
}

// GetFileMetadata retrieves metadata for a specific file.
func (server *FileRepositoryServer) GetFileMetadata(
	requestContext context.Context,
	request *connect.Request[filerepository.GetFileMetadataRequest],
) (*connect.Response[filerepository.GetFileMetadataResponse], error) {
	response, err := server.fileRepositoryUseCase.GetFileMetadata(requestContext, request.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "GetFileMetadata")
	}
	return connect.NewResponse(response), nil
}

// SearchFiles provides advanced search capabilities for files.
func (server *FileRepositoryServer) SearchFiles(
	requestContext context.Context,
	request *connect.Request[filerepository.SearchFilesRequest],
) (*connect.Response[filerepository.SearchFilesResponse], error) {
	response, err := server.fileRepositoryUseCase.SearchFiles(requestContext, request.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "SearchFiles")
	}
	return connect.NewResponse(response), nil
}

// ListFiles returns a paginated list of files.
func (server *FileRepositoryServer) ListFiles(
	requestContext context.Context,
	request *connect.Request[filerepository.ListFilesRequest],
) (*connect.Response[filerepository.ListFilesResponse], error) {
	response, err := server.fileRepositoryUseCase.ListFiles(requestContext, request.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "ListFiles")
	}
	return connect.NewResponse(response), nil
}

// UpdateFileMetadata updates metadata for a specific file.
func (server *FileRepositoryServer) UpdateFileMetadata(
	requestContext context.Context,
	request *connect.Request[filerepository.UpdateFileMetadataRequest],
) (*connect.Response[filerepository.UpdateFileMetadataResponse], error) {
	response, err := server.fileRepositoryUseCase.UpdateFileMetadata(requestContext, request.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "UpdateFileMetadata")
	}
	return connect.NewResponse(response), nil
}

// ConfirmUpload confirms that a file upload was successful.
func (server *FileRepositoryServer) ConfirmUpload(
	requestContext context.Context,
	request *connect.Request[filerepository.ConfirmUploadRequest],
) (*connect.Response[filerepository.ConfirmUploadResponse], error) {
	response, err := server.fileRepositoryUseCase.ConfirmUpload(requestContext, request.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "ConfirmUpload")
	}
	return connect.NewResponse(response), nil
}

// CleanupPendingUploads cleans up stray PENDING records.
func (server *FileRepositoryServer) CleanupPendingUploads(
	requestContext context.Context,
	request *connect.Request[filerepository.CleanupPendingUploadsRequest],
) (*connect.Response[filerepository.CleanupPendingUploadsResponse], error) {
	response, err := server.fileRepositoryUseCase.CleanupPendingUploads(requestContext, request.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "CleanupPendingUploads")
	}
	return connect.NewResponse(response), nil
}

// DeleteFile soft-deletes a file.
func (server *FileRepositoryServer) DeleteFile(
	requestContext context.Context,
	request *connect.Request[filerepository.DeleteFileRequest],
) (*connect.Response[filerepository.DeleteFileResponse], error) {
	response, err := server.fileRepositoryUseCase.DeleteFile(requestContext, request.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "DeleteFile")
	}
	return connect.NewResponse(response), nil
}

// UndeleteFile restores a soft-deleted file.
func (server *FileRepositoryServer) UndeleteFile(
	requestContext context.Context,
	request *connect.Request[filerepository.UndeleteFileRequest],
) (*connect.Response[filerepository.UndeleteFileResponse], error) {
	response, err := server.fileRepositoryUseCase.UndeleteFile(requestContext, request.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "UndeleteFile")
	}
	return connect.NewResponse(response), nil
}

// PurgeFile permanently deletes a file.
func (server *FileRepositoryServer) PurgeFile(
	requestContext context.Context,
	request *connect.Request[filerepository.PurgeFileRequest],
) (*connect.Response[filerepository.PurgeFileResponse], error) {
	response, err := server.fileRepositoryUseCase.PurgeFile(requestContext, request.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "PurgeFile")
	}
	return connect.NewResponse(response), nil
}

// GetAccessLog retrieves access logs for files.
func (server *FileRepositoryServer) GetAccessLog(
	requestContext context.Context,
	request *connect.Request[filerepository.GetAccessLogRequest],
) (*connect.Response[filerepository.GetAccessLogResponse], error) {
	response, err := server.fileRepositoryUseCase.GetAccessLog(requestContext, request.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "GetAccessLog")
	}
	return connect.NewResponse(response), nil
}

// RecordAccess records a new access log entry.
func (server *FileRepositoryServer) RecordAccess(
	requestContext context.Context,
	request *connect.Request[filerepository.RecordAccessRequest],
) (*connect.Response[filerepository.RecordAccessResponse], error) {
	response, err := server.fileRepositoryUseCase.RecordAccess(requestContext, request.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "RecordAccess")
	}
	return connect.NewResponse(response), nil
}
