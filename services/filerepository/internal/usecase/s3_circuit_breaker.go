// Package usecase provides S3 circuit breaker implementation for the file repository service.
//
// S3 CIRCUIT BREAKER CONFIGURATION SUMMARY
// ========================================
//
// This file implements circuit breaker protection for AWS S3 operations to prevent
// cascading failures during S3 outages. Three separate circuit breakers are configured
// with different sensitivity levels based on operation criticality.
//
// CIRCUIT BREAKER THRESHOLDS MATRIX:
// ┌─────────────┬──────────┬────────┬───────────┬──────────┬─────────────┬──────────────┐
// │ Operation   │ Min Req  │ Trip   │ Failure % │ Half-Open│ Reset Time  │ Recovery Time│
// │             │ Before   │ After  │ Threshold │ Requests │ (Interval)  │ (Timeout)    │
// │             │ Eval     │ Fails  │           │ Allowed  │             │              │
// ├─────────────┼──────────┼────────┼───────────┼──────────┼─────────────┼──────────────┤
// │ Presign Ops │    3     │   5    │    60%    │    3     │   30 sec    │    60 sec    │
// │ Delete Ops  │    2     │   3    │    50%    │    2     │   60 sec    │   120 sec    │
// │ Health Check│    1     │   2    │     -     │    1     │   10 sec    │    30 sec    │
// └─────────────┴──────────┴────────┴───────────┴──────────┴─────────────┴──────────────┘
//
// OPERATION TYPES & BEHAVIORS:
//
// 1. PRESIGN OPERATIONS (Upload/Download URL Generation)
//   - Used by: GetPresignedUploadUrl(), GetPresignedDownloadUrl()
//   - Sensitivity: Moderate (allows some transient failures)
//   - Trip Condition: 5+ failures OR ≥60% failure rate (minimum 3 requests)
//   - Rationale: URL generation failures are recoverable, moderate tolerance
//
// 2. DELETE OPERATIONS (File Deletion from S3)
//   - Used by: PurgeFile() for permanent file deletion
//   - Sensitivity: High (conservative to prevent data integrity issues)
//   - Trip Condition: 3+ failures OR ≥50% failure rate (minimum 2 requests)
//   - Rationale: Delete failures can cause orphaned objects, prefer fast failure
//
// 3. HEALTH CHECK OPERATIONS (Service Availability Monitoring)
//   - Used by: HealthCheck() for S3 service monitoring
//   - Sensitivity: Maximum (fastest detection of S3 issues)
//   - Trip Condition: 2+ failures (no percentage threshold)
//   - Rationale: Early warning system, should detect issues quickly
//
// ERROR CLASSIFICATION:
//
// RETRYABLE ERRORS (Trip Circuit Breaker):
//
//	✗ Network: timeout, connection refused, connection reset
//	✗ AWS Service: ServiceUnavailable, InternalError, SlowDown, RequestTimeout
//	✗ HTTP 5xx: 500, 502, 503, 504 (server-side issues)
//
// NON-RETRYABLE ERRORS (Do NOT Trip Circuit Breaker):
//
//	✓ Auth: AccessDenied, InvalidAccessKeyId, SignatureDoesNotMatch
//	✓ Client: NoSuchBucket, NoSuchKey, InvalidRequest
//	✓ HTTP 4xx: 400, 401, 403, 404 (client-side issues)
//
// CIRCUIT BREAKER STATES:
//
// CLOSED (Normal Operation):
//   - All requests pass through to S3
//   - Failure counting and monitoring active
//   - Transitions to OPEN when thresholds exceeded
//
// OPEN (Protection Active):
//   - All requests fail immediately (no S3 calls)
//   - Protects system resources during outages
//   - Transitions to HALF-OPEN after timeout period
//
// HALF-OPEN (Recovery Testing):
//   - Limited test requests allowed through
//   - Success → CLOSED, Failure → OPEN
//   - Automatic recovery detection
//
// BENEFITS DURING AWS S3 OUTAGES:
//
//	✅ Database connections protected (no 30+ second holds)
//	✅ Memory usage controlled (no blocked goroutines)
//	✅ Fast failure (nanoseconds vs 30+ seconds)
//	✅ Service remains available for database operations
//	✅ Automatic recovery when S3 service restored
//	✅ Clear error messages for users and operators
package usecase

import (
	"context"
	"fmt"
	"strings"
	"time"

	"common/herosentry"

	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/sony/gobreaker"
)

// S3OperationType defines the type of S3 operation for circuit breaker monitoring
type S3OperationType string

const (
	S3OpPresignUpload   S3OperationType = "presign_upload"
	S3OpPresignDownload S3OperationType = "presign_download"
	S3OpDeleteObject    S3OperationType = "delete_object"
	S3OpHeadBucket      S3OperationType = "head_bucket"
)

// S3CircuitBreaker wraps S3 operations with circuit breaker protection to prevent
// cascading failures during AWS S3 outages. It implements the Circuit Breaker pattern
// with separate circuits for different operation types, each with tailored thresholds.
//
// Circuit Breaker States:
//   - CLOSED: Normal operation, all requests pass through to S3
//   - OPEN: S3 outage detected, all requests fail fast (no S3 calls)
//   - HALF-OPEN: Recovery testing, limited requests allowed through
//
// Operation Types:
//   - Presign Operations: Upload/download URL generation (less critical)
//   - Delete Operations: File deletion from S3 (more critical, conservative settings)
//   - Health Check Operations: Service availability monitoring (most sensitive)
type S3CircuitBreaker struct {
	s3Client     *s3.Client
	s3BucketName string

	// Separate circuit breakers for different operation types with tailored settings
	presignBreaker *gobreaker.CircuitBreaker // For upload/download URL generation
	deleteBreaker  *gobreaker.CircuitBreaker // For file deletion (more conservative)
	healthBreaker  *gobreaker.CircuitBreaker // For health checks (most sensitive)
}

// NewS3CircuitBreaker creates a new S3 circuit breaker wrapper with optimized settings
// for different operation types. Each circuit breaker has different thresholds based
// on the criticality and expected failure patterns of the operations.
func NewS3CircuitBreaker(s3Client *s3.Client, s3BucketName string) *S3CircuitBreaker {
	// PRESIGN OPERATIONS CIRCUIT BREAKER
	// Used for: GetPresignedUploadUrl, GetPresignedDownloadUrl
	// Behavior: Moderate sensitivity, allows for some transient failures
	//
	// Thresholds:
	//   - Minimum requests before evaluation: 3
	//   - Trip conditions: 5+ total failures OR 60%+ failure rate
	//   - Half-open state: Allow 3 test requests
	//   - Failure count reset: Every 30 seconds
	//   - Recovery attempt: After 60 seconds in OPEN state
	//
	// State Transitions:
	//   CLOSED → OPEN: When 5+ failures OR ≥60% failure rate (min 3 requests)
	//   OPEN → HALF-OPEN: After 60 seconds timeout
	//   HALF-OPEN → CLOSED: When test requests succeed
	//   HALF-OPEN → OPEN: When test requests fail
	presignSettings := gobreaker.Settings{
		Name:        "S3-Presign-Operations",
		MaxRequests: 3,                // Allow 3 requests in half-open state
		Interval:    30 * time.Second, // Reset failure count after 30 seconds
		Timeout:     60 * time.Second, // Try to recover after 60 seconds in open state
		ReadyToTrip: func(counts gobreaker.Counts) bool {
			// Trip if we have 5 or more failures, or failure rate > 60%
			failureRatio := float64(counts.TotalFailures) / float64(counts.Requests)
			return counts.Requests >= 3 && (counts.TotalFailures >= 5 || failureRatio >= 0.6)
		},
		OnStateChange: func(name string, from gobreaker.State, to gobreaker.State) {
			fmt.Printf("Circuit breaker %s changed from %v to %v\n", name, from, to)
		},
	}

	// DELETE OPERATIONS CIRCUIT BREAKER
	// Used for: PurgeFile (permanent file deletion from S3)
	// Behavior: High sensitivity, conservative settings to prevent data loss
	//
	// Thresholds:
	//   - Minimum requests before evaluation: 2
	//   - Trip conditions: 3+ total failures OR 50%+ failure rate
	//   - Half-open state: Allow only 2 test requests
	//   - Failure count reset: Every 60 seconds (longer than presign)
	//   - Recovery attempt: After 120 seconds in OPEN state (longer recovery)
	//
	// State Transitions:
	//   CLOSED → OPEN: When 3+ failures OR ≥50% failure rate (min 2 requests)
	//   OPEN → HALF-OPEN: After 120 seconds timeout
	//   HALF-OPEN → CLOSED: When test requests succeed
	//   HALF-OPEN → OPEN: When test requests fail
	//
	// Rationale: Delete operations are destructive and critical. We prefer to fail
	// fast rather than risk orphaned S3 objects or inconsistent state between
	// database and storage. Conservative settings prevent data integrity issues.
	deleteSettings := gobreaker.Settings{
		Name:        "S3-Delete-Operations",
		MaxRequests: 2,                 // More conservative for delete operations
		Interval:    60 * time.Second,  // Longer reset interval for destructive operations
		Timeout:     120 * time.Second, // Longer recovery timeout
		ReadyToTrip: func(counts gobreaker.Counts) bool {
			// More sensitive for delete operations
			failureRatio := float64(counts.TotalFailures) / float64(counts.Requests)
			return counts.Requests >= 2 && (counts.TotalFailures >= 3 || failureRatio >= 0.5)
		},
		OnStateChange: func(name string, from gobreaker.State, to gobreaker.State) {
			fmt.Printf("Circuit breaker %s changed from %v to %v\n", name, from, to)
		},
	}

	// HEALTH CHECK OPERATIONS CIRCUIT BREAKER
	// Used for: HealthCheck (S3 service availability monitoring)
	// Behavior: Maximum sensitivity, fastest response to detect outages
	//
	// Thresholds:
	//   - Minimum requests before evaluation: 1 (any failure trips)
	//   - Trip conditions: 2+ total failures (very sensitive)
	//   - Half-open state: Allow only 1 test request
	//   - Failure count reset: Every 10 seconds (fastest reset)
	//   - Recovery attempt: After 30 seconds in OPEN state (fastest recovery)
	//
	// State Transitions:
	//   CLOSED → OPEN: When 2+ failures (no minimum request threshold)
	//   OPEN → HALF-OPEN: After 30 seconds timeout
	//   HALF-OPEN → CLOSED: When test request succeeds
	//   HALF-OPEN → OPEN: When test request fails
	//
	// Rationale: Health checks should be the first indicator of S3 issues.
	// Very sensitive settings ensure we detect outages quickly and can
	// respond appropriately in monitoring systems.
	healthSettings := gobreaker.Settings{
		Name:        "S3-Health-Check",
		MaxRequests: 1,                // Only one health check at a time
		Interval:    10 * time.Second, // Quick reset for health checks
		Timeout:     30 * time.Second, // Quick recovery attempt
		ReadyToTrip: func(counts gobreaker.Counts) bool {
			// Trip quickly on health check failures
			return counts.TotalFailures >= 2
		},
		OnStateChange: func(name string, from gobreaker.State, to gobreaker.State) {
			fmt.Printf("Circuit breaker %s changed from %v to %v\n", name, from, to)
		},
	}

	return &S3CircuitBreaker{
		s3Client:       s3Client,
		s3BucketName:   s3BucketName,
		presignBreaker: gobreaker.NewCircuitBreaker(presignSettings),
		deleteBreaker:  gobreaker.NewCircuitBreaker(deleteSettings),
		healthBreaker:  gobreaker.NewCircuitBreaker(healthSettings),
	}
}

// isRetryableS3Error determines if an S3 error should be considered a failure for circuit breaker
// purposes. This function implements intelligent error classification to distinguish between
// errors that indicate S3 service issues (which should trip the circuit breaker) and
// errors that are client-side or permission-related (which should not trip the circuit breaker).
//
// Error Classification:
//
// RETRYABLE ERRORS (Trip Circuit Breaker):
//   - Network errors: timeout, connection refused, connection reset, network issues
//   - AWS service errors: ServiceUnavailable, InternalError, SlowDown, RequestTimeout
//   - HTTP 5xx errors: 500, 502, 503, 504 (server-side issues)
//
// NON-RETRYABLE ERRORS (Do NOT Trip Circuit Breaker):
//   - Authentication errors: AccessDenied, InvalidAccessKeyId, SignatureDoesNotMatch
//   - Authorization errors: Forbidden, insufficient permissions
//   - Client errors: NoSuchBucket, NoSuchKey, InvalidRequest
//   - HTTP 4xx errors: 400, 401, 403, 404 (client-side issues)
//
// Behavior:
//   - Returns true: Error indicates S3 service issue, should count toward circuit breaker failure
//   - Returns false: Error is client-side, should not affect circuit breaker state
//
// This classification prevents circuit breakers from opening due to application bugs
// or configuration issues while still protecting against actual S3 service outages.
func (cb *S3CircuitBreaker) isRetryableS3Error(err error) bool {
	if err == nil {
		return false
	}

	errStr := strings.ToLower(err.Error())

	// Network and timeout errors - these indicate connectivity/infrastructure issues
	if strings.Contains(errStr, "timeout") ||
		strings.Contains(errStr, "connection refused") ||
		strings.Contains(errStr, "connection reset") ||
		strings.Contains(errStr, "network") {
		return true
	}

	// AWS service errors that indicate outage or service degradation
	if strings.Contains(errStr, "serviceunavailable") ||
		strings.Contains(errStr, "internalerror") ||
		strings.Contains(errStr, "slowdown") ||
		strings.Contains(errStr, "requesttimeout") ||
		strings.Contains(errStr, "temporarilyunavailable") {
		return true
	}

	// HTTP 5xx errors from AWS - server-side issues
	if strings.Contains(errStr, "500") ||
		strings.Contains(errStr, "502") ||
		strings.Contains(errStr, "503") ||
		strings.Contains(errStr, "504") {
		return true
	}

	// All other errors (4xx, permission errors, etc.) are considered non-retryable
	// and should not trip the circuit breaker
	return false
}

// PresignPutObjectWithURL executes S3 presign put operation with circuit breaker protection
// and returns just the URL string for upload operations.
//
// Circuit Breaker Behavior:
//   - Uses presignBreaker (moderate sensitivity settings)
//   - CLOSED state: Normal S3 presign operation
//   - OPEN state: Immediate failure with circuit breaker error message
//   - HALF-OPEN state: Limited test requests allowed
//
// Error Handling:
//   - Circuit breaker open: Returns specific error message about service unavailability
//   - Circuit breaker half-open: Returns "too many requests" error if limit exceeded
//   - S3 service errors: Classified by isRetryableS3Error() to determine if should trip breaker
//   - Client errors: Pass through without affecting circuit breaker state
//
// Return Values:
//   - Success: Returns presigned URL string
//   - Failure: Returns empty string and descriptive error
func (cb *S3CircuitBreaker) PresignPutObjectWithURL(ctx context.Context, input *s3.PutObjectInput, optFns ...func(*s3.PresignOptions)) (string, error) {
	result, err := cb.presignBreaker.Execute(func() (interface{}, error) {
		presigner := s3.NewPresignClient(cb.s3Client)
		presignedResult, err := presigner.PresignPutObject(ctx, input, optFns...)
		if err != nil {
			return "", err
		}
		return presignedResult.URL, nil
	})

	if err != nil {
		// Check if this is a circuit breaker error or actual S3 error
		if err == gobreaker.ErrOpenState {
			cbErr := fmt.Errorf("S3 upload service temporarily unavailable due to repeated failures (circuit breaker open)")
			herosentry.CaptureException(ctx, cbErr, herosentry.ErrorTypeExternal, "S3 circuit breaker open for upload")
			return "", cbErr
		}
		if err == gobreaker.ErrTooManyRequests {
			cbErr := fmt.Errorf("S3 upload service recovering, too many concurrent requests (circuit breaker half-open)")
			herosentry.CaptureException(ctx, cbErr, herosentry.ErrorTypeExternal, "S3 circuit breaker half-open for upload")
			return "", cbErr
		}

		// For actual S3 errors, determine if they should trip the circuit breaker
		if cb.isRetryableS3Error(err) {
			herosentry.CaptureException(ctx, err, herosentry.ErrorTypeExternal, "S3 upload service issue")
			return "", fmt.Errorf("S3 upload failed due to service issue: %w", err)
		}

		// Non-retryable errors (like access denied) shouldn't trip the circuit breaker
		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeExternal, "S3 upload failed")
		return "", fmt.Errorf("S3 upload failed: %w", err)
	}

	return result.(string), nil
}

// PresignGetObjectWithURL executes S3 presign get operation with circuit breaker protection
// and returns just the URL string for download operations.
//
// Circuit Breaker Behavior:
//   - Uses presignBreaker (same settings as upload presign operations)
//   - CLOSED state: Normal S3 presign operation
//   - OPEN state: Immediate failure with circuit breaker error message
//   - HALF-OPEN state: Limited test requests allowed (shares state with upload presigns)
//
// Error Handling:
//   - Circuit breaker open: Returns specific error message about download service unavailability
//   - Circuit breaker half-open: Returns "too many requests" error if limit exceeded
//   - S3 service errors: Classified by isRetryableS3Error() to determine if should trip breaker
//   - Client errors: Pass through without affecting circuit breaker state
//
// Return Values:
//   - Success: Returns presigned URL string for download
//   - Failure: Returns empty string and descriptive error
func (cb *S3CircuitBreaker) PresignGetObjectWithURL(ctx context.Context, input *s3.GetObjectInput, optFns ...func(*s3.PresignOptions)) (string, error) {
	result, err := cb.presignBreaker.Execute(func() (interface{}, error) {
		presigner := s3.NewPresignClient(cb.s3Client)
		presignedResult, err := presigner.PresignGetObject(ctx, input, optFns...)
		if err != nil {
			return "", err
		}
		return presignedResult.URL, nil
	})

	if err != nil {
		if err == gobreaker.ErrOpenState {
			cbErr := fmt.Errorf("S3 download service temporarily unavailable due to repeated failures (circuit breaker open)")
			herosentry.CaptureException(ctx, cbErr, herosentry.ErrorTypeExternal, "S3 circuit breaker open for download")
			return "", cbErr
		}
		if err == gobreaker.ErrTooManyRequests {
			cbErr := fmt.Errorf("S3 download service recovering, too many concurrent requests (circuit breaker half-open)")
			herosentry.CaptureException(ctx, cbErr, herosentry.ErrorTypeExternal, "S3 circuit breaker half-open for download")
			return "", cbErr
		}

		if cb.isRetryableS3Error(err) {
			herosentry.CaptureException(ctx, err, herosentry.ErrorTypeExternal, "S3 download service issue")
			return "", fmt.Errorf("S3 download failed due to service issue: %w", err)
		}

		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeExternal, "S3 download failed")
		return "", fmt.Errorf("S3 download failed: %w", err)
	}

	return result.(string), nil
}

// DeleteObject executes S3 delete operation with circuit breaker protection.
// This method uses the most conservative circuit breaker settings due to the
// destructive nature of delete operations.
//
// Circuit Breaker Behavior:
//   - Uses deleteBreaker (high sensitivity, conservative settings)
//   - CLOSED state: Normal S3 delete operation
//   - OPEN state: Immediate failure to prevent inconsistent state
//   - HALF-OPEN state: Very limited test requests (only 2 allowed)
//
// Critical Data Integrity Protection:
//   - Trips faster than other operations (50% failure rate vs 60%)
//   - Longer recovery timeout (120s vs 60s) for additional safety
//   - Prevents orphaned S3 objects when database operations might fail
//   - Ensures atomicity between database and S3 delete operations
//
// Error Handling:
//   - Circuit breaker open: Returns specific error about delete service unavailability
//   - Circuit breaker half-open: Returns "too many requests" error if limit exceeded
//   - S3 service errors: Classified by isRetryableS3Error() to determine if should trip breaker
//   - Client errors: Pass through without affecting circuit breaker state
//
// Return Values:
//   - Success: Returns S3 DeleteObjectOutput
//   - Failure: Returns nil and descriptive error
func (cb *S3CircuitBreaker) DeleteObject(ctx context.Context, input *s3.DeleteObjectInput, optFns ...func(*s3.Options)) (*s3.DeleteObjectOutput, error) {
	result, err := cb.deleteBreaker.Execute(func() (interface{}, error) {
		return cb.s3Client.DeleteObject(ctx, input, optFns...)
	})

	if err != nil {
		if err == gobreaker.ErrOpenState {
			return nil, fmt.Errorf("S3 delete service temporarily unavailable due to repeated failures (circuit breaker open)")
		}
		if err == gobreaker.ErrTooManyRequests {
			return nil, fmt.Errorf("S3 delete service recovering, too many concurrent requests (circuit breaker half-open)")
		}

		if cb.isRetryableS3Error(err) {
			return nil, fmt.Errorf("S3 delete failed due to service issue: %w", err)
		}

		return nil, fmt.Errorf("S3 delete failed: %w", err)
	}

	return result.(*s3.DeleteObjectOutput), nil
}

// HealthCheck performs a lightweight S3 health check with circuit breaker protection.
// This method uses the most sensitive circuit breaker settings to quickly detect
// S3 service issues for monitoring and alerting purposes.
//
// Circuit Breaker Behavior:
//   - Uses healthBreaker (maximum sensitivity, fastest response)
//   - CLOSED state: Normal S3 HeadBucket operation
//   - OPEN state: Immediate failure indicating detected S3 outage
//   - HALF-OPEN state: Single test request to verify recovery
//
// Health Check Strategy:
//   - Uses HeadBucket as lightweight operation (no data transfer)
//   - Trips after only 2 failures (fastest detection)
//   - Quick recovery attempts (30s timeout vs 60s/120s for operations)
//   - Fastest failure count reset (10s vs 30s/60s for operations)
//
// Monitoring Integration:
//   - First indicator of S3 service issues
//   - Can be used by load balancers for health checks
//   - Provides early warning before operational circuits trip
//   - Enables proactive alerting and incident response
//
// Error Handling:
//   - Circuit breaker open: Returns specific error about health check failure
//   - Circuit breaker half-open: Returns "limited" error if request blocked
//   - S3 service errors: All errors are considered indicators of service issues
//   - Client errors: Still passed through but may indicate configuration problems
//
// Return Values:
//   - Success: Returns nil (S3 service is healthy)
//   - Failure: Returns descriptive error about S3 service status
func (cb *S3CircuitBreaker) HealthCheck(ctx context.Context) error {
	_, err := cb.healthBreaker.Execute(func() (interface{}, error) {
		// Use HeadBucket as a lightweight health check
		_, err := cb.s3Client.HeadBucket(ctx, &s3.HeadBucketInput{
			Bucket: &cb.s3BucketName,
		})
		return nil, err
	})

	if err != nil {
		if err == gobreaker.ErrOpenState {
			return fmt.Errorf("S3 service health check failed (circuit breaker open)")
		}
		if err == gobreaker.ErrTooManyRequests {
			return fmt.Errorf("S3 service health check limited (circuit breaker half-open)")
		}
		return fmt.Errorf("S3 service health check failed: %w", err)
	}

	return nil
}

// GetState returns the current state of circuit breakers for monitoring and alerting.
// This method provides visibility into the circuit breaker states for operational monitoring.
//
// Return Value:
//
//	Map with keys: "presign", "delete", "health"
//	Values: gobreaker.State (closed, open, half-open)
//
// Usage:
//   - Monitoring dashboards: Display current service health status
//   - Alerting systems: Trigger alerts when circuits open
//   - Health checks: Include circuit breaker status in service health
//   - Debugging: Understand current protection status during incidents
//
// State Meanings:
//   - closed: Normal operation, requests pass through to S3
//   - open: Protection active, requests fail fast without S3 calls
//   - half-open: Recovery testing, limited requests allowed through
func (cb *S3CircuitBreaker) GetState() map[string]gobreaker.State {
	return map[string]gobreaker.State{
		"presign": cb.presignBreaker.State(),
		"delete":  cb.deleteBreaker.State(),
		"health":  cb.healthBreaker.State(),
	}
}

// GetCounts returns the current failure counts and statistics for circuit breakers.
// This method provides detailed metrics for monitoring, alerting, and capacity planning.
//
// Return Value:
//
//	Map with keys: "presign", "delete", "health"
//	Values: gobreaker.Counts containing:
//	  - Requests: Total number of requests attempted
//	  - TotalSuccesses: Total successful requests
//	  - TotalFailures: Total failed requests
//	  - ConsecutiveSuccesses: Recent consecutive successes
//	  - ConsecutiveFailures: Recent consecutive failures
//
// Usage:
//   - Metrics export: Send to monitoring systems (Prometheus, CloudWatch, etc.)
//   - Capacity planning: Understand request patterns and failure rates
//   - SLA monitoring: Track success/failure ratios
//   - Alerting: Set alerts based on failure thresholds
//   - Debugging: Analyze failure patterns during incidents
//
// Example Metrics:
//   - circuit_breaker_requests_total{operation="presign"}
//   - circuit_breaker_failures_total{operation="delete"}
//   - circuit_breaker_failure_rate{operation="health"}
func (cb *S3CircuitBreaker) GetCounts() map[string]gobreaker.Counts {
	return map[string]gobreaker.Counts{
		"presign": cb.presignBreaker.Counts(),
		"delete":  cb.deleteBreaker.Counts(),
		"health":  cb.healthBreaker.Counts(),
	}
}
