package usecase

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"log"
	"path/filepath"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/google/uuid"

	cmncontext "common/context"
	"common/herosentry"
	commonUtils "common/utils"
	repository "filerepository/internal/data"
	filerepository "proto/hero/filerepository/v1"
)

// Transaction utility functions for proper error handling and resource cleanup

// handleTransactionCleanup provides proper transaction cleanup with error logging.
// This function ensures transactions are properly committed or rolled back,
// preventing resource leaks and providing clear error messages.
//
// Usage pattern:
//
//	tx, err := db.BeginTx(ctx, nil)
//	if err != nil { return err }
//	defer handleTransactionCleanup(ctx, tx, &err)
//
//	// Business logic here...
//	// If any error occurs, it will be captured in err and trigger rollback
//	// If no error, transaction will be committed
func handleTransactionCleanup(ctx context.Context, tx *sql.Tx, businessErr *error) {
	if *businessErr != nil {
		// Business logic failed - rollback transaction
		if rollbackErr := tx.Rollback(); rollbackErr != nil {
			// Rollback failed - combine errors for better debugging
			err := fmt.Errorf("transaction rollback failed (%w) after business error: %v",
				rollbackErr, *businessErr)
			herosentry.CaptureException(ctx, err, herosentry.ErrorTypeInternal)
			*businessErr = err
			log.Printf("CRITICAL: Transaction rollback failed: %v", rollbackErr)
		} else {
			log.Printf("Transaction rolled back successfully due to error: %v", *businessErr)
		}
	} else {
		// Business logic succeeded - commit transaction
		if commitErr := tx.Commit(); commitErr != nil {
			// Commit failed - attempt rollback and set error
			if rollbackErr := tx.Rollback(); rollbackErr != nil {
				err := fmt.Errorf("transaction commit failed (%w) and rollback also failed (%w)",
					commitErr, rollbackErr)
				herosentry.CaptureException(ctx, err, herosentry.ErrorTypeDatabase)
				*businessErr = err
				log.Printf("CRITICAL: Both commit and rollback failed: commit=%v, rollback=%v",
					commitErr, rollbackErr)
			} else {
				err := fmt.Errorf("transaction commit failed: %w", commitErr)
				herosentry.CaptureException(ctx, err, herosentry.ErrorTypeDatabase)
				*businessErr = err
				log.Printf("Transaction commit failed but rollback succeeded: %v", commitErr)
			}
		}
		// Successful commit - no logging needed for normal operation
	}
}

// File size limits
const (
	// MaxFileSizeBytes defines the maximum allowed file size (250MB)
	MaxFileSizeBytes = 250 * 1024 * 1024 // 250MB in bytes
)

// FileRepositoryUseCase defines the use-case layer for file repository operations.
type FileRepositoryUseCase struct {
	database         *sql.DB
	fileRepo         repository.FileRepositoryRepository
	s3Client         *s3.Client
	s3BucketName     string
	s3CircuitBreaker *S3CircuitBreaker
}

// NewFileRepositoryUseCase creates a new FileRepositoryUseCase.
func NewFileRepositoryUseCase(
	database *sql.DB,
	fileRepo repository.FileRepositoryRepository,
	s3Client *s3.Client,
	s3BucketName string,
) (*FileRepositoryUseCase, error) {
	if database == nil {
		return nil, errors.New("database is nil: cannot initialize FileRepositoryUseCase")
	}
	if fileRepo == nil {
		return nil, errors.New("fileRepo is nil: cannot initialize FileRepositoryUseCase")
	}
	if s3Client == nil {
		return nil, errors.New("s3Client is nil: cannot initialize FileRepositoryUseCase")
	}
	if s3BucketName == "" {
		return nil, errors.New("s3BucketName is empty: cannot initialize FileRepositoryUseCase")
	}

	// Initialize S3 circuit breaker
	s3CircuitBreaker := NewS3CircuitBreaker(s3Client, s3BucketName)

	return &FileRepositoryUseCase{
		database:         database,
		fileRepo:         fileRepo,
		s3Client:         s3Client,
		s3BucketName:     s3BucketName,
		s3CircuitBreaker: s3CircuitBreaker,
	}, nil
}

// logAccess automatically records file access events for comprehensive audit trails.
//
// This function is called internally by all file operations to maintain a complete
// audit log of user interactions with files. It captures essential security and
// compliance information including user identity, IP address, and action type.
//
// Parameters:
//   - ctx: Request context containing authentication and IP information
//   - transaction: Database transaction to ensure atomicity with parent operation
//   - fileID: Unique identifier of the file being accessed
//   - action: Type of access (e.g., "download", "metadata_accessed", "file_updated")
//
// Security Features:
//   - User ID automatically extracted from authentication context (prevents spoofing)
//   - IP address captured from request headers (does not supports proxy environments)
//   - Failures are logged but don't interrupt main operations (availability over audit)
//   - All access events are immutable once recorded (audit integrity)
//
// Access Actions Logged:
//   - "download": When GetPresignedDownloadUrl is called (user initiates download)
//   - "metadata_accessed": When GetFileMetadata is called (user views file properties)
//   - "file_updated": When UpdateFileMetadata is called (user modifies metadata)
//   - "upload_confirmed": When ConfirmUpload is called (upload process completed)
//   - "file_deleted": When DeleteFile is called (file soft-deleted)
//   - "file_restored": When UndeleteFile is called (file restored from deletion)
//   - "file_purged": When PurgeFile is called (file permanently deleted)
//
// Note: This function never returns errors to prevent audit logging failures from
// disrupting business operations. Errors are logged to stdout for monitoring.
func (filerepositoryUsecase *FileRepositoryUseCase) logAccess(ctx context.Context, transaction *sql.Tx, fileID, action string) {
	spanContext, _, finish := herosentry.StartSpan(ctx, "FileRepositoryUseCase.logAccess")
	defer finish()

	currentUser := cmncontext.GetUsername(spanContext)
	if currentUser == "" {
		currentUser = repository.SystemOwnerID
	}

	// Extract IP address from context
	ipAddress := cmncontext.GetIPAddress(spanContext)

	accessRequest := &filerepository.RecordAccessRequest{
		FileId:    fileID,
		UserId:    currentUser,
		Action:    action,
		IpAddress: ipAddress,
	}

	// Don't fail the main operation if logging fails
	if _, err := filerepositoryUsecase.fileRepo.RecordAccess(spanContext, transaction, accessRequest); err != nil {
		fmt.Printf("Warning: failed to record access log for action '%s' on file '%s': %v\n", action, fileID, err)
	}
}

// filterSensitiveMetadata implements defense-in-depth security by filtering sensitive
// infrastructure details from file metadata based on the user's organizational access level.
//
// This function enforces the principle of least privilege by hiding sensitive storage
// implementation details from users who don't need them, while preserving full
// transparency for administrative users.
//
// Parameters:
//   - ctx: Request context containing user's allowed organization IDs
//   - metadata: Complete file metadata including sensitive fields
//
// Returns:
//   - Filtered metadata with sensitive fields removed for non-admin users
//   - Complete metadata for users with admin organization access (org ID 1)
//   - nil if input metadata is nil
//
// Security Model:
//   - Admin Organization (ID 1): Full access to all metadata including infrastructure details
//   - Regular Organizations: Filtered view hiding sensitive storage information
//
// Sensitive Fields (Hidden from Non-Admin Users):
//   - Provider: Cloud storage provider (AWS S3, GCP, Azure, etc.)
//   - BucketName: Storage bucket/container name (infrastructure detail)
//   - StorageKey: Object path/key in cloud storage (reveals storage structure)
//   - StorageClass: Storage tier configuration (HOT, WARM, COLD, FROZEN)
//   - ObjectVersion: Cloud storage versioning information
//   - IntegrityHash: Cloud provider checksums (ETag, MD5, CRC32C)
//   - EncryptionKeyId: KMS key references and encryption details
//   - Checksum: Application-level checksums
//   - StorageTags: Cloud storage object tags
//   - ProviderMetadata: Cloud provider-specific metadata
//
// Preserved Fields (Always Visible):
//   - File identification (ID, FileName, FileType, FileSize)
//   - Ownership and organization (OwnerId, OrgId)
//   - Status and lifecycle (Status, CreatedAt, UpdatedAt, LastAccessed)
//   - Access control (IsPublic, DownloadCount)
//   - Application metadata (ExtraMetadata for business logic)
//
// Use Cases:
//   - Multi-tenant SaaS: Hide infrastructure details from customers
//   - Compliance: Limit exposure of sensitive storage configuration
//   - Security: Prevent information disclosure about backend systems
//   - Admin Tools: Provide full visibility for operational teams
func (filerepositoryUsecase *FileRepositoryUseCase) filterSensitiveMetadata(ctx context.Context, metadata *filerepository.FileMetadata) *filerepository.FileMetadata {
	spanContext, _, finish := herosentry.StartSpan(ctx, "FileRepositoryUseCase.filterSensitiveMetadata")
	defer finish()

	if metadata == nil {
		return nil
	}

	// Check if user has access to org ID 1 (admin organization)
	allowedOrgIDs := cmncontext.GetAllowedOrgIDs(spanContext)
	hasAdminAccess := false
	for _, orgID := range allowedOrgIDs {
		if orgID == 1 {
			hasAdminAccess = true
			break
		}
	}

	// If user has admin org access, return full metadata
	if hasAdminAccess {
		return metadata
	}

	// For users without admin org access, create a copy without sensitive storage details
	filtered := &filerepository.FileMetadata{
		Id:            metadata.Id,
		FileName:      metadata.FileName,
		FileType:      metadata.FileType,
		FileSize:      metadata.FileSize,
		Status:        metadata.Status,
		OwnerId:       metadata.OwnerId,
		OrgId:         metadata.OrgId,
		IsPublic:      metadata.IsPublic,
		DownloadCount: metadata.DownloadCount,
		CreatedAt:     metadata.CreatedAt,
		UpdatedAt:     metadata.UpdatedAt,
		LastAccessed:  metadata.LastAccessed,
		ExtraMetadata: metadata.ExtraMetadata,
		// Sensitive fields removed for users without admin org access:
		// - Provider (reveals cloud provider)
		// - BucketName (reveals infrastructure details)
		// - StorageKey (reveals storage path structure)
		// - StorageClass (reveals storage configuration)
	}

	return filtered
}

// generateStorageKey creates a hierarchical, unique storage path for cloud storage
// that optimizes for organization, performance, and human readability.
//
// This function implements a strategic storage key structure that balances multiple
// requirements: uniqueness, organization, performance, and operational visibility.
//
// Parameters:
//   - fileID: Unique UUID for the file (ensures global uniqueness)
//   - fileName: Original filename from user (preserves human readability)
//   - orgID: Organization identifier (enables multi-tenant isolation)
//
// Returns:
//   - Hierarchical storage key: "{orgID}/{year}/{month}/{fileID}_{cleanFileName}"
//
// Storage Key Structure:
//   - Level 1 (orgID): Multi-tenant isolation and billing separation
//   - Level 2 (year): Time-based partitioning for lifecycle management
//   - Level 3 (month): Granular time partitioning for performance
//   - Level 4 (file): UUID + original name for uniqueness + readability
//
// Example Output:
//   - Input: fileID="abc123", fileName="My Document.pdf", orgID=42
//   - Output: "42/2024/06/abc123_My_Document.pdf"
//
// Design Benefits:
//   - Multi-tenancy: Each organization has isolated storage namespace
//   - Performance: Time-based partitioning reduces listing overhead
//   - Lifecycle: Easy to implement retention policies by date
//   - Debugging: Human-readable paths for operational troubleshooting
//   - Uniqueness: UUID prevents collisions even with identical filenames
//   - Compatibility: Filename sanitization ensures cloud storage compatibility
//
// Security Considerations:
//   - Organization isolation prevents cross-tenant access
//   - UUID prevents filename enumeration attacks
//   - Path structure doesn't reveal sensitive information
//
// Cloud Storage Compatibility:
//   - AWS S3: Optimized prefix distribution for performance
//   - Google Cloud Storage: Hierarchical structure for organization
//   - Azure Blob Storage: Container-like organization
//   - MinIO: S3-compatible structure
//
// Filename Sanitization:
//   - Spaces converted to underscores (cloud storage compatibility)
//   - Forward slashes converted to underscores (prevents path confusion)
//   - Preserves file extensions for MIME type detection
func (filerepositoryUsecase *FileRepositoryUseCase) generateStorageKey(fileID, fileName string, orgID int32) string {
	// Note: This function doesn't take context, so we can't use StartSpan here
	// Create a path structure: org_id/year/month/file_id_original_name
	now := time.Now()
	year := now.Format("2006")
	month := now.Format("01")

	// Clean the filename to make it S3-safe
	cleanFileName := strings.ReplaceAll(fileName, " ", "_")
	cleanFileName = strings.ReplaceAll(cleanFileName, "/", "_")

	// Combine file ID with original name to ensure uniqueness but keep readability
	storageFileName := fmt.Sprintf("%s_%s", fileID, cleanFileName)

	return fmt.Sprintf("%d/%s/%s/%s", orgID, year, month, storageFileName)
}

// validateUploadRequest performs comprehensive validation and security enforcement
// for presigned upload requests, implementing defense-in-depth security controls.
//
// This function serves as the primary security gateway for file uploads, enforcing
// authentication, authorization, input validation, and business rules before
// generating presigned URLs that grant direct cloud storage access.
//
// Parameters:
//   - ctx: Request context containing authentication and authorization information
//   - request: Upload request containing file metadata and preferences
//
// Returns:
//   - nil if validation passes and request is modified with security parameters
//   - error describing the first validation failure encountered
//
// Security Validations:
//   - Authentication: Ensures user is properly authenticated
//   - Authorization: Validates user has access to specified organization
//   - Input Sanitization: Prevents injection attacks and malformed data
//   - Business Rules: Enforces file size limits and naming conventions
//
// Request Modifications (Security Enforcement):
//   - owner_id: Automatically set from authentication context (prevents spoofing)
//   - org_id: Validated against user's allowed organizations (prevents cross-tenant access)
//   - expires_in: Bounded to reasonable limits (prevents indefinite access)
//
// Validation Rules:
//   - file_name: Required, must have valid extension
//   - file_type: Required, must be valid MIME type format
//   - owner_id: Must NOT be provided by client (security violation)
//   - org_id: Must be in user's allowed organizations or defaults to user's org
//   - expires_in: Defaults to 15 minutes, maximum 24 hours
//
// Authentication Context Handling:
//   - Cognito users: "cognito:<user_id>" format
//   - Bot services: "bot:<service_name>" format
//   - Basic auth: Plain username from organization API
//   - System operations: "SYSTEM" default
//
// Multi-tenant Security:
//   - Organization isolation enforced at validation layer
//   - Cross-tenant access attempts blocked with detailed error messages
//   - User's allowed organizations verified against permissions service
//
// File Type Security:
//   - MIME type format validation (must contain "/")
//   - File extension requirement (prevents extensionless uploads)
//   - Future: Can be extended with allowlist/blocklist validation
//
// Error Handling:
//   - Detailed error messages for debugging (development)
//   - Security-conscious error messages (production)
//   - Early return on first validation failure (fail-fast)
func (filerepositoryUsecase *FileRepositoryUseCase) validateUploadRequest(ctx context.Context, request *filerepository.PresignedUploadRequest) error {
	spanContext, _, finish := herosentry.StartSpan(ctx, "FileRepositoryUseCase.validateUploadRequest")
	defer finish()

	if request.FileName == "" {
		return errors.New("file_name is required")
	}
	if request.FileType == "" {
		return errors.New("file_type is required")
	}

	// owner_id should not be provided by client - it's derived from authentication context
	if request.OwnerId != "" {
		return errors.New("owner_id should not be provided - it will be automatically set from authentication context")
	}

	// Set owner_id from authentication context
	// GetUsername returns different formats based on authentication method:
	// - Cognito users: "cognito:<user_id>" (e.g., "cognito:abc123-def456")
	// - Bot services: "bot:<service_name>" (e.g., "bot:post-confirmation-lambda")
	// - Basic auth: "actual-username" (plain username from org API user)
	// - System operations: "SYSTEM" (default when no authenticated user)
	username := cmncontext.GetUsername(spanContext)
	if username == "" {
		// Default to SYSTEM if no authenticated user found (e.g., system operations)
		request.OwnerId = repository.SystemOwnerID
	} else {
		request.OwnerId = username
	}

	// If org_id is not provided, use the one from context
	if request.OrgId == 0 {
		request.OrgId = cmncontext.GetOrgId(spanContext)
	}

	// Validate that the final org_id is in the user's allowed organizations
	allowedOrgIDs := cmncontext.GetAllowedOrgIDs(spanContext)

	// DEBUG: Log authentication context
	fmt.Printf("DEBUG FileRepository Auth Context:\n")
	fmt.Printf("  Username: %s\n", cmncontext.GetUsername(spanContext))
	fmt.Printf("  OrgId: %d\n", cmncontext.GetOrgId(spanContext))
	fmt.Printf("  AllowedOrgIDs: %v\n", allowedOrgIDs)
	fmt.Printf("  Requested OrgId: %d\n", request.OrgId)

	if len(allowedOrgIDs) == 0 {
		return errors.New("no allowed organizations found in context")
	}

	orgAllowed := false
	for _, allowedOrgID := range allowedOrgIDs {
		if allowedOrgID == request.OrgId {
			orgAllowed = true
			break
		}
	}
	if !orgAllowed {
		err := fmt.Errorf("access denied: org_id %d is not in allowed organizations", request.OrgId)
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeUnauthorized)
		return err
	}

	// Validate file extension if present
	ext := filepath.Ext(request.FileName)
	if ext == "" {
		return errors.New("file must have an extension")
	}

	// Basic MIME type validation
	if !strings.Contains(request.FileType, "/") {
		return errors.New("invalid file_type format, expected MIME type like 'image/png'")
	}

	// Validate expires_in (default to 15 minutes if not set, max 24 hours)
	if request.ExpiresIn <= 0 {
		request.ExpiresIn = 15 * 60 // 15 minutes default
	}
	if request.ExpiresIn > 24*60*60 {
		return errors.New("expires_in cannot exceed 24 hours")
	}

	return nil
}

// GetPresignedUploadUrl generates a presigned URL for direct upload to S3 and creates a PENDING metadata record.
func (filerepositoryUsecase *FileRepositoryUseCase) GetPresignedUploadUrl(ctx context.Context, request *filerepository.PresignedUploadRequest) (*filerepository.PresignedUploadResponse, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "FileRepositoryUseCase.GetPresignedUploadUrl")
	defer finish()
	span.SetTag("file.name", request.FileName)
	span.SetTag("file.type", request.FileType)
	span.SetTag("org.id", fmt.Sprintf("%d", request.OrgId))

	// Validate the request
	if err := filerepositoryUsecase.validateUploadRequest(spanContext, request); err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation, "Upload request validation failed")
		return nil, fmt.Errorf("validation failed: %w", err)
	}

	// Generate unique file ID
	fileID := uuid.New().String()
	span.SetTag("file.id", fileID)

	// Generate storage key for S3
	storageKey := filerepositoryUsecase.generateStorageKey(fileID, request.FileName, request.OrgId)
	// Don't expose sensitive storage key in spans

	// Begin transaction for metadata operations
	transaction, err := filerepositoryUsecase.database.BeginTx(spanContext, nil)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to begin transaction for upload")
		return nil, fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer handleTransactionCleanup(spanContext, transaction, &err)

	// Create PENDING file metadata record
	currentTime := time.Now()
	metadata := &filerepository.FileMetadata{
		Id:            fileID,
		OrgId:         request.OrgId, // Use org_id from validated request
		OwnerId:       request.OwnerId,
		FileName:      request.FileName,
		FileType:      request.FileType,
		FileSize:      0, // Will be updated on confirm upload
		CreatedAt:     commonUtils.TimeToISO8601String(currentTime),
		UpdatedAt:     commonUtils.TimeToISO8601String(currentTime),
		Status:        filerepository.FileStatus_FILE_STATUS_PENDING,
		Provider:      filerepository.StorageProvider_STORAGE_PROVIDER_AWS_S3,
		StorageKey:    storageKey,
		BucketName:    filerepositoryUsecase.s3BucketName,
		StorageClass:  filerepository.StorageClass_STORAGE_CLASS_HOT, // Default to hot storage
		DownloadCount: 0,
		IsPublic:      false, // Default to private
	}

	// Handle extra metadata if provided
	if request.ExtraMetadata != nil {
		metadata.ExtraMetadata = request.ExtraMetadata
	}

	// Create metadata record in database
	if err = filerepositoryUsecase.fileRepo.CreateFileMetadata(spanContext, transaction, metadata); err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to create file metadata for file %s", fileID))
		return nil, fmt.Errorf("failed to create file metadata: %w", err)
	}

	// Generate presigned URL for S3 upload using circuit breaker
	presignedReq := &s3.PutObjectInput{
		Bucket:      aws.String(filerepositoryUsecase.s3BucketName),
		Key:         aws.String(storageKey),
		ContentType: aws.String(request.FileType),
		// Explicitly require server-side encryption with AWS KMS
		ServerSideEncryption: "aws:kms",
		// Add metadata to S3 object
		Metadata: map[string]string{
			"file-id":    fileID,
			"owner-id":   request.OwnerId,
			"created-at": metadata.CreatedAt,
		},
	}

	// Generate the presigned URL using circuit breaker protection
	// Note: AWS SDK v2 doesn't easily support content-length-range conditions for PUT presigned URLs
	// The size limit is enforced at the application level during ConfirmUpload
	presignedURLString, err := filerepositoryUsecase.s3CircuitBreaker.PresignPutObjectWithURL(spanContext, presignedReq, func(opts *s3.PresignOptions) {
		opts.Expires = time.Duration(request.ExpiresIn) * time.Second
	})
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeExternal, fmt.Sprintf("Failed to generate presigned URL for file %s", fileID))
		return nil, fmt.Errorf("failed to generate presigned URL: %w", err)
	}

	// Transaction will be committed by handleTransactionCleanup since err is nil

	// Return response
	return &filerepository.PresignedUploadResponse{
		PresignedUrl: presignedURLString,
		Metadata:     metadata,
		Message:      fmt.Sprintf("Presigned upload URL generated successfully. Upload expires in %d seconds. Maximum file size: 250MB (%d bytes).", request.ExpiresIn, MaxFileSizeBytes),
	}, nil
}

// GetPresignedDownloadUrl generates a presigned URL for file download.
func (filerepositoryUsecase *FileRepositoryUseCase) GetPresignedDownloadUrl(ctx context.Context, request *filerepository.PresignedDownloadRequest) (*filerepository.PresignedDownloadResponse, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "FileRepositoryUseCase.GetPresignedDownloadUrl")
	defer finish()
	span.SetTag("file.id", request.Id)
	span.SetTag("expires_in", fmt.Sprintf("%d", request.ExpiresIn))

	// Validate request
	if request.Id == "" {
		return nil, errors.New("file id is required")
	}

	// Set default expiration if not provided
	if request.ExpiresIn <= 0 {
		request.ExpiresIn = 15 * 60 // 15 minutes default
	}
	if request.ExpiresIn > 24*60*60 {
		return nil, errors.New("expires_in cannot exceed 24 hours")
	}

	// Begin transaction
	transaction, err := filerepositoryUsecase.database.BeginTx(spanContext, nil)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to begin transaction for download")
		return nil, fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer handleTransactionCleanup(spanContext, transaction, &err)

	// Get file metadata
	metadata, err := filerepositoryUsecase.fileRepo.GetFileMetadata(spanContext, transaction, request.Id)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to get file metadata for file %s", request.Id))
		return nil, fmt.Errorf("failed to get file metadata: %w", err)
	}

	// Check if file is active
	if metadata.Status != filerepository.FileStatus_FILE_STATUS_ACTIVE {
		err := fmt.Errorf("file is not available for download, status: %v", metadata.Status)
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation)
		return nil, err
	}

	// Generate presigned URL for S3 download using circuit breaker
	presignedReq := &s3.GetObjectInput{
		Bucket: aws.String(metadata.BucketName),
		Key:    aws.String(metadata.StorageKey),
	}

	// Generate the presigned URL using circuit breaker protection
	presignedURLString, err := filerepositoryUsecase.s3CircuitBreaker.PresignGetObjectWithURL(spanContext, presignedReq, func(opts *s3.PresignOptions) {
		opts.Expires = time.Duration(request.ExpiresIn) * time.Second
	})
	if err != nil {
		err := fmt.Errorf("failed to generate presigned download URL: %w", err)
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeExternal, fmt.Sprintf("Failed to generate presigned download URL for file %s", request.Id))
		return nil, err
	}

	// Update last accessed timestamp
	if err = filerepositoryUsecase.fileRepo.UpdateLastAccessed(spanContext, transaction, request.Id); err != nil {
		// Log error but don't fail the request
		fmt.Printf("Warning: failed to update last accessed timestamp: %v\n", err)
	}

	// Automatically log download initiation for audit trail
	filerepositoryUsecase.logAccess(spanContext, transaction, request.Id, "download")

	return &filerepository.PresignedDownloadResponse{
		PresignedUrl: presignedURLString,
		Metadata:     metadata,
		Message:      fmt.Sprintf("Presigned download URL generated successfully. URL expires in %d seconds.", request.ExpiresIn),
	}, nil
}

// GetFileMetadata retrieves metadata for a specific file.
func (filerepositoryUsecase *FileRepositoryUseCase) GetFileMetadata(ctx context.Context, request *filerepository.GetFileMetadataRequest) (*filerepository.GetFileMetadataResponse, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "FileRepositoryUseCase.GetFileMetadata")
	defer finish()
	span.SetTag("file.id", request.Id)

	// Validate request
	if request.Id == "" {
		return nil, errors.New("file id is required")
	}

	// Begin transaction
	transaction, err := filerepositoryUsecase.database.BeginTx(spanContext, nil)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to begin transaction for metadata retrieval")
		return nil, fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer handleTransactionCleanup(spanContext, transaction, &err)

	// Get file metadata
	metadata, err := filerepositoryUsecase.fileRepo.GetFileMetadata(spanContext, transaction, request.Id)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to get file metadata for file %s", request.Id))
		return nil, fmt.Errorf("failed to get file metadata: %w", err)
	}

	// Automatically log file metadata access for audit trail
	filerepositoryUsecase.logAccess(spanContext, transaction, request.Id, "metadata_accessed")

	// Filter sensitive metadata based on user role
	filteredMetadata := filerepositoryUsecase.filterSensitiveMetadata(spanContext, metadata)

	return &filerepository.GetFileMetadataResponse{
		Metadata: filteredMetadata,
	}, nil
}

// SearchFiles provides advanced search capabilities for files.
func (filerepositoryUsecase *FileRepositoryUseCase) SearchFiles(ctx context.Context, request *filerepository.SearchFilesRequest) (*filerepository.SearchFilesResponse, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "FileRepositoryUseCase.SearchFiles")
	defer finish()
	span.SetTag("search.page_size", fmt.Sprintf("%d", request.PageSize))
	if request.Query != "" {
		span.SetTag("search.has_query", "true")
	} else {
		span.SetTag("search.has_query", "false")
	}

	// Begin transaction
	transaction, err := filerepositoryUsecase.database.BeginTx(spanContext, nil)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to begin transaction")
		return nil, fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer handleTransactionCleanup(spanContext, transaction, &err)

	// Perform search - now returns protobuf directly
	response, err := filerepositoryUsecase.fileRepo.SearchFiles(spanContext, transaction, request)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to search files")
		return nil, fmt.Errorf("failed to search files: %w", err)
	}

	// Filter sensitive metadata from search results in-place
	for i, result := range response.Results {
		response.Results[i].Metadata = filerepositoryUsecase.filterSensitiveMetadata(spanContext, result.Metadata)
	}

	span.SetTag("search.results_count", fmt.Sprintf("%d", len(response.Results)))

	return response, nil
}

// ListFiles returns a paginated list of files.
func (filerepositoryUsecase *FileRepositoryUseCase) ListFiles(ctx context.Context, request *filerepository.ListFilesRequest) (*filerepository.ListFilesResponse, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "FileRepositoryUseCase.ListFiles")
	defer finish()
	span.SetTag("list.page_size", fmt.Sprintf("%d", request.PageSize))

	// Begin transaction
	transaction, err := filerepositoryUsecase.database.BeginTx(spanContext, nil)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to begin transaction")
		return nil, fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer handleTransactionCleanup(spanContext, transaction, &err)

	// List files - now returns protobuf directly
	response, err := filerepositoryUsecase.fileRepo.ListFiles(spanContext, transaction, request)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to list files")
		return nil, fmt.Errorf("failed to list files: %w", err)
	}

	// Filter sensitive metadata from file list in-place
	for i, file := range response.Files {
		response.Files[i] = filerepositoryUsecase.filterSensitiveMetadata(spanContext, file)
	}

	span.SetTag("list.files_count", fmt.Sprintf("%d", len(response.Files)))

	return response, nil
}

// UpdateFileMetadata updates metadata for a specific file.
func (filerepositoryUsecase *FileRepositoryUseCase) UpdateFileMetadata(ctx context.Context, request *filerepository.UpdateFileMetadataRequest) (*filerepository.UpdateFileMetadataResponse, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "FileRepositoryUseCase.UpdateFileMetadata")
	defer finish()
	span.SetTag("file.id", request.Id)

	// Validate request
	if request.Id == "" {
		return nil, errors.New("file id is required")
	}

	// Begin transaction
	transaction, err := filerepositoryUsecase.database.BeginTx(spanContext, nil)
	if err != nil {
		err := fmt.Errorf("failed to begin transaction: %w", err)
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to begin transaction for metadata update")
		return nil, err
	}
	defer handleTransactionCleanup(spanContext, transaction, &err)

	// Update file metadata
	metadata, err := filerepositoryUsecase.fileRepo.UpdateFileMetadata(spanContext, transaction, request.Id, request)
	if err != nil {
		err := fmt.Errorf("failed to update file metadata: %w", err)
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to update file metadata for file %s", request.Id))
		return nil, err
	}

	// Automatically log file metadata update for audit trail
	filerepositoryUsecase.logAccess(spanContext, transaction, request.Id, "file_updated")

	// Filter sensitive metadata based on user role
	filteredMetadata := filerepositoryUsecase.filterSensitiveMetadata(spanContext, metadata)

	return &filerepository.UpdateFileMetadataResponse{
		Metadata: filteredMetadata,
		Message:  "File metadata updated successfully",
	}, nil
}

// ConfirmUpload confirms that a file upload was successful.
func (filerepositoryUsecase *FileRepositoryUseCase) ConfirmUpload(ctx context.Context, request *filerepository.ConfirmUploadRequest) (*filerepository.ConfirmUploadResponse, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "FileRepositoryUseCase.ConfirmUpload")
	defer finish()
	span.SetTag("file.id", request.PendingId)
	span.SetTag("file.size", fmt.Sprintf("%d", request.FileSize))

	// Validate request
	if request.PendingId == "" {
		return nil, errors.New("pending_id is required")
	}
	if request.FileSize <= 0 {
		return nil, errors.New("file_size must be greater than 0")
	}

	// Enforce file size limit: 250MB maximum
	if request.FileSize > MaxFileSizeBytes {
		err := fmt.Errorf("file size limit exceeded: maximum allowed size is 250MB (%d bytes), actual size is %d bytes", MaxFileSizeBytes, request.FileSize)
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation)
		return nil, err
	}

	// Begin transaction
	transaction, err := filerepositoryUsecase.database.BeginTx(spanContext, nil)
	if err != nil {
		err := fmt.Errorf("failed to begin transaction: %w", err)
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to begin transaction for upload confirmation")
		return nil, err
	}
	defer handleTransactionCleanup(spanContext, transaction, &err)

	// Get pending file metadata to validate ownership
	metadata, err := filerepositoryUsecase.fileRepo.GetFileMetadata(spanContext, transaction, request.PendingId)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to get pending file metadata for file %s", request.PendingId))
		return nil, fmt.Errorf("pending upload not found")
	}

	// Validate that the user confirming the upload is the owner
	currentUser := cmncontext.GetUsername(spanContext)
	if currentUser == "" {
		currentUser = repository.SystemOwnerID
	}

	if metadata.OwnerId != currentUser {
		err := fmt.Errorf("access denied: cannot confirm upload for file you don't own")
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeUnauthorized)
		return nil, err
	}

	// Validate file is in PENDING status
	if metadata.Status != filerepository.FileStatus_FILE_STATUS_PENDING {
		err := fmt.Errorf("file is not in pending status, current status: %v", metadata.Status)
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation)
		return nil, err
	}

	// Confirm upload and update status to ACTIVE
	confirmedMetadata, err := filerepositoryUsecase.fileRepo.ConfirmUpload(spanContext, transaction, request.PendingId, request.FileSize)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to confirm upload for file %s", request.PendingId))
		return nil, fmt.Errorf("failed to confirm upload: %w", err)
	}

	// Automatically log upload confirmation for audit trail
	filerepositoryUsecase.logAccess(spanContext, transaction, request.PendingId, "upload_confirmed")

	// Filter sensitive metadata based on user role
	filteredMetadata := filerepositoryUsecase.filterSensitiveMetadata(spanContext, confirmedMetadata)

	return &filerepository.ConfirmUploadResponse{
		Metadata: filteredMetadata,
		Message:  "Upload confirmed successfully",
	}, nil
}

// CleanupPendingUploads cleans up stray PENDING records.
func (filerepositoryUsecase *FileRepositoryUseCase) CleanupPendingUploads(ctx context.Context, request *filerepository.CleanupPendingUploadsRequest) (*filerepository.CleanupPendingUploadsResponse, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "FileRepositoryUseCase.CleanupPendingUploads")
	defer finish()
	span.SetTag("older_than", request.OlderThan)

	// Validate request
	if request.OlderThan == "" {
		return nil, errors.New("older_than timestamp is required")
	}

	// Validate timestamp format
	if _, err := time.Parse(time.RFC3339, request.OlderThan); err != nil {
		err := fmt.Errorf("invalid older_than timestamp format, expected ISO-8601: %w", err)
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation)
		return nil, err
	}

	// Begin transaction
	transaction, err := filerepositoryUsecase.database.BeginTx(spanContext, nil)
	if err != nil {
		err := fmt.Errorf("failed to begin transaction: %w", err)
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to begin transaction for cleanup")
		return nil, err
	}
	defer handleTransactionCleanup(spanContext, transaction, &err)

	// Cleanup pending uploads
	removedCount, err := filerepositoryUsecase.fileRepo.CleanupPendingUploads(spanContext, transaction, request.OlderThan)
	if err != nil {
		err := fmt.Errorf("failed to cleanup pending uploads: %w", err)
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to cleanup pending uploads")
		return nil, err
	}

	span.SetTag("removed_count", fmt.Sprintf("%d", removedCount))

	return &filerepository.CleanupPendingUploadsResponse{
		RemovedCount: removedCount,
		Message:      fmt.Sprintf("Successfully cleaned up %d pending upload records", removedCount),
	}, nil
}

// DeleteFile soft-deletes a file.
func (filerepositoryUsecase *FileRepositoryUseCase) DeleteFile(ctx context.Context, request *filerepository.DeleteFileRequest) (*filerepository.DeleteFileResponse, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "FileRepositoryUseCase.DeleteFile")
	defer finish()
	span.SetTag("file.id", request.Id)

	// Validate request
	if request.Id == "" {
		return nil, errors.New("file id is required")
	}

	// Begin transaction
	transaction, err := filerepositoryUsecase.database.BeginTx(spanContext, nil)
	if err != nil {
		err := fmt.Errorf("failed to begin transaction: %w", err)
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to begin transaction for file deletion")
		return nil, err
	}
	defer handleTransactionCleanup(spanContext, transaction, &err)

	// Soft delete the file
	if err = filerepositoryUsecase.fileRepo.DeleteFile(spanContext, transaction, request.Id); err != nil {
		err := fmt.Errorf("failed to delete file: %w", err)
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to delete file %s", request.Id))
		return nil, err
	}

	// Automatically log file deletion for audit trail
	filerepositoryUsecase.logAccess(spanContext, transaction, request.Id, "file_deleted")

	return &filerepository.DeleteFileResponse{
		Message: "File marked as deleted successfully",
	}, nil
}

// UndeleteFile restores a soft-deleted file.
func (filerepositoryUsecase *FileRepositoryUseCase) UndeleteFile(ctx context.Context, request *filerepository.UndeleteFileRequest) (*filerepository.UndeleteFileResponse, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "FileRepositoryUseCase.UndeleteFile")
	defer finish()
	span.SetTag("file.id", request.Id)

	// Validate request
	if request.Id == "" {
		return nil, errors.New("file id is required")
	}

	// Begin transaction
	transaction, err := filerepositoryUsecase.database.BeginTx(spanContext, nil)
	if err != nil {
		err := fmt.Errorf("failed to begin transaction: %w", err)
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to begin transaction for undelete")
		return nil, err
	}
	defer handleTransactionCleanup(spanContext, transaction, &err)

	// Undelete the file
	metadata, err := filerepositoryUsecase.fileRepo.UndeleteFile(spanContext, transaction, request.Id)
	if err != nil {
		err := fmt.Errorf("failed to undelete file: %w", err)
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to undelete file %s", request.Id))
		return nil, err
	}

	// Automatically log file restoration for audit trail
	filerepositoryUsecase.logAccess(spanContext, transaction, request.Id, "file_restored")

	// Filter sensitive metadata based on user role
	filteredMetadata := filerepositoryUsecase.filterSensitiveMetadata(spanContext, metadata)

	return &filerepository.UndeleteFileResponse{
		Metadata: filteredMetadata,
		Message:  "File restored successfully",
	}, nil
}

// PurgeFile permanently deletes a file.
func (filerepositoryUsecase *FileRepositoryUseCase) PurgeFile(ctx context.Context, request *filerepository.PurgeFileRequest) (*filerepository.PurgeFileResponse, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "FileRepositoryUseCase.PurgeFile")
	defer finish()
	span.SetTag("file.id", request.Id)

	// Validate request
	if request.Id == "" {
		return nil, errors.New("file id is required")
	}

	// Begin transaction
	transaction, err := filerepositoryUsecase.database.BeginTx(spanContext, nil)
	if err != nil {
		err := fmt.Errorf("failed to begin transaction: %w", err)
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to begin transaction for file purge")
		return nil, err
	}
	defer handleTransactionCleanup(spanContext, transaction, &err)

	// First get the file metadata to know what to delete from S3
	metadata, err := filerepositoryUsecase.fileRepo.GetFileMetadata(spanContext, transaction, request.Id)
	if err != nil {
		err := fmt.Errorf("failed to get file metadata for purge: %w", err)
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to get file metadata for purge of file %s", request.Id))
		return nil, err
	}

	// Delete from cloud storage first using circuit breaker
	deleteObjectInput := &s3.DeleteObjectInput{
		Bucket: aws.String(metadata.BucketName),
		Key:    aws.String(metadata.StorageKey),
	}

	_, err = filerepositoryUsecase.s3CircuitBreaker.DeleteObject(spanContext, deleteObjectInput)
	if err != nil {
		err := fmt.Errorf("failed to delete object from S3: %w", err)
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeExternal, fmt.Sprintf("Failed to delete object from S3 for file %s", request.Id))
		return nil, err
	}

	// Automatically log file purge for audit trail (before deletion)
	filerepositoryUsecase.logAccess(spanContext, transaction, request.Id, "file_purged")

	// Then purge from database
	if err = filerepositoryUsecase.fileRepo.PurgeFile(spanContext, transaction, request.Id); err != nil {
		err := fmt.Errorf("failed to purge file from database: %w", err)
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to purge file %s from database", request.Id))
		return nil, err
	}

	// Transaction will be committed by handleTransactionCleanup since err is nil

	return &filerepository.PurgeFileResponse{
		Message: "File permanently deleted from both database and cloud storage",
	}, nil
}

// GetAccessLog retrieves access logs for files.
func (filerepositoryUsecase *FileRepositoryUseCase) GetAccessLog(ctx context.Context, request *filerepository.GetAccessLogRequest) (*filerepository.GetAccessLogResponse, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "FileRepositoryUseCase.GetAccessLog")
	defer finish()
	span.SetTag("file.id", request.FileId)

	// Validate request
	if request.FileId == "" {
		return nil, errors.New("file_id is required")
	}

	// Begin transaction
	transaction, err := filerepositoryUsecase.database.BeginTx(spanContext, nil)
	if err != nil {
		err := fmt.Errorf("failed to begin transaction: %w", err)
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to begin transaction for access log retrieval")
		return nil, err
	}
	defer handleTransactionCleanup(spanContext, transaction, &err)

	// Get access logs - now returns protobuf directly
	response, err := filerepositoryUsecase.fileRepo.GetAccessLog(spanContext, transaction, request)
	if err != nil {
		err := fmt.Errorf("failed to get access log: %w", err)
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to get access log for file %s", request.FileId))
		return nil, err
	}

	span.SetTag("access_log.entries_count", fmt.Sprintf("%d", len(response.Entries)))

	return response, nil
}

// RecordAccess records a new access log entry.
func (filerepositoryUsecase *FileRepositoryUseCase) RecordAccess(ctx context.Context, request *filerepository.RecordAccessRequest) (*filerepository.RecordAccessResponse, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "FileRepositoryUseCase.RecordAccess")
	defer finish()
	span.SetTag("file.id", request.FileId)
	span.SetTag("action", request.Action)

	// Validate request
	if request.FileId == "" {
		return nil, errors.New("file_id is required")
	}
	if request.Action == "" {
		return nil, errors.New("action is required")
	}

	// Security: Override user_id with authenticated user to prevent audit log manipulation
	// Users cannot create fake audit logs for other users
	currentUser := cmncontext.GetUsername(spanContext)
	if currentUser == "" {
		currentUser = repository.SystemOwnerID
	}
	request.UserId = currentUser

	// Automatically set IP address from context if not provided
	if request.IpAddress == "" {
		request.IpAddress = cmncontext.GetIPAddress(spanContext)
	}

	// Begin transaction
	transaction, err := filerepositoryUsecase.database.BeginTx(spanContext, nil)
	if err != nil {
		err := fmt.Errorf("failed to begin transaction: %w", err)
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to begin transaction for recording access")
		return nil, err
	}
	defer handleTransactionCleanup(spanContext, transaction, &err)

	// Record access
	entryID, err := filerepositoryUsecase.fileRepo.RecordAccess(spanContext, transaction, request)
	if err != nil {
		err := fmt.Errorf("failed to record access: %w", err)
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to record access for file %s", request.FileId))
		return nil, err
	}

	// If this is a download action, increment the download counter
	if strings.ToLower(request.Action) == "download" {
		if err = filerepositoryUsecase.fileRepo.IncrementDownloadCount(spanContext, transaction, request.FileId); err != nil {
			// Log error but don't fail the request
			fmt.Printf("Warning: failed to increment download count: %v\n", err)
		}
	}

	return &filerepository.RecordAccessResponse{
		EntryId: entryID,
		Message: "Access recorded successfully",
	}, nil
}
