package test

import (
	"context"
	"fmt"
	"net/http"
	"testing"
	"time"

	// Ignore the import errors - it is not a issue you can still run the tests
	"proto/hero/assets/v2"
	assetsconnect "proto/hero/assets/v2/assetsconnect"
	featureflags "proto/hero/featureflags/v1"
	featureflagsconnect "proto/hero/featureflags/v1/featureflagsconnect"

	"connectrpc.com/connect"
)

const assetsServiceURL = "http://localhost:9086" // Assets service URL

// Test constants
const testOrgID = int32(1)

func TestE2E_FeatureFlagsService(t *testing.T) {
	t.Logf("%s", ColorTest("🚀 Starting Feature Flags Service E2E Tests"))

	// Create HTTP clients with auth header
	httpClient := http.DefaultClient
	AddAuthHeader(httpClient)

	// Create service clients
	featureFlagsClient := featureflagsconnect.NewFeatureFlagsServiceClient(httpClient, ServiceBaseURL)
	assetClient := assetsconnect.NewAssetRegistryServiceClient(httpClient, assetsServiceURL)

	// Use a 30-second timeout for all calls
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Track created assets for cleanup
	var createdAssetIDs []string
	defer func() {
		t.Logf("%s", ColorInfo("🧹 Cleaning up test data..."))
		cleanupAssets(t, createdAssetIDs, assetClient, ctx)
		cleanupFeatureFlags(t, featureFlagsClient, ctx, testOrgID)
	}()

	// ========================================================================
	// Step 1: Create test assets
	// ========================================================================
	t.Logf("%s", ColorSubtest("📝 Step 1: Creating test assets"))

	// Create first test asset
	asset1Request := &assets.CreateAssetRequest{
		Asset: &assets.Asset{
			CognitoJwtSub:      "test-sub-1",
			Name:               "Test Asset 1 for Feature Flags",
			Type:               assets.AssetType_ASSET_TYPE_TEST,
			Status:             assets.AssetStatus_ASSET_STATUS_AVAILABLE,
			OrgId:              testOrgID,
			ContactNo:          "+14234567890",
			AdditionalInfoJson: "{}",
		},
	}
	asset1Response, err := assetClient.CreateAsset(ctx, connect.NewRequest(asset1Request))
	if err != nil {
		t.Fatalf("%s", ColorError(fmt.Sprintf("❌ Failed to create asset 1: %v", err)))
	}
	asset1ID := asset1Response.Msg.Asset.Id
	createdAssetIDs = append(createdAssetIDs, asset1ID)
	t.Logf("%s", ColorSuccess(fmt.Sprintf("✅ Created asset 1 with ID: %s", asset1ID)))

	// Create second test asset
	asset2Request := &assets.CreateAssetRequest{
		Asset: &assets.Asset{
			CognitoJwtSub:      "test-sub-2",
			Name:               "Test Asset 2 for Feature Flags",
			Type:               assets.AssetType_ASSET_TYPE_TEST,
			Status:             assets.AssetStatus_ASSET_STATUS_AVAILABLE,
			OrgId:              testOrgID,
			ContactNo:          "+14234567891",
			AdditionalInfoJson: "{}",
		},
	}
	asset2Response, err := assetClient.CreateAsset(ctx, connect.NewRequest(asset2Request))
	if err != nil {
		t.Fatalf("%s", ColorError(fmt.Sprintf("❌ Failed to create asset 2: %v", err)))
	}
	asset2ID := asset2Response.Msg.Asset.Id
	createdAssetIDs = append(createdAssetIDs, asset2ID)
	t.Logf("%s", ColorSuccess(fmt.Sprintf("✅ Created asset 2 with ID: %s", asset2ID)))

	// ========================================================================
	// Step 2: Test features without any flags set (should all be disabled)
	// ========================================================================
	t.Logf("%s", ColorSubtest("📝 Step 2: Testing features without any flags set"))

	// Test that all features are disabled by default for asset 1
	for _, feature := range []featureflags.Feature{
		featureflags.Feature_FEATURE_EXPERIMENTAL_CAMERA,
		featureflags.Feature_FEATURE_EXPERIMENTAL_PUSH_TO_TALK,
		featureflags.Feature_FEATURE_EXPERIMENTAL_DEMO,
	} {
		isEnabledRequest := &featureflags.IsEnabledRequest{
			OrgId:   testOrgID,
			Feature: feature,
			AssetId: asset1ID,
		}
		isEnabledResponse, err := featureFlagsClient.IsEnabled(ctx, connect.NewRequest(isEnabledRequest))
		if err != nil {
			t.Fatalf("%s", ColorError(fmt.Sprintf("❌ Failed to check feature %v: %v", feature, err)))
		}
		// Debug: Log the actual response
		t.Logf("DEBUG: Feature %v response: enabled=%v, full message=%+v", feature, isEnabledResponse.Msg.Enabled, isEnabledResponse.Msg)
		if isEnabledResponse.Msg.Enabled {
			t.Fatalf("%s", ColorError(fmt.Sprintf("❌ Feature %v should be disabled by default", feature)))
		}
		t.Logf("%s", ColorSuccess(fmt.Sprintf("✅ Feature %v is disabled by default for asset 1", feature)))
	}

	// Get all enabled features for asset 1 (should be empty)
	getEnabledRequest := &featureflags.GetEnabledFeaturesRequest{
		OrgId:   testOrgID,
		AssetId: asset1ID,
	}
	getEnabledResponse, err := featureFlagsClient.GetEnabledFeatures(ctx, connect.NewRequest(getEnabledRequest))
	if err != nil {
		t.Fatalf("%s", ColorError(fmt.Sprintf("❌ Failed to get enabled features: %v", err)))
	}
	if len(getEnabledResponse.Msg.EnabledFeatures) != 0 {
		t.Fatalf("%s", ColorError(fmt.Sprintf("❌ Expected no enabled features, got %d", len(getEnabledResponse.Msg.EnabledFeatures))))
	}
	t.Logf("%s", ColorSuccess("✅ No features enabled by default"))

	// ========================================================================
	// Step 3: Enable org-wide feature flags
	// ========================================================================
	t.Logf("%s", ColorSubtest("📝 Step 3: Enabling org-wide feature flags"))

	// Enable EXPERIMENTAL_CAMERA org-wide
	setFeatureRequest := &featureflags.SetFeatureTargetRequest{
		OrgId:   testOrgID,
		Feature: featureflags.Feature_FEATURE_EXPERIMENTAL_CAMERA,
		AssetId: "", // Empty means org-wide
		Enabled: true,
	}
	setFeatureResponse, err := featureFlagsClient.SetFeatureTarget(ctx, connect.NewRequest(setFeatureRequest))
	if err != nil {
		t.Fatalf("%s", ColorError(fmt.Sprintf("❌ Failed to enable org-wide feature: %v", err)))
	}
	if !setFeatureResponse.Msg.Success {
		t.Fatalf("%s", ColorError("❌ Failed to enable org-wide feature"))
	}
	t.Logf("%s", ColorSuccess("✅ Enabled EXPERIMENTAL_CAMERA org-wide"))

	// Verify both assets have the feature enabled
	for _, assetID := range []string{asset1ID, asset2ID} {
		isEnabledRequest := &featureflags.IsEnabledRequest{
			OrgId:   testOrgID,
			Feature: featureflags.Feature_FEATURE_EXPERIMENTAL_CAMERA,
			AssetId: assetID,
		}
		isEnabledResponse, err := featureFlagsClient.IsEnabled(ctx, connect.NewRequest(isEnabledRequest))
		if err != nil {
			t.Fatalf("%s", ColorError(fmt.Sprintf("❌ Failed to check feature: %v", err)))
		}
		if !isEnabledResponse.Msg.Enabled {
			t.Fatalf("%s", ColorError(fmt.Sprintf("❌ EXPERIMENTAL_CAMERA should be enabled org-wide for asset %s", assetID)))
		}
		t.Logf("%s", ColorSuccess(fmt.Sprintf("✅ EXPERIMENTAL_CAMERA is enabled for asset %s", assetID)))
	}

	// ========================================================================
	// Step 4: Enable asset-specific features
	// ========================================================================
	t.Logf("%s", ColorSubtest("📝 Step 4: Enabling asset-specific features"))

	// Enable PUSH_TO_TALK for asset 1 only
	setAssetFeatureRequest := &featureflags.SetFeatureTargetRequest{
		OrgId:   testOrgID,
		Feature: featureflags.Feature_FEATURE_EXPERIMENTAL_PUSH_TO_TALK,
		AssetId: asset1ID,
		Enabled: true,
	}
	setAssetFeatureResponse, err := featureFlagsClient.SetFeatureTarget(ctx, connect.NewRequest(setAssetFeatureRequest))
	if err != nil {
		t.Fatalf("%s", ColorError(fmt.Sprintf("❌ Failed to enable asset-specific feature: %v", err)))
	}
	if !setAssetFeatureResponse.Msg.Success {
		t.Fatalf("%s", ColorError("❌ Failed to enable asset-specific feature"))
	}
	t.Logf("%s", ColorSuccess("✅ Enabled PUSH_TO_TALK for asset 1"))

	// Verify asset 1 has PUSH_TO_TALK enabled
	isEnabledRequest := &featureflags.IsEnabledRequest{
		OrgId:   testOrgID,
		Feature: featureflags.Feature_FEATURE_EXPERIMENTAL_PUSH_TO_TALK,
		AssetId: asset1ID,
	}
	isEnabledResponse, err := featureFlagsClient.IsEnabled(ctx, connect.NewRequest(isEnabledRequest))
	if err != nil {
		t.Fatalf("%s", ColorError(fmt.Sprintf("❌ Failed to check feature: %v", err)))
	}
	if !isEnabledResponse.Msg.Enabled {
		t.Fatalf("%s", ColorError("❌ PUSH_TO_TALK should be enabled for asset 1"))
	}
	t.Logf("%s", ColorSuccess("✅ PUSH_TO_TALK is enabled for asset 1"))

	// Verify asset 2 does NOT have PUSH_TO_TALK enabled
	isEnabledRequest2 := &featureflags.IsEnabledRequest{
		OrgId:   testOrgID,
		Feature: featureflags.Feature_FEATURE_EXPERIMENTAL_PUSH_TO_TALK,
		AssetId: asset2ID,
	}
	isEnabledResponse2, err := featureFlagsClient.IsEnabled(ctx, connect.NewRequest(isEnabledRequest2))
	if err != nil {
		t.Fatalf("%s", ColorError(fmt.Sprintf("❌ Failed to check feature: %v", err)))
	}
	if isEnabledResponse2.Msg.Enabled {
		t.Fatalf("%s", ColorError("❌ PUSH_TO_TALK should NOT be enabled for asset 2"))
	}
	t.Logf("%s", ColorSuccess("✅ PUSH_TO_TALK is correctly disabled for asset 2"))

	// ========================================================================
	// Step 5: Test bulk operations
	// ========================================================================
	t.Logf("%s", ColorSubtest("📝 Step 5: Testing bulk operations"))

	// Enable multiple features for asset 2
	setMultipleRequest := &featureflags.SetMultipleFeaturesRequest{
		OrgId:   testOrgID,
		AssetId: asset2ID,
		EnableFeatures: []featureflags.Feature{
			featureflags.Feature_FEATURE_EXPERIMENTAL_PUSH_TO_TALK,
			featureflags.Feature_FEATURE_EXPERIMENTAL_DEMO,
		},
		DisableFeatures: []featureflags.Feature{}, // None to disable
	}
	setMultipleResponse, err := featureFlagsClient.SetMultipleFeatures(ctx, connect.NewRequest(setMultipleRequest))
	if err != nil {
		t.Fatalf("%s", ColorError(fmt.Sprintf("❌ Failed to set multiple features: %v", err)))
	}
	if !setMultipleResponse.Msg.Success {
		t.Fatalf("%s", ColorError("❌ Failed to set multiple features"))
	}
	t.Logf("%s", ColorSuccess("✅ Enabled multiple features for asset 2"))

	// Get all enabled features for asset 2
	getEnabledRequest2 := &featureflags.GetEnabledFeaturesRequest{
		OrgId:   testOrgID,
		AssetId: asset2ID,
	}
	getEnabledResponse2, err := featureFlagsClient.GetEnabledFeatures(ctx, connect.NewRequest(getEnabledRequest2))
	if err != nil {
		t.Fatalf("%s", ColorError(fmt.Sprintf("❌ Failed to get enabled features: %v", err)))
	}

	// Asset 2 should have: EXPERIMENTAL_CAMERA (org-wide) + 2 asset-specific features
	expectedFeatureCount := 3
	if len(getEnabledResponse2.Msg.EnabledFeatures) != expectedFeatureCount {
		t.Fatalf("%s", ColorError(fmt.Sprintf("❌ Expected %d enabled features for asset 2, got %d",
			expectedFeatureCount, len(getEnabledResponse2.Msg.EnabledFeatures))))
	}
	t.Logf("%s", ColorSuccess(fmt.Sprintf("✅ Asset 2 has %d features enabled", expectedFeatureCount)))

	// ========================================================================
	// Step 6: Test asset-specific override of org-wide setting
	// ========================================================================
	t.Logf("%s", ColorSubtest("📝 Step 6: Testing asset-specific override"))

	// Disable EXPERIMENTAL_CAMERA for asset 1 (overriding org-wide setting)
	overrideRequest := &featureflags.SetFeatureTargetRequest{
		OrgId:   testOrgID,
		Feature: featureflags.Feature_FEATURE_EXPERIMENTAL_CAMERA,
		AssetId: asset1ID,
		Enabled: false,
	}
	overrideResponse, err := featureFlagsClient.SetFeatureTarget(ctx, connect.NewRequest(overrideRequest))
	if err != nil {
		t.Fatalf("%s", ColorError(fmt.Sprintf("❌ Failed to override feature: %v", err)))
	}
	if !overrideResponse.Msg.Success {
		t.Fatalf("%s", ColorError("❌ Failed to override feature"))
	}
	t.Logf("%s", ColorSuccess("✅ Disabled EXPERIMENTAL_CAMERA for asset 1 (overriding org-wide)"))

	// Verify asset 1 has EXPERIMENTAL_CAMERA disabled
	checkOverrideRequest := &featureflags.IsEnabledRequest{
		OrgId:   testOrgID,
		Feature: featureflags.Feature_FEATURE_EXPERIMENTAL_CAMERA,
		AssetId: asset1ID,
	}
	checkOverrideResponse, err := featureFlagsClient.IsEnabled(ctx, connect.NewRequest(checkOverrideRequest))
	if err != nil {
		t.Fatalf("%s", ColorError(fmt.Sprintf("❌ Failed to check feature: %v", err)))
	}
	if checkOverrideResponse.Msg.Enabled {
		t.Fatalf("%s", ColorError("❌ EXPERIMENTAL_CAMERA should be disabled for asset 1"))
	}
	t.Logf("%s", ColorSuccess("✅ EXPERIMENTAL_CAMERA is correctly disabled for asset 1"))

	// Verify asset 2 still has EXPERIMENTAL_CAMERA enabled (from org-wide setting)
	checkOrgWideRequest := &featureflags.IsEnabledRequest{
		OrgId:   testOrgID,
		Feature: featureflags.Feature_FEATURE_EXPERIMENTAL_CAMERA,
		AssetId: asset2ID,
	}
	checkOrgWideResponse, err := featureFlagsClient.IsEnabled(ctx, connect.NewRequest(checkOrgWideRequest))
	if err != nil {
		t.Fatalf("%s", ColorError(fmt.Sprintf("❌ Failed to check feature: %v", err)))
	}
	if !checkOrgWideResponse.Msg.Enabled {
		t.Fatalf("%s", ColorError("❌ EXPERIMENTAL_CAMERA should still be enabled for asset 2"))
	}
	t.Logf("%s", ColorSuccess("✅ EXPERIMENTAL_CAMERA is still enabled for asset 2 (org-wide)"))

	// ========================================================================
	// Step 7: Test disabling features with bulk operation
	// ========================================================================
	t.Logf("%s", ColorSubtest("📝 Step 7: Testing bulk disable operations"))

	// Disable some features and enable others for asset 1
	bulkUpdateRequest := &featureflags.SetMultipleFeaturesRequest{
		OrgId:   testOrgID,
		AssetId: asset1ID,
		EnableFeatures: []featureflags.Feature{
			featureflags.Feature_FEATURE_EXPERIMENTAL_DEMO,
		},
		DisableFeatures: []featureflags.Feature{
			featureflags.Feature_FEATURE_EXPERIMENTAL_PUSH_TO_TALK, // Was enabled before
		},
	}
	bulkUpdateResponse, err := featureFlagsClient.SetMultipleFeatures(ctx, connect.NewRequest(bulkUpdateRequest))
	if err != nil {
		t.Fatalf("%s", ColorError(fmt.Sprintf("❌ Failed to bulk update features: %v", err)))
	}
	if !bulkUpdateResponse.Msg.Success {
		t.Fatalf("%s", ColorError("❌ Failed to bulk update features"))
	}
	t.Logf("%s", ColorSuccess("✅ Bulk updated features for asset 1"))

	// Verify PUSH_TO_TALK is now disabled for asset 1
	checkDisabledRequest := &featureflags.IsEnabledRequest{
		OrgId:   testOrgID,
		Feature: featureflags.Feature_FEATURE_EXPERIMENTAL_PUSH_TO_TALK,
		AssetId: asset1ID,
	}
	checkDisabledResponse, err := featureFlagsClient.IsEnabled(ctx, connect.NewRequest(checkDisabledRequest))
	if err != nil {
		t.Fatalf("%s", ColorError(fmt.Sprintf("❌ Failed to check feature: %v", err)))
	}
	if checkDisabledResponse.Msg.Enabled {
		t.Fatalf("%s", ColorError("❌ PUSH_TO_TALK should be disabled for asset 1"))
	}
	t.Logf("%s", ColorSuccess("✅ PUSH_TO_TALK is correctly disabled for asset 1"))

	// Get final enabled features for asset 1
	getFinalRequest := &featureflags.GetEnabledFeaturesRequest{
		OrgId:   testOrgID,
		AssetId: asset1ID,
	}
	getFinalResponse, err := featureFlagsClient.GetEnabledFeatures(ctx, connect.NewRequest(getFinalRequest))
	if err != nil {
		t.Fatalf("%s", ColorError(fmt.Sprintf("❌ Failed to get enabled features: %v", err)))
	}

	// Asset 1 should have: EXPERIMENTAL_DEMO only (EXPERIMENTAL_CAMERA is disabled, PUSH_TO_TALK is disabled)
	expectedFinalCount := 1
	if len(getFinalResponse.Msg.EnabledFeatures) != expectedFinalCount {
		t.Fatalf("%s", ColorError(fmt.Sprintf("❌ Expected %d enabled features for asset 1, got %d",
			expectedFinalCount, len(getFinalResponse.Msg.EnabledFeatures))))
	}
	t.Logf("%s", ColorSuccess(fmt.Sprintf("✅ Asset 1 has %d features enabled after bulk update", expectedFinalCount)))

	t.Logf("%s", ColorSuccess("🎉 All Feature Flags E2E tests passed!"))
}

// cleanupAssets deletes test assets
func cleanupAssets(t *testing.T, assetIDs []string, assetClient assetsconnect.AssetRegistryServiceClient, ctx context.Context) {
	for _, assetID := range assetIDs {
		deleteRequest := &assets.DeleteAssetRequest{Id: assetID}
		_, err := assetClient.DeleteAsset(ctx, connect.NewRequest(deleteRequest))
		if err != nil {
			t.Logf("%s", ColorWarning(fmt.Sprintf("cleanup: failed to delete asset %s: %v", assetID, err)))
		}
	}
}

// cleanupFeatureFlags removes org-wide feature flag settings for the test organization
func cleanupFeatureFlags(t *testing.T, featureFlagsClient featureflagsconnect.FeatureFlagsServiceClient, ctx context.Context, orgID int32) {
	// Get all possible features to clean up
	allFeatures := []featureflags.Feature{
		featureflags.Feature_FEATURE_EXPERIMENTAL_CAMERA,
		featureflags.Feature_FEATURE_EXPERIMENTAL_PUSH_TO_TALK,
		featureflags.Feature_FEATURE_EXPERIMENTAL_DEMO,
	}

	// Disable all features org-wide to reset to default state
	// Note: This doesn't delete the records but sets them to disabled
	// Asset-specific settings are cleaned up via ON DELETE CASCADE when assets are deleted
	setMultipleRequest := &featureflags.SetMultipleFeaturesRequest{
		OrgId:           orgID,
		AssetId:         "", // Empty means org-wide
		EnableFeatures:  []featureflags.Feature{},
		DisableFeatures: allFeatures,
	}

	_, err := featureFlagsClient.SetMultipleFeatures(ctx, connect.NewRequest(setMultipleRequest))
	if err != nil {
		t.Logf("%s", ColorWarning(fmt.Sprintf("cleanup: failed to disable org-wide features: %v", err)))
	} else {
		t.Logf("%s", ColorInfo("✅ Reset org-wide feature flags to disabled"))
	}
}
