package repository

import (
	"context"
	"database/sql"
	"fmt"
	"strings"

	"common/database"
	"common/herosentry"

	featureflags "proto/hero/featureflags/v1"

	"github.com/google/uuid"
	_ "github.com/lib/pq" // PostgreSQL driver
)

// PostgresFeatureFlagsRepository implements FeatureFlagsRepository using PostgreSQL
type PostgresFeatureFlagsRepository struct {
	database *sql.DB
}

// NewPostgresFeatureFlagsRepository creates a new PostgreSQL feature flags repository
func NewPostgresFeatureFlagsRepository(database *sql.DB) *PostgresFeatureFlagsRepository {
	return &PostgresFeatureFlagsRepository{database: database}
}

// nullIfEmpty returns nil for empty strings so ExecContext writes SQL NULL
func nullIfEmpty(inputString string) interface{} {
	if strings.TrimSpace(inputString) == "" {
		return nil
	}
	return inputString
}

// SetFeatureTarget enables or disables a feature for a specific target
func (repository *PostgresFeatureFlagsRepository) SetFeatureTarget(context context.Context, transaction *sql.Tx, organizationID int32, feature featureflags.Feature, assetID string, enabled bool) error {
	spanContext, span, finish := herosentry.StartSpan(context, "PostgresFeatureFlagsRepository.SetFeatureTarget")
	defer finish()
	span.SetTag("org.id", fmt.Sprintf("%d", organizationID))
	span.SetTag("feature", feature.String())
	span.SetTag("enabled", fmt.Sprintf("%t", enabled))
	if assetID != "" {
		span.SetTag("asset.id", assetID)
	}

	return database.WithSessionErr(repository.database, spanContext, transaction, func(sessionTransaction *sql.Tx) error {
		// We cannot use ON CONFLICT with partial unique indexes directly.
		// PostgreSQL's ON CONFLICT clause requires a single constraint that matches exactly,
		// but we have two partial unique indexes:
		// 1. unique_org_wide_feature: (org_id, feature) WHERE asset_id IS NULL
		// 2. unique_asset_feature: (org_id, feature, asset_id) WHERE asset_id IS NOT NULL
		//
		// Instead, we use UPDATE-then-INSERT pattern:
		// First try to update existing record, if no rows affected then insert new one.
		// This approach works correctly with our partial unique indexes.

		// First, try to update existing record
		var updateQuery string
		var args []interface{}

		if assetID == "" {
			// For org-wide settings (NULL asset_id)
			updateQuery = `
				UPDATE feature_flags 
				SET enabled = $1, updated_at = CURRENT_TIMESTAMP
				WHERE org_id = $2 AND feature = $3 AND asset_id IS NULL
			`
			args = []interface{}{enabled, organizationID, int32(feature)}
		} else {
			// For asset-specific settings
			updateQuery = `
				UPDATE feature_flags 
				SET enabled = $1, updated_at = CURRENT_TIMESTAMP
				WHERE org_id = $2 AND feature = $3 AND asset_id = $4
			`
			args = []interface{}{enabled, organizationID, int32(feature), assetID}
		}

		result, err := sessionTransaction.ExecContext(context, updateQuery, args...)
		if err != nil {
			return err
		}

		// Check if any rows were updated
		rowsAffected, err := result.RowsAffected()
		if err != nil {
			return err
		}

		// If no rows were updated, insert a new record
		if rowsAffected == 0 {
			insertQuery := `
				INSERT INTO feature_flags (id, org_id, feature, asset_id, enabled)
				VALUES ($1, $2, $3, $4, $5)
			`

			featureFlagID := uuid.New().String()
			_, err = sessionTransaction.ExecContext(context, insertQuery,
				featureFlagID,
				organizationID,
				int32(feature),
				nullIfEmpty(assetID),
				enabled,
			)
			if err != nil {
				return err
			}
		}

		return nil
	})
}

// SetMultipleFeatures enables/disables multiple features for a target in a single operation
func (repository *PostgresFeatureFlagsRepository) SetMultipleFeatures(context context.Context, transaction *sql.Tx, organizationID int32, assetID string, enableFeatures []featureflags.Feature, disableFeatures []featureflags.Feature) error {
	spanContext, span, finish := herosentry.StartSpan(context, "PostgresFeatureFlagsRepository.SetMultipleFeatures")
	defer finish()
	span.SetTag("org.id", fmt.Sprintf("%d", organizationID))
	if assetID != "" {
		span.SetTag("asset.id", assetID)
	}
	span.SetTag("enable_features.count", fmt.Sprintf("%d", len(enableFeatures)))
	span.SetTag("disable_features.count", fmt.Sprintf("%d", len(disableFeatures)))

	return database.WithSessionErr(repository.database, spanContext, transaction, func(sessionTransaction *sql.Tx) error {
		// Process features to enable
		for _, feature := range enableFeatures {
			if err := repository.setFeatureWithTransaction(spanContext, sessionTransaction, organizationID, feature, assetID, true); err != nil {
				return fmt.Errorf("failed to enable feature %v: %w", feature, err)
			}
		}

		// Process features to disable
		for _, feature := range disableFeatures {
			if err := repository.setFeatureWithTransaction(spanContext, sessionTransaction, organizationID, feature, assetID, false); err != nil {
				return fmt.Errorf("failed to disable feature %v: %w", feature, err)
			}
		}

		return nil
	})
}

// setFeatureWithTransaction is a helper method to set a feature within an existing transaction
func (repository *PostgresFeatureFlagsRepository) setFeatureWithTransaction(context context.Context, transaction *sql.Tx, organizationID int32, feature featureflags.Feature, assetID string, enabled bool) error {
	// Same UPDATE-then-INSERT pattern as SetFeatureTarget
	// See SetFeatureTarget for detailed explanation about why we can't use ON CONFLICT

	// First, try to update existing record
	var updateQuery string
	var args []interface{}

	if assetID == "" {
		// For org-wide settings (NULL asset_id)
		updateQuery = `
			UPDATE feature_flags 
			SET enabled = $1, updated_at = CURRENT_TIMESTAMP
			WHERE org_id = $2 AND feature = $3 AND asset_id IS NULL
		`
		args = []interface{}{enabled, organizationID, int32(feature)}
	} else {
		// For asset-specific settings
		updateQuery = `
			UPDATE feature_flags 
			SET enabled = $1, updated_at = CURRENT_TIMESTAMP
			WHERE org_id = $2 AND feature = $3 AND asset_id = $4
		`
		args = []interface{}{enabled, organizationID, int32(feature), assetID}
	}

	result, err := transaction.ExecContext(context, updateQuery, args...)
	if err != nil {
		return err
	}

	// Check if any rows were updated
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}

	// If no rows were updated, insert a new record
	if rowsAffected == 0 {
		insertQuery := `
			INSERT INTO feature_flags (id, org_id, feature, asset_id, enabled)
			VALUES ($1, $2, $3, $4, $5)
		`

		featureFlagID := uuid.New().String()
		_, err = transaction.ExecContext(context, insertQuery,
			featureFlagID,
			organizationID,
			int32(feature),
			nullIfEmpty(assetID),
			enabled,
		)
		if err != nil {
			return err
		}
	}

	return nil
}

// IsEnabled checks if a feature is enabled for a specific target
func (repository *PostgresFeatureFlagsRepository) IsEnabled(context context.Context, transaction *sql.Tx, organizationID int32, feature featureflags.Feature, assetID string) (bool, string, error) {
	spanContext, span, finish := herosentry.StartSpan(context, "PostgresFeatureFlagsRepository.IsEnabled")
	defer finish()
	span.SetTag("org.id", fmt.Sprintf("%d", organizationID))
	span.SetTag("feature", feature.String())
	if assetID != "" {
		span.SetTag("asset.id", assetID)
	}

	type result struct {
		enabled bool
		reason  string
	}
	res, err := database.WithSession(repository.database, spanContext, transaction, func(sessionTransaction *sql.Tx) (result, error) {
		// Check asset-specific setting first, then fall back to org-wide setting
		query := `
			SELECT enabled, 
				CASE 
					WHEN asset_id IS NOT NULL THEN 'asset_setting'
					ELSE 'org_setting'
				END as reason
			FROM feature_flags
			WHERE org_id = $1 AND feature = $2 
			AND (asset_id = $3 OR asset_id IS NULL)
			ORDER BY 
				CASE WHEN asset_id IS NOT NULL THEN 0 ELSE 1 END
			LIMIT 1
		`

		var enabled bool
		var reason string
		err := sessionTransaction.QueryRowContext(spanContext, query,
			organizationID,
			int32(feature),
			nullIfEmpty(assetID),
		).Scan(&enabled, &reason)

		if err == sql.ErrNoRows {
			// No setting found, default to disabled
			return result{enabled: false, reason: "default"}, nil
		}

		if err != nil {
			return result{}, err
		}

		return result{enabled: enabled, reason: reason}, nil
	})

	if err != nil {
		return false, "", err
	}

	return res.enabled, res.reason, nil
}

// GetEnabledFeatures returns all enabled features for a target
func (repository *PostgresFeatureFlagsRepository) GetEnabledFeatures(context context.Context, transaction *sql.Tx, organizationID int32, assetID string) ([]featureflags.Feature, error) {
	spanContext, span, finish := herosentry.StartSpan(context, "PostgresFeatureFlagsRepository.GetEnabledFeatures")
	defer finish()
	span.SetTag("org.id", fmt.Sprintf("%d", organizationID))
	if assetID != "" {
		span.SetTag("asset.id", assetID)
	}

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTransaction *sql.Tx) ([]featureflags.Feature, error) {
		// Get all enabled features for the asset and org-wide settings
		// Asset-specific settings override org-wide settings
		query := `
			WITH ranked_features AS (
				SELECT 
					feature,
					enabled,
					ROW_NUMBER() OVER (
						PARTITION BY feature 
						ORDER BY CASE WHEN asset_id IS NOT NULL THEN 0 ELSE 1 END
					) as rank
				FROM feature_flags
				WHERE org_id = $1 
				AND (asset_id = $2 OR asset_id IS NULL)
			)
			SELECT feature FROM ranked_features
			WHERE rank = 1 AND enabled = true
		`

		var enabledFeatures []featureflags.Feature

		rows, err := sessionTransaction.QueryContext(spanContext, query,
			organizationID,
			nullIfEmpty(assetID),
		)
		if err != nil {
			return nil, err
		}
		defer rows.Close()

		for rows.Next() {
			var featureValue int32
			if err := rows.Scan(&featureValue); err != nil {
				return nil, err
			}
			enabledFeatures = append(enabledFeatures, featureflags.Feature(featureValue))
		}

		return enabledFeatures, rows.Err()
	})
}

// DeleteFeatureTarget removes a feature setting for a specific target
func (repository *PostgresFeatureFlagsRepository) DeleteFeatureTarget(context context.Context, transaction *sql.Tx, organizationID int32, feature featureflags.Feature, assetID string) error {
	spanContext, span, finish := herosentry.StartSpan(context, "PostgresFeatureFlagsRepository.DeleteFeatureTarget")
	defer finish()
	span.SetTag("org.id", fmt.Sprintf("%d", organizationID))
	span.SetTag("feature", feature.String())
	if assetID != "" {
		span.SetTag("asset.id", assetID)
	}

	return database.WithSessionErr(repository.database, spanContext, transaction, func(sessionTransaction *sql.Tx) error {
		query := `
			DELETE FROM feature_flags
			WHERE org_id = $1 AND feature = $2 AND asset_id = $3
		`

		_, err := sessionTransaction.ExecContext(spanContext, query,
			organizationID,
			int32(feature),
			nullIfEmpty(assetID),
		)

		return err
	})
}
