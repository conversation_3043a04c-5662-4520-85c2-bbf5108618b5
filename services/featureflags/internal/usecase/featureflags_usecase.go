package usecase

import (
	"context"
	"database/sql"
	"fmt"

	"common/herosentry"
	featureflags "proto/hero/featureflags/v1"

	featureFlagsRepository "featureflags/internal/data"
)

// FeatureFlagsUseCase groups business-level operations for feature flags and
// delegates data persistence to repository implementations.
type FeatureFlagsUseCase struct {
	databaseConnection     *sql.DB
	featureFlagsRepository featureFlagsRepository.FeatureFlagsRepository
}

// NewFeatureFlagsUseCase constructs a fully-initialized FeatureFlagsUseCase instance.
func NewFeatureFlagsUseCase(
	databaseConnection *sql.DB,
	featureFlagsRepository featureFlagsRepository.FeatureFlagsRepository,
) (*FeatureFlagsUseCase, error) {
	if databaseConnection == nil {
		err := fmt.Errorf("database connection is nil")
		herosentry.CaptureException(context.Background(), err, herosentry.ErrorTypeInternal)
		return nil, err
	}
	if featureFlagsRepository == nil {
		err := fmt.Errorf("feature flags repository must not be nil")
		herosentry.CaptureException(context.Background(), err, herosentry.ErrorTypeInternal)
		return nil, err
	}

	return &FeatureFlagsUseCase{
		databaseConnection:     databaseConnection,
		featureFlagsRepository: featureFlagsRepository,
	}, nil
}

// executeInTransaction wraps a series of repository calls in a SQL transaction, ensuring
// commit on success and rollback on error or panic.
func (useCase *FeatureFlagsUseCase) executeInTransaction(context context.Context, transactionalWork func(transaction *sql.Tx) error) error {
	transaction, transactionError := useCase.databaseConnection.BeginTx(context, nil)
	if transactionError != nil {
		err := fmt.Errorf("failed to begin transaction: %w", transactionError)
		herosentry.CaptureException(context, err, herosentry.ErrorTypeDatabase)
		return err
	}

	defer func() {
		if recoverError := recover(); recoverError != nil {
			_ = transaction.Rollback()
			panic(recoverError)
		}
	}()

	if err := transactionalWork(transaction); err != nil {
		_ = transaction.Rollback()
		return err
	}

	if commitError := transaction.Commit(); commitError != nil {
		err := fmt.Errorf("failed to commit transaction: %w", commitError)
		herosentry.CaptureException(context, err, herosentry.ErrorTypeDatabase)
		return err
	}

	return nil
}

// IsEnabled checks if a feature is enabled for a specific target
func (useCase *FeatureFlagsUseCase) IsEnabled(context context.Context, request *featureflags.IsEnabledRequest) (*featureflags.IsEnabledResponse, error) {
	spanContext, span, finish := herosentry.StartSpan(context, "FeatureFlagsUseCase.IsEnabled")
	defer finish()
	span.SetTag("org.id", fmt.Sprintf("%d", request.OrgId))
	span.SetTag("feature", request.Feature.String())
	if request.AssetId != "" {
		span.SetTag("asset.id", request.AssetId)
	}

	if request.OrgId == 0 {
		err := fmt.Errorf("organization ID is required")
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation)
		return nil, err
	}
	if request.Feature == featureflags.Feature_FEATURE_UNSPECIFIED {
		err := fmt.Errorf("feature is required")
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation)
		return nil, err
	}

	enabled, reason, err := useCase.featureFlagsRepository.IsEnabled(
		spanContext,
		nil, // No transaction needed for read operation
		request.OrgId,
		request.Feature,
		request.AssetId,
	)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to check feature status")
		return nil, fmt.Errorf("failed to check feature status: %w", err)
	}

	return &featureflags.IsEnabledResponse{
		Enabled:   enabled,
		Evaluated: true,
		Reason:    reason,
	}, nil
}

// SetFeatureTarget enables or disables a feature for a specific target
func (useCase *FeatureFlagsUseCase) SetFeatureTarget(context context.Context, request *featureflags.SetFeatureTargetRequest) (*featureflags.SetFeatureTargetResponse, error) {
	spanContext, span, finish := herosentry.StartSpan(context, "FeatureFlagsUseCase.SetFeatureTarget")
	defer finish()
	span.SetTag("org.id", fmt.Sprintf("%d", request.OrgId))
	span.SetTag("feature", request.Feature.String())
	span.SetTag("enabled", fmt.Sprintf("%t", request.Enabled))
	if request.AssetId != "" {
		span.SetTag("asset.id", request.AssetId)
	}

	if request.OrgId == 0 {
		err := fmt.Errorf("organization ID is required")
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation)
		return nil, err
	}
	if request.Feature == featureflags.Feature_FEATURE_UNSPECIFIED {
		err := fmt.Errorf("feature is required")
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation)
		return nil, err
	}

	err := useCase.executeInTransaction(spanContext, func(transaction *sql.Tx) error {
		return useCase.featureFlagsRepository.SetFeatureTarget(
			spanContext,
			transaction,
			request.OrgId,
			request.Feature,
			request.AssetId,
			request.Enabled,
		)
	})
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to set feature target")
		return nil, fmt.Errorf("failed to set feature target: %w", err)
	}

	return &featureflags.SetFeatureTargetResponse{
		Success: true,
	}, nil
}

// SetMultipleFeatures enables/disables multiple features for a target in a single operation
func (useCase *FeatureFlagsUseCase) SetMultipleFeatures(context context.Context, request *featureflags.SetMultipleFeaturesRequest) (*featureflags.SetMultipleFeaturesResponse, error) {
	spanContext, span, finish := herosentry.StartSpan(context, "FeatureFlagsUseCase.SetMultipleFeatures")
	defer finish()
	span.SetTag("org.id", fmt.Sprintf("%d", request.OrgId))
	if request.AssetId != "" {
		span.SetTag("asset.id", request.AssetId)
	}
	span.SetTag("enable_features.count", fmt.Sprintf("%d", len(request.EnableFeatures)))
	span.SetTag("disable_features.count", fmt.Sprintf("%d", len(request.DisableFeatures)))

	if request.OrgId == 0 {
		err := fmt.Errorf("organization ID is required")
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation)
		return nil, err
	}

	// Validate features
	for _, feature := range request.EnableFeatures {
		if feature == featureflags.Feature_FEATURE_UNSPECIFIED {
			err := fmt.Errorf("invalid feature in enable list")
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation)
			return nil, err
		}
	}
	for _, feature := range request.DisableFeatures {
		if feature == featureflags.Feature_FEATURE_UNSPECIFIED {
			err := fmt.Errorf("invalid feature in disable list")
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation)
			return nil, err
		}
	}

	err := useCase.executeInTransaction(spanContext, func(transaction *sql.Tx) error {
		return useCase.featureFlagsRepository.SetMultipleFeatures(
			spanContext,
			transaction,
			request.OrgId,
			request.AssetId,
			request.EnableFeatures,
			request.DisableFeatures,
		)
	})
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to set multiple features")
		return nil, fmt.Errorf("failed to set multiple features: %w", err)
	}

	return &featureflags.SetMultipleFeaturesResponse{
		Success: true,
	}, nil
}

// GetEnabledFeatures returns all enabled features for a target
func (useCase *FeatureFlagsUseCase) GetEnabledFeatures(context context.Context, request *featureflags.GetEnabledFeaturesRequest) (*featureflags.GetEnabledFeaturesResponse, error) {
	spanContext, span, finish := herosentry.StartSpan(context, "FeatureFlagsUseCase.GetEnabledFeatures")
	defer finish()
	span.SetTag("org.id", fmt.Sprintf("%d", request.OrgId))
	if request.AssetId != "" {
		span.SetTag("asset.id", request.AssetId)
	}

	if request.OrgId == 0 {
		err := fmt.Errorf("organization ID is required")
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation)
		return nil, err
	}

	enabledFeatures, err := useCase.featureFlagsRepository.GetEnabledFeatures(
		spanContext,
		nil, // No transaction needed for read operation
		request.OrgId,
		request.AssetId,
	)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to get enabled features")
		return nil, fmt.Errorf("failed to get enabled features: %w", err)
	}

	return &featureflags.GetEnabledFeaturesResponse{
		EnabledFeatures: enabledFeatures,
	}, nil
}
