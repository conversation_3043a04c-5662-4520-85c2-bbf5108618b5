package connect

import (
	"context"
	"log/slog"

	connecthelper "common/connect"

	"connectrpc.com/connect"

	featureflags "proto/hero/featureflags/v1"
	featureflagsconnect "proto/hero/featureflags/v1/featureflagsconnect"

	"featureflags/internal/usecase"
)

// FeatureFlagsServer implements all RPCs defined in FeatureFlagsService.
// Each handler validates the request, invokes the corresponding
// business-logic method on FeatureFlagsUseCase, logs the call, and converts
// domain errors into Connect error codes.
type FeatureFlagsServer struct {
	featureflagsconnect.UnimplementedFeatureFlagsServiceHandler

	featureFlagsUseCase *usecase.FeatureFlagsUseCase
	logger              *slog.Logger
}

// NewFeatureFlagsServer constructs a FeatureFlagsServer. If logger is nil, slog.Default()
// is used so that a non-nil logger is always available.
func NewFeatureFlagsServer(featureFlagsUseCase *usecase.FeatureFlagsUseCase, logger *slog.Logger) *FeatureFlagsServer {
	if logger == nil {
		logger = slog.Default()
	}
	return &FeatureFlagsServer{
		featureFlagsUseCase: featureFlagsUseCase,
		logger:              logger.With("component", "FeatureFlagsServer"),
	}
}

// IsEnabled checks if a feature is enabled for a specific target
func (server *FeatureFlagsServer) IsEnabled(
	context context.Context,
	request *connect.Request[featureflags.IsEnabledRequest],
) (*connect.Response[featureflags.IsEnabledResponse], error) {
	server.logger.InfoContext(context, "IsEnabled called",
		"org_id", request.Msg.OrgId,
		"feature", request.Msg.Feature,
		"asset_id", request.Msg.AssetId,
	)

	response, err := server.featureFlagsUseCase.IsEnabled(context, request.Msg)
	if err != nil {
		server.logger.ErrorContext(context, "IsEnabled failed", "error", err)
		return nil, connecthelper.AsConnectError(context, err, "IsEnabled")
	}

	server.logger.DebugContext(context, "IsEnabled completed",
		"enabled", response.Enabled,
	)

	return connect.NewResponse(response), nil
}

// SetFeatureTarget enables or disables a feature for a specific target
func (server *FeatureFlagsServer) SetFeatureTarget(
	context context.Context,
	request *connect.Request[featureflags.SetFeatureTargetRequest],
) (*connect.Response[featureflags.SetFeatureTargetResponse], error) {
	server.logger.InfoContext(context, "SetFeatureTarget called",
		"org_id", request.Msg.OrgId,
		"feature", request.Msg.Feature,
		"asset_id", request.Msg.AssetId,
		"enabled", request.Msg.Enabled,
	)

	response, err := server.featureFlagsUseCase.SetFeatureTarget(context, request.Msg)
	if err != nil {
		server.logger.ErrorContext(context, "SetFeatureTarget failed", "error", err)
		return nil, connecthelper.AsConnectError(context, err, "SetFeatureTarget")
	}

	server.logger.InfoContext(context, "SetFeatureTarget completed",
		"success", response.Success,
	)

	return connect.NewResponse(response), nil
}

// SetMultipleFeatures enables/disables multiple features for a target in a single operation
func (server *FeatureFlagsServer) SetMultipleFeatures(
	context context.Context,
	request *connect.Request[featureflags.SetMultipleFeaturesRequest],
) (*connect.Response[featureflags.SetMultipleFeaturesResponse], error) {
	server.logger.InfoContext(context, "SetMultipleFeatures called",
		"org_id", request.Msg.OrgId,
		"asset_id", request.Msg.AssetId,
		"enable_count", len(request.Msg.EnableFeatures),
		"disable_count", len(request.Msg.DisableFeatures),
	)

	response, err := server.featureFlagsUseCase.SetMultipleFeatures(context, request.Msg)
	if err != nil {
		server.logger.ErrorContext(context, "SetMultipleFeatures failed", "error", err)
		return nil, connecthelper.AsConnectError(context, err, "SetMultipleFeatures")
	}

	server.logger.InfoContext(context, "SetMultipleFeatures completed",
		"success", response.Success,
	)

	return connect.NewResponse(response), nil
}

// GetEnabledFeatures returns all enabled features for a target
func (server *FeatureFlagsServer) GetEnabledFeatures(
	context context.Context,
	request *connect.Request[featureflags.GetEnabledFeaturesRequest],
) (*connect.Response[featureflags.GetEnabledFeaturesResponse], error) {

	response, err := server.featureFlagsUseCase.GetEnabledFeatures(context, request.Msg)
	if err != nil {
		server.logger.ErrorContext(context, "GetEnabledFeatures failed", "error", err)
		return nil, connecthelper.AsConnectError(context, err, "GetEnabledFeatures")
	}

	return connect.NewResponse(response), nil
}
