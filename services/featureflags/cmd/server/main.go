package main

import (
	"common/database"
	"common/herosentry"
	"common/middleware"
	"context"
	"database/sql"
	"log"
	"net/http"
	"os"
	"time"

	featureflags "featureflags/internal"
	featureflagsRepository "featureflags/internal/data"
)

// fatalWithFlush ensures Sentry events are sent before exiting
func fatalWithFlush(err error, msg string) {
	herosentry.CaptureException(context.Background(), err, herosentry.ErrorTypeInternal, msg)
	herosentry.Flush()
	log.Fatalf("%s: %v", msg, err)
}

func main() {
	// Initialize herosentry for error tracking and performance monitoring
	// Filter out validation and not-found errors to reduce noise
	falseVal := false
	err := herosentry.Init("featureflags-service", herosentry.Config{
		CustomSamplingRules: map[string]float64{
			"*Service.List*": 0.01,
		},
		// Filter out expected errors to reduce Sentry noise
		CaptureValidationErrors: &falseVal, // Don't capture validation/bad request errors
		CaptureNotFoundErrors:   &falseVal, // Don't capture 404/not found errors
	})
	if err != nil {
		log.Fatalf("Herosentry initialization failed: %v", err)
	}
	defer herosentry.Flush()

	baseMux := http.NewServeMux()

	// Initialize PostgreSQL database
	databaseURL, err := database.CreateDBURL()
	if err != nil {
		fatalWithFlush(err, "Failed to get postgres db url")
	}

	postgresDatabase, openError := sql.Open("postgres", databaseURL)
	if openError != nil {
		fatalWithFlush(openError, "Failed to open postgres db")
	}

	// Configure connection pool - feature flags is a low-traffic read-heavy service
	// Cacheable data, minimal DB load
	postgresDatabase.SetMaxOpenConns(25) // Small allocation for flag lookups
	postgresDatabase.SetMaxIdleConns(5)  // Minimal idle pool
	postgresDatabase.SetConnMaxLifetime(5 * time.Minute)
	postgresDatabase.SetConnMaxIdleTime(90 * time.Second)

	// Initialize Feature Flags Repository
	featureFlagsRepo, featureFlagsDB, err := featureflagsRepository.NewFeatureFlagsRepository(postgresDatabase)
	if err != nil {
		fatalWithFlush(err, "Failed to initialize feature flags repository")
	}

	// Register all endpoints
	featureflags.RegisterRoutes(baseMux, featureFlagsDB, featureFlagsRepo)

	// Wrap with database pool monitoring middleware
	mux := herosentry.DBPoolMiddleware(postgresDatabase)(baseMux)

	// Create a new mux for health endpoints that bypasses auth
	healthMux := middleware.NewHealthMux(middleware.HealthMuxConfig{
		ServiceNames: []string{
			"hero.featureflags.v1.FeatureFlagsService",
		},
		HealthResponse: "FEATURE FLAGS SERVICE IS HEALTHY",
	})

	skipPermissions := os.Getenv("SKIP_PERMISSIONS_CHECK") == "true"

	server, err := middleware.NewServerWithHealth(
		mux,
		healthMux,
		!skipPermissions,
	)
	if err != nil {
		fatalWithFlush(err, "Failed to create server")
	}

	if err := middleware.StartServer(server); err != nil {
		fatalWithFlush(err, "Failed to serve")
	}
}
