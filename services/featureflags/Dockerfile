# Use the official Go image as a builder
FROM golang:1.24-alpine as builder

# Install the CA certificates
RUN apk update && apk add ca-certificates && rm -rf /var/cache/apk/*

# keep the same directory structure as the repo itself,
# which makes the relative import for the proto library work
COPY lib/proto /lib/proto
COPY lib/common /lib/common
WORKDIR /services/featureflags

# Copy the Go modules files and download dependencies
COPY services/featureflags/go.mod services/featureflags/go.sum ./
RUN go mod download

# Copy the application code
COPY services/featureflags .

# Build the Go binary
RUN GOFIPS140=v1.0.0 go build -o server ./cmd/server

# Use a minimal base image to reduce image size
FROM debian:bookworm-slim

# Copy CA certificates from the builder
COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/

# Set the working directory
WORKDIR /app

# Copy the Go binary from the builder
COPY --from=builder /services/featureflags/server .

# Expose the port the app runs on
EXPOSE 8080

# Start the Go server
CMD ["./server"]