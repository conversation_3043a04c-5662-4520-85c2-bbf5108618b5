# Development Dockerfile with hot reload support
FROM golang:1.24

# Install CA certificates
RUN apt-get update && apt-get install -y \
    ca-certificates \
    build-essential \
    pkg-config \
    libglib2.0-dev \
    libgstreamer1.0-dev \
    libgstreamer-plugins-base1.0-dev \
    gstreamer1.0-plugins-base \
    gstreamer1.0-plugins-good \
    gstreamer1.0-plugins-bad \
    gstreamer1.0-plugins-ugly \
    gstreamer1.0-libav \
    && rm -rf /var/lib/apt/lists/*

# Install Air for hot reloading (air-verse/air)
RUN go install github.com/air-verse/air@latest

# Set working directory to match repo structure
WORKDIR /app

# Copy shared libraries first (these change less frequently)
COPY lib/proto /app/lib/proto
COPY lib/common /app/lib/common

# Copy go mod files and download dependencies
COPY services/sensors/go.mod services/sensors/go.sum /app/services/sensors/
WORKDIR /app/services/sensors
RUN go mod download

# Service code will be volume mounted for development (no COPY needed)

# Ensure CGO builds succeed for gstreamer/glib bindings
ENV CGO_ENABLED=1

# Expose the port
EXPOSE 8080

# Use Air for hot reloading
CMD ["air", "-c", ".air.toml"]