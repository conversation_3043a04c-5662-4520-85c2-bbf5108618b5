# Development Dockerfile with hot reload support
FROM golang:1.24

# Install CA certificates
RUN apt-get update && apt-get install -y ca-certificates && rm -rf /var/lib/apt/lists/*

# Install Air for hot reloading (air-verse/air)
RUN go install github.com/air-verse/air@latest

# Set working directory to match repo structure
WORKDIR /app

# Copy shared libraries first (these change less frequently)
COPY lib/proto /app/lib/proto
COPY lib/common /app/lib/common

# Copy go mod files and download dependencies
COPY services/communications/go.mod services/communications/go.sum /app/services/communications/
WORKDIR /app/services/communications
RUN go mod download

# Service code will be volume mounted for development (no COPY needed)

# Expose the port
EXPOSE 8080

# Use Air for hot reloading
CMD ["air", "-c", ".air.toml"]