package cellularcall

import (
	"database/sql"
	"net/http"

	"common/herosentry"

	"connectrpc.com/connect"

	cellularcallApi "communications/internal/cellularcall/api/connect"
	cellularcallData "communications/internal/cellularcall/data"
	cellularcallUsecase "communications/internal/cellularcall/usecase"
	conversationv1connect "proto/hero/communications/v1/conversationconnect"
)

// RegisterRoutes registers all cellular call service routes
func RegisterRoutes(
	mux *http.ServeMux,
	callQueueDB *sql.DB,
	callQueueRepo cellularcallData.CallQueueRepository,
	twilioAccountSid string,
	twilioAPIKeySid string,
	twilioAPIKeySecret string,
	twilioAuthToken string,
	waitURL string,
	situationsServiceURL string,
	orgsServiceURL string,
	commsServerPublicDomain string,
	assetsServiceURL string,
	callForwardTimeoutSeconds int,
) (cellularcallUsecase.CellularCallUsecase, error) {
	// Create the usecase
	cellularCallUsecase, err := cellularcallUsecase.NewCellularCallUsecase(
		twilioAccountSid,
		twilioAPIKeySid,
		twilioAPIKeySecret,
		twilioAuthToken,
		waitURL,
		situationsServiceURL,
		orgsServiceURL,
		commsServerPublicDomain,
		assetsServiceURL,
		callQueueRepo,
		callForwardTimeoutSeconds,
	)
	if err != nil {
		return nil, err
	}

	// Create the service server
	cellCallServiceServer := cellularcallApi.NewCellularCallServiceServer(cellularCallUsecase)

	// Register the RPC handler with herosentry interceptor for distributed tracing
	path, handler := conversationv1connect.NewCellularCallServiceHandler(
		cellCallServiceServer,
		connect.WithInterceptors(herosentry.RPCServiceInterceptor()),
	)
	mux.Handle(path, handler)

	// Register Twilio webhook endpoints
	registerTwilioWebhooks(mux, cellularCallUsecase)

	return cellularCallUsecase, nil
}

// registerTwilioWebhooks sets up the necessary Twilio webhook endpoints
func registerTwilioWebhooks(mux *http.ServeMux, cellularCallUC cellularcallUsecase.CellularCallUsecase) {
	// Primary voice webhook - handles all call types
	mux.Handle("/hero.communications.v1.TwilioWebhookService/voice", herosentry.HTTPMiddleware(
		http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			if err := r.ParseForm(); err != nil {
				herosentry.CaptureException(r.Context(), err, herosentry.ErrorTypeValidation, "Failed to parse form data in voice webhook", true)
				http.Error(w, "Error parsing form data", http.StatusBadRequest)
				return
			}

			cellularCallUC.HandleVoiceRequest(w, r)
		}),
	))

	// Status callback handler
	mux.Handle("/hero.communications.v1.TwilioWebhookService/callstatus", herosentry.HTTPMiddleware(
		http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			if err := r.ParseForm(); err != nil {
				herosentry.CaptureException(r.Context(), err, herosentry.ErrorTypeValidation, "Failed to parse form data in callstatus webhook", true)
				http.Error(w, "Error parsing form data", http.StatusBadRequest)
				return
			}

			cellularCallUC.HandleCallStatusRequest(w, r)
		}),
	))

	// TwiML endpoint for connecting an asset to a specific customer call
	mux.Handle("/hero.communications.v1.TwilioWebhookService/twiml/connectAgent", herosentry.HTTPMiddleware(
		http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			cellularCallUC.HandleConnectAgentTwiMLRequest(w, r)
		}),
	))

	// Agent dial status callback handler
	mux.Handle("/hero.communications.v1.TwilioWebhookService/twilio/agent-dial-status", herosentry.HTTPMiddleware(
		http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			if err := r.ParseForm(); err != nil {
				herosentry.CaptureException(r.Context(), err, herosentry.ErrorTypeValidation, "Failed to parse form data in agent-dial-status webhook", true)
				http.Error(w, "Error parsing form data", http.StatusBadRequest)
				return
			}

			cellularCallUC.HandleAgentDialStatusRequest(w, r)
		}),
	))

	// Wait/Hold webhook for smart queue and hold messaging
	mux.Handle("/hero.communications.v1.TwilioWebhookService/waithold", herosentry.HTTPMiddleware(
		http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			cellularCallUC.HandleWaitHoldRequest(w, r)
		}),
	))

	// Forward action callback for Dial action from ForwardDialResponse
	mux.Handle("/hero.communications.v1.TwilioWebhookService/twiml/forward-action", herosentry.HTTPMiddleware(
		http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			if err := r.ParseForm(); err != nil {
				herosentry.CaptureException(r.Context(), err, herosentry.ErrorTypeValidation, "Failed to parse form data in forward-action webhook", true)
				http.Error(w, "Error parsing form data", http.StatusBadRequest)
				return
			}
			cellularCallUC.HandleForwardActionRequest(w, r)
		}),
	))
}
