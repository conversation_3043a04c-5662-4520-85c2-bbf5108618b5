package usecase

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"math"
	"net/http"
	"regexp"
	"strings"
	"time"

	clients "common/clients/services"
	"common/herosentry"
	"common/utils"

	"github.com/twilio/twilio-go/client/jwt"
	"google.golang.org/protobuf/types/known/timestamppb"

	"communications/internal/cellularcall/client"
	"communications/internal/cellularcall/data"
	"communications/internal/cellularcall/twiml"

	assetsv2 "proto/hero/assets/v2"
	conversationv1 "proto/hero/communications/v1"
	orgs "proto/hero/orgs/v1"
	situationsv2 "proto/hero/situations/v2"

	"connectrpc.com/connect"
)

// Twilio call status values (see: https://www.twilio.com/docs/voice/twiml/voice-status-callback)
const (
	TwilioCallStatusInitiated  = "initiated"
	TwilioCallStatusRinging    = "ringing"
	TwilioCallStatusInProgress = "in-progress"
	TwilioCallStatusCompleted  = "completed"
	TwilioCallStatusBusy       = "busy"
	TwilioCallStatusNoAnswer   = "no-answer"
	TwilioCallStatusFailed     = "failed"
	TwilioCallStatusCanceled   = "canceled"
	TwilioCallStatusAnswered   = "answered"
)

// CellularCallUsecase defines the business logic for Twilio-based cellular calls.
type CellularCallUsecase interface {
	// GetCellularCallAccessToken generates a Twilio Access Token for cellular calls.
	GetCellularCallAccessToken(ctx context.Context, req *conversationv1.GetCellularCallAccessTokenRequest) (*conversationv1.GetCellularCallAccessTokenResponse, error)
	// HandleCall builds a TwiML response for handling inbound or outbound calls.
	HandleCall(ctx context.Context, req *conversationv1.HandleCallRequest) (*conversationv1.HandleCallResponse, error)
	// Queue management methods
	QueueCall(ctx context.Context, req *conversationv1.QueueCallRequest) (*conversationv1.QueueCallResponse, error)
	DequeueCall(ctx context.Context, req *conversationv1.DequeueCallRequest) (*conversationv1.DequeueCallResponse, error)
	DequeueCallBySid(ctx context.Context, req *conversationv1.DequeueCallBySidRequest) (*conversationv1.DequeueCallBySidResponse, error)
	GetQueueStatus(ctx context.Context, req *conversationv1.GetQueueStatusRequest) (*conversationv1.GetQueueStatusResponse, error)
	HoldCall(ctx context.Context, req *conversationv1.HoldCallRequest) (*conversationv1.HoldCallResponse, error)
	ResumeCall(ctx context.Context, req *conversationv1.ResumeCallRequest) (*conversationv1.ResumeCallResponse, error)
	GetAssetHeldCalls(ctx context.Context, req *conversationv1.GetAssetHeldCallsRequest) (*conversationv1.GetAssetHeldCallsResponse, error)
	EndCall(ctx context.Context, req *conversationv1.EndCallRequest) (*conversationv1.EndCallResponse, error)
	// VoiceHandler is the unified HTTP endpoint for inbound calls
	HandleVoiceRequest(w http.ResponseWriter, r *http.Request)
	// HandleCallStatusRequest is the unified HTTP endpoint for call status updates
	HandleCallStatusRequest(w http.ResponseWriter, r *http.Request)
	// HandleAgentDialStatusRequest processes Twilio status callbacks for the agent leg of calls
	HandleAgentDialStatusRequest(w http.ResponseWriter, r *http.Request)
	// HandleConnectAgentTwiMLRequest generates TwiML to connect an agent to a specific customer call
	HandleConnectAgentTwiMLRequest(w http.ResponseWriter, r *http.Request)
	// HandleWaitHoldRequest generates smart TwiML for queue wait and hold messaging
	HandleWaitHoldRequest(w http.ResponseWriter, r *http.Request)
	// HandleForwardActionRequest processes Dial action callbacks from call forwarding attempts
	HandleForwardActionRequest(w http.ResponseWriter, r *http.Request)
	GetSituationForCall(ctx context.Context, req *conversationv1.GetSituationForCallRequest) (*conversationv1.GetSituationForCallResponse, error)
	// RevertSelectiveClaim reverts a call from pending_selective_assignment back to waiting state
	RevertSelectiveClaim(ctx context.Context, req *conversationv1.RevertSelectiveClaimRequest) (*conversationv1.RevertSelectiveClaimResponse, error)
	ListCalls(ctx context.Context, req *conversationv1.ListCallsRequest) (*conversationv1.ListCallsResponse, error)
	// UpdateCallerName updates the caller name for a specific call
	UpdateCallerName(ctx context.Context, req *conversationv1.UpdateCallerNameRequest) (*conversationv1.UpdateCallerNameResponse, error)
}

// FIFOStrategy implements a first-in, first-out queue selection strategy
type FIFOStrategy struct{}

// SelectNextCall selects the first call in the waiting queue
func (s *FIFOStrategy) SelectNextCall(ctx context.Context, calls []data.QueuedCall) (data.QueuedCall, int, bool, error) {
	if len(calls) == 0 {
		return data.QueuedCall{}, -1, false, nil
	}
	return calls[0], 0, true, nil
}

type cellularCallUsecase struct {
	twilioAccountSid        string
	twilioAPIKeySid         string
	twilioAPIKeySecret      string
	twilioAuthToken         string
	callQueueRepo           data.CallQueueRepository
	waitURL                 string
	situationClient         clients.SituationsClient
	twilioQueueName         string
	orgClient               clients.OrgsClient
	twilioClient            client.TwilioClient
	commsServerPublicDomain string
	assetsClient            clients.AssetsClient
	// Call forwarding configuration
	callForwardTimeoutSeconds int
}

// tryHandleCallForwarding determines if the incoming call should be forwarded and, if so,
// generates and writes the forwarding TwiML. Returns true if the request was fully handled.
// HTTP handlers and forwarding helpers were moved to handlers.go to isolate transport logic.

// NewCellularCallUsecase creates a new CellularCallUsecase with the required Twilio credentials.
func NewCellularCallUsecase(
	twilioAccountSid string,
	twilioAPIKeySid string,
	twilioAPIKeySecret string,
	twilioAuthToken string,
	waitURL string,
	situationsServiceURL string,
	orgsServiceURL string,
	commsServerPublicDomain string,
	assetsServiceURL string,
	queueRepo data.CallQueueRepository,
	callForwardTimeoutSeconds int,
) (CellularCallUsecase, error) {
	// Validate required parameters.
	if twilioAccountSid == "" || twilioAPIKeySid == "" || twilioAPIKeySecret == "" {
		return nil, errors.New("missing required Twilio credentials")
	}

	// Validate that repository is provided
	if queueRepo == nil {
		return nil, errors.New("call queue repository is required")
	}

	// Set default FIFO strategy for the queue
	queueRepo.SetQueueStrategy(&FIFOStrategy{})

	// Initialize situation client with distributed tracing using herosentry
	situationClient := clients.NewSituationsClient(situationsServiceURL, herosentry.RPCClientInterceptor())

	// Initialize org client with distributed tracing using herosentry
	orgClient := clients.NewOrgsClient(orgsServiceURL, herosentry.RPCClientInterceptor())

	// Initialize assets client with distributed tracing using herosentry
	assetsClient := clients.NewAssetsClient(assetsServiceURL, herosentry.RPCClientInterceptor())

	// Create the Twilio client
	twilioClient := client.NewTwilioClient(twilioAccountSid, twilioAuthToken)

	return &cellularCallUsecase{
		twilioAccountSid:          twilioAccountSid,
		twilioAPIKeySid:           twilioAPIKeySid,
		twilioAPIKeySecret:        twilioAPIKeySecret,
		twilioAuthToken:           twilioAuthToken,
		callQueueRepo:             queueRepo,
		waitURL:                   waitURL,
		situationClient:           situationClient,
		orgClient:                 orgClient,
		twilioClient:              twilioClient,
		commsServerPublicDomain:   commsServerPublicDomain,
		assetsClient:              assetsClient,
		callForwardTimeoutSeconds: callForwardTimeoutSeconds,
	}, nil
}

// sanitizeTwilioIdentity removes invalid characters from Twilio identities
// Twilio identities must be alphanumeric, hyphens, underscores, and periods only
func sanitizeTwilioIdentity(identity string) string {
	// Replace spaces with hyphens
	identity = strings.ReplaceAll(identity, " ", "-")

	// Remove any characters that aren't alphanumeric, hyphens, underscores, or periods
	reg := regexp.MustCompile(`[^a-zA-Z0-9\-_.]`)
	identity = reg.ReplaceAllString(identity, "")

	// Ensure it's not empty
	if identity == "" {
		identity = "unknown"
	}

	return identity
}

// GetCellularCallAccessToken generates a Twilio Access Token with a VoiceGrant.
func (uc *cellularCallUsecase) GetCellularCallAccessToken(
	ctx context.Context,
	req *conversationv1.GetCellularCallAccessTokenRequest,
) (*conversationv1.GetCellularCallAccessTokenResponse, error) {
	// The RPCServiceInterceptor already creates the transaction, so we just create a child span
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "CellularCallUseCase.GetCellularCallAccessToken")
	defer finishSpan()

	span.SetTag("identity", req.Identity)
	span.SetTag("session_suffix", req.SessionSuffix)
	span.SetTag("expire", fmt.Sprintf("%d", req.Expire))

	// Simple validation - asset ID is required for token generation
	if req.Identity == "" {
		err := fmt.Errorf("asset ID is required for token generation")
		span.SetTag("error", "true")
		herosentry.CaptureException(spanContext, err, "Asset ID validation failed")
		return nil, err
	}

	orgTwilioDetails, err := uc.callQueueRepo.GetOrgTwilioDetails(spanContext)
	if err != nil {
		span.SetTag("error", "true")
		herosentry.CaptureException(spanContext, err, "Failed to get org twilio details")
		return nil, fmt.Errorf("failed to get org twilio details: %w", err)
	}

	// Build identity using unified formatter (uses asset name and session suffix internally)
	identity, err := uc.formatAgentIdentityWithSession(spanContext, req.Identity, req.SessionSuffix)
	if err != nil {
		span.SetTag("error", "true")
		herosentry.CaptureException(spanContext, err, "Failed to format identity")
		return nil, fmt.Errorf("failed to format identity: %w", err)
	}

	expireSeconds := req.Expire
	if expireSeconds <= 0 {
		expireSeconds = 3600
	}

	params := jwt.AccessTokenParams{
		AccountSid:    uc.twilioAccountSid,
		SigningKeySid: uc.twilioAPIKeySid,
		Secret:        uc.twilioAPIKeySecret,
		Identity:      identity,
		Ttl:           float64(expireSeconds),
	}

	tokenObj := jwt.CreateAccessToken(params)

	voiceGrant := &jwt.VoiceGrant{
		Incoming: jwt.Incoming{
			Allow: true,
		},
		Outgoing: jwt.Outgoing{
			ApplicationSid: orgTwilioDetails.TwimlAppSid,
		},
	}
	tokenObj.AddGrant(voiceGrant)

	// Convert to JWT string
	tokenString, err := tokenObj.ToJwt()
	if err != nil {
		span.SetTag("error", "true")
		herosentry.CaptureException(spanContext, err, "Failed to generate Twilio access token")
		return nil, fmt.Errorf("failed to generate Twilio access token: %w", err)
	}

	// Log successful token generation
	log.Printf("Generated Twilio access token for identity: %s (expires in %d seconds)",
		identity, expireSeconds)

	return &conversationv1.GetCellularCallAccessTokenResponse{
		Token:    tokenString,
		Identity: identity,
	}, nil
}

// QueueCall enqueues a call and generates TwiML to place the caller in a queue.
func (uc *cellularCallUsecase) QueueCall(
	ctx context.Context,
	req *conversationv1.QueueCallRequest,
) (*conversationv1.QueueCallResponse, error) {
	// The RPCServiceInterceptor already creates the transaction, so we just create a child span
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "CellularCallUseCase.QueueCall")
	defer finishSpan()

	span.SetTag("caller", req.Caller)
	span.SetTag("caller_name", req.CallerName)

	log.Printf("QueueCall: Starting to enqueue call from %s", req.Caller)

	if req.Caller == "" {
		err := errors.New("caller number is required")
		span.SetTag("error", "true")
		herosentry.CaptureException(spanContext, err, "Caller number validation failed")
		log.Printf("ERROR: Caller number is missing in request")
		return nil, err
	}

	callSID := ""
	if req.Attributes != nil && req.Attributes["CallSid"] != "" {
		callSID = req.Attributes["CallSid"]
		log.Printf("Using Twilio CallSID: %s", callSID)
	} else {
		callSID = fmt.Sprintf("queue-call-%d", time.Now().Unix())
		log.Printf("Generated internal CallSID: %s", callSID)
	}
	span.SetTag("call_sid", callSID)

	// Create the queued call
	log.Printf("Creating QueuedCall object with caller: %s, name: %s", req.Caller, req.CallerName)
	queuedCall := data.QueuedCall{
		CallSID:     callSID,
		Caller:      req.Caller,
		CallerName:  req.CallerName,
		EnqueueTime: time.Now(),
		State:       data.CallStateWaiting,
		Attributes:  make(map[string]string),
	}

	if req.Attributes != nil {
		log.Printf("Adding %d attributes to call", len(req.Attributes))
		for k, v := range req.Attributes {
			queuedCall.Attributes[k] = v
		}
	}

	// Create situation
	log.Printf("Attempting to create default situation for call")
	_, situationSpan, situationFinishSpan := herosentry.StartSpan(spanContext, "communications.create_default_situation")
	situationSpan.SetTag("caller", queuedCall.Caller)
	situationSpan.SetTag("call_sid", callSID)
	situationID, err := uc.createDefaultSituationForCall(spanContext, queuedCall)
	situationFinishSpan()
	if err != nil {
		situationSpan.SetTag("error", "true")
		herosentry.CaptureException(spanContext, err, "Failed to create situation for call")
		log.Printf("Warning: Failed to create situation for call %s: %v", callSID, err)
	} else {
		situationSpan.SetTag("situation_id", situationID)
		queuedCall.SituationID = situationID
		log.Printf("Created situation %s for call %s", situationID, callSID)
	}

	// Add to queue repository
	log.Printf("Adding call to queue repository")
	err = uc.callQueueRepo.EnqueueCall(spanContext, queuedCall)
	if err != nil {
		span.SetTag("error", "true")
		herosentry.CaptureException(spanContext, err, "Failed to enqueue call in repository")
		log.Printf("ERROR: Failed to enqueue call in repository: %v", err)
		return nil, fmt.Errorf("failed to enqueue call: %w", err)
	}
	log.Printf("Successfully added call to queue repository")

	// Get the queue name for the current organization
	queueName, err := uc.callQueueRepo.GetTwilioQueueName(spanContext)
	if err != nil {
		span.SetTag("error", "true")
		herosentry.CaptureException(spanContext, err, "Failed to get org-specific queue name")
		return nil, fmt.Errorf("error getting org-specific queue name: %v", err)
	}

	queueSid, err := uc.callQueueRepo.GetTwilioQueueSid(spanContext)
	if err != nil {
		span.SetTag("error", "true")
		herosentry.CaptureException(spanContext, err, "Failed to get org-specific queue SID")
		return nil, fmt.Errorf("error getting org-specific queue SID: %v", err)
	}

	// Get org Twilio details for authentication
	orgTwilioDetails, err := uc.callQueueRepo.GetOrgTwilioDetails(spanContext)
	if err != nil {
		span.SetTag("error", "true")
		herosentry.CaptureException(spanContext, err, "Failed to get org twilio details")
		return nil, fmt.Errorf("failed to get org twilio details: %w", err)
	}

	// Get org API user credentials for basic auth
	_, orgUserLookupSpan, orgUserFinishSpan := herosentry.StartSpan(spanContext, "communications.lookup_org_api_user")
	orgUserLookupSpan.SetTag("user_id", orgTwilioDetails.TwilioApiUserId)
	getOrgAPIUserPrivateRequest := &orgs.GetOrgAPIUserPrivateByIdRequest{
		UserId: orgTwilioDetails.TwilioApiUserId,
	}
	basicTwilioUserAuth, err := uc.orgClient.GetOrgAPIUserPrivateById(spanContext, connect.NewRequest(getOrgAPIUserPrivateRequest))
	orgUserFinishSpan()
	if err != nil {
		orgUserLookupSpan.SetTag("error", "true")
		span.SetTag("error", "true")
		herosentry.CaptureException(spanContext, err, "Failed to get org API user for smart wait URL")
		log.Printf("ERROR: Failed to get org API user for smart wait URL: %v", err)
		return nil, fmt.Errorf("failed to get org API user for authentication: %w", err)
	} else {
		orgUserLookupSpan.SetTag("username", basicTwilioUserAuth.Msg.OrgApiUser.Id)
	}

	// Generate TwiML with smart wait endpoint including basic auth
	_, twimlGenerationSpan, twimlFinishSpan := herosentry.StartSpan(spanContext, "communications.generate_queue_twiml")
	twimlGenerationSpan.SetTag("queue_name", queueName)
	twimlGenerationSpan.SetTag("call_sid", callSID)
	basicUserAuth := basicTwilioUserAuth.Msg.OrgApiUser.Id + ":" + basicTwilioUserAuth.Msg.OrgApiUser.RawPassword
	smartWaitURL := fmt.Sprintf("https://%s@%s/hero.communications.v1.TwilioWebhookService/waithold?action=enqueue", basicUserAuth, uc.commsServerPublicDomain)
	log.Printf("Generating TwiML with queue name: %s, smartWaitUrl: [redacted]", queueName)
	// Remove welcome message - let smart wait messaging handle all caller communication
	twimlResponse, err := twiml.QueueResponse(queueName, smartWaitURL, "")
	twimlFinishSpan()
	if err != nil {
		twimlGenerationSpan.SetTag("error", "true")
		span.SetTag("error", "true")
		herosentry.CaptureException(spanContext, err, "Failed to generate queue TwiML")
		log.Printf("ERROR: Failed to generate TwiML: %v", err)
		return nil, fmt.Errorf("failed to generate queue TwiML: %w", err)
	} else {
		twimlGenerationSpan.SetTag("twiml_length", fmt.Sprintf("%d", len(twimlResponse)))
	}

	log.Printf("Generated queue TwiML; length=%d", len(twimlResponse))

	response := &conversationv1.QueueCallResponse{
		QueueName: queueName,
		QueueSid:  queueSid,
		CallSid:   callSID,
		Twiml:     twimlResponse,
	}
	span.SetTag("rpc.status", "success")
	log.Printf("QueueCall: Completed successfully, returning response")
	return response, nil
}

// DequeueCall connects an asset to the next call in the queue.
// DEPRECATED: This method is obsolete. Use DequeueCallBySid for selective call handling.

func (uc *cellularCallUsecase) DequeueCall(
	ctx context.Context,
	req *conversationv1.DequeueCallRequest,
) (*conversationv1.DequeueCallResponse, error) {
	log.Printf("DequeueCall: Asset %s accepting next call", req.AssetId)
	if req.AssetId == "" {
		return nil, errors.New("asset ID is required")
	}

	// Try to dequeue a call from the repository
	call, ok, err := uc.callQueueRepo.DequeueCall(ctx, req.AssetId)
	if err != nil {
		return nil, fmt.Errorf("failed to dequeue call: %w", err)
	}

	if !ok {
		log.Print("No calls available in queue")
		return &conversationv1.DequeueCallResponse{
			Success:    false,
			CallSid:    "",
			Caller:     "",
			CallerName: "",
		}, nil
	}

	// Convert repository attributes to proto message attributes
	attributes := make(map[string]string)
	for k, v := range call.Attributes {
		attributes[k] = v
	}

	if call.SituationID != "" {
		log.Printf("Call dequeued with situation ID: %s", call.SituationID)
	}

	log.Printf("Dequeued call %s from %s for asset %s", call.CallSID, call.Caller, req.AssetId)

	// Create the response
	return &conversationv1.DequeueCallResponse{
		Success:       true,
		CallSid:       call.CallSID,
		Caller:        call.Caller,
		CallerName:    call.CallerName,
		Attributes:    attributes,
		QueueName:     uc.twilioQueueName,
		SituationId:   call.SituationID,
		CallStartTime: timestamppb.New(*call.CallStartTime),
	}, nil
}

// DequeueCallBySid connects an asset to a specific call by SID
func (uc *cellularCallUsecase) DequeueCallBySid(
	ctx context.Context,
	req *conversationv1.DequeueCallBySidRequest,
) (*conversationv1.DequeueCallBySidResponse, error) {
	// The RPCServiceInterceptor already creates the transaction, so we just create a child span
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "CellularCallUseCase.DequeueCallBySid")
	defer finishSpan()

	span.SetTag("call_sid", req.CallSid)
	span.SetTag("asset_id", req.AssetId)
	span.SetTag("session_suffix", req.SessionSuffix)

	log.Printf("DequeueCallBySid: Starting to dequeue call %s for asset %s", req.CallSid, req.AssetId)

	if req.AssetId == "" || req.CallSid == "" {
		err := errors.New("missing required parameters: asset_id and call_sid")
		span.SetTag("error", "true")
		herosentry.CaptureException(spanContext, err, "Missing required parameters")
		return nil, err
	}

	// Try to dequeue the specific call by SID
	call, found, err := uc.callQueueRepo.DequeueCallBySid(spanContext, req.CallSid, req.AssetId)
	if err != nil {
		span.SetTag("error", "true")
		herosentry.CaptureException(spanContext, err, "Failed to dequeue call by SID from repository")
		log.Printf("Error dequeuing call by SID from repository: %v", err)
		return nil, fmt.Errorf("failed to dequeue call from repository: %w", err)
	}

	if !found {
		span.SetTag("call_not_found", "true")
		log.Printf("Call with SID %s not found or not in waiting state in repository", req.CallSid)
		return &conversationv1.DequeueCallBySidResponse{
			Success: false,
		}, nil
	}

	// Construct the relative path for the TwiML endpoint
	relativeTwiMLPath := fmt.Sprintf("/hero.communications.v1.TwilioWebhookService/twiml/connectAgent?agentId=%s&customerSid=%s&sessionSuffix=%s",
		req.AssetId, call.CallSID, req.SessionSuffix)

	// Env var if local development. Read the readme for more details. (COMMS_SERVER_PUBLIC_DOMAIN)
	publicCommunicationsServiceURL := "https://" + uc.commsServerPublicDomain

	connectAgentURL := publicCommunicationsServiceURL + relativeTwiMLPath
	log.Printf("DequeueCallBySid: Constructed connectAgentURL for redirection: %s", connectAgentURL)

	var redirectErr error
	maxRetries := 4 // Number of retries (total 5 attempts)
	retryDelay := 500 * time.Millisecond

	// Get the queue SID for the current organization
	queueSid, err := uc.callQueueRepo.GetTwilioQueueSid(spanContext)
	if err != nil {
		span.SetTag("error", "true")
		herosentry.CaptureException(spanContext, err, "Failed to get org-specific queue SID")
		return nil, fmt.Errorf("failed to get org-specific queue SID: %w", err)
	}

	for i := 0; i <= maxRetries; i++ {
		log.Printf("Attempt %d to redirect queue member %s (Queue: %s) to %s", i+1, call.CallSID, queueSid, connectAgentURL)
		redirectErr = uc.twilioClient.RedirectQueueMember(ctx, queueSid, call.CallSID, connectAgentURL)
		if redirectErr == nil {
			// Success
			log.Printf("Successfully redirected queue member %s on attempt %d", call.CallSID, i+1)
			break
		}

		log.Printf("Attempt %d failed to redirect queue member %s: %v", i+1, call.CallSID, redirectErr)

		if i < maxRetries {
			log.Printf("Waiting %v before next retry...", retryDelay)
			time.Sleep(retryDelay)
			retryDelay *= 2 // Exponential backoff (e.g., 500ms, 1s)
		}
	}

	if redirectErr != nil {
		span.SetTag("error", "true")
		span.SetTag("redirect_failed", "true")
		span.SetTag("retry_count", fmt.Sprintf("%d", maxRetries+1))
		herosentry.CaptureException(spanContext, redirectErr, "All redirect attempts failed")
		log.Printf("All %d attempts failed to redirect queue member %s. Last error: %v", maxRetries+1, call.CallSID, redirectErr)

		// Revert the call state in the database
		log.Printf("Reverting call %s state in DB due to redirection failure.", call.CallSID)
		// Revert the state *before* returning the error to the client.
		// Use a new context for this critical cleanup operation if the original context might have been cancelled.
		revertCtx, cancelRevert := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancelRevert()

		reverted, dbRevertErr := uc.callQueueRepo.RevertSelectiveClaim(revertCtx, call.CallSID)
		switch {
		case dbRevertErr != nil:
			herosentry.CaptureException(spanContext, dbRevertErr, "CRITICAL: Failed to revert call state after redirection failure")
			log.Printf("CRITICAL: Failed to revert call state for %s after redirection failure: %v. Original error: %v", call.CallSID, dbRevertErr, redirectErr)
			// Even if revert fails, we should still return the original redirection error,
			// as that's what the caller (frontend) needs to know about.
		case reverted:
			log.Printf("Successfully reverted call %s to waiting state after redirection failure.", call.CallSID)
		default:
			log.Printf("No need to revert call %s state after redirection failure.", call.CallSID)
		}
		// Return the original error from the Twilio client
		return nil, fmt.Errorf("failed to redirect queue member after %d attempts: %w", maxRetries+1, redirectErr)
	}

	// Create response
	span.SetTag("rpc.status", "success")
	resp := &conversationv1.DequeueCallBySidResponse{
		Success:     true,
		CallSid:     call.CallSID,
		Caller:      call.Caller,
		CallerName:  call.CallerName,
		SituationId: call.SituationID,
	}

	log.Printf("DequeueCallBySid: Successfully claimed call %s for asset %s", call.CallSID, req.AssetId)
	return resp, nil
}

// createDefaultSituationForCall creates a basic situation for an incoming call.
func (uc *cellularCallUsecase) createDefaultSituationForCall(ctx context.Context, call data.QueuedCall) (string, error) {
	// First, check if there's an existing active situation for this caller
	searchReq := &situationsv2.SearchSituationsRequest{
		Query: call.Caller, // Search by caller number in all searchable fields
		Status: []situationsv2.SituationStatus{
			situationsv2.SituationStatus_SITUATION_STATUS_CREATED,
			situationsv2.SituationStatus_SITUATION_STATUS_TRIAGING,
			situationsv2.SituationStatus_SITUATION_STATUS_DISPATCHING,
			situationsv2.SituationStatus_SITUATION_STATUS_ADDRESSING,
			situationsv2.SituationStatus_SITUATION_STATUS_ESCALATED,
		},
		PageSize: 1,
		OrderBy:  situationsv2.SearchOrderBy_SEARCH_ORDER_BY_UPDATE_TIME, // Get most recently updated
	}

	searchResp, err := uc.situationClient.SearchSituations(ctx, connect.NewRequest(searchReq))
	if err != nil {
		return "", fmt.Errorf("error searching for existing situations: %w", err)
	}

	// If we found an active situation, return its ID
	if len(searchResp.Msg.Situations) > 0 {
		// Verify the situation is actually for this caller (since we're using a general search)
		situation := searchResp.Msg.Situations[0]
		if situation.ContactNo == call.Caller {
			return situation.Id, nil
		}
	}

	// If we get here, either no active situation was found or the found one wasn't for this caller
	// In either case, we'll proceed to create a new situation

	details := map[string]interface{}{
		"caller":      call.Caller,
		"callerName":  call.CallerName,
		"callTime":    time.Now().Format(time.RFC3339),
		"description": fmt.Sprintf("Incoming call from %s (%s)", call.CallerName, call.Caller),
		"callerNo":    call.Caller,
		"callSid":     call.CallSID,
	}

	currentTime := time.Now()
	updates := []*situationsv2.UpdateEntry{
		{
			Message:      fmt.Sprintf("Incident is created by phone call coming from %s", call.Caller),
			Timestamp:    utils.TimeToISO8601String(currentTime),
			UpdateSource: situationsv2.UpdateSource_UPDATE_SOURCE_API_SIDE_EFFECT,
			EventType:    "info change",
		},
	}

	detailsJSON, err := json.Marshal(details)
	if err != nil {
		return "", fmt.Errorf("failed to marshal incident details: %w", err)
	}

	situation := &situationsv2.Situation{
		Title:              fmt.Sprintf("Incoming call from %s", call.Caller),
		Type:               situationsv2.SituationType_SITUATION_TYPE_UNSPECIFIED,
		AdditionalInfoJson: string(detailsJSON),
		Status:             situationsv2.SituationStatus_SITUATION_STATUS_CREATED,
		TriggerSource:      situationsv2.TriggerSource_TRIGGER_SOURCE_PHONE_CALL,
		ContactNo:          call.Caller,
		ReporterName:       call.CallerName, // Set the reporter name from caller name
		CreateTime:         utils.TimeToISO8601String(currentTime),
		UpdateTime:         utils.TimeToISO8601String(currentTime),
		Updates:            updates,
	}

	createReq := &situationsv2.CreateSituationRequest{
		Situation: situation,
	}

	createResp, err := uc.situationClient.CreateSituation(ctx, connect.NewRequest(createReq))
	if err != nil {
		return "", fmt.Errorf("failed to create situation: %w", err)
	}

	return createResp.Msg.Situation.Id, nil
}

func (uc *cellularCallUsecase) GetSituationForCall(
	ctx context.Context,
	req *conversationv1.GetSituationForCallRequest,
) (*conversationv1.GetSituationForCallResponse, error) {
	// Add debug logs here
	log.Printf("GetSituationForCall called for CallSID: %s", req.CallSid)

	// Get call details from our repository
	call, found, err := uc.callQueueRepo.GetCallByCallSID(ctx, req.CallSid)
	if err != nil {
		log.Printf("Error getting call: %v", err)
		return nil, fmt.Errorf("error retrieving call: %w", err)
	}
	if !found {
		log.Printf("Call not found: %s", req.CallSid)
		return &conversationv1.GetSituationForCallResponse{
			Found: false,
		}, nil
	}

	log.Printf("Call found with situation ID: %s", call.SituationID)

	// Check if the call has a linked situation
	if call.SituationID == "" {
		return &conversationv1.GetSituationForCallResponse{
			Found: false,
		}, nil
	}

	getReq := &situationsv2.GetSituationRequest{
		Id: call.SituationID,
	}

	// Retrieve the situation details
	situationResp, err := uc.situationClient.GetSituation(ctx,
		connect.NewRequest(getReq))
	if err != nil {
		log.Printf("Error getting situation: %v", err)
		return nil, fmt.Errorf("error retrieving situation: %w", err)
	}

	situation := situationResp.Msg

	log.Printf("Retrieved situation: %+v", situation)

	return &conversationv1.GetSituationForCallResponse{
		Found:       true,
		SituationId: call.SituationID,
		Situation:   situation,
	}, nil
}

// Helper function to safely convert an int to int32 with overflow checking
func safeInt32Conversion(value int) int32 {
	if value > int(^uint32(0)>>1) {
		return int32(^uint32(0) >> 1) // Max int32 value
	}
	return int32(value) //nolint:gosec // Safe conversion
}

// GetQueueStatus returns the current state of the queue.
func (uc *cellularCallUsecase) GetQueueStatus(
	ctx context.Context,
	req *conversationv1.GetQueueStatusRequest,
) (*conversationv1.GetQueueStatusResponse, error) {
	queueSize, waitingCalls, err := uc.callQueueRepo.GetQueueStatus(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get queue status: %w", err)
	}
	var holdSize int
	allAssets := make(map[string]bool)
	for i := range waitingCalls {
		call := waitingCalls[i]
		if call.AssetID != "" {
			allAssets[call.AssetID] = true
		}
	}
	for assetID := range allAssets {
		heldCalls, err := uc.callQueueRepo.GetHeldCalls(ctx, assetID)
		if err != nil {
			log.Printf("Warning: Failed to get held calls for asset %s: %v", assetID, err)
			continue
		}
		holdSize += len(heldCalls)
	}
	protoWaitingCalls := make([]*conversationv1.QueuedCall, len(waitingCalls))
	for i := range waitingCalls {
		// Use the helper function for mapping
		protoWaitingCalls[i] = mapRepoCallToProtoCall(&waitingCalls[i])
	}

	var nextCall *conversationv1.QueuedCall
	if queueSize > 0 && len(protoWaitingCalls) > 0 {
		nextCall = protoWaitingCalls[0] // Assuming the first waiting call is the next one (FIFO)
	}

	safeQueueSize := safeInt32Conversion(queueSize)
	safeHoldSize := safeInt32Conversion(holdSize)

	return &conversationv1.GetQueueStatusResponse{
		QueueSize:    safeQueueSize,
		HoldSize:     safeHoldSize,
		NextCall:     nextCall,          // Already mapped by the loop above if it exists
		WaitingCalls: protoWaitingCalls, // Already mapped by the loop above
		// OnHoldCalls field is not populated here, GetAssetHeldCalls handles that.
	}, nil
}

// HoldCall places a call on hold.
func (uc *cellularCallUsecase) HoldCall(
	ctx context.Context,
	req *conversationv1.HoldCallRequest,
) (*conversationv1.HoldCallResponse, error) {
	// The RPCServiceInterceptor already creates the transaction, so we just create a child span
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "CellularCallUseCase.HoldCall")
	defer finishSpan()

	span.SetTag("call_sid", req.CallSid)
	span.SetTag("asset_id", req.AssetId)

	log.Printf("HoldCall: Placing call %s on hold for asset %s", req.CallSid, req.AssetId)

	// 1. Update internal state
	err := uc.callQueueRepo.HoldCall(spanContext, req.CallSid, req.AssetId)
	if err != nil {
		span.SetTag("error", "true")
		herosentry.CaptureException(spanContext, err, "Failed to hold call in repository")
		return nil, fmt.Errorf("failed to hold call in repo: %w", err)
	}

	// Logging to check direction of call
	call, found, err := uc.callQueueRepo.GetCallByCallSID(spanContext, req.CallSid)
	if err != nil {
		log.Printf("Error retrieving call %s: %v", req.CallSid, err)
	} else if found {
		log.Printf("Holding call %s - Direction: %s, State: %s", req.CallSid, call.Direction, call.State)
	}

	// 2. Redirect the call to internal wait/hold webhook for looping no-music hold
	orgTwilioDetails, err := uc.callQueueRepo.GetOrgTwilioDetails(spanContext)
	if err != nil {
		span.SetTag("error", "true")
		herosentry.CaptureException(spanContext, err, "Failed to get org twilio details for hold redirect")
		return nil, fmt.Errorf("failed to get org twilio details: %w", err)
	}

	// Lookup org API user to embed basic auth in the hold URL
	_, orgUserLookupSpan, orgUserFinishSpan := herosentry.StartSpan(spanContext, "communications.lookup_org_api_user")
	orgUserLookupSpan.SetTag("user_id", orgTwilioDetails.TwilioApiUserId)
	username, password, _, err := uc.getOrgAPIUserBasicAuthByUserId(spanContext, orgTwilioDetails.TwilioApiUserId)
	orgUserFinishSpan()
	if err != nil {
		orgUserLookupSpan.SetTag("error", "true")
		span.SetTag("error", "true")
		herosentry.CaptureException(spanContext, err, "Failed to get org API user for hold redirect URL")
		log.Printf("ERROR: Failed to get org API user for hold redirect URL: %v", err)
		return nil, fmt.Errorf("failed to get org API user for authentication: %w", err)
	} else {
		orgUserLookupSpan.SetTag("username", username)
	}

	holdURL := uc.buildAuthedURL(username, password, "/hero.communications.v1.TwilioWebhookService/waithold?action=hold")

	// Generate redirect TwiML so Twilio fetches hold loop from our internal webhook
	redirectTwiML, err := twiml.RedirectResponse(holdURL)
	if err != nil {
		span.SetTag("error", "true")
		herosentry.CaptureException(spanContext, err, "Failed to generate hold redirect TwiML")
		return nil, fmt.Errorf("failed to generate hold redirect TwiML: %w", err)
	}

	log.Printf("Sending hold redirect TwiML to internal waithold; length=%d", len(redirectTwiML))

	if err := uc.twilioClient.ModifyCall(spanContext, req.CallSid, redirectTwiML); err != nil {
		span.SetTag("error", "true")
		herosentry.CaptureException(spanContext, err, "Failed to redirect call to internal hold webhook via Twilio")
		return nil, fmt.Errorf("failed to place call on hold: %w", err)
	}

	span.SetTag("rpc.status", "success")
	return &conversationv1.HoldCallResponse{Success: true}, nil
}

// Helper function to map repository QueuedCall to Protobuf QueuedCall
func mapRepoCallToProtoCall(call *data.QueuedCall) *conversationv1.QueuedCall {
	if call == nil {
		return nil
	}

	attributes := make(map[string]string)
	for k, v := range call.Attributes {
		attributes[k] = v
	}

	protoCall := &conversationv1.QueuedCall{
		CallSid:     call.CallSID,
		Caller:      call.Caller,
		CallerName:  call.CallerName,
		EnqueueTime: timestamppb.New(call.EnqueueTime),
		Attributes:  attributes,
		AssetId:     call.AssetID,
		SituationId: call.SituationID,
		// TODO: Map History, Priority, Notes if they become relevant
	}

	// Map nullable timestamps
	if call.CallStartTime != nil {
		protoCall.CallStartTime = timestamppb.New(*call.CallStartTime)
	}
	if call.CallEndTime != nil {
		protoCall.CallEndTime = timestamppb.New(*call.CallEndTime)
	}
	if call.LastHoldStart != nil {
		protoCall.LastHoldStart = timestamppb.New(*call.LastHoldStart)
	}

	return protoCall
}

// GetAssetHeldCalls returns calls that an asset has placed on hold.
func (uc *cellularCallUsecase) GetAssetHeldCalls(
	ctx context.Context,
	req *conversationv1.GetAssetHeldCallsRequest,
) (*conversationv1.GetAssetHeldCallsResponse, error) {
	if req.AssetId == "" {
		return nil, errors.New("asset ID is required")
	}
	heldCalls, err := uc.callQueueRepo.GetHeldCalls(ctx, req.AssetId)
	if err != nil {
		return nil, fmt.Errorf("failed to get held calls: %w", err)
	}
	protoHeldCalls := make([]*conversationv1.QueuedCall, len(heldCalls))
	for i := range heldCalls {
		// Use the helper function for mapping
		protoHeldCalls[i] = mapRepoCallToProtoCall(&heldCalls[i])
	}
	return &conversationv1.GetAssetHeldCallsResponse{
		HeldCalls: protoHeldCalls,
	}, nil
}

// ResumeCall connects an asset to a call that was previously on hold.
func (uc *cellularCallUsecase) ResumeCall(
	ctx context.Context,
	req *conversationv1.ResumeCallRequest,
) (*conversationv1.ResumeCallResponse, error) {
	// The RPCServiceInterceptor already creates the transaction, so we just create a child span
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "CellularCallUseCase.ResumeCall")
	defer finishSpan()

	span.SetTag("call_sid", req.CallSid)
	span.SetTag("asset_id", req.AssetId)

	log.Printf("ResumeCall: Asset %s resuming call %s", req.AssetId, req.CallSid)
	orgTwilioDetails, err := uc.callQueueRepo.GetOrgTwilioDetails(spanContext)
	if err != nil {
		span.SetTag("error", "true")
		herosentry.CaptureException(spanContext, err, "Failed to get org twilio details")
		return nil, fmt.Errorf("failed to get org twilio details: %w", err)
	}

	if req.CallSid == "" {
		err := errors.New("call SID is required")
		span.SetTag("error", "true")
		herosentry.CaptureException(spanContext, err, "Call SID validation failed")
		return nil, err
	}
	if req.AssetId == "" {
		err := errors.New("asset ID is required")
		span.SetTag("error", "true")
		herosentry.CaptureException(spanContext, err, "Asset ID validation failed")
		return nil, err
	}

	// Update call state in repository
	if err := uc.callQueueRepo.ResumeCall(spanContext, req.CallSid, req.AssetId); err != nil {
		return nil, fmt.Errorf("failed to update call state: %w", err)
	}

	// Simple session-based identity generation - session suffix is required
	sessionSuffix := req.CurrentSessionSuffix
	if sessionSuffix == "" {
		return nil, fmt.Errorf("session suffix is required for call resume")
	}

	log.Printf("Resuming call %s for asset %s with session suffix: %s", req.CallSid, req.AssetId, sessionSuffix)

	// Generate client identity using current session suffix
	clientIdentity, err := uc.formatAgentIdentityWithSession(ctx, req.AssetId, sessionSuffix)
	if err != nil {
		return nil, fmt.Errorf("failed to resume call %s: unable to format identity for asset %s: %w", req.CallSid, req.AssetId, err)
	}

	log.Printf("Resuming call %s with identity: %s", req.CallSid, clientIdentity)

	// Generate client dial TwiML with the appropriate identity
	clientTwiML, err := twiml.ClientDialResponse(clientIdentity, orgTwilioDetails.TwilioNumber)
	if err != nil {
		return nil, fmt.Errorf("failed to generate client dial TwiML: %w", err)
	}

	// Use the Twilio client to modify the call directly
	if err := uc.twilioClient.ModifyCall(ctx, req.CallSid, clientTwiML); err != nil {
		return nil, fmt.Errorf("failed to resume call: %w", err)
	}

	span.SetTag("rpc.status", "success")
	return &conversationv1.ResumeCallResponse{Success: true}, nil
}

// EndCall terminates an active call.
func (uc *cellularCallUsecase) EndCall(
	ctx context.Context,
	req *conversationv1.EndCallRequest,
) (*conversationv1.EndCallResponse, error) {
	// The RPCServiceInterceptor already creates the transaction, so we just create a child span
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "CellularCallUseCase.EndCall")
	defer finishSpan()

	span.SetTag("call_sid", req.CallSid)
	span.SetTag("asset_id", req.AssetId)

	log.Printf("EndCall: Ending call %s for asset %s", req.CallSid, req.AssetId)
	if req.CallSid == "" {
		err := errors.New("call SID is required")
		span.SetTag("error", "true")
		herosentry.CaptureException(spanContext, err, "Call SID validation failed")
		return nil, err
	}

	// Update internal state by ending the call in the repository
	err := uc.callQueueRepo.EndCall(spanContext, req.CallSid)
	if err != nil {
		log.Printf("Warning: Error ending call in repo: %v", err)
		// return nil, fmt.Errorf("failed to end call: %w", err)
	}
	log.Printf("Call %s ended in repository successfully", req.CallSid)

	// Generate the Hangup TwiML response
	hangupXML, err := twiml.HangupResponse()
	if err != nil {
		log.Printf("Error generating hangup TwiML: %v", err)
		return nil, fmt.Errorf("failed to generate hangup TwiML: %w", err)
	}

	// Use the Twilio client to modify the call, sending the Hangup TwiML
	if err := uc.twilioClient.ModifyCall(ctx, req.CallSid, hangupXML); err != nil {
		log.Printf("Error modifying call to hangup: %v", err)
		return nil, fmt.Errorf("failed to end call via Twilio: %w", err)
	}

	span.SetTag("rpc.status", "success")
	log.Printf("Call %s terminated via Twilio successfully", req.CallSid)
	return &conversationv1.EndCallResponse{
		Success: true,
	}, nil
}

// HandleCall constructs a TwiML response for handling a call.
func (uc *cellularCallUsecase) HandleCall(
	ctx context.Context,
	req *conversationv1.HandleCallRequest,
) (*conversationv1.HandleCallResponse, error) {
	var responseXML string
	var err error

	orgTwilioDetails, err := uc.callQueueRepo.GetOrgTwilioDetails(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get org twilio details: %w", err)
	}

	// Get action from request attributes if available
	action := ""
	if req.Attributes != nil && req.Attributes["action"] != "" {
		action = req.Attributes["action"]
	}

	// If we have a dequeue action in attributes, handle it that way
	const actionDequeue = "dequeue"

	if action == actionDequeue || req.Flow == actionDequeue {
		queueName := ""
		if req.Attributes != nil && req.Attributes["queue"] != "" {
			queueName = req.Attributes["queue"]
		}
		if queueName == "" {
			queueName = uc.twilioQueueName
		}

		responseXML, err = twiml.DequeueResponse(queueName)
		if err != nil {
			return nil, fmt.Errorf("failed to generate dequeue TwiML: %w", err)
		}
	} else {
		switch req.Flow {
		case "outbound":
			// Outbound call: Dial the provided number.
			if req.To == "" || req.To == orgTwilioDetails.TwilioNumber {
				return nil, errors.New("invalid 'to' parameter for outbound call")
			}

			responseXML, err = twiml.OutboundDialResponse(req.To, orgTwilioDetails.TwilioNumber)
			if err != nil {
				return nil, fmt.Errorf("failed to generate outbound TwiML: %w", err)
			}

		case "client-inbound":
			// Client inbound call: Dial the client device.
			if req.Caller == "" {
				return nil, errors.New("missing 'caller' parameter for client inbound call")
			}

			responseXML, err = twiml.ClientDialResponse(req.To, req.Caller)
			if err != nil {
				return nil, fmt.Errorf("failed to generate client inbound TwiML: %w", err)
			}

		default:
			// For backward compatibility, fall back to legacy logic if Flow is not provided.
			switch {
			case req.To != "" && req.To != orgTwilioDetails.TwilioNumber:
				// Treat as outbound call.
				responseXML, err = twiml.OutboundDialResponse(req.To, orgTwilioDetails.TwilioNumber)
				if err != nil {
					return nil, fmt.Errorf("failed to generate outbound TwiML: %w", err)
				}
			case req.Caller != "":
				// Treat as client inbound call.
				responseXML, err = twiml.ClientDialResponse(req.To, req.Caller)
				if err != nil {
					return nil, fmt.Errorf("failed to generate inbound TwiML: %w", err)
				}
			default:
				return nil, errors.New("insufficient call parameters: missing 'to', 'caller', or 'flow'")
			}
		}
	}

	return &conversationv1.HandleCallResponse{
		Twiml: responseXML,
	}, nil
}

// HTTP handlers were moved to handlers.go

// processForwardStatusEvent handles Twilio callstatus webhooks when eventType=forward is provided.
// It updates or creates a call_forward_events record based on DialCallStatus and identifiers.
// HTTP handlers were moved to handlers.go

// HandleAgentDialStatusRequest processes Twilio status callbacks for the agent leg of calls
// HTTP handlers were moved to handlers.go

// RevertSelectiveClaim handles reverting a call from pending_selective_assignment to waiting
func (uc *cellularCallUsecase) RevertSelectiveClaim(
	ctx context.Context,
	req *conversationv1.RevertSelectiveClaimRequest,
) (*conversationv1.RevertSelectiveClaimResponse, error) {
	// The RPCServiceInterceptor already creates the transaction, so we just create a child span
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "CellularCallUseCase.RevertSelectiveClaim")
	defer finishSpan()

	span.SetTag("call_sid", req.CallSid)

	// Validate input
	if req.CallSid == "" {
		err := errors.New("call SID is required")
		span.SetTag("error", "true")
		herosentry.CaptureException(spanContext, err, "Missing call SID in RevertSelectiveClaim request")
		log.Printf("Error: Missing call SID in RevertSelectiveClaim request")
		return nil, connect.NewError(connect.CodeInvalidArgument, err)
	}

	log.Printf("Attempting to revert selective claim for call %s", req.CallSid)

	// Call repository method to revert the call state
	reverted, err := uc.callQueueRepo.RevertSelectiveClaim(spanContext, req.CallSid)
	if err != nil {
		span.SetTag("error", "true")
		herosentry.CaptureException(spanContext, err, "Failed to revert selective claim")
		log.Printf("Error reverting selective claim for call %s: %v", req.CallSid, err)
		return nil, connect.NewError(connect.CodeInternal, err)
	}

	span.SetTag("reverted", fmt.Sprintf("%t", reverted))

	// Log the result
	if reverted {
		log.Printf("Successfully reverted selective claim for call %s", req.CallSid)
	} else {
		log.Printf("Call %s was not in pending_selective_assignment state, no changes made", req.CallSid)
	}

	// Return success response
	span.SetTag("rpc.status", "success")
	return &conversationv1.RevertSelectiveClaimResponse{
		Success:  true,
		Reverted: reverted,
	}, nil
}

// ListCalls returns paginated call records with optional filtering
func (uc *cellularCallUsecase) ListCalls(
	ctx context.Context,
	req *conversationv1.ListCallsRequest,
) (*conversationv1.ListCallsResponse, error) {
	// The RPCServiceInterceptor already creates the transaction, so we just create a child span
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "CellularCallUseCase.ListCalls")
	defer finishSpan()

	span.SetTag("page", fmt.Sprintf("%d", req.Page))
	span.SetTag("page_size", fmt.Sprintf("%d", req.PageSize))

	// Parse date filters
	var startDate, endDate *time.Time
	if req.StartDate != "" {
		t, err := time.Parse(time.RFC3339, req.StartDate)
		if err != nil {
			span.SetTag("error", "true")
			herosentry.CaptureException(spanContext, err, "Failed to parse start date")
			log.Printf("Error parsing start date: %v", err)
			return nil, fmt.Errorf("invalid start date format: %w", err)
		}
		startDate = &t
		span.SetTag("start_date", req.StartDate)
	}
	if req.EndDate != "" {
		t, err := time.Parse(time.RFC3339, req.EndDate)
		if err != nil {
			span.SetTag("error", "true")
			herosentry.CaptureException(spanContext, err, "Failed to parse end date")
			log.Printf("Error parsing end date: %v", err)
			return nil, fmt.Errorf("invalid end date format: %w", err)
		}
		endDate = &t
		span.SetTag("end_date", req.EndDate)
	}

	// Set defaults
	page := int(req.Page)
	if page < 1 {
		page = 1
	}
	pageSize := int(req.PageSize)
	if pageSize < 1 {
		pageSize = 50
	}
	if pageSize > 100 {
		pageSize = 100
	}
	sortOrder := req.SortOrder
	if sortOrder == "" {
		sortOrder = "desc"
	}

	// Get calls from repository
	calls, totalCount, err := uc.callQueueRepo.ListCalls(spanContext, startDate, endDate, page, pageSize, sortOrder, req.State, req.Direction)
	if err != nil {
		span.SetTag("error", "true")
		herosentry.CaptureException(spanContext, err, "Failed to list calls from repository")
		log.Printf("Error listing calls: %v", err)
		return nil, fmt.Errorf("failed to list calls: %w", err)
	}

	// Convert repository calls to proto messages
	protoCalls := make([]*conversationv1.QueuedCall, 0, len(calls))
	for i := range calls {
		call := &calls[i]
		protoCall := &conversationv1.QueuedCall{
			CallSid:     call.CallSID,
			Caller:      call.Caller,
			CallerName:  call.CallerName,
			EnqueueTime: timestamppb.New(call.EnqueueTime),
			Attributes:  call.Attributes,
			Priority:    0, // Default priority if not stored
			AssetId:     call.AssetID,
			SituationId: call.SituationID,
			Notes:       "",
			History:     []*conversationv1.CallEvent{},
			Direction:   call.Direction,
		}

		if call.CallStartTime != nil {
			protoCall.CallStartTime = timestamppb.New(*call.CallStartTime)
		}
		if call.CallEndTime != nil {
			protoCall.CallEndTime = timestamppb.New(*call.CallEndTime)
		}
		if call.LastHoldStart != nil {
			protoCall.LastHoldStart = timestamppb.New(*call.LastHoldStart)
		}

		protoCalls = append(protoCalls, protoCall)
	}

	span.SetTag("returned_calls", fmt.Sprintf("%d", len(protoCalls)))
	span.SetTag("total_count", fmt.Sprintf("%d", totalCount))

	// Calculate next page if there are more results
	var nextPage *int32
	// Safe conversion with bounds checking
	if totalCount > math.MaxInt32 {
		totalCount = math.MaxInt32
	}
	totalPages := (totalCount + pageSize - 1) / pageSize // Ceiling division
	if page < totalPages {
		nextPageInt := page + 1
		if nextPageInt >= 0 && nextPageInt <= math.MaxInt32 {
			nextPageValue := int32(nextPageInt)
			nextPage = &nextPageValue
		}
	}

	// Safe conversion for total count
	var totalCountInt32 int32
	if totalCount <= math.MaxInt32 && totalCount >= 0 {
		totalCountInt32 = int32(totalCount) // #nosec G115 - bounds checked above
	} else {
		totalCountInt32 = math.MaxInt32
	}

	return &conversationv1.ListCallsResponse{
		Calls:      protoCalls,
		TotalCount: totalCountInt32,
		NextPage:   nextPage,
	}, nil
}

// formatAgentIdentityWithSession converts a raw asset ID to the formatted identity used by clients with optional session suffix
func (uc *cellularCallUsecase) formatAgentIdentityWithSession(ctx context.Context, assetID string, sessionSuffix string) (string, error) {
	if assetID == "" {
		return "", fmt.Errorf("empty asset ID")
	}

	const prefixLength = 8
	var idPrefixPart string
	var namePartVal string

	// Use assetID for prefix part
	if len(assetID) > prefixLength {
		idPrefixPart = assetID[:prefixLength]
	} else {
		idPrefixPart = assetID // Use full assetID if shorter than prefixLength
	}

	// Try to get asset name for the name part
	getAssetReq := &assetsv2.GetAssetRequest{Id: assetID}
	assetResp, err := uc.assetsClient.GetAsset(ctx, connect.NewRequest(getAssetReq))

	var fetchedAssetName string
	if err != nil {
		log.Printf("Warning: Failed to fetch asset name for %s, using asset ID as fallback for name part: %v", assetID, err)
		fetchedAssetName = assetID // Use asset ID as fallback for name part
	} else {
		fetchedAssetName = assetResp.Msg.Asset.Name
		if fetchedAssetName == "" {
			log.Printf("Warning: Asset %s has empty name, using asset ID as fallback for name part", assetID)
			fetchedAssetName = assetID // Use asset ID as fallback for name part
		}
	}

	// Sanitize the fetched asset name (which could be the assetID fallback) for Twilio
	namePartVal = sanitizeTwilioIdentity(fetchedAssetName)

	// Construct the identity string in the new format: idPrefixPart-namePartVal-sessionSuffix
	var identity string
	if sessionSuffix != "" {
		identity = fmt.Sprintf("%s-%s-%s", idPrefixPart, namePartVal, sessionSuffix)
	} else {
		// If no sessionSuffix, omit it and its preceding separator from the format
		identity = fmt.Sprintf("%s-%s", idPrefixPart, namePartVal)
	}

	return identity, nil
}

// getOrgAPIUserBasicAuthByUserId fetches the org API user credentials and returns username, password, and a preformatted "username:password" string.
func (uc *cellularCallUsecase) getOrgAPIUserBasicAuthByUserId(ctx context.Context, userId string) (string, string, string, error) {
	getOrgAPIUserPrivateRequest := &orgs.GetOrgAPIUserPrivateByIdRequest{UserId: userId}
	resp, err := uc.orgClient.GetOrgAPIUserPrivateById(ctx, connect.NewRequest(getOrgAPIUserPrivateRequest))
	if err != nil {
		return "", "", "", err
	}
	if resp == nil || resp.Msg == nil || resp.Msg.OrgApiUser == nil {
		return "", "", "", fmt.Errorf("org API user lookup returned empty result")
	}
	username := resp.Msg.OrgApiUser.Id
	password := resp.Msg.OrgApiUser.RawPassword
	basicAuth := username + ":" + password
	return username, password, basicAuth, nil
}

// buildAuthedURL constructs a full https URL using embedded basic-auth against the communications public domain.
// pathWithLeadingSlashAndQuery should start with "/" and include any query string if needed.
func (uc *cellularCallUsecase) buildAuthedURL(username, password, pathWithLeadingSlashAndQuery string) string {
	return fmt.Sprintf("https://%s:%s@%s%s", username, password, uc.commsServerPublicDomain, pathWithLeadingSlashAndQuery)
}

// updateSituationReporterName updates the reporter name for a given situation ID
func (uc *cellularCallUsecase) updateSituationReporterName(ctx context.Context, situationID string, reporterName string) error {
	if situationID == "" || reporterName == "" {
		return fmt.Errorf("invalid situation update parameters")
	}
	req := &situationsv2.UpdateSituationRequest{
		Situation: &situationsv2.Situation{
			Id:           situationID,
			ReporterName: reporterName,
		},
	}
	_, err := uc.situationClient.UpdateSituation(ctx, connect.NewRequest(req))
	return err
}

// UpdateCallerName updates the caller name for a call
func (uc *cellularCallUsecase) UpdateCallerName(ctx context.Context, req *conversationv1.UpdateCallerNameRequest) (*conversationv1.UpdateCallerNameResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "CellularCallUsecase.UpdateCallerName")
	defer finishSpan()
	span.SetTag("call_sid", req.CallSid)
	span.SetTag("caller_name", req.CallerName)

	log.Printf("UpdateCallerName: Updating caller name for call %s to: %s", req.CallSid, req.CallerName)

	// Validate request
	if req.CallSid == "" {
		span.SetTag("error", "true")
		span.SetTag("error.type", "validation")
		herosentry.CaptureException(spanContext, fmt.Errorf("missing call SID"), herosentry.ErrorTypeValidation, "Missing call SID for UpdateCallerName")
		return &conversationv1.UpdateCallerNameResponse{
			Success: false,
			Message: "Call SID is required",
		}, nil
	}

	// Update the caller name in the database (call_queue). If not found, fall back to forwarded-call path
	err := uc.callQueueRepo.UpdateCallerName(spanContext, req.CallSid, req.CallerName)
	if err != nil {
		// If the call was not found in call_queue, attempt forwarded-call fallback via call_forward_events
		if errors.Is(err, data.ErrCallNotFound) {
			log.Printf("UpdateCallerName: Call %s not found in call_queue; attempting forwarded-call fallback", req.CallSid)
			// Look up any forward event by this CallSid and update the linked situation reporter name
			forwardEvt, found, fwdErr := uc.callQueueRepo.GetCallForwardEventByCallSid(spanContext, req.CallSid)
			if fwdErr != nil {
				span.SetTag("error", "true")
				herosentry.CaptureException(spanContext, fwdErr, herosentry.ErrorTypeDatabase, "Failed to lookup call_forward_events for UpdateCallerName fallback")
				return &conversationv1.UpdateCallerNameResponse{
					Success: false,
					Message: fmt.Sprintf("Failed to update caller name: %v", fwdErr),
				}, nil
			}
			if found && forwardEvt.SituationID != nil && *forwardEvt.SituationID != "" && req.CallerName != "" {
				// Update situation reporter name directly
				upErr := uc.updateSituationReporterName(spanContext, *forwardEvt.SituationID, req.CallerName)
				if upErr != nil {
					log.Printf("UpdateCallerName: Warning - failed to update situation reporter name for forwarded call: %v", upErr)
					return &conversationv1.UpdateCallerNameResponse{
						Success: false,
						Message: fmt.Sprintf("Failed to update situation reporter name: %v", upErr),
					}, nil
				}
				log.Printf("UpdateCallerName: Successfully updated forwarded call's situation %s reporter name to: %s", *forwardEvt.SituationID, req.CallerName)
				return &conversationv1.UpdateCallerNameResponse{
					Success: true,
					Message: "Caller name updated successfully (forwarded call)",
				}, nil
			}
			// Not found in forwarded events either
			return &conversationv1.UpdateCallerNameResponse{
				Success: false,
				Message: "Call not found",
			}, nil
		}

		span.SetTag("error", "true")
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to update caller name")
		log.Printf("UpdateCallerName: Failed to update caller name for call %s: %v", req.CallSid, err)
		return &conversationv1.UpdateCallerNameResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to update caller name: %v", err),
		}, nil
	}

	// Also update the associated situation if it exists
	call, found, err := uc.callQueueRepo.GetCallByCallSID(spanContext, req.CallSid)
	if err != nil {
		log.Printf("UpdateCallerName: Warning - failed to get call for situation update: %v", err)
	} else if found && call.SituationID != "" && req.CallerName != "" {
		// Update the situation's reporter name
		err := uc.updateSituationReporterName(spanContext, call.SituationID, req.CallerName)
		if err != nil {
			log.Printf("UpdateCallerName: Warning - failed to update situation reporter name: %v", err)
		} else {
			log.Printf("UpdateCallerName: Successfully updated situation %s reporter name to: %s", call.SituationID, req.CallerName)
		}
	}

	log.Printf("UpdateCallerName: Successfully updated caller name for call %s", req.CallSid)
	return &conversationv1.UpdateCallerNameResponse{
		Success: true,
		Message: "Caller name updated successfully",
	}, nil
}
