root = "."
testdata_dir = "testdata"
tmp_dir = "tmp"

[build]
  # Match prod closer: FIPS + trim path
  cmd = "GOFIPS140=v1.0.0 go build -trimpath -o ./tmp/main ./cmd/server"
  bin = "./tmp/main"

  # Snappy but stable
  delay = 400
  kill_delay = "1s"
  send_interrupt = true
  stop_on_error = true

  include_ext = ["go", "mod", "sum"]
  exclude_dir = ["tmp", "vendor", ".git", "node_modules"]
  exclude_regex = ["_test.go"]

  log = "build-errors.log"

[color]
  build = "yellow"
  main  = "magenta"
  runner = "green"
  watcher = "cyan"

[log]
  time = true

[misc]
  clean_on_exit = false         # keep binary/logs for post-mortem

[screen]
  clear_on_rebuild = true
  keep_scroll = true