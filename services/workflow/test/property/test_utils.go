package test

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"strings"
	"testing"

	property "proto/hero/property/v1"
	propertyConnect "proto/hero/property/v1/propertyconnect"

	"connectrpc.com/connect"
	"google.golang.org/protobuf/types/known/structpb"
)

const ServiceBaseURL = "http://localhost:9086" // Property service runs on port 9086

// ANSI color codes for terminal output
const (
	ColorReset  = "\033[0m"
	ColorRed    = "\033[31m"
	ColorGreen  = "\033[32m"
	ColorYellow = "\033[33m"
	ColorBlue   = "\033[34m"
	ColorPurple = "\033[35m"
	ColorCyan   = "\033[36m"
	ColorWhite  = "\033[37m"
	ColorBold   = "\033[1m"

	// Background colors
	BgRed    = "\033[41m"
	BgGreen  = "\033[42m"
	BgYellow = "\033[43m"
)

// Color helper functions for better test output
func Green(text string) string {
	return ColorGreen + text + ColorReset
}

func Red(text string) string {
	return ColorRed + text + ColorReset
}

func Yellow(text string) string {
	return ColorYellow + text + ColorReset
}

func Blue(text string) string {
	return ColorBlue + text + ColorReset
}

func Cyan(text string) string {
	return ColorCyan + text + ColorReset
}

func Bold(text string) string {
	return ColorBold + text + ColorReset
}

func Purple(text string) string {
	return ColorPurple + text + ColorReset
}

// Status indicators
func Success(text string) string {
	return Green("✅ " + text)
}

func Failure(text string) string {
	return Red("❌ " + text)
}

func Warning(text string) string {
	return Yellow("⚠️  " + text)
}

func Info(text string) string {
	return Blue("ℹ️  " + text)
}

func Progress(text string) string {
	return Cyan("🔄 " + text)
}

func Header(text string) string {
	return Bold(Cyan("━━━ " + text + " ━━━"))
}

func Subheader(text string) string {
	return Bold(Purple("▶ " + text))
}

// AddAuthHeader adds an authentication header to the HTTP client.
// It reads the token directly from the token.txt file.
func AddAuthHeader(client *http.Client) {
	client.Transport = &AuthTransport{
		base: client.Transport,
	}
}

// AuthTransport is a custom RoundTripper that adds authorization headers.
type AuthTransport struct {
	base http.RoundTripper
}

// RoundTrip implements the http.RoundTripper interface
func (t *AuthTransport) RoundTrip(req *http.Request) (*http.Response, error) {
	token, err := getAccessToken()
	if err != nil {
		fmt.Printf("%s\n", Warning(fmt.Sprintf("Could not get access token: %v", err)))
	} else {
		req.Header.Set("Authorization", "Bearer "+token)
	}

	// If the base RoundTripper is nil, use the default one
	if t.base == nil {
		return http.DefaultTransport.RoundTrip(req)
	}

	return t.base.RoundTrip(req)
}

// getAccessToken retrieves the access token for authentication
// It reads the token directly from the token.txt file
func getAccessToken() (string, error) {
	// Read from token.txt file
	tokenBytes, err := os.ReadFile("token.txt")
	if err != nil {
		return "", fmt.Errorf("failed to read token.txt: %w", err)
	}

	// Clean the token (remove whitespace)
	token := strings.TrimSpace(string(tokenBytes))
	if token == "" {
		return "", fmt.Errorf("token.txt is empty")
	}

	return token, nil
}

// MockPropertyFileAttachment creates a mock PropertyFileReference for testing
func MockPropertyFileAttachment(fileID, caption, displayName, fileCategory string, displayOrder int32) *property.PropertyFileReference {
	return &property.PropertyFileReference{
		Id:           "", // Will be generated by the server
		PropertyId:   "", // Will be set by the server
		FileId:       fileID,
		Caption:      caption,
		DisplayName:  displayName,
		DisplayOrder: displayOrder,
		FileCategory: fileCategory,
		Metadata:     &structpb.Struct{}, // Use empty struct instead of nil
	}
}

// MockAddPropertyFileAttachmentRequest creates a mock AddPropertyFileAttachmentRequest for testing
func MockAddPropertyFileAttachmentRequest(propertyID string, fileAttachment *property.PropertyFileReference) *property.AddPropertyFileAttachmentRequest {
	return &property.AddPropertyFileAttachmentRequest{
		PropertyId:     propertyID,
		FileAttachment: fileAttachment,
	}
}

// MockListPropertyFileAttachmentsRequest creates a mock ListPropertyFileAttachmentsRequest for testing
func MockListPropertyFileAttachmentsRequest(propertyID string, fileCategory string, pageSize int32) *property.ListPropertyFileAttachmentsRequest {
	return &property.ListPropertyFileAttachmentsRequest{
		PropertyId:   propertyID,
		FileCategory: fileCategory,
		PageSize:     pageSize,
		PageToken:    "",
	}
}

// MockRemovePropertyFileAttachmentRequest creates a mock RemovePropertyFileAttachmentRequest for testing
func MockRemovePropertyFileAttachmentRequest(propertyID, attachmentID string) *property.RemovePropertyFileAttachmentRequest {
	return &property.RemovePropertyFileAttachmentRequest{
		PropertyId:   propertyID,
		AttachmentId: attachmentID,
	}
}

// CreateTestPropertyWithAttachments creates a test property with multiple file attachments for testing
func CreateTestPropertyWithAttachments(t *testing.T, propertyClient propertyConnect.PropertyServiceClient, ctx context.Context) (string, []string) {
	// Create dynamic form data
	propertyDetails := map[string]interface{}{
		"description":  "Test evidence item with attachments",
		"quantity":     "1",
		"category":     "Weapon",
		"identifiers":  "Test Manufacturer Test Model",
		"owner":        "",
		"condition":    "Good",
		"serialNumber": "SN123456789",
		"value":        "1000.00",
		"propertyType": property.NIBRSPropertyType_PROPERTY_TYPE_SEIZED.String(),
	}

	detailsStruct, err := structpb.NewStruct(propertyDetails)
	if err != nil {
		t.Fatalf("Failed to create details struct: %v", err)
	}

	// Create a test property
	testProperty := &property.Property{
		OrgId:            1,
		PropertyNumber:   "TEST-MEDIA-PROP-001",
		PropertyStatus:   property.PropertyStatus_PROPERTY_STATUS_CHECKED_IN,
		IsEvidence:       true,
		RetentionPeriod:  "30 days",
		DisposalType:     property.PropertyDisposalType_PROPERTY_DISPOSAL_TYPE_UNSPECIFIED,
		Notes:            "Test property with attachments",
		CurrentCustodian: "Officer Smith",
		CurrentLocation:  "Evidence Room A",
		Details:          detailsStruct,
	}

	req := &property.CreatePropertyRequest{
		Property: testProperty,
	}

	resp, err := propertyClient.CreateProperty(ctx, connect.NewRequest(req))
	if err != nil {
		t.Fatalf("Failed to create test property: %v", err)
	}

	propertyID := resp.Msg.Property.Id
	attachmentIDs := make([]string, 0)

	// Add multiple test attachments
	testAttachments := []struct {
		fileID       string
		caption      string
		displayName  string
		fileCategory string
		displayOrder int32
	}{
		{"test-file-id-001", "Test photo 1", "photo_001.jpg", "photo", 1},
		{"test-file-id-002", "Test document 1", "document_001.pdf", "document", 2},
		{"test-file-id-003", "Test photo 2", "photo_002.jpg", "photo", 3},
		{"test-file-id-004", "Test video 1", "video_001.mp4", "video", 4},
		{"test-file-id-005", "Test document 2", "document_002.pdf", "document", 5},
	}

	for _, attachment := range testAttachments {
		fileAttachment := MockPropertyFileAttachment(
			attachment.fileID,
			attachment.caption,
			attachment.displayName,
			attachment.fileCategory,
			attachment.displayOrder,
		)

		addReq := MockAddPropertyFileAttachmentRequest(propertyID, fileAttachment)
		addResp, err := propertyClient.AddPropertyFileAttachment(ctx, connect.NewRequest(addReq))
		if err != nil {
			t.Fatalf("Failed to add test attachment: %v", err)
		}

		attachmentIDs = append(attachmentIDs, addResp.Msg.FileAttachment.Id)
	}

	return propertyID, attachmentIDs
}
