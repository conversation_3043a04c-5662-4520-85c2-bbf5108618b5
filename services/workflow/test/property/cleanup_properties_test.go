package test

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"strings"
	"testing"
	"time"

	property "proto/hero/property/v1"
	propertyConnect "proto/hero/property/v1/propertyconnect"

	"connectrpc.com/connect"
)

// TestCleanupAllProperties removes all test properties from the database
// This test should be run after other tests to clean up test data
func TestCleanupAllProperties(t *testing.T) {
	httpClient := http.DefaultClient
	AddAuthHeader(httpClient)
	ctx, cancel := context.WithTimeout(context.Background(), 300*time.Second)
	defer cancel()

	propertyClient := propertyConnect.NewPropertyServiceClient(httpClient, ServiceBaseURL)

	t.Logf("%s", Header("🧹 Property Cleanup Test"))

	// Step 1: Search for test properties
	t.Run("SearchTestProperties", func(t *testing.T) {
		t.Logf("%s", Subheader("Searching for test properties to clean up"))

		// Create date range for the last 24 hours
		now := time.Now()
		oneDayAgo := now.Add(-24 * time.Hour)

		dateRange := &property.DateRange{
			From: oneDayAgo.Format(time.RFC3339),
			To:   now.Format(time.RFC3339),
		}

		// Search strategies: date range + test-related keywords
		searchStrategies := []struct {
			name  string
			query string
		}{
			{"Recent Test Properties", "test property"},
			{"Recent API Tests", "API test"},
			{"Recent Test Evidence", "Test evidence"},
			{"Recent Test Officers", "test-officer"},
			{"Recent Test Cases", "CASE-2024"},
			{"Recent Test Data", "test data"},
			{"Recent Test Items", "test item"},
			{"Recent Test Evidence", "test evidence"},
		}

		var allTestProperties []*property.Property

		// Search with date range filter for each strategy
		for _, strategy := range searchStrategies {
			req := &property.SearchPropertiesRequest{
				Query:     strategy.query,
				PageSize:  100,
				DateRange: dateRange,
			}

			resp, err := propertyClient.SearchProperties(ctx, connect.NewRequest(req))
			if err != nil {
				t.Logf("%s", Warning(fmt.Sprintf("Search failed for '%s': %v", strategy.name, err)))
				continue
			}

			allTestProperties = append(allTestProperties, resp.Msg.Properties...)
			t.Logf("%s", Info(fmt.Sprintf("Found %d properties matching '%s' (last 24h)", len(resp.Msg.Properties), strategy.name)))
		}

		// Also search for properties created in the last 24 hours without text query
		// to catch any test properties that might not match our keywords
		req := &property.SearchPropertiesRequest{
			PageSize:  100,
			DateRange: dateRange,
		}

		resp, err := propertyClient.SearchProperties(ctx, connect.NewRequest(req))
		if err != nil {
			t.Logf("%s", Warning(fmt.Sprintf("Date range search failed: %v", err)))
		} else {
			// Filter recent properties for test indicators
			for _, prop := range resp.Msg.Properties {
				if isTestProperty(prop) {
					allTestProperties = append(allTestProperties, prop)
				}
			}
			t.Logf("%s", Info(fmt.Sprintf("Found %d additional test properties from date range search", len(resp.Msg.Properties))))
		}

		// Remove duplicates based on property ID
		uniqueProperties := make(map[string]*property.Property)
		for _, prop := range allTestProperties {
			uniqueProperties[prop.Id] = prop
		}

		t.Logf("%s", Info(fmt.Sprintf("Total unique test properties found: %d", len(uniqueProperties))))

		// Step 2: Delete test properties
		if len(uniqueProperties) > 0 {
			t.Run("DeleteTestProperties", func(t *testing.T) {
				t.Logf("%s", Subheader("Deleting test properties"))

				deletedCount := 0
				failedCount := 0

				for propertyID, prop := range uniqueProperties {
					req := &property.DeletePropertyRequest{
						Id: propertyID,
					}

					_, err := propertyClient.DeleteProperty(ctx, connect.NewRequest(req))
					if err != nil {
						t.Logf("%s", Warning(fmt.Sprintf("Failed to delete property %s: %v", propertyID, err)))
						failedCount++
					} else {
						t.Logf("%s", Info(fmt.Sprintf("Deleted property: %s (%s)", propertyID, prop.Notes)))
						deletedCount++
					}

					// Add a small delay to avoid overwhelming the service
					time.Sleep(100 * time.Millisecond)
				}

				t.Logf("%s", Success(fmt.Sprintf("Cleanup completed: %d deleted, %d failed", deletedCount, failedCount)))

				if failedCount > 0 {
					t.Logf("%s", Warning(fmt.Sprintf("%d properties could not be deleted (they may have been already deleted or don't exist)", failedCount)))
				}
			})
		} else {
			t.Logf("%s", Info("No test properties found to clean up"))
		}
	})

	// Step 3: Verify cleanup
	t.Run("VerifyCleanup", func(t *testing.T) {
		t.Logf("%s", Subheader("Verifying cleanup was successful"))

		// Search again to make sure test properties are gone
		req := &property.SearchPropertiesRequest{
			Query:    "test property",
			PageSize: 10,
		}

		resp, err := propertyClient.SearchProperties(ctx, connect.NewRequest(req))
		if err != nil {
			t.Logf("%s", Warning(fmt.Sprintf("Verification search failed: %v", err)))
		} else {
			remainingCount := len(resp.Msg.Properties)
			if remainingCount == 0 {
				t.Logf("%s", Success("All test properties successfully cleaned up"))
			} else {
				t.Logf("%s", Warning(fmt.Sprintf("%d test properties may still exist", remainingCount)))
			}
		}
	})

	t.Logf("%s", Success("Property cleanup test completed"))
}

// TestCleanupSpecificProperties removes properties with specific IDs
// This is useful for cleaning up properties created by specific tests
func TestCleanupSpecificProperties(t *testing.T) {
	httpClient := http.DefaultClient
	AddAuthHeader(httpClient)
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	propertyClient := propertyConnect.NewPropertyServiceClient(httpClient, ServiceBaseURL)

	t.Logf("%s", Header("🎯 Specific Property Cleanup Test"))

	// Read property IDs from the created_property_ids.txt file
	propertyIDs, err := readPropertyIDsFromFile("created_property_ids.txt")
	if err != nil {
		t.Logf("%s", Warning(fmt.Sprintf("Could not read property IDs from file: %v", err)))
		t.Logf("%s", Info("Skipping specific property cleanup"))
		return
	}

	if len(propertyIDs) == 0 {
		t.Logf("%s", Info("No property IDs found in file, skipping specific cleanup"))
		return
	}

	t.Logf("%s", Info(fmt.Sprintf("Found %d property IDs to clean up", len(propertyIDs))))

	// Delete each property
	deletedCount := 0
	failedCount := 0

	for _, propertyID := range propertyIDs {
		req := &property.DeletePropertyRequest{
			Id: propertyID,
		}

		_, err := propertyClient.DeleteProperty(ctx, connect.NewRequest(req))
		if err != nil {
			t.Logf("%s", Warning(fmt.Sprintf("Failed to delete property %s: %v", propertyID, err)))
			failedCount++
		} else {
			t.Logf("%s", Info(fmt.Sprintf("Deleted property: %s", propertyID)))
			deletedCount++
		}

		// Add a small delay to avoid overwhelming the service
		time.Sleep(100 * time.Millisecond)
	}

	t.Logf("%s", Success(fmt.Sprintf("Specific cleanup completed: %d deleted, %d failed", deletedCount, failedCount)))

	if failedCount > 0 {
		t.Logf("%s", Warning(fmt.Sprintf("%d properties could not be deleted", failedCount)))
	}
}

// readPropertyIDsFromFile reads property IDs from a file
func readPropertyIDsFromFile(filename string) ([]string, error) {
	// Read the file
	content, err := os.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("failed to read file %s: %w", filename, err)
	}

	// Split content into lines
	lines := strings.Split(string(content), "\n")

	var propertyIDs []string

	// Process each line
	for i, line := range lines {
		// Trim whitespace
		line = strings.TrimSpace(line)

		// Skip empty lines and comments
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		// Validate that the line looks like a property ID
		// Property IDs should be non-empty and not contain whitespace
		if line != "" && !strings.Contains(line, " ") {
			propertyIDs = append(propertyIDs, line)
		} else {
			// Log warning for invalid lines but continue processing
			fmt.Printf("warning: invalid property ID format at line %d: %q\n", i+1, line)
		}
	}

	return propertyIDs, nil
}

// isTestProperty determines if a property is likely a test property
// based on various indicators in the property data
func isTestProperty(prop *property.Property) bool {
	if prop == nil {
		return false
	}

	// Check for test-related keywords in various fields
	testKeywords := []string{
		"test", "TEST", "Test",
		"api", "API", "Api",
		"demo", "Demo", "DEMO",
		"sample", "Sample", "SAMPLE",
		"mock", "Mock", "MOCK",
		"fake", "Fake", "FAKE",
		"dummy", "Dummy", "DUMMY",
		"temporary", "Temporary", "TEMPORARY",
	}

	// Check notes field
	if prop.Notes != "" {
		notes := prop.Notes
		for _, keyword := range testKeywords {
			if strings.Contains(notes, keyword) {
				return true
			}
		}
	}

	// Check current custodian field
	if prop.CurrentCustodian != "" {
		custodian := prop.CurrentCustodian
		for _, keyword := range testKeywords {
			if strings.Contains(custodian, keyword) {
				return true
			}
		}
	}

	// Check current location field
	if prop.CurrentLocation != "" {
		location := prop.CurrentLocation
		for _, keyword := range testKeywords {
			if strings.Contains(location, keyword) {
				return true
			}
		}
	}

	// Check property ID for test patterns
	if prop.Id != "" {
		id := prop.Id
		if strings.Contains(id, "test") || strings.Contains(id, "TEST") {
			return true
		}
	}

	return false
}
