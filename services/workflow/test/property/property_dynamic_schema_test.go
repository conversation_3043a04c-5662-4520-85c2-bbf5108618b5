package test

import (
	"context"
	"fmt"
	"net/http"
	"testing"
	"time"

	entities "proto/hero/entity/v1"
	entitiesConnect "proto/hero/entity/v1/entityconnect"
	property "proto/hero/property/v1"
	propertyConnect "proto/hero/property/v1/propertyconnect"

	"connectrpc.com/connect"
	"google.golang.org/protobuf/types/known/structpb"
)

// TestPropertyAPI_DynamicSchemaFlow tests the complete flow of creating properties with dynamic schemas
func TestPropertyAPI_DynamicSchemaFlow(t *testing.T) {
	httpClient := http.DefaultClient
	AddAuthHeader(httpClient)
	ctx, cancel := context.WithTimeout(context.Background(), 120*time.Second)
	defer cancel()

	propertyClient := propertyConnect.NewPropertyServiceClient(httpClient, ServiceBaseURL)
	entityClient := entitiesConnect.NewEntityServiceClient(httpClient, ServiceBaseURL)

	t.Logf("%s", Header("🏠 Testing Property API with Dynamic Schemas"))

	var createdSchemaID string
	var createdPropertyID string
	var createdLegacyPropertyID string

	// Cleanup function to run at the end
	defer func() {
		t.Logf("%s", Info("Starting cleanup of test data..."))

		// Delete properties first
		if createdPropertyID != "" {
			delReq := &property.DeletePropertyRequest{Id: createdPropertyID}
			_, err := propertyClient.DeleteProperty(ctx, connect.NewRequest(delReq))
			if err != nil {
				t.Logf("%s", Warning(fmt.Sprintf("Failed to delete property %s: %v", createdPropertyID, err)))
			} else {
				t.Logf("%s", Success(fmt.Sprintf("Cleaned up property: %s", createdPropertyID)))
			}
		}

		if createdLegacyPropertyID != "" {
			delReq := &property.DeletePropertyRequest{Id: createdLegacyPropertyID}
			_, err := propertyClient.DeleteProperty(ctx, connect.NewRequest(delReq))
			if err != nil {
				t.Logf("%s", Warning(fmt.Sprintf("Failed to delete legacy property %s: %v", createdLegacyPropertyID, err)))
			} else {
				t.Logf("%s", Success(fmt.Sprintf("Cleaned up legacy property: %s", createdLegacyPropertyID)))
			}
		}

		// Delete schema
		if createdSchemaID != "" {
			delReq := &entities.DeleteAllVersionsOfEntitySchemaRequest{Id: createdSchemaID}
			_, err := entityClient.DeleteAllVersionsOfEntitySchema(ctx, connect.NewRequest(delReq))
			if err != nil {
				t.Logf("%s", Warning(fmt.Sprintf("Failed to delete schema %s: %v", createdSchemaID, err)))
			} else {
				t.Logf("%s", Success(fmt.Sprintf("Cleaned up schema: %s", createdSchemaID)))
			}
		}
	}()

	// Test 1: Create a dynamic property schema
	t.Run("CreatePropertySchema", func(t *testing.T) {
		t.Logf("%s", Subheader("Testing CreateEntitySchema for Properties"))

		// Create schema definition for property forms
		schemaDefinition := map[string]interface{}{
			"type": "object",
			"properties": map[string]interface{}{
				"description": map[string]interface{}{
					"type":        "string",
					"title":       "Description",
					"description": "Detailed description of the property",
				},
				"category": map[string]interface{}{
					"type":  "string",
					"title": "Category",
					"enum":  []interface{}{"weapon", "jewelry", "electronics", "currency", "drugs", "other"},
				},
				"serialNumber": map[string]interface{}{
					"type":  "string",
					"title": "Serial Number",
				},
				"identifiers": map[string]interface{}{
					"type":  "string",
					"title": "Make/Model/Brand",
				},
				"value": map[string]interface{}{
					"type":  "string",
					"title": "Estimated Value",
				},
				"condition": map[string]interface{}{
					"type":  "string",
					"title": "Condition",
					"enum":  []interface{}{"excellent", "good", "fair", "poor", "damaged"},
				},
				"quantity": map[string]interface{}{
					"type":  "string",
					"title": "Quantity",
				},
				"owner": map[string]interface{}{
					"type":  "string",
					"title": "Owner (if known)",
				},
			},
			"required": []interface{}{"description"},
		}

		schemaStruct, err := structpb.NewStruct(schemaDefinition)
		if err != nil {
			t.Fatalf("Failed to create schema struct: %v", err)
		}

		testSchema := &entities.EntitySchema{
			OrgId:            1,
			Name:             "Property Evidence Form v2",
			Description:      "Enhanced property evidence form with dynamic fields",
			SchemaDefinition: schemaStruct,
			Version:          1,
			EntityType:       entities.EntityType_ENTITY_TYPE_PROPERTY,
			Status:           entities.RecordStatus_RECORD_STATUS_ACTIVE,
			Tags:             []string{"property", "evidence", "dynamic-form", "v2"},
		}

		req := &entities.CreateEntitySchemaRequest{
			Schema: testSchema,
		}

		resp, err := entityClient.CreateEntitySchema(ctx, connect.NewRequest(req))
		if err != nil {
			t.Logf("%s", Failure("CreateEntitySchema failed"))
			t.Logf("%s", Info("Error: "+err.Error()))
			t.Fatalf("CreateEntitySchema failed: %v", err)
		}

		createdSchemaID = resp.Msg.Schema.Id
		t.Logf("%s", Success("CreateEntitySchema successful"))
		t.Logf("%s", Info(fmt.Sprintf("Created schema ID: %s", createdSchemaID)))

		// Verify the schema
		if createdSchemaID == "" {
			t.Fatalf("Created schema ID is empty")
		}
		if resp.Msg.Schema.EntityType != testSchema.EntityType {
			t.Errorf("Expected EntityType %d, got %d", testSchema.EntityType, resp.Msg.Schema.EntityType)
		}
	})

	// Test 2: Create property with dynamic schema
	t.Run("CreatePropertyWithDynamicSchema", func(t *testing.T) {
		t.Logf("%s", Subheader("Testing CreateProperty with Dynamic Schema"))

		if createdSchemaID == "" {
			t.Skip("Skipping test due to failed schema creation")
		}

		// Create dynamic form data
		dynamicDetails := map[string]interface{}{
			"description":  "Seized handgun from suspect",
			"category":     "weapon",
			"serialNumber": "HG789123456",
			"identifiers":  "Smith & Wesson Model 9mm",
			"value":        "850.00",
			"condition":    "good",
			"quantity":     "1",
			"owner":        "Unknown",
			"propertyType": "seized",
			"customField":  "Additional evidence notes", // Dynamic schemas can accept extra fields
		}

		detailsStruct, err := structpb.NewStruct(dynamicDetails)
		if err != nil {
			t.Fatalf("Failed to create details struct: %v", err)
		}

		testProperty := &property.Property{
			OrgId:            1,
			PropertyNumber:   "DYN-PROP-001",
			PropertyStatus:   property.PropertyStatus_PROPERTY_STATUS_COLLECTED,
			IsEvidence:       true,
			RetentionPeriod:  "indefinite",
			DisposalType:     property.PropertyDisposalType_PROPERTY_DISPOSAL_TYPE_UNSPECIFIED,
			Notes:            "Test property with dynamic schema",
			CurrentCustodian: "Detective Johnson",
			CurrentLocation:  "Evidence Room B-12",

			// Dynamic schema fields
			SchemaId:      createdSchemaID,
			SchemaVersion: 1,
			Details:       detailsStruct,

			// PropertySchema field removed from proto - use Details instead
		}

		req := &property.CreatePropertyRequest{
			Property: testProperty,
		}

		resp, err := propertyClient.CreateProperty(ctx, connect.NewRequest(req))
		if err != nil {
			t.Logf("%s", Failure("CreateProperty with dynamic schema failed"))
			t.Logf("%s", Info("Error: "+err.Error()))
			t.Fatalf("CreateProperty with dynamic schema failed: %v", err)
		}

		createdPropertyID = resp.Msg.Property.Id
		t.Logf("%s", Success("CreateProperty with dynamic schema successful"))
		t.Logf("%s", Info(fmt.Sprintf("Created property ID: %s", createdPropertyID)))

		// Verify the created property
		if createdPropertyID == "" {
			t.Fatalf("Created property ID is empty")
		}
		if resp.Msg.Property.SchemaId != testProperty.SchemaId {
			t.Errorf("Expected SchemaId %s, got %s", testProperty.SchemaId, resp.Msg.Property.SchemaId)
		}
		if resp.Msg.Property.SchemaVersion != testProperty.SchemaVersion {
			t.Errorf("Expected SchemaVersion %d, got %d", testProperty.SchemaVersion, resp.Msg.Property.SchemaVersion)
		}
		if resp.Msg.Property.Details == nil {
			t.Fatalf("Expected Details to be present, got nil")
		}

		// Verify details content
		detailsMap := resp.Msg.Property.Details.AsMap()
		if detailsMap["description"] != "Seized handgun from suspect" {
			t.Errorf("Expected description in details, got %v", detailsMap["description"])
		}
		if detailsMap["category"] != "weapon" {
			t.Errorf("Expected category in details, got %v", detailsMap["category"])
		}

		t.Logf("%s", Success("Dynamic schema property verification passed"))
	})

	// Test 3: Create property with legacy PropertySchema (for comparison)
	t.Run("CreatePropertyWithLegacySchema", func(t *testing.T) {
		t.Logf("%s", Subheader("Testing CreateProperty with Legacy PropertySchema"))

		// Create legacy-style property data
		legacyDetails := map[string]interface{}{
			"description":  "Recovered stolen jewelry",
			"quantity":     "3",
			"category":     "jewelry",
			"identifiers":  "Gold necklace, 2 rings",
			"owner":        "Jane Doe",
			"condition":    "excellent",
			"serialNumber": "",
			"value":        "2500.00",
			"propertyType": property.NIBRSPropertyType_PROPERTY_TYPE_RECOVERED.String(),
		}

		legacyDetailsStruct, err := structpb.NewStruct(legacyDetails)
		if err != nil {
			t.Fatalf("Failed to create legacy details struct: %v", err)
		}

		testProperty := &property.Property{
			OrgId:            1,
			PropertyNumber:   "LEG-PROP-001",
			PropertyStatus:   property.PropertyStatus_PROPERTY_STATUS_COLLECTED,
			IsEvidence:       true,
			RetentionPeriod:  "30 days",
			DisposalType:     property.PropertyDisposalType_PROPERTY_DISPOSAL_TYPE_UNSPECIFIED,
			Notes:            "Test property with legacy schema",
			CurrentCustodian: "Officer Martinez",
			CurrentLocation:  "Evidence Room A-5",

			// Legacy approach - use Details instead of PropertySchema
			Details: legacyDetailsStruct,

			// Dynamic schema fields should be empty for legacy properties
			SchemaId:      "",
			SchemaVersion: 0,
		}

		req := &property.CreatePropertyRequest{
			Property: testProperty,
		}

		resp, err := propertyClient.CreateProperty(ctx, connect.NewRequest(req))
		if err != nil {
			t.Logf("%s", Failure("CreateProperty with legacy schema failed"))
			t.Logf("%s", Info("Error: "+err.Error()))
			t.Fatalf("CreateProperty with legacy schema failed: %v", err)
		}

		createdLegacyPropertyID = resp.Msg.Property.Id
		t.Logf("%s", Success("CreateProperty with legacy schema successful"))
		t.Logf("%s", Info(fmt.Sprintf("Created legacy property ID: %s", createdLegacyPropertyID)))

		// Verify legacy property structure
		if resp.Msg.Property.Details == nil {
			t.Fatalf("Expected Details to be present for legacy property")
		}
		if resp.Msg.Property.SchemaId != "" {
			t.Errorf("Expected empty SchemaId for legacy property, got %s", resp.Msg.Property.SchemaId)
		}
		// Legacy properties now use Details instead of PropertySchema

		t.Logf("%s", Success("Legacy schema property verification passed"))
	})

	// Test 4: Retrieve and verify both property types
	t.Run("GetPropertiesAndVerifySchemaTypes", func(t *testing.T) {
		t.Logf("%s", Subheader("Testing GetProperty for both schema types"))

		if createdPropertyID == "" || createdLegacyPropertyID == "" {
			t.Skip("Skipping test due to failed property creation")
		}

		// Get dynamic schema property
		dynReq := &property.GetPropertyRequest{Id: createdPropertyID}
		dynResp, err := propertyClient.GetProperty(ctx, connect.NewRequest(dynReq))
		if err != nil {
			t.Fatalf("GetProperty for dynamic schema failed: %v", err)
		}

		// Verify dynamic property
		dynProp := dynResp.Msg.Property
		if dynProp.SchemaId == "" {
			t.Errorf("Dynamic property should have SchemaId")
		}
		if dynProp.Details == nil {
			t.Errorf("Dynamic property should have Details")
		}
		// PropertySchema field removed from proto

		// Get legacy schema property
		legReq := &property.GetPropertyRequest{Id: createdLegacyPropertyID}
		legResp, err := propertyClient.GetProperty(ctx, connect.NewRequest(legReq))
		if err != nil {
			t.Fatalf("GetProperty for legacy schema failed: %v", err)
		}

		// Verify legacy property
		legProp := legResp.Msg.Property
		if legProp.SchemaId != "" {
			t.Errorf("Legacy property should not have SchemaId, got %s", legProp.SchemaId)
		}
		if legProp.Details == nil {
			t.Errorf("Legacy property should have Details")
		}
		// PropertySchema field removed from proto

		t.Logf("%s", Success("Schema type verification passed"))
		t.Logf("%s", Info(fmt.Sprintf("Dynamic property: SchemaId=%s, Details present", dynProp.SchemaId)))
		t.Logf("%s", Info("Legacy property: PropertySchema present, no dynamic fields"))
	})

	// Test 5: Test schema validation behavior
	t.Run("TestSchemaValidation", func(t *testing.T) {
		t.Logf("%s", Subheader("Testing Schema Validation"))

		if createdSchemaID == "" {
			t.Skip("Skipping test due to failed schema creation")
		}

		// Test creating property with missing required fields
		invalidDetails := map[string]interface{}{
			"category": "weapon",
			// Missing required "description" field
		}

		invalidStruct, err := structpb.NewStruct(invalidDetails)
		if err != nil {
			t.Fatalf("Failed to create invalid details struct: %v", err)
		}

		invalidProperty := &property.Property{
			OrgId:          1,
			PropertyNumber: "INVALID-PROP-001",
			PropertyStatus: property.PropertyStatus_PROPERTY_STATUS_COLLECTED,
			SchemaId:       createdSchemaID,
			SchemaVersion:  1,
			Details:        invalidStruct,
		}

		req := &property.CreatePropertyRequest{
			Property: invalidProperty,
		}

		_, err = propertyClient.CreateProperty(ctx, connect.NewRequest(req))
		if err == nil {
			t.Errorf("Expected validation error for property with missing required fields, but creation succeeded")
		} else {
			t.Logf("%s", Success("Validation correctly rejected property with missing required fields"))
			t.Logf("%s", Info(fmt.Sprintf("Validation error: %s", err.Error())))
		}
	})

	t.Logf("%s", Success("All dynamic schema tests completed successfully!"))
}

// TestPropertyAPI_SchemaEvolution tests schema versioning and evolution
func TestPropertyAPI_SchemaEvolution(t *testing.T) {
	httpClient := http.DefaultClient
	AddAuthHeader(httpClient)
	ctx, cancel := context.WithTimeout(context.Background(), 90*time.Second)
	defer cancel()

	propertyClient := propertyConnect.NewPropertyServiceClient(httpClient, ServiceBaseURL)
	entityClient := entitiesConnect.NewEntityServiceClient(httpClient, ServiceBaseURL)

	t.Logf("%s", Header("🔄 Testing Property Schema Evolution"))

	var schemaV1ID, schemaV2ID string
	var propertyV1ID, propertyV2ID string

	// Cleanup
	defer func() {
		// Delete properties
		for _, propID := range []string{propertyV1ID, propertyV2ID} {
			if propID != "" {
				delReq := &property.DeletePropertyRequest{Id: propID}
				_, _ = propertyClient.DeleteProperty(ctx, connect.NewRequest(delReq))
			}
		}
		// Delete schemas
		for _, schemaID := range []string{schemaV1ID, schemaV2ID} {
			if schemaID != "" {
				delReq := &entities.DeleteAllVersionsOfEntitySchemaRequest{Id: schemaID}
				_, _ = entityClient.DeleteAllVersionsOfEntitySchema(ctx, connect.NewRequest(delReq))
			}
		}
	}()

	// Create Schema V1
	t.Run("CreateSchemaV1", func(t *testing.T) {
		schemaDefinition := map[string]interface{}{
			"type": "object",
			"properties": map[string]interface{}{
				"description": map[string]interface{}{
					"type":  "string",
					"title": "Description",
				},
				"category": map[string]interface{}{
					"type":  "string",
					"title": "Category",
				},
			},
			"required": []interface{}{"description"},
		}

		schemaStruct, _ := structpb.NewStruct(schemaDefinition)
		testSchema := &entities.EntitySchema{
			OrgId:            1,
			Name:             "Property Schema V1",
			Description:      "Initial property schema",
			SchemaDefinition: schemaStruct,
			Version:          1,
			EntityType:       entities.EntityType_ENTITY_TYPE_PROPERTY,
			Status:           entities.RecordStatus_RECORD_STATUS_ACTIVE,
		}

		req := &entities.CreateEntitySchemaRequest{Schema: testSchema}
		resp, err := entityClient.CreateEntitySchema(ctx, connect.NewRequest(req))
		if err != nil {
			t.Fatalf("CreateEntitySchema V1 failed: %v", err)
		}

		schemaV1ID = resp.Msg.Schema.Id
		t.Logf("%s", Success(fmt.Sprintf("Created Schema V1: %s", schemaV1ID)))
	})

	// Create Property with Schema V1
	t.Run("CreatePropertyWithSchemaV1", func(t *testing.T) {
		details := map[string]interface{}{
			"description": "Test item for V1 schema",
			"category":    "electronics",
		}

		detailsStruct, _ := structpb.NewStruct(details)
		testProperty := &property.Property{
			OrgId:          1,
			SchemaId:       schemaV1ID,
			SchemaVersion:  1,
			Details:        detailsStruct,
			PropertyStatus: property.PropertyStatus_PROPERTY_STATUS_COLLECTED,
		}

		req := &property.CreatePropertyRequest{Property: testProperty}
		resp, err := propertyClient.CreateProperty(ctx, connect.NewRequest(req))
		if err != nil {
			t.Fatalf("CreateProperty with V1 schema failed: %v", err)
		}

		propertyV1ID = resp.Msg.Property.Id
		t.Logf("%s", Success(fmt.Sprintf("Created Property with V1 Schema: %s", propertyV1ID)))
	})

	// Create Schema V2 (evolved)
	t.Run("CreateSchemaV2", func(t *testing.T) {
		schemaDefinition := map[string]interface{}{
			"type": "object",
			"properties": map[string]interface{}{
				"description": map[string]interface{}{
					"type":  "string",
					"title": "Description",
				},
				"category": map[string]interface{}{
					"type":  "string",
					"title": "Category",
				},
				"serialNumber": map[string]interface{}{ // NEW FIELD
					"type":  "string",
					"title": "Serial Number",
				},
				"location": map[string]interface{}{ // NEW FIELD
					"type":  "string",
					"title": "Found Location",
				},
			},
			"required": []interface{}{"description"}, // Same requirements
		}

		schemaStruct, _ := structpb.NewStruct(schemaDefinition)
		testSchema := &entities.EntitySchema{
			OrgId:            1,
			Name:             "Property Schema V2",
			Description:      "Enhanced property schema with new fields",
			SchemaDefinition: schemaStruct,
			Version:          2,
			EntityType:       entities.EntityType_ENTITY_TYPE_PROPERTY,
			Status:           entities.RecordStatus_RECORD_STATUS_ACTIVE,
		}

		req := &entities.CreateEntitySchemaRequest{Schema: testSchema}
		resp, err := entityClient.CreateEntitySchema(ctx, connect.NewRequest(req))
		if err != nil {
			t.Fatalf("CreateEntitySchema V2 failed: %v", err)
		}

		schemaV2ID = resp.Msg.Schema.Id
		t.Logf("%s", Success(fmt.Sprintf("Created Schema V2: %s", schemaV2ID)))
	})

	// Create Property with Schema V2
	t.Run("CreatePropertyWithSchemaV2", func(t *testing.T) {
		details := map[string]interface{}{
			"description":  "Test item for V2 schema",
			"category":     "weapon",
			"serialNumber": "V2-12345",             // NEW FIELD
			"location":     "Downtown parking lot", // NEW FIELD
		}

		detailsStruct, _ := structpb.NewStruct(details)
		testProperty := &property.Property{
			OrgId:          1,
			SchemaId:       schemaV2ID,
			SchemaVersion:  2,
			Details:        detailsStruct,
			PropertyStatus: property.PropertyStatus_PROPERTY_STATUS_COLLECTED,
		}

		req := &property.CreatePropertyRequest{Property: testProperty}
		resp, err := propertyClient.CreateProperty(ctx, connect.NewRequest(req))
		if err != nil {
			t.Fatalf("CreateProperty with V2 schema failed: %v", err)
		}

		propertyV2ID = resp.Msg.Property.Id
		t.Logf("%s", Success(fmt.Sprintf("Created Property with V2 Schema: %s", propertyV2ID)))
	})

	// Verify both properties coexist
	t.Run("VerifySchemaCoexistence", func(t *testing.T) {
		// Get V1 property
		v1Req := &property.GetPropertyRequest{Id: propertyV1ID}
		v1Resp, err := propertyClient.GetProperty(ctx, connect.NewRequest(v1Req))
		if err != nil {
			t.Fatalf("GetProperty V1 failed: %v", err)
		}

		// Get V2 property
		v2Req := &property.GetPropertyRequest{Id: propertyV2ID}
		v2Resp, err := propertyClient.GetProperty(ctx, connect.NewRequest(v2Req))
		if err != nil {
			t.Fatalf("GetProperty V2 failed: %v", err)
		}

		// Verify different schema versions
		v1Prop := v1Resp.Msg.Property
		v2Prop := v2Resp.Msg.Property

		if v1Prop.SchemaVersion != 1 {
			t.Errorf("Expected V1 property to have schema version 1, got %d", v1Prop.SchemaVersion)
		}
		if v2Prop.SchemaVersion != 2 {
			t.Errorf("Expected V2 property to have schema version 2, got %d", v2Prop.SchemaVersion)
		}

		// Verify V2 has additional fields
		v1Details := v1Prop.Details.AsMap()
		v2Details := v2Prop.Details.AsMap()

		if _, hasSerial := v1Details["serialNumber"]; hasSerial {
			t.Errorf("V1 property should not have serialNumber field")
		}
		if _, hasSerial := v2Details["serialNumber"]; !hasSerial {
			t.Errorf("V2 property should have serialNumber field")
		}

		t.Logf("%s", Success("Schema evolution test passed"))
		t.Logf("%s", Info(fmt.Sprintf("V1 Property fields: %d", len(v1Details))))
		t.Logf("%s", Info(fmt.Sprintf("V2 Property fields: %d", len(v2Details))))
	})

	t.Logf("%s", Success("Schema evolution tests completed successfully!"))
}
