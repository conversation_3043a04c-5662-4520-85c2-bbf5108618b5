package test

import (
	"context"
	"fmt"
	"net/http"
	"testing"
	"time"

	property "proto/hero/property/v1"
	propertyConnect "proto/hero/property/v1/propertyconnect"

	"connectrpc.com/connect"
)

// TestPropertySearch_Basic tests the basic property search functionality
func TestPropertySearch_Basic(t *testing.T) {
	httpClient := http.DefaultClient
	AddAuthHeader(httpClient)
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	propertyClient := propertyConnect.NewPropertyServiceClient(httpClient, ServiceBaseURL)

	t.Logf("%s", Header("🔍 Testing Property Search API"))

	// Test 1: Basic search with empty query (should return all properties)
	t.Run("EmptyQuery", func(t *testing.T) {
		t.Logf("%s", Subheader("Testing search with empty query"))

		req := &property.SearchPropertiesRequest{
			Query:    "",
			PageSize: 10,
		}

		resp, err := propertyClient.SearchProperties(ctx, connect.NewRequest(req))
		if err != nil {
			t.Logf("%s", Failure("SearchProperties failed"))
			t.Logf("%s", Info("Error: "+err.Error()))
			t.Fatalf("SearchProperties failed: %v", err)
		}

		t.Logf("%s", Success("SearchProperties API call successful"))
		t.Logf("%s", Info(fmt.Sprintf("Found %d properties", len(resp.Msg.Properties))))
		t.Logf("%s", Info(fmt.Sprintf("Total count: %d", resp.Msg.TotalCount)))

		// Even if no properties exist, the API should work
		if resp.Msg.Properties == nil {
			t.Logf("%s", Warning("Properties slice is nil (expected if no properties exist)"))
		} else {
			t.Logf("%s", Success("Properties slice is properly initialized"))
		}
	})

	// Test 2: Search with a specific query
	t.Run("SpecificQuery", func(t *testing.T) {
		t.Logf("%s", Subheader("Testing search with specific query"))

		req := &property.SearchPropertiesRequest{
			Query:    "test",
			PageSize: 5,
		}

		resp, err := propertyClient.SearchProperties(ctx, connect.NewRequest(req))
		if err != nil {
			t.Logf("%s", Failure("SearchProperties with query failed"))
			t.Logf("%s", Info("Error: "+err.Error()))
			t.Fatalf("SearchProperties with query failed: %v", err)
		}

		t.Logf("%s", Success("SearchProperties with query successful"))
		t.Logf("%s", Info(fmt.Sprintf("Found %d properties matching 'test'", len(resp.Msg.Properties))))
	})

	// Test 3: Search with pagination
	t.Run("Pagination", func(t *testing.T) {
		t.Logf("%s", Subheader("Testing search with pagination"))

		req := &property.SearchPropertiesRequest{
			Query:    "",
			PageSize: 3,
		}

		resp, err := propertyClient.SearchProperties(ctx, connect.NewRequest(req))
		if err != nil {
			t.Logf("%s", Failure("SearchProperties with pagination failed"))
			t.Logf("%s", Info("Error: "+err.Error()))
			t.Fatalf("SearchProperties with pagination failed: %v", err)
		}

		t.Logf("%s", Success("SearchProperties with pagination successful"))
		t.Logf("%s", Info(fmt.Sprintf("Page size: %d, Found: %d", req.PageSize, len(resp.Msg.Properties))))

		if len(resp.Msg.Properties) > int(req.PageSize) {
			t.Logf("%s", Warning("More results than page size (this might be expected)"))
		}
	})

	// Test 4: Search with ordering
	t.Run("Ordering", func(t *testing.T) {
		t.Logf("%s", Subheader("Testing search with ordering"))

		req := &property.SearchPropertiesRequest{
			Query:    "",
			PageSize: 5,
			OrderBy:  property.SearchOrderBy_SEARCH_ORDER_BY_CREATED_AT,
		}

		resp, err := propertyClient.SearchProperties(ctx, connect.NewRequest(req))
		if err != nil {
			t.Logf("%s", Failure("SearchProperties with ordering failed"))
			t.Logf("%s", Info("Error: "+err.Error()))
			t.Fatalf("SearchProperties with ordering failed: %v", err)
		}

		t.Logf("%s", Success("SearchProperties with ordering successful"))
		t.Logf("%s", Info(fmt.Sprintf("Found %d properties ordered by created_at", len(resp.Msg.Properties))))
	})

	t.Logf("%s", Success("All property search tests completed successfully"))
}
