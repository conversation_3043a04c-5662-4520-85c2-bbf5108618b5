#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Emojis and symbols
SUCCESS="✅"
FAILURE="❌"
WARNING="⚠️"
INFO="ℹ️"
PROGRESS="🔄"
CLEANUP="🧹"

# Helper functions for colored output
print_header() {
    echo -e "\n${BOLD}${CYAN}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
    echo -e "${BOLD}${CYAN}━━━ $1 ━━━${NC}"
    echo -e "${BOLD}${CYAN}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}\n"
}

print_success() {
    echo -e "${GREEN}${SUCCESS} $1${NC}"
}

print_failure() {
    echo -e "${RED}${FAILURE} $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}${WARNING} $1${NC}"
}

print_info() {
    echo -e "${BLUE}${INFO} $1${NC}"
}

print_progress() {
    echo -e "${CYAN}${PROGRESS} $1${NC}"
}

print_cleanup() {
    echo -e "${PURPLE}${CLEANUP} $1${NC}"
}

# Property service test runner
# Usage: ./run_tests.sh [crud|list|batch|custody|status|filters|errors|all|populate|cleanup] [nocache]
# Default (no arguments): runs all tests

print_header "PROPERTY SERVICE TEST RUNNER"

# Default test type
TEST_TYPE="all"
NO_CACHE=""

# Parse command line arguments
if [ "$1" == "crud" ]; then
  TEST_TYPE="crud"
  print_info "Running CRUD tests..."
elif [ "$1" == "list" ]; then
  TEST_TYPE="list"
  print_info "Running list and search tests..."
elif [ "$1" == "batch" ]; then
  TEST_TYPE="batch"
  print_info "Running batch operation tests..."
elif [ "$1" == "custody" ]; then
  TEST_TYPE="custody"
  print_info "Running custody chain tests..."
elif [ "$1" == "status" ]; then
  TEST_TYPE="status"
  print_info "Running status and permissions tests..."
elif [ "$1" == "filters" ]; then
  TEST_TYPE="filters"
  print_info "Running list by filters tests..."
elif [ "$1" == "errors" ]; then
  TEST_TYPE="errors"
  print_info "Running error handling tests..."
elif [ "$1" == "media" ]; then
  TEST_TYPE="media"
  print_info "Running media operations tests..."
elif [ "$1" == "concurrent-media" ]; then
  TEST_TYPE="concurrent-media"
  print_info "Running concurrent media operations tests..."
elif [ "$1" == "populate" ]; then
  TEST_TYPE="populate"
  print_info "Running populate script to create test properties..."
elif [ "$1" == "cleanup" ]; then
  TEST_TYPE="cleanup"
  print_cleanup "Running cleanup script to delete test properties..."
elif [ "$1" == "all" ] || [ "$1" == "" ]; then
  TEST_TYPE="all"
  print_info "Running all property tests..."
elif [ "$1" == "help" ] || [ "$1" == "-h" ] || [ "$1" == "--help" ]; then
  echo "Property Service Test Runner"
  echo "==========================="
  echo ""
  echo "Usage: $0 [test-type] [options]"
  echo ""
  echo "Test Types:"
  echo "  crud        Basic CRUD operations (Create, Read, Update, Delete)"
  echo "  list        List and search operations"
  echo "  batch       Batch operations (BatchGetProperties)"
  echo "  custody     Custody chain operations"
  echo "  status      Status updates and permissions"
  echo "  filters     List by filters (case number, officer)"
  echo "  errors      Error handling tests"
  echo "  media       Media operations tests"
  echo "  concurrent-media Concurrent media operations tests"
  echo "  populate    Create test data in database"
  echo "  cleanup     Remove test data from database"
  echo "  all         Run all property tests (default)"
  echo ""
  echo "Options:"
  echo "  nocache     Bypass Go test cache (-count=1)"
  echo ""
  echo "Examples:"
  echo "  $0                    # Run all tests"
  echo "  $0 crud              # Run CRUD tests only"
  echo "  $0 populate          # Create test properties"
  echo "  $0 crud nocache      # Run CRUD tests bypassing cache"
  echo ""
  echo "Prerequisites:"
  echo "  - Token file: token.txt (contains COGNITO_ACCESS_TOKEN)"
  echo "  - Working directory: services/workflow/"
  echo "  - Property service running on localhost:9086"
  echo ""
  exit 0
else
  print_failure "Unknown test type: $1"
  echo -e "${YELLOW}Usage: $0 [crud|list|batch|custody|status|filters|errors|all|populate|cleanup] [nocache]${NC}"
  echo -e "${YELLOW}Use '$0 help' for detailed information.${NC}"
  exit 1
fi

# Check for nocache argument
if [ "$2" == "nocache" ]; then
  NO_CACHE="-count=1"
  print_warning "Bypassing test cache..."
fi

# Get directory of this script
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
TOKEN_FILE="$SCRIPT_DIR/token.txt"

print_progress "Validating setup..."

# Check if token file exists
if [ ! -f "$TOKEN_FILE" ]; then
  print_failure "Token file '$TOKEN_FILE' not found."
  echo -e "${YELLOW}Please create a token file with: echo 'your-token-here' > token.txt${NC}"
  exit 1
fi

# Read the token from the file
TOKEN=$(cat "$TOKEN_FILE" | tr -d '\n\r ')

# Verify token is not empty
if [ -z "$TOKEN" ]; then
  print_failure "No token found in '$TOKEN_FILE'."
  echo -e "${YELLOW}Please add your authentication token to the file.${NC}"
  exit 1
fi

if [ "$TOKEN" == "example-token-xxx" ]; then
  print_warning "Using example token. Please update token.txt with a real authentication token."
fi

print_success "Token loaded successfully"

# Export the token as environment variable
export COGNITO_ACCESS_TOKEN="$TOKEN"

# Change to the workflow directory
cd "$SCRIPT_DIR/../../"
print_info "Changed to workflow directory: $(pwd)"

# Function to run a test and capture result
run_test() {
    local test_name="$1"
    local test_pattern="$2"
    
    echo -e "\n${BOLD}${PURPLE}▶ Running $test_name tests...${NC}"
    
    if go test -v -run "$test_pattern" $NO_CACHE ./test/property; then
        print_success "$test_name tests PASSED"
        return 0
    else
        print_failure "$test_name tests FAILED"
        return 1
    fi
}

# Run the tests
case "$TEST_TYPE" in
  crud)
    run_test "CRUD" "^TestPropertyAPI_CRUD$"
    exit $?
    ;;
  list)
    run_test "List and Search" "^TestPropertyAPI_ListAndSearch$"
    exit $?
    ;;
  batch)
    run_test "Batch Operations" "^TestPropertyAPI_BatchOperations$"
    exit $?
    ;;
  custody)
    run_test "Custody Chain" "^TestPropertyAPI_CustodyChain$"
    exit $?
    ;;
  status)
    run_test "Status and Permissions" "^TestPropertyAPI_StatusAndPermissions$"
    exit $?
    ;;
  filters)
    run_test "List by Filters" "^TestPropertyAPI_ListByFilters$"
    exit $?
    ;;
  errors)
    run_test "Error Handling" "^TestPropertyAPI_ErrorHandling$"
    exit $?
    ;;
  media)
    run_test "Media Operations" "^TestPropertyAPI_MediaOperations$"
    exit $?
    ;;
  concurrent-media)
    run_test "Concurrent Media Operations" "^TestPropertyAPI_ConcurrentMediaOperations$"
    exit $?
    ;;
  populate)
    run_test "Populate" "^TestPopulateProperties$"
    exit $?
    ;;
  cleanup)
    run_test "Cleanup" "^TestCleanupAllProperties$"
    exit $?
    ;;
  all)
    print_header "RUNNING ALL PROPERTY SERVICE TESTS"
    
    # Track overall results
    OVERALL_RESULT=0
    
    # Run CRUD tests
    run_test "CRUD" "^TestPropertyAPI_CRUD$"
    CRUD_RESULT=$?
    if [ $CRUD_RESULT -ne 0 ]; then
        OVERALL_RESULT=1
    fi
    
    # Run list and search tests
    run_test "List and Search" "^TestPropertyAPI_ListAndSearch$"
    LIST_RESULT=$?
    if [ $LIST_RESULT -ne 0 ]; then
        OVERALL_RESULT=1
    fi
    
    # Run batch operation tests
    run_test "Batch Operations" "^TestPropertyAPI_BatchOperations$"
    BATCH_RESULT=$?
    if [ $BATCH_RESULT -ne 0 ]; then
        OVERALL_RESULT=1
    fi
    
    # Run custody chain tests
    run_test "Custody Chain" "^TestPropertyAPI_CustodyChain$"
    CUSTODY_RESULT=$?
    if [ $CUSTODY_RESULT -ne 0 ]; then
        OVERALL_RESULT=1
    fi
    
    # Run status and permissions tests
    run_test "Status and Permissions" "^TestPropertyAPI_StatusAndPermissions$"
    STATUS_RESULT=$?
    if [ $STATUS_RESULT -ne 0 ]; then
        OVERALL_RESULT=1
    fi
    
    # Run list by filters tests
    run_test "List by Filters" "^TestPropertyAPI_ListByFilters$"
    FILTERS_RESULT=$?
    if [ $FILTERS_RESULT -ne 0 ]; then
        OVERALL_RESULT=1
    fi
    
    # Run error handling tests
    run_test "Error Handling" "^TestPropertyAPI_ErrorHandling$"
    ERRORS_RESULT=$?
    if [ $ERRORS_RESULT -ne 0 ]; then
        OVERALL_RESULT=1
    fi
    
    # Run media operations tests
    run_test "Media Operations" "^TestPropertyAPI_MediaOperations$"
    MEDIA_RESULT=$?
    if [ $MEDIA_RESULT -ne 0 ]; then
        OVERALL_RESULT=1
    fi
    
    # Run concurrent media operations tests
    run_test "Concurrent Media Operations" "^TestPropertyAPI_ConcurrentMediaOperations$"
    CONCURRENT_MEDIA_RESULT=$?
    if [ $CONCURRENT_MEDIA_RESULT -ne 0 ]; then
        OVERALL_RESULT=1
    fi
    
    # Check for populate tests (if they exist)
    echo -e "\n${BOLD}${PURPLE}▶ Checking for populate tests...${NC}"
    if go test -v -run "^TestPopulateProperties$" $NO_CACHE ./test/property 2>/dev/null | grep -q "PASS\|FAIL"; then
        run_test "Populate" "^TestPopulateProperties$"
        POPULATE_RESULT=$?
        if [ $POPULATE_RESULT -ne 0 ]; then
            OVERALL_RESULT=1
        fi
    else
        print_info "No populate tests found (this is normal)"
        POPULATE_RESULT=0
    fi
    
    # Final summary
    echo ""
    print_header "TEST EXECUTION SUMMARY"
    
    if [ $CRUD_RESULT -eq 0 ]; then
        print_success "CRUD Tests: PASSED"
    else
        print_failure "CRUD Tests: FAILED"
    fi
    
    if [ $LIST_RESULT -eq 0 ]; then
        print_success "List and Search Tests: PASSED"
    else
        print_failure "List and Search Tests: FAILED"
    fi
    
    if [ $BATCH_RESULT -eq 0 ]; then
        print_success "Batch Operation Tests: PASSED"
    else
        print_failure "Batch Operation Tests: FAILED"
    fi
    
    if [ $CUSTODY_RESULT -eq 0 ]; then
        print_success "Custody Chain Tests: PASSED"
    else
        print_failure "Custody Chain Tests: FAILED"
    fi
    
    if [ $STATUS_RESULT -eq 0 ]; then
        print_success "Status and Permissions Tests: PASSED"
    else
        print_failure "Status and Permissions Tests: FAILED"
    fi
    
    if [ $FILTERS_RESULT -eq 0 ]; then
        print_success "List by Filters Tests: PASSED"
    else
        print_failure "List by Filters Tests: FAILED"
    fi
    
    if [ $ERRORS_RESULT -eq 0 ]; then
        print_success "Error Handling Tests: PASSED"
    else
        print_failure "Error Handling Tests: FAILED"
    fi
    
    if [ $MEDIA_RESULT -eq 0 ]; then
        print_success "Media Operations Tests: PASSED"
    else
        print_failure "Media Operations Tests: FAILED"
    fi

    if [ $CONCURRENT_MEDIA_RESULT -eq 0 ]; then
        print_success "Concurrent Media Operations Tests: PASSED"
    else
        print_failure "Concurrent Media Operations Tests: FAILED"
    fi
    
    if [ $POPULATE_RESULT -eq 0 ]; then
        print_success "Populate Tests: PASSED (or not present)"
    else
        print_failure "Populate Tests: FAILED"
    fi
    
    echo ""
    if [ $OVERALL_RESULT -eq 0 ]; then
        print_success "🎉 ALL TESTS PASSED! 🎉"
        echo -e "${GREEN}${BOLD}Property service is working correctly!${NC}"
    else
        print_failure "❌ SOME TESTS FAILED! ❌"
        echo -e "${RED}${BOLD}Please check the test output above for details.${NC}"
    fi
    
    exit $OVERALL_RESULT
    ;;
esac 