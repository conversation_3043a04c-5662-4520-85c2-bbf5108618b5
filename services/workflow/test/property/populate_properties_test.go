package test

import (
	"context"
	"crypto/rand"
	"database/sql"
	"fmt"
	"math/big"
	"net/http"
	"os"
	"strings"
	"testing"
	"time"

	property "proto/hero/property/v1"
	propertyConnect "proto/hero/property/v1/propertyconnect"

	"connectrpc.com/connect"
	_ "github.com/lib/pq"
	"google.golang.org/protobuf/types/known/structpb"
)

const (
	// Number of test properties to create for populate test
	populateNumProperties = 150
)

// Test data pools for property population
var (
	// Property types
	populatePropertyTypes = []property.NIBRSPropertyType{
		property.NIBRSPropertyType_PROPERTY_TYPE_FOUND,
		property.NIBRSPropertyType_PROPERTY_TYPE_SEIZED,
		property.NIBRSPropertyType_PROPERTY_TYPE_STOLEN,
		property.NIBRSPropertyType_PROPERTY_TYPE_RECOVERED,
	}

	// Property statuses
	populatePropertyStatuses = []property.PropertyStatus{
		property.PropertyStatus_PROPERTY_STATUS_COLLECTED,
		property.PropertyStatus_PROPERTY_STATUS_CHECKED_OUT,
		property.PropertyStatus_PROPERTY_STATUS_DISPOSED,
		property.PropertyStatus_PROPERTY_STATUS_MISSING,
		property.PropertyStatus_PROPERTY_STATUS_AWAITING_DISPOSITION,
	}

	// Disposal types
	populateDisposalTypes = []property.PropertyDisposalType{
		property.PropertyDisposalType_PROPERTY_DISPOSAL_TYPE_UNSPECIFIED,
		property.PropertyDisposalType_PROPERTY_DISPOSAL_TYPE_RELEASED,
		property.PropertyDisposalType_PROPERTY_DISPOSAL_TYPE_DESTROYED,
		property.PropertyDisposalType_PROPERTY_DISPOSAL_TYPE_AUCTIONED,
		property.PropertyDisposalType_PROPERTY_DISPOSAL_TYPE_AGENCY_RETAIN,
		property.PropertyDisposalType_PROPERTY_DISPOSAL_TYPE_TRANSFERRED,
	}

	// Property categories
	populatePropertyCategories = []string{
		"Firearm", "Electronics", "Jewelry", "Vehicle", "Clothing", "Tools",
		"Documents", "Currency", "Drugs", "Weapons", "Personal Items", "Equipment",
		"Computers", "Phones", "Cameras", "Audio Equipment", "Sports Equipment",
		"Musical Instruments", "Art", "Antiques", "Books", "Medications",
	}

	// Property makes/models/brands
	populatePropertyMakes = []string{
		"Apple", "Samsung", "Sony", "Canon", "Nikon", "Dell", "HP", "Lenovo",
		"Glock", "Smith & Wesson", "Beretta", "Ruger", "Colt", "Remington",
		"Toyota", "Honda", "Ford", "Chevrolet", "BMW", "Mercedes", "Audi",
		"Rolex", "Cartier", "Tiffany", "Pandora", "Kay Jewelers",
		"Snap-on", "Craftsman", "DeWalt", "Milwaukee", "Makita",
	}

	// Property conditions
	populatePropertyConditions = []string{
		"Excellent", "Good", "Fair", "Poor", "Damaged", "New", "Used",
		"Broken", "Repaired", "Refurbished", "Antique", "Vintage",
	}

	// Officers
	populateOfficers = []string{
		"officer-001", "officer-002", "officer-003", "officer-004", "officer-005",
		"officer-006", "officer-007", "officer-008", "officer-009", "officer-010",
		"officer-011", "officer-012", "officer-013", "officer-014", "officer-015",
		"officer-016", "officer-017", "officer-018", "officer-019", "officer-020",
	}

	// Custodians
	populateCustodians = []string{
		"Evidence Room A", "Evidence Room B", "Evidence Room C", "Property Room 1", "Property Room 2",
		"Detective Smith", "Detective Johnson", "Detective Williams", "Detective Brown", "Detective Jones",
		"Officer Davis", "Officer Miller", "Officer Wilson", "Officer Moore", "Officer Taylor",
		"Sergeant Anderson", "Sergeant Thomas", "Sergeant Jackson", "Sergeant White", "Sergeant Harris",
	}

	// Storage locations
	populateStorageLocations = []string{
		"Evidence Room A-12", "Evidence Room B-05", "Evidence Room C-18", "Property Room 1-03", "Property Room 2-07",
		"Safe A-01", "Safe B-02", "Safe C-03", "Vault 1", "Vault 2",
		"Locker A-15", "Locker B-22", "Locker C-08", "Cabinet 1", "Cabinet 2",
		"Shelf A-04", "Shelf B-11", "Shelf C-16", "Drawer 1", "Drawer 2",
	}

	// Notes templates
	populateNotesTemplates = []string{
		"Found during routine patrol",
		"Seized during traffic stop",
		"Recovered from burglary scene",
		"Turned in by citizen",
		"Evidence from crime scene",
		"Confiscated during arrest",
		"Recovered stolen property",
		"Abandoned property",
		"Evidence in ongoing investigation",
		"Property held for safekeeping",
		"Recovered from pawn shop",
		"Seized during search warrant",
		"Found at accident scene",
		"Recovered from suspect",
		"Evidence from domestic dispute",
	}

	// Retention periods
	populateRetentionPeriods = []string{
		"30 days", "60 days", "90 days", "6 months", "1 year", "2 years", "5 years",
		"Until case closed", "Indefinite", "Until owner claims", "Until disposal",
	}

	// Created by users
	populateCreatedByUsers = []string{
		"user-001", "user-002", "user-003", "user-004", "user-005",
		"user-006", "user-007", "user-008", "user-009", "user-010",
		"user-011", "user-012", "user-013", "user-014", "user-015",
	}
)

// Utility functions for property population
func secureRandomIntForProperties(min, max int) int {
	n, err := rand.Int(rand.Reader, big.NewInt(int64(max-min+1)))
	if err != nil {
		return min
	}
	return int(n.Int64()) + min
}

func randomChoiceForProperties[T any](slice []T) T {
	if len(slice) == 0 {
		var zero T
		return zero
	}
	return slice[secureRandomIntForProperties(0, len(slice)-1)]
}

func randomDateTimeForProperties() string {
	// Generate a random date between 2020-01-01 and 2024-12-31
	start := time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC)
	end := time.Date(2024, 12, 31, 23, 59, 59, 0, time.UTC)

	delta := end.Sub(start)
	randomDuration := time.Duration(secureRandomIntForProperties(0, int(delta)))
	randomTime := start.Add(randomDuration)

	return randomTime.Format("2006-01-02T15:04:05Z07:00")
}

func randomSerialNumberForProperties() string {
	// Generate a random serial number
	chars := "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	var result strings.Builder
	for i := 0; i < 8; i++ {
		result.WriteByte(chars[secureRandomIntForProperties(0, len(chars)-1)])
	}
	return result.String()
}

func randomValueForProperties() string {
	// Generate a random monetary value
	value := secureRandomIntForProperties(10, 50000)
	return fmt.Sprintf("$%d", value)
}

func randomQuantityForProperties() string {
	// Generate a random quantity
	quantity := secureRandomIntForProperties(1, 100)
	return fmt.Sprintf("%d", quantity)
}

func createPopulatePropertyData(propertyType property.NIBRSPropertyType) map[string]interface{} {
	category := randomChoiceForProperties(populatePropertyCategories)
	makeModel := randomChoiceForProperties(populatePropertyMakes)
	condition := randomChoiceForProperties(populatePropertyConditions)
	serialNumber := randomSerialNumberForProperties()
	collectedValue := randomValueForProperties()
	quantity := randomQuantityForProperties()

	return map[string]interface{}{
		"description":  fmt.Sprintf("A %s %s in %s condition", makeModel, category, condition),
		"quantity":     quantity,
		"category":     category,
		"identifiers":  makeModel,
		"owner":        fmt.Sprintf("Owner %d", secureRandomIntForProperties(1, 50)),
		"condition":    condition,
		"serialNumber": serialNumber,
		"value":        collectedValue,
		"propertyType": propertyType.String(),
	}
}

func createPopulateCustodyEvent(_ int) *property.CustodyEvent {
	actionTypes := []property.CustodyActionType{
		property.CustodyActionType_CUSTODY_ACTION_TYPE_COLLECTED,
		property.CustodyActionType_CUSTODY_ACTION_TYPE_TRANSFERRED,
		property.CustodyActionType_CUSTODY_ACTION_TYPE_CHECKED_OUT,
		property.CustodyActionType_CUSTODY_ACTION_TYPE_CHECKED_IN,
	}

	actionType := randomChoiceForProperties(actionTypes)
	timestamp := randomDateTimeForProperties()
	officerID := randomChoiceForProperties(populateOfficers)
	location := randomChoiceForProperties(populateStorageLocations)

	var notes string
	switch actionType {
	case property.CustodyActionType_CUSTODY_ACTION_TYPE_COLLECTED:
		notes = "Property collected from scene"
	case property.CustodyActionType_CUSTODY_ACTION_TYPE_TRANSFERRED:
		notes = "Property transferred to evidence room"
	case property.CustodyActionType_CUSTODY_ACTION_TYPE_CHECKED_OUT:
		notes = "Property checked out for investigation"
	case property.CustodyActionType_CUSTODY_ACTION_TYPE_CHECKED_IN:
		notes = "Property checked back in"
	}

	return &property.CustodyEvent{
		Timestamp:          timestamp,
		TransferringUserId: officerID,
		TransferringAgency: "Police Department",
		ReceivingUserId:    officerID,
		ReceivingAgency:    "Police Department",
		NewLocation:        location,
		ActionType:         actionType,
		Notes:              notes,
		EvidenceNumber:     fmt.Sprintf("EVD-%03d", secureRandomIntForProperties(1, 999)),
	}
}

func TestPopulateProperties(t *testing.T) {
	t.Logf("🚀 Starting to populate %d diverse properties for comprehensive testing...", populateNumProperties)

	// Setup
	httpClient := http.DefaultClient
	httpClient.Timeout = 30 * time.Second
	AddAuthHeader(httpClient)

	ctx, cancel := context.WithTimeout(context.Background(), 15*time.Minute)
	defer cancel()

	propertyClient := propertyConnect.NewPropertyServiceClient(httpClient, ServiceBaseURL)

	// Create test entity schema for properties
	testSchemaId := "test-property-schema"
	err := createTestEntitySchema(testSchemaId)
	if err != nil {
		t.Logf("⚠️ Warning: Failed to create test schema, continuing anyway: %v", err)
		// Don't fail the test - schema might already exist
	}

	// Create properties
	t.Logf("🏗️ Creating %d diverse properties...", populateNumProperties)
	var propertyIDs []string

	for i := 0; i < populateNumProperties; i++ {
		// Generate property attributes
		propertyType := randomChoiceForProperties(populatePropertyTypes)
		propertyStatus := randomChoiceForProperties(populatePropertyStatuses)
		disposalType := randomChoiceForProperties(populateDisposalTypes)
		isEvidence := secureRandomIntForProperties(0, 1) == 1
		currentCustodian := randomChoiceForProperties(populateCustodians)
		currentLocation := randomChoiceForProperties(populateStorageLocations)
		createdBy := randomChoiceForProperties(populateCreatedByUsers)
		notes := randomChoiceForProperties(populateNotesTemplates)
		retentionPeriod := randomChoiceForProperties(populateRetentionPeriods)

		// Create property data
		propertyDetails := createPopulatePropertyData(propertyType)

		// Create initial custody event
		initialCustodyEvent := createPopulateCustodyEvent(i)

		// Convert details to structpb
		detailsStruct, err := structpb.NewStruct(propertyDetails)
		if err != nil {
			t.Logf("⚠️ Failed to create details struct for property %d: %v", i, err)
			continue
		}

		prop := &property.Property{
			OrgId:            1,
			PropertyNumber:   fmt.Sprintf("PROP-%04d", i+1), // Added property number
			PropertyStatus:   propertyStatus,
			IsEvidence:       isEvidence,
			RetentionPeriod:  retentionPeriod,
			DisposalType:     disposalType,
			Notes:            notes,
			CurrentCustodian: currentCustodian,
			CurrentLocation:  currentLocation,
			CustodyChain:     []*property.CustodyEvent{initialCustodyEvent},
			SchemaId:         "test-property-schema", // Required for dynamic schema validation
			SchemaVersion:    1,
			Details:          detailsStruct,
			CreatedBy:        createdBy,
			UpdatedBy:        createdBy,
			Version:          1,
			ResourceType:     "PROPERTY",
		}

		req := &property.CreatePropertyRequest{Property: prop}
		resp, err := propertyClient.CreateProperty(ctx, connect.NewRequest(req))
		if err != nil {
			t.Logf("⚠️ Failed to create property %d: %v", i, err)
			continue
		}

		propertyIDs = append(propertyIDs, resp.Msg.Property.Id)

		// Progress logging
		if (i+1)%25 == 0 || i == populateNumProperties-1 {
			t.Logf("📊 Created %d/%d properties...", i+1, populateNumProperties)
		}

		// Rate limiting to avoid overwhelming the server
		if i%10 == 0 {
			time.Sleep(200 * time.Millisecond)
		} else {
			time.Sleep(50 * time.Millisecond)
		}
	}

	// Save property IDs to file for potential cleanup
	propertyIDsContent := strings.Join(propertyIDs, "\n")
	err = os.WriteFile("created_property_ids.txt", []byte(propertyIDsContent), 0600)
	if err != nil {
		t.Logf("⚠️ Warning: Failed to save property IDs to file: %v", err)
	} else {
		t.Logf("💾 Saved %d property IDs to created_property_ids.txt", len(propertyIDs))
	}

	// Summary
	t.Logf("✅ Successfully created %d properties!", len(propertyIDs))
	t.Logf("📊 Property distribution:")

	// Note: We can't easily get the exact counts without querying, but we can estimate
	expectedPerType := populateNumProperties / len(populatePropertyTypes)
	for _, propertyType := range populatePropertyTypes {
		t.Logf("   - %s: ~%d properties", propertyType.String(), expectedPerType)
	}

	t.Logf("📝 Files created:")
	t.Logf("   - created_property_ids.txt: %d property IDs", len(propertyIDs))
	t.Logf("🎉 Property population test completed successfully!")
}

// createTestEntitySchema creates a basic entity schema for testing properties
func createTestEntitySchema(schemaId string) error {
	// Connect to database using environment variable
	dbUrl := os.Getenv("DATABASE_URL")
	if dbUrl == "" {
		dbUrl = "postgres://postgres:postgres@localhost:5432/hero_test?sslmode=disable"
	}

	db, err := sql.Open("postgres", dbUrl)
	if err != nil {
		return err
	}
	defer db.Close()

	// Check if schema already exists
	var exists bool
	err = db.QueryRow("SELECT EXISTS(SELECT 1 FROM entity_schemas WHERE id = $1)", schemaId).Scan(&exists)
	if err != nil {
		return err
	}

	if exists {
		return nil // Schema already exists
	}

	// Create a basic property entity schema
	schemaDefinition := `{
		"type": "object",
		"properties": {
			"description": {"type": "string"},
			"quantity": {"type": "string"},
			"category": {"type": "string"},
			"propertyType": {"type": "string"}
		}
	}`

	_, err = db.Exec(`
		INSERT INTO entity_schemas (
			id, org_id, name, description, schema_definition,
			create_time, update_time, created_by, updated_by,
			version, entity_type, status, resource_type
		) VALUES ($1, $2, $3, $4, $5, NOW(), NOW(), $6, $7, $8, $9, $10, $11)
	`, schemaId, 1, "Test Property Schema", "Schema for testing property creation",
		schemaDefinition, "test-system", "test-system", 1, 1, 1, "ENTITY_SCHEMA")

	return err
}
