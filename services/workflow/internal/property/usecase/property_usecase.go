package usecase

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"time"

	property "proto/hero/property/v1"
	propertyRepository "workflow/internal/property/data"

	herosentry "common/herosentry"

	_ "modernc.org/sqlite" // required to support transaction for in-memory db
)

const FixedResourceTypeProperty = "PROPERTY"

// getPropertyTypeDisplayString converts protobuf enum to human-readable string
func getPropertyTypeDisplayString(propertyType property.NIBRSPropertyType) string {
	switch propertyType {
	case property.NIBRSPropertyType_PROPERTY_TYPE_RECOVERED:
		return "Recovered"
	case property.NIBRSPropertyType_PROPERTY_TYPE_STOLEN:
		return "Stolen"
	case property.NIBRSPropertyType_PROPERTY_TYPE_SEIZED:
		return "Seized"
	case property.NIBRSPropertyType_PROPERTY_TYPE_UNKNOWN:
		return "Unknown"
	case property.NIBRSPropertyType_PROPERTY_TYPE_FOUND:
		return "Found"
	case property.NIBRSPropertyType_PROPERTY_TYPE_BURNED:
		return "Burned"
	case property.NIBRSPropertyType_PROPERTY_TYPE_FORGED:
		return "Forged"
	case property.NIBRSPropertyType_PROPERTY_TYPE_DAMAGED:
		return "Damaged"
	default:
		return "Property"
	}
}

// mapCustodyActionToPropertyStatus aligns property status with custody chain state
// Note: For LOGGED items, the caller should provide additional context about property type
func mapCustodyActionToPropertyStatus(action property.CustodyActionType) property.PropertyStatus {
	switch action {
	case property.CustodyActionType_CUSTODY_ACTION_TYPE_COLLECTED:
		return property.PropertyStatus_PROPERTY_STATUS_COLLECTED
	case property.CustodyActionType_CUSTODY_ACTION_TYPE_LOGGED:
		// FIXED: Logged items are reported but NOT in physical custody
		// This is a context-dependent mapping - different property types should map differently:
		// - Stolen property → PROPERTY_STATUS_STOLEN
		// - Missing property → PROPERTY_STATUS_MISSING
		// - Found property (reported by citizen) → PROPERTY_STATUS_FOUND
		// Default to MISSING for safety, but caller should use mapLoggedPropertyStatus() instead
		return property.PropertyStatus_PROPERTY_STATUS_MISSING
	case property.CustodyActionType_CUSTODY_ACTION_TYPE_CHECKED_IN:
		return property.PropertyStatus_PROPERTY_STATUS_CHECKED_IN
	case property.CustodyActionType_CUSTODY_ACTION_TYPE_CHECKED_OUT:
		return property.PropertyStatus_PROPERTY_STATUS_CHECKED_OUT
	case property.CustodyActionType_CUSTODY_ACTION_TYPE_DISPOSED:
		return property.PropertyStatus_PROPERTY_STATUS_DISPOSED
	case property.CustodyActionType_CUSTODY_ACTION_TYPE_RELEASED:
		// Released items are no longer in our custody - mark as disposed
		return property.PropertyStatus_PROPERTY_STATUS_DISPOSED
	case property.CustodyActionType_CUSTODY_ACTION_TYPE_TRANSFERRED:
		// Transferred items are no longer in our custody - mark as disposed
		return property.PropertyStatus_PROPERTY_STATUS_DISPOSED
	case property.CustodyActionType_CUSTODY_ACTION_TYPE_MOVED:
		// For moves within facility, keep checked-in status
		return property.PropertyStatus_PROPERTY_STATUS_CHECKED_IN
	default:
		return property.PropertyStatus_PROPERTY_STATUS_UNSPECIFIED
	}
}

// mapLoggedPropertyStatus maps LOGGED custody actions to appropriate status based on property type
func mapLoggedPropertyStatus(propertyType property.NIBRSPropertyType) property.PropertyStatus {
	switch propertyType {
	case property.NIBRSPropertyType_PROPERTY_TYPE_STOLEN:
		return property.PropertyStatus_PROPERTY_STATUS_STOLEN
	case property.NIBRSPropertyType_PROPERTY_TYPE_FOUND:
		return property.PropertyStatus_PROPERTY_STATUS_FOUND
	case property.NIBRSPropertyType_PROPERTY_TYPE_RECOVERED:
		return property.PropertyStatus_PROPERTY_STATUS_RECOVERED
	default:
		// Default for unknown/missing property types
		return property.PropertyStatus_PROPERTY_STATUS_MISSING
	}
}

// PropertyUseCase defines the use-case layer for property operations.
type PropertyUseCase struct {
	database     *sql.DB // Used for transactional operations like AddCustodyEvent
	propertyRepo propertyRepository.PropertyRepository
}

// NewPropertyUseCase creates a new PropertyUseCase.
func NewPropertyUseCase(
	database *sql.DB,
	propertyRepo propertyRepository.PropertyRepository) (*PropertyUseCase, error) {
	// For in-memory propertyRepository, we need a dummy DB to support transactions.
	if database == nil {
		return nil, errors.New("database is nil: cannot initialize PropertyUseCase")
	}

	return &PropertyUseCase{
		database:     database,
		propertyRepo: propertyRepo,
	}, nil
}

// executeInTx executes the provided function within a database transaction.
func (propertyUseCase *PropertyUseCase) executeInTx(ctx context.Context, transactionalWork func(transaction *sql.Tx) error) error {
	spanContext, _, finishSpan := herosentry.StartSpan(ctx, "PropertyUseCase.executeInTx")
	defer finishSpan()

	tx, err := propertyUseCase.database.BeginTx(spanContext, &sql.TxOptions{
		Isolation: sql.LevelReadCommitted,
		ReadOnly:  false,
	})
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to begin transaction")
		return fmt.Errorf("failed to begin transaction: %w", err)
	}

	// Execute the transactional work first
	workErr := transactionalWork(tx)

	defer func() {
		if p := recover(); p != nil {
			// A panic occurred, rollback and re-panic
			rollbackErr := tx.Rollback()
			if rollbackErr != nil {
				herosentry.CaptureException(spanContext, rollbackErr, herosentry.ErrorTypeDatabase, "Failed to rollback transaction after panic")
			}
			panic(p) // re-throw panic after Rollback
		} else if workErr != nil {
			// Something went wrong, rollback
			rollbackErr := tx.Rollback()
			if rollbackErr != nil {
				herosentry.CaptureException(spanContext, rollbackErr, herosentry.ErrorTypeDatabase, "Failed to rollback transaction")
			}
		} else {
			// All good, commit
			commitErr := tx.Commit()
			if commitErr != nil {
				herosentry.CaptureException(spanContext, commitErr, herosentry.ErrorTypeDatabase, "Failed to commit transaction")
				workErr = fmt.Errorf("failed to commit transaction: %w", commitErr)
			}
		}
	}()

	return workErr
}

// CreateProperty creates a new property.
func (propertyUseCase *PropertyUseCase) CreateProperty(ctx context.Context, propertyRecord *property.Property) error {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PropertyUseCase.CreateProperty")
	defer finish()
	span.SetTag("property.org_id", fmt.Sprintf("%d", propertyRecord.OrgId))
	// Property type is now determined from dynamic schema details
	span.SetTag("property.type", propertyRecord.NibrsPropertyType.String())
	span.SetTag("property.status", propertyRecord.PropertyStatus.String())

	if propertyRecord.OrgId == 0 {
		herosentry.CaptureException(spanContext, errors.New("organization ID is required"), herosentry.ErrorTypeValidation, "Property creation validation failed")
		return fmt.Errorf("organization ID is required")
	}

	// Set default values
	if propertyRecord.CreateTime == "" {
		propertyRecord.CreateTime = time.Now().UTC().Format(time.RFC3339)
	}
	if propertyRecord.UpdateTime == "" {
		propertyRecord.UpdateTime = propertyRecord.CreateTime
	}
	if propertyRecord.Version == 0 {
		propertyRecord.Version = 1
	}
	// Status field removed from database schema - using PropertyStatus instead
	propertyRecord.ResourceType = FixedResourceTypeProperty

	// Validate that we have dynamic schema fields (PropertySchema support removed)
	if propertyRecord.SchemaId == "" || propertyRecord.Details == nil {
		herosentry.CaptureException(spanContext, errors.New("property must have dynamic schema (SchemaId + Details)"), herosentry.ErrorTypeValidation, "Property creation validation failed")
		return fmt.Errorf("property must have dynamic schema (SchemaId + Details)")
	}

	// Implement "Logged" status logic: any property without explicit status goes to INTAKE_PENDING
	// The presence of custodian/location doesn't necessarily mean it's in custody - it could be assigned but not collected
	if propertyRecord.PropertyStatus == property.PropertyStatus_PROPERTY_STATUS_UNSPECIFIED {
		// Default to "Logged" (INTAKE_PENDING) for any property without explicit status
		// This ensures properties start in "Logged" status and must be explicitly moved to other statuses
		propertyRecord.PropertyStatus = property.PropertyStatus_PROPERTY_STATUS_INTAKE_PENDING
	}

	// Use transaction for property creation and initial custody event
	err := propertyUseCase.executeInTx(spanContext, func(tx *sql.Tx) error {
		// Create the property
		createErr := propertyUseCase.propertyRepo.CreateProperty(spanContext, tx, propertyRecord)
		if createErr != nil {
			return createErr
		}

		// Get property type display string from enum
		typeStr := getPropertyTypeDisplayString(propertyRecord.NibrsPropertyType)

		// Only create custody events for properties that are actually in our custody
		// Do NOT create custody events for properties that are not in custody (stolen, missing, logged but not collected)
		var shouldCreateCustodyEvent bool
		var custodyActionType property.CustodyActionType
		var custodyNotes string

		switch propertyRecord.PropertyStatus {
		case property.PropertyStatus_PROPERTY_STATUS_CHECKED_IN:
			// Property in custody and checked in
			shouldCreateCustodyEvent = true
			custodyActionType = property.CustodyActionType_CUSTODY_ACTION_TYPE_CHECKED_IN
			custodyNotes = fmt.Sprintf("%s property checked into evidence", typeStr)
		case property.PropertyStatus_PROPERTY_STATUS_COLLECTED:
			// Property collected but not yet checked in
			shouldCreateCustodyEvent = true
			custodyActionType = property.CustodyActionType_CUSTODY_ACTION_TYPE_COLLECTED
			custodyNotes = fmt.Sprintf("%s property collected into custody", typeStr)
		case property.PropertyStatus_PROPERTY_STATUS_CHECKED_OUT:
			// Property checked out from custody
			shouldCreateCustodyEvent = true
			custodyActionType = property.CustodyActionType_CUSTODY_ACTION_TYPE_CHECKED_OUT
			custodyNotes = fmt.Sprintf("%s property checked out", typeStr)
		case property.PropertyStatus_PROPERTY_STATUS_INTAKE_PENDING:
			// Property logged but not in custody - NO custody event
			shouldCreateCustodyEvent = false
		case property.PropertyStatus_PROPERTY_STATUS_MISSING:
		case property.PropertyStatus_PROPERTY_STATUS_STOLEN:
			// Property not in our custody (stolen/missing) - NO custody event
			shouldCreateCustodyEvent = false
		default:
			// For other statuses, don't create custody events unless explicitly in custody
			shouldCreateCustodyEvent = false
		}

		// Only create custody event if property is actually in our custody
		if shouldCreateCustodyEvent {
			initialCustodyEvent := &property.CustodyEvent{
				Timestamp:          time.Now().UTC().Format(time.RFC3339),
				TransferringUserId: propertyRecord.CreatedBy,
				ReceivingUserId:    propertyRecord.CurrentCustodian,
				NewLocation:        propertyRecord.CurrentLocation,
				ActionType:         custodyActionType,
				Notes:              custodyNotes,
			}

			// Add initial custody event
			custodyErr := propertyUseCase.propertyRepo.AddCustodyEvent(spanContext, tx, propertyRecord.Id, initialCustodyEvent)
			if custodyErr != nil {
				return custodyErr
			}
		}

		return nil
	})
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to create property")
		return err
	}

	span.SetTag("property.id", propertyRecord.Id)
	return nil
}

// GetProperty retrieves a property by its ID.
func (propertyUseCase *PropertyUseCase) GetProperty(ctx context.Context, propertyID string) (*property.Property, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PropertyUseCase.GetProperty")
	defer finish()
	span.SetTag("property.id", propertyID)

	if propertyID == "" {
		herosentry.CaptureException(spanContext, errors.New("property ID is required"), herosentry.ErrorTypeValidation, "Property retrieval validation failed")
		return nil, fmt.Errorf("property ID is required")
	}

	propertyRecord, err := propertyUseCase.propertyRepo.GetProperty(spanContext, nil, propertyID)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to get property %s", propertyID))
		return nil, err
	}

	if propertyRecord != nil {
		span.SetTag("property.org_id", fmt.Sprintf("%d", propertyRecord.OrgId))
		span.SetTag("property.type", propertyRecord.NibrsPropertyType.String())
		span.SetTag("property.status", propertyRecord.PropertyStatus.String())
	}

	return propertyRecord, nil
}

// DeleteProperty deletes a property.
func (propertyUseCase *PropertyUseCase) DeleteProperty(ctx context.Context, propertyID string) error {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PropertyUseCase.DeleteProperty")
	defer finish()
	span.SetTag("property.id", propertyID)

	if propertyID == "" {
		herosentry.CaptureException(spanContext, errors.New("property ID is required"), herosentry.ErrorTypeValidation, "Property deletion validation failed")
		return fmt.Errorf("property ID is required")
	}

	err := propertyUseCase.propertyRepo.DeleteProperty(spanContext, nil, propertyID)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to delete property %s", propertyID))
		return err
	}

	return nil
}

// ListProperties lists properties with pagination and filtering.
func (propertyUseCase *PropertyUseCase) ListProperties(ctx context.Context, pageSize int, pageToken string, propertyType property.NIBRSPropertyType, propertyStatus property.PropertyStatus, orderBy string) ([]*property.Property, string, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PropertyUseCase.ListProperties")
	defer finish()
	span.SetTag("list.page_size", fmt.Sprintf("%d", pageSize))
	span.SetTag("list.property_type", propertyType.String())
	span.SetTag("list.property_status", propertyStatus.String())
	span.SetTag("list.order_by", orderBy)

	if pageSize <= 0 {
		pageSize = 50 // Default page size
	}
	if pageSize > 1000 {
		pageSize = 1000 // Maximum page size
	}

	result, err := propertyUseCase.propertyRepo.ListProperties(spanContext, nil, pageSize, pageToken, propertyType, propertyStatus, orderBy)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to list properties")
		return nil, "", err
	}

	span.SetTag("list.results_count", fmt.Sprintf("%d", len(result.Properties)))
	return result.Properties, result.NextPageToken, nil
}

// UpdateProperty updates an existing property.
func (propertyUseCase *PropertyUseCase) UpdateProperty(ctx context.Context, updatedProperty *property.Property) (*property.Property, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PropertyUseCase.UpdateProperty")
	defer finish()
	span.SetTag("property.id", updatedProperty.Id)
	span.SetTag("property.org_id", fmt.Sprintf("%d", updatedProperty.OrgId))
	span.SetTag("property.type", updatedProperty.NibrsPropertyType.String())
	span.SetTag("property.status", updatedProperty.PropertyStatus.String())

	if updatedProperty.Id == "" {
		herosentry.CaptureException(spanContext, errors.New("property ID is required"), herosentry.ErrorTypeValidation, "Property update validation failed")
		return nil, fmt.Errorf("property ID is required")
	}

	// Update the update time
	updatedProperty.UpdateTime = time.Now().UTC().Format(time.RFC3339)

	propertyRecord, err := propertyUseCase.propertyRepo.UpdateProperty(spanContext, nil, updatedProperty)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to update property %s", updatedProperty.Id))
		return nil, err
	}

	return propertyRecord, nil
}

// SearchProperties performs advanced search on properties.
func (propertyUseCase *PropertyUseCase) SearchProperties(ctx context.Context, searchRequest *property.SearchPropertiesRequest) (*property.SearchPropertiesResponse, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PropertyUseCase.SearchProperties")
	defer finish()
	span.SetTag("search.page_size", fmt.Sprintf("%d", searchRequest.PageSize))
	if searchRequest.Query != "" {
		span.SetTag("search.has_query", "true")
	} else {
		span.SetTag("search.has_query", "false")
	}

	if searchRequest == nil {
		herosentry.CaptureException(spanContext, errors.New("search request is required"), herosentry.ErrorTypeValidation, "Property search validation failed")
		return nil, fmt.Errorf("search request is required")
	}

	response, err := propertyUseCase.propertyRepo.SearchProperties(spanContext, nil, searchRequest)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to search properties")
		return nil, err
	}

	span.SetTag("search.results_count", fmt.Sprintf("%d", len(response.Properties)))
	return response, nil
}

// BatchGetProperties retrieves multiple properties by their IDs.
func (propertyUseCase *PropertyUseCase) BatchGetProperties(ctx context.Context, propertyIDs []string) ([]*property.Property, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PropertyUseCase.BatchGetProperties")
	defer finish()
	span.SetTag("batch.property_count", fmt.Sprintf("%d", len(propertyIDs)))

	if len(propertyIDs) == 0 {
		return []*property.Property{}, nil
	}

	properties, err := propertyUseCase.propertyRepo.BatchGetProperties(spanContext, nil, propertyIDs)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to batch get properties")
		return nil, err
	}

	span.SetTag("batch.results_count", fmt.Sprintf("%d", len(properties)))
	return properties, nil
}

// AddCustodyEvent adds a new custody event to a property's chain of custody.
func (propertyUseCase *PropertyUseCase) AddCustodyEvent(ctx context.Context, propertyID string, custodyEvent *property.CustodyEvent) error {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PropertyUseCase.AddCustodyEvent")
	defer finish()
	span.SetTag("property.id", propertyID)
	span.SetTag("custody.action_type", custodyEvent.ActionType.String())
	span.SetTag("custody.timestamp", custodyEvent.Timestamp)

	if propertyID == "" {
		herosentry.CaptureException(spanContext, errors.New("property ID is required"), herosentry.ErrorTypeValidation, "Custody event validation failed")
		return fmt.Errorf("property ID is required")
	}
	if custodyEvent == nil {
		herosentry.CaptureException(spanContext, errors.New("custody event is required"), herosentry.ErrorTypeValidation, "Custody event validation failed")
		return fmt.Errorf("custody event is required")
	}

	// Set timestamp if not provided
	if custodyEvent.Timestamp == "" {
		custodyEvent.Timestamp = time.Now().UTC().Format(time.RFC3339)
	}

	// Validate action type
	if custodyEvent.ActionType == property.CustodyActionType_CUSTODY_ACTION_TYPE_UNSPECIFIED {
		herosentry.CaptureException(spanContext, errors.New("custody action type is required"), herosentry.ErrorTypeValidation, "Custody event validation failed")
		return fmt.Errorf("custody action type is required")
	}

	// Use transaction for atomic operation
	err := propertyUseCase.executeInTx(spanContext, func(tx *sql.Tx) error {
		// Add the custody event first
		err := propertyUseCase.propertyRepo.AddCustodyEvent(spanContext, tx, propertyID, custodyEvent)
		if err != nil {
			return err
		}

		// Get the updated property (now with the new custody event)
		currentProperty, err := propertyUseCase.propertyRepo.GetProperty(spanContext, tx, propertyID)
		if err != nil {
			return err
		}

		// Update property status to align with custody action (except for transfers)
		var newStatus property.PropertyStatus
		var newPropertyType string

		// Special handling for check-in of stolen/missing items
		switch custodyEvent.ActionType {
		case property.CustodyActionType_CUSTODY_ACTION_TYPE_CHECKED_IN:
			switch currentProperty.PropertyStatus {
			case property.PropertyStatus_PROPERTY_STATUS_STOLEN:
				// Stolen property that gets checked in: status becomes checked in, type becomes recovered
				newStatus = mapCustodyActionToPropertyStatus(custodyEvent.ActionType)
				newPropertyType = "PROPERTY_TYPE_RECOVERED"
			case property.PropertyStatus_PROPERTY_STATUS_MISSING:
				// Missing property that gets checked in: status becomes checked in, type becomes found
				newStatus = mapCustodyActionToPropertyStatus(custodyEvent.ActionType)
				newPropertyType = "PROPERTY_TYPE_FOUND"
			default:
				// Normal check-in for other statuses
				newStatus = mapCustodyActionToPropertyStatus(custodyEvent.ActionType)
			}
		case property.CustodyActionType_CUSTODY_ACTION_TYPE_LOGGED:
			// For LOGGED items, use context-aware mapping based on property type
			propertyType := currentProperty.NibrsPropertyType
			newStatus = mapLoggedPropertyStatus(propertyType)
		default:
			newStatus = mapCustodyActionToPropertyStatus(custodyEvent.ActionType)
		}

		// Update property status and evidence number if needed
		needsUpdate := false
		if newStatus != property.PropertyStatus_PROPERTY_STATUS_UNSPECIFIED && newStatus != currentProperty.PropertyStatus {
			currentProperty.PropertyStatus = newStatus
			needsUpdate = true
		}

		// Update property type for stolen→recovered and missing→found transitions
		if newPropertyType != "" {
			// Convert string constant to proper enum value
			var enumValue property.NIBRSPropertyType
			switch newPropertyType {
			case "PROPERTY_TYPE_RECOVERED":
				enumValue = property.NIBRSPropertyType_PROPERTY_TYPE_RECOVERED
			case "PROPERTY_TYPE_FOUND":
				enumValue = property.NIBRSPropertyType_PROPERTY_TYPE_FOUND
			case "PROPERTY_TYPE_STOLEN":
				enumValue = property.NIBRSPropertyType_PROPERTY_TYPE_STOLEN
			case "PROPERTY_TYPE_MISSING":
				enumValue = property.NIBRSPropertyType_PROPERTY_TYPE_UNKNOWN // Map missing to unknown
			case "PROPERTY_TYPE_SEIZED":
				enumValue = property.NIBRSPropertyType_PROPERTY_TYPE_SEIZED
			case "PROPERTY_TYPE_SAFEKEEPING":
				enumValue = property.NIBRSPropertyType_PROPERTY_TYPE_FOUND // Map safekeeping to found
			default:
				enumValue = property.NIBRSPropertyType_PROPERTY_TYPE_FOUND // default fallback
			}

			currentProperty.NibrsPropertyType = enumValue
			needsUpdate = true
		}

		// Update property number for check-ins with evidence number
		if custodyEvent.ActionType == property.CustodyActionType_CUSTODY_ACTION_TYPE_CHECKED_IN && custodyEvent.EvidenceNumber != "" {
			currentProperty.PropertyNumber = custodyEvent.EvidenceNumber
			needsUpdate = true
		}

		// Update current location if the custody event provides a new location
		if custodyEvent.NewLocation != "" && custodyEvent.NewLocation != "Unknown location" {
			currentProperty.CurrentLocation = custodyEvent.NewLocation
			needsUpdate = true
		}

		if needsUpdate {
			currentProperty.UpdateTime = time.Now().UTC().Format(time.RFC3339)
			_, err = propertyUseCase.propertyRepo.UpdateProperty(spanContext, tx, currentProperty)
			if err != nil {
				return err
			}
		}

		return nil
	})
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to add custody event for property %s", propertyID))
		return err
	}

	return nil
}

// GetCustodyChain retrieves the complete chain of custody for a property.
func (propertyUseCase *PropertyUseCase) GetCustodyChain(ctx context.Context, propertyID string) ([]*property.CustodyEvent, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PropertyUseCase.GetCustodyChain")
	defer finish()
	span.SetTag("property.id", propertyID)

	custodyChain, err := propertyUseCase.propertyRepo.GetCustodyChain(spanContext, nil, propertyID)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to get custody chain")
		return nil, err
	}

	span.SetTag("custody.events_count", fmt.Sprintf("%d", len(custodyChain)))
	return custodyChain, nil
}

// ListPropertyFileAttachments lists all file attachments for a property.
func (propertyUseCase *PropertyUseCase) ListPropertyFileAttachments(ctx context.Context, request *property.ListPropertyFileAttachmentsRequest) (*property.ListPropertyFileAttachmentsResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "PropertyUseCase.ListPropertyFileAttachments")
	defer finishSpan()

	span.SetTag("property.id", request.PropertyId)

	if request.PageSize <= 0 {
		request.PageSize = 50 // Default page size
	}
	if request.PageSize > 1000 {
		request.PageSize = 1000 // Max page size
	}

	span.SetTag("pagination.page_size", fmt.Sprintf("%d", request.PageSize))

	response, err := propertyUseCase.propertyRepo.ListPropertyFileAttachments(spanContext, nil, request)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to list property file attachments")
		return nil, err
	}

	if response != nil && response.FileAttachments != nil {
		span.SetTag("result.count", fmt.Sprintf("%d", len(response.FileAttachments)))
	}

	return response, nil
}

// AddPropertyFileAttachment adds a file attachment to a property.
func (propertyUseCase *PropertyUseCase) AddPropertyFileAttachment(ctx context.Context, request *property.AddPropertyFileAttachmentRequest) (*property.AddPropertyFileAttachmentResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "PropertyUseCase.AddPropertyFileAttachment")
	defer finishSpan()

	span.SetTag("property.id", request.PropertyId)
	span.SetTag("file.id", request.FileAttachment.FileId)

	// Validate that the property exists
	_, err := propertyUseCase.propertyRepo.GetProperty(spanContext, nil, request.PropertyId)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeNotFound, "Property not found")
		return nil, fmt.Errorf("property not found: %s", request.PropertyId)
	}

	var response *property.AddPropertyFileAttachmentResponse
	updateErr := propertyUseCase.executeInTx(spanContext, func(transaction *sql.Tx) error {
		var repositoryErr error
		response, repositoryErr = propertyUseCase.propertyRepo.AddPropertyFileAttachment(spanContext, transaction, request)
		return repositoryErr
	})
	if updateErr != nil {
		herosentry.CaptureException(spanContext, updateErr, herosentry.ErrorTypeDatabase, "Failed to add property file attachment")
		return nil, updateErr
	}

	return response, nil
}

// RemovePropertyFileAttachment removes a file attachment from a property.
func (propertyUseCase *PropertyUseCase) RemovePropertyFileAttachment(ctx context.Context, request *property.RemovePropertyFileAttachmentRequest) (*property.RemovePropertyFileAttachmentResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "PropertyUseCase.RemovePropertyFileAttachment")
	defer finishSpan()

	span.SetTag("property.id", request.PropertyId)
	span.SetTag("attachment.id", request.AttachmentId)

	var response *property.RemovePropertyFileAttachmentResponse
	updateErr := propertyUseCase.executeInTx(spanContext, func(transaction *sql.Tx) error {
		var repositoryErr error
		response, repositoryErr = propertyUseCase.propertyRepo.RemovePropertyFileAttachment(spanContext, transaction, request)
		return repositoryErr
	})
	if updateErr != nil {
		herosentry.CaptureException(spanContext, updateErr, herosentry.ErrorTypeDatabase, "Failed to remove property file attachment")
		return nil, updateErr
	}

	return response, nil
}
