package repository

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"

	cmncontext "common/context"
	database "common/database"
	herosentry "common/herosentry"
	property "proto/hero/property/v1"

	"github.com/google/uuid"
	_ "github.com/lib/pq"
	"google.golang.org/protobuf/types/known/structpb"
)

// PostgresPropertyRepository implements PropertyRepository using PostgreSQL.
type PostgresPropertyRepository struct {
	db *sql.DB
}

// NewPostgresPropertyRepository creates a new PostgresPropertyRepository.
func NewPostgresPropertyRepository(db *sql.DB) *PostgresPropertyRepository {
	return &PostgresPropertyRepository{db: db}
}

// nullIfEmpty converts empty strings to nil for database NULL values
func nullIfEmpty(inputStr string) interface{} {
	if strings.TrimSpace(inputStr) == "" {
		return nil
	}
	return inputStr
}

// CreateProperty stores a new property.
func (repo *PostgresPropertyRepository) CreateProperty(ctx context.Context, tx *sql.Tx, propertyRecord *property.Property) error {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PostgresPropertyRepository.CreateProperty")
	defer finish()
	span.SetTag("property.id", propertyRecord.Id)
	span.SetTag("property.org_id", fmt.Sprintf("%d", propertyRecord.OrgId))
	// Property type is now determined from dynamic schema details
	span.SetTag("property.type", propertyRecord.NibrsPropertyType.String())
	span.SetTag("property.status", propertyRecord.PropertyStatus.String())

	return database.WithSessionErr(repo.db, spanContext, tx, func(sessionTx *sql.Tx) error {
		// Generate ID if not provided
		if propertyRecord.Id == "" {
			propertyRecord.Id = uuid.New().String()
		}

		query := `
			INSERT INTO properties (
				id, org_id, property_number, is_evidence, retention_period,
				property_status, nibrs_property_type, disposal_type, notes, current_custodian,
				current_location, custody_chain, schema_id, schema_version, details, create_time, update_time,
				created_by, updated_by, version, resource_type
			) VALUES (
				$1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21
			)
		`

		// Set default values
		if propertyRecord.CreateTime == "" {
			propertyRecord.CreateTime = time.Now().UTC().Format(time.RFC3339)
		}
		if propertyRecord.UpdateTime == "" {
			propertyRecord.UpdateTime = propertyRecord.CreateTime
		}
		if propertyRecord.Version == 0 {
			propertyRecord.Version = 1
		}
		// Status field removed - using PropertyStatus enum instead
		propertyRecord.ResourceType = FixedResourceTypeProperty

		// Convert custody chain to JSONB
		custodyChainJSON, err := json.Marshal(propertyRecord.CustodyChain)
		if err != nil {
			err := fmt.Errorf("failed to marshal custody chain: %w", err)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal)
			return err
		}

		// Convert details to JSONB
		var detailsJSON []byte
		if propertyRecord.Details != nil {
			detailsJSON, err = json.Marshal(propertyRecord.Details.AsMap())
			if err != nil {
				return fmt.Errorf("failed to marshal details: %w", err)
			}
		}

		// Handle empty strings as NULL for asset ID fields
		createdBy := nullIfEmpty(propertyRecord.CreatedBy)
		updatedBy := nullIfEmpty(propertyRecord.UpdatedBy)

		// Handle empty schema_id as NULL for foreign key constraint
		schemaId := nullIfEmpty(propertyRecord.SchemaId)

		// Ensure nibrs_property_type has a valid value (not UNSPECIFIED which is omitted from JSON)
		if propertyRecord.NibrsPropertyType == property.NIBRSPropertyType_PROPERTY_TYPE_UNSPECIFIED {
			propertyRecord.NibrsPropertyType = property.NIBRSPropertyType_PROPERTY_TYPE_FOUND // Default to FOUND
		}

		_, err = sessionTx.ExecContext(ctx, query,
			propertyRecord.Id,
			propertyRecord.OrgId,
			propertyRecord.PropertyNumber,
			propertyRecord.IsEvidence,
			propertyRecord.RetentionPeriod,
			propertyRecord.PropertyStatus,
			propertyRecord.NibrsPropertyType,
			propertyRecord.DisposalType,
			propertyRecord.Notes,
			propertyRecord.CurrentCustodian,
			propertyRecord.CurrentLocation,
			custodyChainJSON,
			schemaId,
			propertyRecord.SchemaVersion,
			detailsJSON,
			propertyRecord.CreateTime,
			propertyRecord.UpdateTime,
			createdBy,
			updatedBy,
			propertyRecord.Version,
			propertyRecord.ResourceType,
		)

		if err != nil {
			err := fmt.Errorf("failed to create property: %w", err)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return err
		}

		return nil
	})
}

// GetProperty returns a property by its ID.
func (repo *PostgresPropertyRepository) GetProperty(ctx context.Context, tx *sql.Tx, propertyID string) (*property.Property, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PostgresPropertyRepository.GetProperty")
	defer finish()
	span.SetTag("property.id", propertyID)

	return database.WithSession(repo.db, spanContext, tx, func(sessionTx *sql.Tx) (*property.Property, error) {

		query := `
			SELECT id, org_id, property_number, is_evidence, retention_period,
				   property_status, nibrs_property_type, disposal_type, notes, current_custodian,
				   current_location, custody_chain, schema_id, schema_version, details, create_time, update_time,
				   created_by, updated_by, version, resource_type
			FROM properties
			WHERE id = $1
		`

		var propertyRecord property.Property
		var custodyChainJSON []byte
		var detailsJSON []byte
		var createdByNull sql.NullString
		var updatedByNull sql.NullString

		err := sessionTx.QueryRowContext(spanContext, query, propertyID).Scan(
			&propertyRecord.Id,
			&propertyRecord.OrgId,
			&propertyRecord.PropertyNumber,
			&propertyRecord.IsEvidence,
			&propertyRecord.RetentionPeriod,
			&propertyRecord.PropertyStatus,
			&propertyRecord.NibrsPropertyType,
			&propertyRecord.DisposalType,
			&propertyRecord.Notes,
			&propertyRecord.CurrentCustodian,
			&propertyRecord.CurrentLocation,
			&custodyChainJSON,
			&propertyRecord.SchemaId,
			&propertyRecord.SchemaVersion,
			&detailsJSON,
			&propertyRecord.CreateTime,
			&propertyRecord.UpdateTime,
			&createdByNull,
			&updatedByNull,
			&propertyRecord.Version,
			&propertyRecord.ResourceType,
		)

		if err != nil {
			if err == sql.ErrNoRows {
				return nil, ErrPropertyNotFound
			}
			err := fmt.Errorf("failed to get property: %w", err)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return nil, err
		}

		// Handle nullable asset ID fields
		if createdByNull.Valid {
			propertyRecord.CreatedBy = createdByNull.String
		}
		if updatedByNull.Valid {
			propertyRecord.UpdatedBy = updatedByNull.String
		}

		// Parse custody chain JSON
		if len(custodyChainJSON) > 0 {
			err = json.Unmarshal(custodyChainJSON, &propertyRecord.CustodyChain)
			if err != nil {
				// Failed to unmarshal custody chain
			}
		}

		// Parse details JSON
		if len(detailsJSON) > 0 {
			var detailsMap map[string]interface{}
			err = json.Unmarshal(detailsJSON, &detailsMap)
			if err != nil {
				// Failed to unmarshal details
			} else {
				detailsStruct, err := structpb.NewStruct(detailsMap)
				if err != nil {
					// Failed to create structpb from details
				} else {
					propertyRecord.Details = detailsStruct
				}
			}
		}

		return &propertyRecord, nil
	})
}

// ListProperties returns a paginated list of properties.
// Note: propertyType parameter is deprecated since property types are now stored in dynamic schema details
func (repo *PostgresPropertyRepository) ListProperties(ctx context.Context, tx *sql.Tx, pageSize int, pageToken string, propertyType property.NIBRSPropertyType, propertyStatus property.PropertyStatus, orderBy string) (*PaginatedProperties, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PostgresPropertyRepository.ListProperties")
	defer finish()
	span.SetTag("list.page_size", fmt.Sprintf("%d", pageSize))
	span.SetTag("list.property_type", propertyType.String())
	span.SetTag("list.property_status", propertyStatus.String())
	span.SetTag("list.order_by", orderBy)

	return database.WithSession(repo.db, spanContext, tx, func(sessionTx *sql.Tx) (*PaginatedProperties, error) {
		// Build the base query
		baseQuery := `
			SELECT id, org_id, property_number, is_evidence, retention_period,
				   property_status, nibrs_property_type, disposal_type, notes, current_custodian,
				   current_location, custody_chain, schema_id, schema_version, details, create_time, update_time,
				   created_by, updated_by, version, resource_type
			FROM properties
		`

		// Add filters
		var conditions []string
		var args []interface{}
		argIndex := 1

		// Property type filter is now in the schema, so we'll skip it in basic list
		// Advanced filtering can be done through search with field queries

		if propertyStatus != property.PropertyStatus_PROPERTY_STATUS_UNSPECIFIED {
			conditions = append(conditions, fmt.Sprintf("property_status = $%d", argIndex))
			args = append(args, int32(propertyStatus))
			argIndex++
		}

		if len(conditions) > 0 {
			baseQuery += " AND " + conditions[0]
			for i := 1; i < len(conditions); i++ {
				baseQuery += " AND " + conditions[i]
			}
		}

		// Add ordering
		if orderBy == "" {
			orderBy = "create_time DESC"
		}
		baseQuery += " ORDER BY " + orderBy

		// Add pagination
		if pageToken != "" {
			// TODO: Implement cursor-based pagination
			baseQuery += fmt.Sprintf(" OFFSET $%d", argIndex)
			args = append(args, 0) // Placeholder for offset calculation
			argIndex++
		}

		baseQuery += fmt.Sprintf(" LIMIT $%d", argIndex)
		args = append(args, pageSize+1) // Get one extra to check if there are more results

		rows, err := sessionTx.QueryContext(spanContext, baseQuery, args...)
		if err != nil {
			err := fmt.Errorf("failed to list properties: %w", err)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return nil, err
		}
		defer rows.Close()

		var properties []*property.Property
		for rows.Next() {
			var propertyRecord property.Property
			var custodyChainJSON []byte
			var detailsJSON []byte

			err := rows.Scan(
				&propertyRecord.Id,
				&propertyRecord.OrgId,
				&propertyRecord.PropertyNumber,
				&propertyRecord.IsEvidence,
				&propertyRecord.RetentionPeriod,
				&propertyRecord.PropertyStatus,
				&propertyRecord.NibrsPropertyType,
				&propertyRecord.DisposalType,
				&propertyRecord.Notes,
				&propertyRecord.CurrentCustodian,
				&propertyRecord.CurrentLocation,
				&custodyChainJSON,
				&propertyRecord.SchemaId,
				&propertyRecord.SchemaVersion,
				&detailsJSON,
				&propertyRecord.CreateTime,
				&propertyRecord.UpdateTime,
				&propertyRecord.CreatedBy,
				&propertyRecord.UpdatedBy,
				&propertyRecord.Version,
				&propertyRecord.ResourceType,
			)

			if err != nil {
				err := fmt.Errorf("failed to scan property: %w", err)
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
				return nil, err
			}

			// Parse custody chain JSON
			if len(custodyChainJSON) > 0 {
				err = json.Unmarshal(custodyChainJSON, &propertyRecord.CustodyChain)
				if err != nil {
					// Failed to unmarshal custody chain
				}
			}

			// Parse details JSON
			if len(detailsJSON) > 0 {
				var detailsMap map[string]interface{}
				err = json.Unmarshal(detailsJSON, &detailsMap)
				if err != nil {
					// Failed to unmarshal details
				} else {
					detailsStruct, err := structpb.NewStruct(detailsMap)
					if err != nil {
						// Failed to create structpb from details
					} else {
						propertyRecord.Details = detailsStruct
					}
				}
			}

			properties = append(properties, &propertyRecord)
		}

		if err = rows.Err(); err != nil {
			err := fmt.Errorf("error iterating properties: %w", err)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return nil, err
		}

		// Handle pagination
		var nextPageToken string
		if len(properties) > pageSize {
			properties = properties[:pageSize]
			// TODO: Generate next page token
		}

		return &PaginatedProperties{
			Properties:    properties,
			NextPageToken: nextPageToken,
		}, nil
	})
}

// DeleteProperty physically deletes a property from the store.
func (repo *PostgresPropertyRepository) DeleteProperty(ctx context.Context, tx *sql.Tx, propertyID string) error {
	spanContext, _, finishSpan := herosentry.StartSpan(ctx, "PostgresPropertyRepository.DeleteProperty")
	defer finishSpan()

	return database.WithSessionErr(repo.db, spanContext, tx, func(sessionTx *sql.Tx) error {
		query := `DELETE FROM properties WHERE id = $1`

		result, err := sessionTx.ExecContext(spanContext, query, propertyID)
		if err != nil {
			err := fmt.Errorf("failed to delete property: %w", err)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return err
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil {
			err := fmt.Errorf("failed to get rows affected: %w", err)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return err
		}

		if rowsAffected == 0 {
			return ErrPropertyNotFound
		}

		return nil
	})
}

// UpdateProperty updates the property fields.
func (repo *PostgresPropertyRepository) UpdateProperty(ctx context.Context, tx *sql.Tx, propertyRecord *property.Property) (*property.Property, error) {
	spanContext, _, finishSpan := herosentry.StartSpan(ctx, "PostgresPropertyRepository.UpdateProperty")
	defer finishSpan()

	return database.WithSession(repo.db, spanContext, tx, func(sessionTx *sql.Tx) (*property.Property, error) {
		// First, get the existing property to ensure it exists
		existingProperty, err := repo.GetProperty(spanContext, sessionTx, propertyRecord.Id)
		if err != nil {
			return nil, err
		}

		// Update the update time
		propertyRecord.UpdateTime = time.Now().UTC().Format(time.RFC3339)
		propertyRecord.Version = existingProperty.Version + 1

		query := `
			UPDATE properties SET
				org_id = $1, property_number = $2, is_evidence = $3,
				retention_period = $4, property_status = $5, nibrs_property_type = $6, disposal_type = $7, notes = $8,
				current_custodian = $9, current_location = $10, 
				custody_chain = $11, schema_id = $12, schema_version = $13, details = $14, update_time = $15, updated_by = $16,
				version = $17, resource_type = $18
			WHERE id = $19
		`

		// Convert custody chain to JSONB
		custodyChainJSON, err := json.Marshal(propertyRecord.CustodyChain)
		if err != nil {
			err := fmt.Errorf("failed to marshal custody chain: %w", err)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal)
			return nil, err
		}

		// Convert details to JSONB
		var detailsJSON []byte
		if propertyRecord.Details != nil {
			detailsJSON, err = json.Marshal(propertyRecord.Details.AsMap())
			if err != nil {
				return nil, fmt.Errorf("failed to marshal details: %w", err)
			}
		}

		// Handle empty schema_id as NULL for foreign key constraint
		schemaId := nullIfEmpty(propertyRecord.SchemaId)

		result, err := sessionTx.ExecContext(ctx, query,
			propertyRecord.OrgId,
			propertyRecord.PropertyNumber,
			propertyRecord.IsEvidence,
			propertyRecord.RetentionPeriod,
			propertyRecord.PropertyStatus,
			propertyRecord.NibrsPropertyType,
			propertyRecord.DisposalType,
			propertyRecord.Notes,
			propertyRecord.CurrentCustodian,
			propertyRecord.CurrentLocation,
			custodyChainJSON,
			schemaId,
			propertyRecord.SchemaVersion,
			detailsJSON,
			propertyRecord.UpdateTime,
			propertyRecord.UpdatedBy,
			propertyRecord.Version,
			propertyRecord.ResourceType,
			propertyRecord.Id,
		)

		if err != nil {
			err := fmt.Errorf("failed to update property: %w", err)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return nil, err
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil {
			return nil, fmt.Errorf("failed to get rows affected: %w", err)
		}

		if rowsAffected == 0 {
			return nil, ErrPropertyNotFound
		}

		return propertyRecord, nil
	})
}

// SearchProperties executes a comprehensive multi-criteria search against the properties dataset.
// This function supports text search, filtering, pagination, and sorting, similar to SearchEntities.
func (repo *PostgresPropertyRepository) SearchProperties(ctx context.Context, tx *sql.Tx, searchRequest *property.SearchPropertiesRequest) (*property.SearchPropertiesResponse, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PostgresPropertyRepository.SearchProperties")
	defer finish()
	span.SetTag("search.page_size", fmt.Sprintf("%d", searchRequest.PageSize))
	if searchRequest.Query != "" {
		span.SetTag("search.has_query", "true")
	} else {
		span.SetTag("search.has_query", "false")
	}

	return database.WithSession(repo.db, spanContext, tx, func(sessionTx *sql.Tx) (*property.SearchPropertiesResponse, error) {

		// Step 1: Validate and set page size
		pageSize := int(searchRequest.PageSize)
		if pageSize <= 0 {
			pageSize = 50
		}
		if pageSize > 1000 {
			pageSize = 1000
		}

		// Step 2: Parse pagination offset from page token
		offset := 0
		if searchRequest.PageToken != "" {
			if parsedOffset, err := strconv.Atoi(searchRequest.PageToken); err == nil && parsedOffset >= 0 {
				offset = parsedOffset
			}
		}

		// Step 3: Build WHERE conditions
		var conditions []string
		var args []interface{}
		argCounter := 1

		// Organization filter (required for access control)
		organizationID := cmncontext.GetOrgId(spanContext)
		conditions = append(conditions, fmt.Sprintf("org_id = $%d", argCounter))
		args = append(args, organizationID)
		argCounter++

		// Property status filter
		if searchRequest.PropertyStatus != property.PropertyStatus_PROPERTY_STATUS_UNSPECIFIED {
			conditions = append(conditions, fmt.Sprintf("property_status = $%d", argCounter))
			args = append(args, int32(searchRequest.PropertyStatus))
			argCounter++
		}

		// Property type filter using top-level nibrs_property_type column
		if searchRequest.NibrsPropertyType != property.NIBRSPropertyType_PROPERTY_TYPE_UNSPECIFIED {
			conditions = append(conditions, fmt.Sprintf("nibrs_property_type = $%d", argCounter))
			args = append(args, int32(searchRequest.NibrsPropertyType))
			argCounter++
		}

		// Date range filter
		if searchRequest.DateRange != nil {
			if searchRequest.DateRange.From != "" {
				conditions = append(conditions, fmt.Sprintf("create_time >= $%d", argCounter))
				args = append(args, searchRequest.DateRange.From)
				argCounter++
			}
			if searchRequest.DateRange.To != "" {
				conditions = append(conditions, fmt.Sprintf("create_time <= $%d", argCounter))
				args = append(args, searchRequest.DateRange.To)
				argCounter++
			}
		}

		// Text search (general query)
		if searchRequest.Query != "" {
			var searchConditions []string
			for _, field := range []string{"id", "notes", "current_custodian", "current_location"} {
				searchConditions = append(searchConditions, fmt.Sprintf("%s ILIKE $%d", field, argCounter))
				args = append(args, "%"+searchRequest.Query+"%")
				argCounter++
			}
			conditions = append(conditions, "("+strings.Join(searchConditions, " OR ")+")")
		}

		// Field-specific queries
		if len(searchRequest.FieldQueries) > 0 {
			for _, fieldQuery := range searchRequest.FieldQueries {
				field := fieldQuery.Field
				if field == "" || fieldQuery.Query == "" {
					continue
				}

				// Handle different field types appropriately
				switch field {
				case "property_type", "propertyType":
					// Property type is stored in top-level nibrs_property_type column as enum value
					var enumValue property.NIBRSPropertyType
					queryLower := strings.ToLower(fieldQuery.Query)
					switch queryLower {
					case "found", "property_type_found":
						enumValue = property.NIBRSPropertyType_PROPERTY_TYPE_FOUND
					case "seized", "property_type_seized":
						enumValue = property.NIBRSPropertyType_PROPERTY_TYPE_SEIZED
					case "stolen", "property_type_stolen":
						enumValue = property.NIBRSPropertyType_PROPERTY_TYPE_STOLEN
					case "recovered", "property_type_recovered":
						enumValue = property.NIBRSPropertyType_PROPERTY_TYPE_RECOVERED
					case "safekeeping", "property_type_safekeeping":
						enumValue = property.NIBRSPropertyType_PROPERTY_TYPE_FOUND // Map safekeeping to found
					case "missing", "property_type_missing":
						enumValue = property.NIBRSPropertyType_PROPERTY_TYPE_UNKNOWN // Map missing to unknown
					default:
						// Try to parse as enum string (e.g., "PROPERTY_TYPE_FOUND")
						if strings.HasPrefix(strings.ToUpper(fieldQuery.Query), "PROPERTY_TYPE_") {
							enumString := strings.ToUpper(fieldQuery.Query)
							switch enumString {
							case "PROPERTY_TYPE_FOUND":
								enumValue = property.NIBRSPropertyType_PROPERTY_TYPE_FOUND
							case "PROPERTY_TYPE_SEIZED":
								enumValue = property.NIBRSPropertyType_PROPERTY_TYPE_SEIZED
							case "PROPERTY_TYPE_STOLEN":
								enumValue = property.NIBRSPropertyType_PROPERTY_TYPE_STOLEN
							case "PROPERTY_TYPE_RECOVERED":
								enumValue = property.NIBRSPropertyType_PROPERTY_TYPE_RECOVERED
							case "PROPERTY_TYPE_SAFEKEEPING":
								enumValue = property.NIBRSPropertyType_PROPERTY_TYPE_FOUND // Map safekeeping to found
							case "PROPERTY_TYPE_MISSING":
								enumValue = property.NIBRSPropertyType_PROPERTY_TYPE_UNKNOWN // Map missing to unknown
							default:
								enumValue = property.NIBRSPropertyType_PROPERTY_TYPE_FOUND // fallback
							}
						} else {
							enumValue = property.NIBRSPropertyType_PROPERTY_TYPE_FOUND // fallback
						}
					}
					// Query nibrs_property_type from top-level column
					conditions = append(conditions, fmt.Sprintf("nibrs_property_type = $%d", argCounter))
					args = append(args, int32(enumValue))
				case "property_status":
					// Convert enum string to correct integer value based on actual PropertyStatus enum
					var propertyStatusInt int32
					switch strings.ToUpper(fieldQuery.Query) {
					case "UNSPECIFIED":
						propertyStatusInt = 0 // PROPERTY_STATUS_UNSPECIFIED
					case "INTAKE_PENDING":
						propertyStatusInt = 1 // PROPERTY_STATUS_INTAKE_PENDING
					case "COLLECTED":
						propertyStatusInt = 2 // PROPERTY_STATUS_COLLECTED
					case "CHECKED_IN":
						propertyStatusInt = 3 // PROPERTY_STATUS_CHECKED_IN
					case "CHECKED_OUT":
						propertyStatusInt = 4 // PROPERTY_STATUS_CHECKED_OUT
					case "RECOVERED":
						propertyStatusInt = 5 // PROPERTY_STATUS_RECOVERED
					case "FOUND":
						propertyStatusInt = 6 // PROPERTY_STATUS_FOUND
					case "SAFEKEEPING":
						propertyStatusInt = 7 // PROPERTY_STATUS_SAFEKEEPING
					case "AWAITING_DISPOSITION":
						propertyStatusInt = 8 // PROPERTY_STATUS_AWAITING_DISPOSITION
					case "DISPOSED":
						propertyStatusInt = 9 // PROPERTY_STATUS_DISPOSED
					case "MISSING":
						propertyStatusInt = 10 // PROPERTY_STATUS_MISSING
					case "STOLEN":
						propertyStatusInt = 11 // PROPERTY_STATUS_STOLEN
					default:
						// Try to parse as integer
						if val, err := strconv.ParseInt(fieldQuery.Query, 10, 32); err == nil {
							propertyStatusInt = int32(val)
						} else {
							continue // Skip invalid values
						}
					}
					conditions = append(conditions, fmt.Sprintf("property_status = $%d", argCounter))
					args = append(args, propertyStatusInt)
				case "is_evidence", "version":
					// These are integer fields, use exact match
					if val, err := strconv.ParseInt(fieldQuery.Query, 10, 32); err == nil {
						conditions = append(conditions, fmt.Sprintf("%s = $%d", field, argCounter))
						args = append(args, int32(val))
					} else {
						continue // Skip invalid values
					}
				default:
					// Text fields, use ILIKE for case-insensitive search
					conditions = append(conditions, fmt.Sprintf("%s ILIKE $%d", field, argCounter))
					args = append(args, "%"+fieldQuery.Query+"%")
				}
				argCounter++
			}
		}

		// Step 4: Build the main query
		query := `SELECT id, org_id, property_number, is_evidence, retention_period, property_status, nibrs_property_type, disposal_type, notes, current_custodian, current_location, custody_chain, schema_id, schema_version, details, create_time, update_time, created_by, updated_by, version, resource_type FROM properties`
		if len(conditions) > 0 {
			query += " WHERE " + strings.Join(conditions, " AND ")
		}

		// Step 5: Add ORDER BY
		switch searchRequest.OrderBy {
		case property.SearchOrderBy_SEARCH_ORDER_BY_CREATED_AT:
			query += " ORDER BY create_time DESC"
		case property.SearchOrderBy_SEARCH_ORDER_BY_UPDATED_AT:
			query += " ORDER BY update_time DESC"
		case property.SearchOrderBy_SEARCH_ORDER_BY_STATUS:
			query += " ORDER BY property_status ASC"
		case property.SearchOrderBy_SEARCH_ORDER_BY_RELEVANCE:
			query += " ORDER BY create_time DESC"
		default:
			query += " ORDER BY create_time DESC"
		}

		// Step 6: Pagination
		query += fmt.Sprintf(" LIMIT $%d OFFSET $%d", argCounter, argCounter+1)
		args = append(args, pageSize, offset)

		// Step 7: Execute the main query
		rows, err := sessionTx.QueryContext(spanContext, query, args...)
		if err != nil {
			err := fmt.Errorf("failed to search properties: %w", err)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return nil, err
		}
		defer rows.Close()

		var properties []*property.Property
		for rows.Next() {
			var propertyRecord property.Property
			var custodyChainJSON []byte
			var detailsJSON []byte
			var retentionPeriodNullable, notesNullable, currentCustodianNullable, currentLocationNullable, createdByNullable, updatedByNullable sql.NullString
			var disposalTypeNullable sql.NullInt32
			err := rows.Scan(
				&propertyRecord.Id,
				&propertyRecord.OrgId,
				&propertyRecord.PropertyNumber,
				&propertyRecord.IsEvidence,
				&retentionPeriodNullable,
				&propertyRecord.PropertyStatus,
				&propertyRecord.NibrsPropertyType,
				&disposalTypeNullable,
				&notesNullable,
				&currentCustodianNullable,
				&currentLocationNullable,
				&custodyChainJSON,
				&propertyRecord.SchemaId,
				&propertyRecord.SchemaVersion,
				&detailsJSON,
				&propertyRecord.CreateTime,
				&propertyRecord.UpdateTime,
				&createdByNullable,
				&updatedByNullable,
				&propertyRecord.Version,
				&propertyRecord.ResourceType,
			)
			if err != nil {
				err := fmt.Errorf("failed to scan property: %w", err)
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
				return nil, err
			}

			// Handle nullable string fields
			if retentionPeriodNullable.Valid {
				propertyRecord.RetentionPeriod = retentionPeriodNullable.String
			} else {
				propertyRecord.RetentionPeriod = ""
			}
			if disposalTypeNullable.Valid {
				propertyRecord.DisposalType = property.PropertyDisposalType(disposalTypeNullable.Int32)
			} else {
				propertyRecord.DisposalType = property.PropertyDisposalType_PROPERTY_DISPOSAL_TYPE_UNSPECIFIED
			}
			if notesNullable.Valid {
				propertyRecord.Notes = notesNullable.String
			} else {
				propertyRecord.Notes = ""
			}
			if currentCustodianNullable.Valid {
				propertyRecord.CurrentCustodian = currentCustodianNullable.String
			} else {
				propertyRecord.CurrentCustodian = ""
			}
			if currentLocationNullable.Valid {
				propertyRecord.CurrentLocation = currentLocationNullable.String
			} else {
				propertyRecord.CurrentLocation = ""
			}
			if createdByNullable.Valid {
				propertyRecord.CreatedBy = createdByNullable.String
			} else {
				propertyRecord.CreatedBy = ""
			}
			if updatedByNullable.Valid {
				propertyRecord.UpdatedBy = updatedByNullable.String
			} else {
				propertyRecord.UpdatedBy = ""
			}
			if len(custodyChainJSON) > 0 {
				err = json.Unmarshal(custodyChainJSON, &propertyRecord.CustodyChain)
				if err != nil {
					// Failed to unmarshal custody chain
				}
			}

			// Parse details JSON
			if len(detailsJSON) > 0 {
				var detailsMap map[string]interface{}
				err = json.Unmarshal(detailsJSON, &detailsMap)
				if err != nil {
					// Failed to unmarshal details
				} else {
					detailsStruct, err := structpb.NewStruct(detailsMap)
					if err != nil {
						// Failed to create structpb from details
					} else {
						propertyRecord.Details = detailsStruct
					}
				}
			}

			properties = append(properties, &propertyRecord)
		}
		if err = rows.Err(); err != nil {
			err := fmt.Errorf("error iterating properties: %w", err)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return nil, err
		}

		// Step 8: Get total count for pagination
		countQuery := "SELECT COUNT(*) FROM properties"
		if len(conditions) > 0 {
			countQuery += " WHERE " + strings.Join(conditions, " AND ")
		}
		countRows, err := sessionTx.QueryContext(spanContext, countQuery, args[:len(args)-2]...)
		if err != nil {
			err := fmt.Errorf("failed to get total count: %w", err)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return nil, err
		}
		defer countRows.Close()
		var totalCount int
		if countRows.Next() {
			err = countRows.Scan(&totalCount)
			if err != nil {
				err := fmt.Errorf("failed to scan total count: %w", err)
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
				return nil, err
			}
		}

		// Step 9: Next page token
		var nextPageToken string
		if len(properties) == pageSize {
			nextPageToken = strconv.Itoa(offset + pageSize)
		}

		// Step 10: Highlights (simple)
		var highlights []*property.HighlightResult
		if searchRequest.Query != "" {
			highlights = append(highlights, &property.HighlightResult{
				Field:     "general",
				Fragments: []string{searchRequest.Query},
			})
		}

		// Check for integer overflow before conversion
		if totalCount > math.MaxInt32 {
			err := fmt.Errorf("total count exceeds maximum int32 value")
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal)
			return nil, err
		}

		// Safe conversion after bounds check
		// nolint:gosec
		totalCountInt32 := int32(totalCount)

		return &property.SearchPropertiesResponse{
			Properties:    properties,
			NextPageToken: nextPageToken,
			Highlights:    highlights,
			TotalCount:    totalCountInt32,
		}, nil
	})
}

// BatchGetProperties retrieves multiple properties by their IDs.
func (repo *PostgresPropertyRepository) BatchGetProperties(ctx context.Context, tx *sql.Tx, propertyIDs []string) ([]*property.Property, error) {
	spanContext, _, finishSpan := herosentry.StartSpan(ctx, "PostgresPropertyRepository.BatchGetProperties")
	defer finishSpan()

	return database.WithSession(repo.db, spanContext, tx, func(sessionTx *sql.Tx) ([]*property.Property, error) {
		if len(propertyIDs) == 0 {
			return []*property.Property{}, nil
		}

		// Build the query with placeholders
		placeholders := make([]string, len(propertyIDs))
		args := make([]interface{}, len(propertyIDs))
		for i, id := range propertyIDs {
			placeholders[i] = fmt.Sprintf("$%d", i+1)
			args[i] = id
		}

		// Build the IN clause properly
		inClause := strings.Join(placeholders, ", ")
		// nolint:gosec
		query := "SELECT id, org_id, property_number, is_evidence, retention_period, property_status, nibrs_property_type, disposal_type, notes, current_custodian, current_location, custody_chain, schema_id, schema_version, details, create_time, update_time, created_by, updated_by, version, resource_type FROM properties WHERE id IN (" + inClause + ")"

		rows, err := sessionTx.QueryContext(spanContext, query, args...)
		if err != nil {
			err := fmt.Errorf("failed to batch get properties: %w", err)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return nil, err
		}
		defer rows.Close()

		var properties []*property.Property
		for rows.Next() {
			var propertyRecord property.Property
			var custodyChainJSON []byte
			var detailsJSON []byte
			var createdByNull sql.NullString
			var updatedByNull sql.NullString

			err := rows.Scan(
				&propertyRecord.Id,
				&propertyRecord.OrgId,
				&propertyRecord.PropertyNumber,
				&propertyRecord.IsEvidence,
				&propertyRecord.RetentionPeriod,
				&propertyRecord.PropertyStatus,
				&propertyRecord.NibrsPropertyType,
				&propertyRecord.DisposalType,
				&propertyRecord.Notes,
				&propertyRecord.CurrentCustodian,
				&propertyRecord.CurrentLocation,
				&custodyChainJSON,
				&propertyRecord.SchemaId,
				&propertyRecord.SchemaVersion,
				&detailsJSON,
				&propertyRecord.CreateTime,
				&propertyRecord.UpdateTime,
				&createdByNull,
				&updatedByNull,
				&propertyRecord.Version,
				&propertyRecord.ResourceType,
			)

			if err != nil {
				err := fmt.Errorf("failed to scan property: %w", err)
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
				return nil, err
			}

			// Handle nullable asset ID fields
			if createdByNull.Valid {
				propertyRecord.CreatedBy = createdByNull.String
			}
			if updatedByNull.Valid {
				propertyRecord.UpdatedBy = updatedByNull.String
			}

			// Parse custody chain JSON
			if len(custodyChainJSON) > 0 {
				err = json.Unmarshal(custodyChainJSON, &propertyRecord.CustodyChain)
				if err != nil {
					// Failed to unmarshal custody chain
				}
			}

			// Parse details JSON
			if len(detailsJSON) > 0 {
				var detailsMap map[string]interface{}
				err = json.Unmarshal(detailsJSON, &detailsMap)
				if err != nil {
					// Failed to unmarshal details
				} else {
					detailsStruct, err := structpb.NewStruct(detailsMap)
					if err != nil {
						// Failed to create structpb from details
					} else {
						propertyRecord.Details = detailsStruct
					}
				}
			}

			properties = append(properties, &propertyRecord)
		}

		if err = rows.Err(); err != nil {
			err := fmt.Errorf("error iterating properties: %w", err)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return nil, err
		}

		return properties, nil
	})
}

// AddCustodyEvent adds a new custody event to a property's chain of custody.
func (repo *PostgresPropertyRepository) AddCustodyEvent(ctx context.Context, tx *sql.Tx, propertyID string, custodyEvent *property.CustodyEvent) error {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PostgresPropertyRepository.AddCustodyEvent")
	defer finish()
	span.SetTag("property.id", propertyID)
	span.SetTag("custody.action_type", custodyEvent.ActionType.String())
	span.SetTag("custody.timestamp", custodyEvent.Timestamp)

	// Get the current property
	propertyRecord, err := repo.GetProperty(spanContext, tx, propertyID)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to get property for custody event: %s", propertyID))
		return err
	}

	// Add the new custody event
	propertyRecord.CustodyChain = append(propertyRecord.CustodyChain, custodyEvent)

	// Update the property with the new custody chain
	_, err = repo.UpdateProperty(spanContext, tx, propertyRecord)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to update property with custody event: %s", propertyID))
		return err
	}
	return nil
}

// GetCustodyChain retrieves the complete chain of custody for a property.
func (repo *PostgresPropertyRepository) GetCustodyChain(ctx context.Context, tx *sql.Tx, propertyID string) ([]*property.CustodyEvent, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PostgresPropertyRepository.GetCustodyChain")
	defer finish()
	span.SetTag("property.id", propertyID)

	return database.WithSession(repo.db, spanContext, tx, func(sessionTx *sql.Tx) ([]*property.CustodyEvent, error) {

		query := `
			SELECT custody_chain
			FROM properties
			WHERE id = $1
		`

		var custodyChainJSON []byte
		err := sessionTx.QueryRowContext(spanContext, query, propertyID).Scan(&custodyChainJSON)
		if err != nil {
			if err == sql.ErrNoRows {
				err := fmt.Errorf("property not found: %s", propertyID)
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeNotFound)
				return nil, err
			}
			err := fmt.Errorf("failed to get custody chain: %w", err)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return nil, err
		}

		var custodyChain []*property.CustodyEvent
		if len(custodyChainJSON) > 0 {
			err = json.Unmarshal(custodyChainJSON, &custodyChain)
			if err != nil {
				err := fmt.Errorf("failed to unmarshal custody chain: %w", err)
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal)
				return nil, err
			}
		}
		return custodyChain, nil
	})
}

// ListPropertyFileAttachments lists all file attachments for a property.
func (repo *PostgresPropertyRepository) ListPropertyFileAttachments(ctx context.Context, tx *sql.Tx, request *property.ListPropertyFileAttachmentsRequest) (*property.ListPropertyFileAttachmentsResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "PostgresPropertyRepository.ListPropertyFileAttachments")
	defer finishSpan()

	span.SetTag("property.id", request.PropertyId)
	span.SetTag("list.page_size", fmt.Sprintf("%d", request.PageSize))
	span.SetTag("list.page_token", request.PageToken)
	if request.FileCategory != "" {
		span.SetTag("filter.file_category", request.FileCategory)
	}

	return database.WithSession(repo.db, spanContext, tx, func(sessionTx *sql.Tx) (*property.ListPropertyFileAttachmentsResponse, error) {
		// Build the base query
		baseQuery := `
			SELECT id, property_id, file_id, caption, display_name, display_order, file_category, metadata, org_id
			FROM property_file_attachments
			WHERE property_id = $1
		`

		queryParams := []interface{}{request.PropertyId}
		paramIndex := 2

		// Add file category filter if specified
		if request.FileCategory != "" {
			baseQuery += fmt.Sprintf(" AND file_category = $%d", paramIndex)
			queryParams = append(queryParams, request.FileCategory)
			paramIndex++
		}

		// Add ordering
		baseQuery += " ORDER BY display_order, id"

		// Handle pagination
		pageSize := int(request.PageSize)
		if pageSize <= 0 {
			pageSize = 50 // Default page size
		}
		if pageSize > 1000 {
			pageSize = 1000 // Max page size
		}

		offset := 0
		if request.PageToken != "" {
			parsedOffset, err := strconv.Atoi(request.PageToken)
			if err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation, "Invalid page token")
				return nil, fmt.Errorf("invalid page token: %w", err)
			}
			offset = parsedOffset
		}

		// Add pagination to query
		baseQuery += fmt.Sprintf(" LIMIT $%d OFFSET $%d", paramIndex, paramIndex+1)
		queryParams = append(queryParams, pageSize+1, offset) // +1 to check if there are more results

		// Execute query
		rows, err := sessionTx.QueryContext(spanContext, baseQuery, queryParams...)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to query property file attachments")
			return nil, fmt.Errorf("failed to list property file attachments: %w", err)
		}
		defer rows.Close()

		var fileAttachments []*property.PropertyFileReference
		for rows.Next() {
			var (
				attachmentId, propertyId, fileId, caption, displayName, fileCategory string
				displayOrder                                                         int32
				metadataBytes                                                        []byte
				orgID                                                                int32
			)
			if err := rows.Scan(&attachmentId, &propertyId, &fileId, &caption, &displayName, &displayOrder, &fileCategory, &metadataBytes, &orgID); err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to scan file attachment row")
				return nil, fmt.Errorf("failed to scan file attachment row: %w", err)
			}

			var metadataStruct *structpb.Struct
			if len(metadataBytes) > 0 {
				var m map[string]interface{}
				if err := json.Unmarshal(metadataBytes, &m); err == nil {
					metadataStruct, _ = structpb.NewStruct(m)
				}
			} else {
				// Create empty struct for nil metadata
				metadataStruct, _ = structpb.NewStruct(map[string]interface{}{})
			}

			fileAttachments = append(fileAttachments, &property.PropertyFileReference{
				Id:           attachmentId,
				PropertyId:   propertyId,
				FileId:       fileId,
				Caption:      caption,
				DisplayName:  displayName,
				DisplayOrder: displayOrder,
				FileCategory: fileCategory,
				Metadata:     metadataStruct,
			})
		}

		// Check for next page
		var nextPageToken string
		if len(fileAttachments) > pageSize {
			fileAttachments = fileAttachments[:pageSize] // Trim to actual page size
			nextPageToken = strconv.Itoa(offset + pageSize)
		}

		return &property.ListPropertyFileAttachmentsResponse{
			FileAttachments: fileAttachments,
			NextPageToken:   nextPageToken,
		}, nil
	})
}

// AddPropertyFileAttachment adds a file attachment to a property.
func (repo *PostgresPropertyRepository) AddPropertyFileAttachment(ctx context.Context, tx *sql.Tx, request *property.AddPropertyFileAttachmentRequest) (*property.AddPropertyFileAttachmentResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "PostgresPropertyRepository.AddPropertyFileAttachment")
	defer finishSpan()

	span.SetTag("property.id", request.PropertyId)
	span.SetTag("file.id", request.FileAttachment.FileId)

	return database.WithSession(repo.db, spanContext, tx, func(sessionTx *sql.Tx) (*property.AddPropertyFileAttachmentResponse, error) {
		// Generate attachment ID if not provided
		attachmentId := request.FileAttachment.Id
		if attachmentId == "" {
			attachmentId = uuid.New().String()
		}

		// Convert metadata to JSONB
		var metadataJSON []byte
		if request.FileAttachment.Metadata != nil {
			var err error
			metadataJSON, err = json.Marshal(request.FileAttachment.Metadata.AsMap())
			if err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, "Failed to marshal metadata")
				return nil, fmt.Errorf("failed to marshal metadata: %w", err)
			}
		} else {
			// Use empty JSON object for nil metadata
			metadataJSON = []byte("{}")
		}

		// Insert the file attachment
		query := `
			INSERT INTO property_file_attachments (
				id, property_id, file_id, caption, display_name, display_order, file_category, metadata, org_id
			) VALUES (
				$1, $2, $3, $4, $5, $6, $7, $8, $9
			)
		`

		// Get the org_id from the property
		var orgID int32
		err := sessionTx.QueryRowContext(spanContext, "SELECT org_id FROM properties WHERE id = $1", request.PropertyId).Scan(&orgID)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to get property org_id")
			return nil, fmt.Errorf("failed to get property org_id: %w", err)
		}

		_, err = sessionTx.ExecContext(spanContext, query,
			attachmentId,
			request.PropertyId,
			request.FileAttachment.FileId,
			request.FileAttachment.Caption,
			request.FileAttachment.DisplayName,
			request.FileAttachment.DisplayOrder,
			request.FileAttachment.FileCategory,
			metadataJSON,
			orgID,
		)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to insert property file attachment")
			return nil, fmt.Errorf("failed to add property file attachment: %w", err)
		}

		// Create the response
		fileReference := &property.PropertyFileReference{
			Id:           attachmentId,
			PropertyId:   request.PropertyId,
			FileId:       request.FileAttachment.FileId,
			Caption:      request.FileAttachment.Caption,
			DisplayName:  request.FileAttachment.DisplayName,
			DisplayOrder: request.FileAttachment.DisplayOrder,
			FileCategory: request.FileAttachment.FileCategory,
			Metadata:     request.FileAttachment.Metadata,
		}

		return &property.AddPropertyFileAttachmentResponse{
			FileAttachment: fileReference,
		}, nil
	})
}

// RemovePropertyFileAttachment removes a file attachment from a property.
func (repo *PostgresPropertyRepository) RemovePropertyFileAttachment(ctx context.Context, tx *sql.Tx, request *property.RemovePropertyFileAttachmentRequest) (*property.RemovePropertyFileAttachmentResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "PostgresPropertyRepository.RemovePropertyFileAttachment")
	defer finishSpan()

	span.SetTag("property.id", request.PropertyId)
	span.SetTag("attachment.id", request.AttachmentId)

	return database.WithSession(repo.db, spanContext, tx, func(sessionTx *sql.Tx) (*property.RemovePropertyFileAttachmentResponse, error) {
		// Delete the file attachment
		query := `
			DELETE FROM property_file_attachments
			WHERE id = $1 AND property_id = $2
		`

		result, err := sessionTx.ExecContext(spanContext, query, request.AttachmentId, request.PropertyId)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to delete property file attachment")
			return nil, fmt.Errorf("failed to remove property file attachment: %w", err)
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to get rows affected")
			return nil, fmt.Errorf("failed to get rows affected: %w", err)
		}

		if rowsAffected == 0 {
			err := fmt.Errorf("property file attachment not found: %s", request.AttachmentId)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeNotFound)
			return nil, err
		}

		return &property.RemovePropertyFileAttachmentResponse{}, nil
	})
}
