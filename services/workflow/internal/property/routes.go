package property

import (
	"database/sql"
	"log"
	"net/http"

	"common/herosentry"
	"proto/hero/property/v1/propertyconnect"
	"workflow/internal/common/middleware"
	"workflow/internal/property/api/connect"
	propertyRepository "workflow/internal/property/data"
	"workflow/internal/property/usecase"

	connectgo "connectrpc.com/connect"
)

func RegisterRoutes(
	mux *http.ServeMux,
	propertyDB *sql.DB,
	propertyRepo propertyRepository.PropertyRepository) {
	// Initialize Property UseCase
	propertyUseCase, err := usecase.NewPropertyUseCase(propertyDB, propertyRepo)
	if err != nil {
		log.Fatalf("Failed to initialize Property Use Case: %v", err)
	}

	// Create our Connect-based Property Server
	propertyServer := connect.NewPropertyServer(propertyUseCase, nil)

	// Generate HTTP handler from Connect with herosentry interceptor
	servicePath, serviceHandler := propertyconnect.NewPropertyServiceHandler(
		propertyServer,
		connectgo.WithInterceptors(herosentry.RPCServiceInterceptor()),
	)

	// Add the handler to the mux
	mux.Handle(servicePath, middleware.LoggingMiddleware(serviceHandler))
}
