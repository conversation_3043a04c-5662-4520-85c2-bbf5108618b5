# Property Module

In our system, a **Property** represents a critical operational entity—whether it's evidence collected during an investigation, seized items from a traffic stop, found property turned in by citizens, or items held for safekeeping—that plays a vital role in law enforcement operations. Each property is registered with unique details including its current status, chain of custody, and evidence classification. This rich data set is essential for maintaining evidence integrity and supporting legal proceedings.

During operations, properties are continuously tracked and managed through our **Property Management Service**. The workflow leverages these properties by:
- **Chain of custody tracking:** Maintaining complete audit trails of property custody changes and transfers.
- **Evidence management:** Handling evidence-specific workflows, retention periods, and disposal requirements.
- **Status monitoring:** Tracking property status through its lifecycle from collection to final disposition.
- **Case association:** Linking properties to specific cases and officers for investigative support.

---

## Running Locally

When you run `make run`, the service will, by default, run on `localhost:9086`. It uses the locally running PostgreSQL Docker container for database purposes.  
If you encounter database errors, run `make db` **before** `make run` to ensure that the local database is properly set up and migrated to the correct version.

If you prefer to switch to an in‑memory database, update the `<root>/docker-compose.yml` file's `workflow-service > environment > REPO_TYPE` value to `inmemory`.

---

## Authentication and Rate Limiting

### Authentication

All API endpoints require authentication using Bearer tokens. Include your authentication token in the `Authorization` header with each request:

```http
Authorization: Bearer <your-token-here>
```

**Authentication Requirements:**
- Valid JWT token issued by the authentication service
- Token must be active and not expired
- Token must have appropriate permissions for the requested operation
- Organization context is automatically derived from the token

**Token Format:**
```json
{
  "sub": "user-id",
  "org_id": "organization-id",
  "permissions": ["property:read", "property:write"],
  "exp": 1640995200
}
```

### Rate Limiting

The Property Service implements rate limiting to ensure fair usage and system stability.

**Rate Limit Policies:**

| Limit Type | Limit | Window | Description |
|------------|-------|--------|-------------|
| Per User | 1000 requests | 1 minute | Individual user request limit |
| Per Organization | 5000 requests | 1 minute | Organization-wide request limit |
| Per Endpoint | 100 requests | 1 minute | Specific endpoint limit |
| Search Operations | 50 requests | 1 minute | Search-specific limit |

**Rate Limit Headers:**
```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 850
X-RateLimit-Reset: 1640995260
X-RateLimit-Reset-Time: 2022-01-01T12:01:00Z
```

### Error Response Format

When rate limits are exceeded or authentication fails, the service returns structured error responses:

**Rate Limit Exceeded (429):**
```json
{
  "error": {
    "code": "RATE_LIMIT_EXCEEDED",
    "message": "Rate limit exceeded. Try again in 60 seconds.",
    "details": {
      "limit": 1000,
      "remaining": 0,
      "reset_time": "2022-01-01T12:01:00Z",
      "retry_after": 60
    }
  }
}
```

**Authentication Error (401):**
```json
{
  "error": {
    "code": "UNAUTHENTICATED",
    "message": "Invalid or missing authentication token",
    "details": {
      "required_scopes": ["property:read"]
    }
  }
}
```

**Permission Error (403):**
```json
{
  "error": {
    "code": "PERMISSION_DENIED",
    "message": "Insufficient permissions for this operation",
    "details": {
      "required_permissions": ["property:write"],
      "user_permissions": ["property:read"]
    }
  }
}
```

### Recommended Retry Strategies

**For Rate Limit Errors (429):**
1. **Exponential Backoff**: Start with 1 second delay, double on each retry
2. **Maximum Retries**: Limit to 3-5 retries to avoid overwhelming the service
3. **Respect Retry-After**: Use the `retry_after` value from the error response

**Example Retry Implementation:**
```javascript
async function makeRequestWithRetry(url, options, maxRetries = 3) {
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      const response = await fetch(url, options);
      
      if (response.status === 429) {
        const error = await response.json();
        const retryAfter = error.error.details.retry_after || 60;
        
        if (attempt < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, retryAfter * 1000));
          continue;
        }
      }
      
      return response;
    } catch (error) {
      if (attempt === maxRetries) throw error;
      await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
    }
  }
}
```

**For Authentication Errors (401):**
1. **Token Refresh**: Attempt to refresh the authentication token
2. **Re-authentication**: Redirect user to login if token refresh fails
3. **No Retry**: Do not retry with the same invalid token

**For Permission Errors (403):**
1. **No Retry**: Permission errors indicate insufficient access rights
2. **User Notification**: Inform the user about missing permissions
3. **Escalation**: Provide option to request additional permissions

**Best Practices:**
- Implement circuit breaker pattern for repeated failures
- Log retry attempts for monitoring and debugging
- Use jitter in retry delays to prevent thundering herd
- Monitor rate limit usage to optimize request patterns
- Cache responses when appropriate to reduce API calls

---

# Data Model Reference

## Enums

### NIBRSPropertyType
Defines the NIBRS property types:

| Name                        | Value | Description                                |
|-----------------------------|-------|--------------------------------------------|
| PROPERTY_TYPE_UNSPECIFIED   | 0     | Default unspecified property type.         |
| PROPERTY_TYPE_NONE          | 1     | None.                                      |
| PROPERTY_TYPE_BURNED        | 2     | Burned.                                    |
| PROPERTY_TYPE_FORGED        | 3     | Forged.                                    |
| PROPERTY_TYPE_DAMAGED       | 4     | Damaged.                                   |
| PROPERTY_TYPE_RECOVERED     | 5     | Recovered.                                 |
| PROPERTY_TYPE_SEIZED        | 6     | Seized.                                    |
| PROPERTY_TYPE_STOLEN        | 7     | Stolen.                                    |
| PROPERTY_TYPE_UNKNOWN       | 8     | Unknown.                                   |
| PROPERTY_TYPE_FOUND         | 9     | Found.                                     |

### PropertyStatus
Defines the operational status of a property:

| Name                              | Value | Description                                  |
|-----------------------------------|-------|----------------------------------------------|
| PROPERTY_STATUS_UNSPECIFIED       | 0     | Default unspecified property status.         |
| PROPERTY_STATUS_INTAKE_PENDING    | 1     | Intake pending.                              |
| PROPERTY_STATUS_COLLECTED         | 2     | Property collected into custody.             |
| PROPERTY_STATUS_CHECKED_IN        | 3     | Checked into evidence.                       |
| PROPERTY_STATUS_CHECKED_OUT       | 4     | Checked out for analysis/court.              |
| PROPERTY_STATUS_RECOVERED         | 5     | Recovered.                                   |
| PROPERTY_STATUS_FOUND             | 6     | Found.                                       |
| PROPERTY_STATUS_SAFEKEEPING       | 7     | Safekeeping.                                 |
| PROPERTY_STATUS_AWAITING_DISPOSITION | 8  | Awaiting disposition.                        |
| PROPERTY_STATUS_DISPOSED          | 9     | Disposed.                                    |
| PROPERTY_STATUS_MISSING           | 10    | Missing.                                     |
| PROPERTY_STATUS_STOLEN            | 11    | Stolen.                                      |

### PropertyDisposalType
Defines how property was disposed:

| Name                                | Value | Description                                |
|-------------------------------------|-------|--------------------------------------------|
| PROPERTY_DISPOSAL_TYPE_UNSPECIFIED | 0     | Default unspecified disposal type.         |
| PROPERTY_DISPOSAL_TYPE_RELEASED    | 1     | Property was released to owner.            |
| PROPERTY_DISPOSAL_TYPE_DESTROYED   | 2     | Property was destroyed.                    |
| PROPERTY_DISPOSAL_TYPE_AUCTIONED   | 3     | Property was auctioned.                    |
| PROPERTY_DISPOSAL_TYPE_AGENCY_RETAIN | 4   | Property was retained by agency.           |
| PROPERTY_DISPOSAL_TYPE_TRANSFERRED | 5     | Property was transferred to another agency.|

### CustodyActionType
Defines the types of custody changes:

| Name                                   | Value | Description                                  |
|----------------------------------------|-------|----------------------------------------------|
| CUSTODY_ACTION_TYPE_UNSPECIFIED        | 0     | Default unspecified custody action.          |
| CUSTODY_ACTION_TYPE_COLLECTED          | 1     | Property initially collected into custody.   |
| CUSTODY_ACTION_TYPE_CHECKED_IN         | 2     | Property checked into evidence room.         |
| CUSTODY_ACTION_TYPE_CHECKED_OUT        | 3     | Property checked out.                        |
| CUSTODY_ACTION_TYPE_TRANSFERRED        | 4     | Property transferred to another entity.      |
| CUSTODY_ACTION_TYPE_RELEASED           | 5     | Property released to owner.                  |
| CUSTODY_ACTION_TYPE_DISPOSED           | 6     | Property disposed/destroyed.                 |
| CUSTODY_ACTION_TYPE_LOGGED             | 7     | Reported but not in physical custody.        |
| CUSTODY_ACTION_TYPE_MOVED              | 8     | Moved to a new location.                     |

### CustodyEntityType
Defines the receiving entity types for custody events:

| Name                          | Value |
|-------------------------------|-------|
| CUSTODY_ENTITY_TYPE_UNSPECIFIED | 0   |
| CUSTODY_ENTITY_TYPE_PERSON      | 1   |
| CUSTODY_ENTITY_TYPE_CRIME_LAB   | 2   |
| CUSTODY_ENTITY_TYPE_COURT       | 3   |
| CUSTODY_ENTITY_TYPE_SPECIALIST  | 4   |
| CUSTODY_ENTITY_TYPE_CSI         | 5   |
| CUSTODY_ENTITY_TYPE_ORGANIZATION| 6   |
| CUSTODY_ENTITY_TYPE_OTHER       | 7   |

### CustodyAgencyType
Defines the agency types for custody events:

| Name                          | Value |
|-------------------------------|-------|
| CUSTODY_AGENCY_TYPE_UNSPECIFIED | 0   |
| CUSTODY_AGENCY_TYPE_PD          | 1   |
| CUSTODY_AGENCY_TYPE_OTHER       | 2   |

### SearchOrderBy
Defines the ordering options for search results:

| Name                                    | Value | Description                                |
|-----------------------------------------|-------|--------------------------------------------|
| SEARCH_ORDER_BY_UNSPECIFIED            | 0     | Default unspecified order.                 |
| SEARCH_ORDER_BY_RELEVANCE               | 1     | Order by search relevance.                 |
| SEARCH_ORDER_BY_CREATED_AT              | 2     | Order by creation time.                    |
| SEARCH_ORDER_BY_UPDATED_AT              | 3     | Order by update time.                      |
| SEARCH_ORDER_BY_STATUS                  | 4     | Order by status.                           |

---

## Messages

### Property
Represents a property entity.

| Field              | Type                      | Description                                                                      |
|--------------------|---------------------------|----------------------------------------------------------------------------------|
| id                 | string                    | Unique identifier for the property.                                             |
| orgId              | int32                     | Organization identifier.                                                         |
| propertyNumber     | string                    | Property/Evidence tracking number.                                              |
| propertyStatus     | PropertyStatus            | Current status of the property.                                                 |
| isEvidence         | bool                      | Whether this property is evidence.                                              |
| retentionPeriod    | string                    | Retention period for evidence.                                                  |
| disposalType       | PropertyDisposalType      | How property was disposed.                                                       |
| notes              | string                    | Additional notes about the property.                                            |
| currentCustodian   | string                    | Current person/agency with custody.                                             |
| currentLocation    | string                    | Current location of the property.                                               |
| custodyChain       | repeated CustodyEvent     | Complete chain of custody history.                                              |
| schemaId           | string                    | ID of the dynamic schema this property conforms to.                             |
| schemaVersion      | int32                     | Version of the schema used.                                                     |
| details            | Struct                    | Dynamic payload validated by the resolved schema.                               |
| createTime         | string                    | ISO8601 timestamp when the property was created.                                |
| updateTime         | string                    | ISO8601 timestamp when the property was last updated.                           |
| createdBy          | string                    | User ID of the creator.                                                         |
| updatedBy          | string                    | User ID of the last updater.                                                    |
| version            | int32                     | Version number (for audit/history).                                             |
| resourceType       | string                    | Fixed value `"PROPERTY"`.                                                       |
| nibrsPropertyType  | NIBRSPropertyType         | NIBRS property classification.                                                  |

### CustodyEvent
Represents a single chain of custody entry.

| Field                   | Type                | Description                                           |
|-------------------------|---------------------|-------------------------------------------------------|
| timestamp               | string              | ISO8601 timestamp of the custody change.              |
| transferringUserId      | string              | User/Officer transferring the item.                   |
| transferringAgency      | string              | Agency transferring the item.                         |
| receivingUserId         | string              | Receiving user/officer (optional for disposal).       |
| receivingAgency         | string              | Receiving agency.                                     |
| newLocation             | string              | New location (e.g., "Evidence Room A-12").          |
| actionType              | CustodyActionType   | Type of custody action.                               |
| notes                   | string              | Reason/purpose for the change.                        |
| caseNumber              | string              | Associated case number.                               |
| evidenceNumber          | string              | Evidence tracking number.                             |
| performingOfficerId     | string              | Officer performing the action.                        |
| receivingEntityType     | CustodyEntityType   | Type of receiving entity.                             |
| receivingEntityName     | string              | Name of the receiving entity.                         |
| checkoutLength          | string              | Length of checkout (e.g., "2 weeks").               |
| reason                  | string              | Specific reason for the action.                       |
| receivingAgencyType     | CustodyAgencyType   | Type of receiving agency.                             |
| confirmationReceived    | bool                | Whether receipt confirmation was obtained.            |
| collectionLocation      | string              | Original collection location.                         |
| disposalType            | PropertyDisposalType| Disposal type (for DISPOSED actions).                 |

### PropertyFileReference
Represents a file attachment for a property.

| Field         | Type   | Description                               |
|---------------|--------|-------------------------------------------|
| id            | string | Attachment identifier.                    |
| propertyId    | string | Associated property ID.                   |
| fileId        | string | File ID in the file repository.           |
| caption       | string | Caption for the file.                     |
| displayName   | string | Display name for the file.                |
| displayOrder  | int32  | Ordering index.                           |
| fileCategory  | string | Category for filtering/grouping.          |
| metadata      | Struct | Additional metadata.                      |

### DateRange
Represents a time range for filtering search results.

| Field | Type   | Description                                           |
|-------|--------|-------------------------------------------------------|
| from  | string | Start time in RFC3339 format (inclusive).            |
| to    | string | End time in RFC3339 format (inclusive).              |

### FieldQuery
Represents a field-specific search query.

| Field | Type   | Description                                           |
|-------|--------|-------------------------------------------------------|
| field | string | Field to search in (id, notes, current_custodian, current_location). |
| query | string | Search term for this specific field.                  |

### HighlightResult
Represents highlighted search results for a property.

| Field     | Type             | Description                                           |
|-----------|------------------|-------------------------------------------------------|
| field     | string           | Field name that had a match.                          |
| fragments | repeated string  | Highlighted fragments with matched terms.             |

---

## Overview of Endpoints

The service defines the following operations:

1. **[CreateProperty](#1-createproperty)**
2. **[GetProperty](#2-getproperty)**
3. **[ListProperties](#3-listproperties)**
4. **[UpdateProperty](#4-updateproperty)**
5. **[DeleteProperty](#5-deleteproperty)**
6. **[SearchProperties](#6-searchproperties)**
7. **[BatchGetProperties](#7-batchgetproperties)**
8. **[AddCustodyEvent](#8-addcustodyevent)**
9. **[GetCustodyChain](#9-getcustodychain)**
10. **[ListPropertyFileAttachments](#10-listpropertyfileattachments)**
11. **[AddPropertyFileAttachment](#11-addpropertyfileattachment)**
12. **[RemovePropertyFileAttachment](#12-removepropertyfileattachment)**

---

### 1. CreateProperty

**Method:** `CreateProperty`  
**Route:** `POST /hero.property.v1.PropertyService/CreateProperty`

> **Note:** 
> - The property provided should **not** include an `id` field; it will be generated by the system.
> - The `orgId` field is required and must be provided in the request.


#### Message Fields

**CreatePropertyRequest:**

| Field | Type  | Description                                                                                     |
|-------|-------|-------------------------------------------------------------------------------------------------|
| property | Property | Property object to be created. **Note:** The `id` should not be provided; it will be generated. The `orgId` field is required.    |

**CreatePropertyResponse:**

| Field | Type  | Description                                                                               |
|-------|-------|-------------------------------------------------------------------------------------------|
| property | Property | The newly created property object with a generated `id` and default field values.             |

#### Sample Request and Response

**Request (JSON):**
```json
{
  "property": {
    "orgId": 1,
    "nibrsPropertyType": "PROPERTY_TYPE_SEIZED",
    "propertyStatus": "PROPERTY_STATUS_COLLECTED",
    "isEvidence": true,
    "retentionPeriod": "30 days",
    "notes": "Seized during traffic stop"
  }
}
```

**Response (JSON):**
```json
{
  "property": {
    "id": "prop-123",
    "orgId": 1,
    "nibrsPropertyType": "PROPERTY_TYPE_SEIZED",
    "propertyStatus": "PROPERTY_STATUS_COLLECTED",
    "isEvidence": true,
    "retentionPeriod": "30 days",
    "disposalType": "PROPERTY_DISPOSAL_TYPE_UNSPECIFIED",
    "notes": "Seized during traffic stop",
    "schemaId": "",
    "schemaVersion": 0,
    "details": {},
    "currentCustodian": "",
    "currentLocation": "",
    "custodyChain": [],
    "createTime": "2025-01-14T11:00:00Z",
    "updateTime": "2025-01-14T11:00:00Z",
    "createdBy": "user-123",
    "updatedBy": "user-123",
    "version": 1,
    "resourceType": "PROPERTY"
  }
}
```

---

### 2. GetProperty

**Method:** `GetProperty`  
**Route:** `POST /hero.property.v1.PropertyService/GetProperty`

#### Message Fields

**GetPropertyRequest:**

| Field | Type   | Description                                |
|-------|--------|--------------------------------------------|
| id    | string | Unique identifier for the property.         |

**GetPropertyResponse:**

| Field | Type  | Description                                                                               |
|-------|-------|-------------------------------------------------------------------------------------------|
| property | Property | The property object containing full property details (refer to the [Property](#property) data model).   |

#### Sample Request and Response

**Request (JSON):**
```json
{
  "id": "prop-123"
}
```

**Response (JSON):**
```json
{
  "property": {
    "id": "prop-123",
    "orgId": 1,
    "nibrsPropertyType": "PROPERTY_TYPE_SEIZED",
    "propertyStatus": "PROPERTY_STATUS_CHECKED_IN",
    "isEvidence": true,
    "retentionPeriod": "30 days",
    "disposalType": "PROPERTY_DISPOSAL_TYPE_UNSPECIFIED",
    "notes": "Seized during traffic stop",
    "schemaId": "",
    "schemaVersion": 0,
    "details": {},
    "currentCustodian": "officer-456",
    "currentLocation": "Evidence Room A-12",
    "custodyChain": [
      {
        "timestamp": "2025-01-14T11:00:00Z",
        "transferringUserId": "officer-789",
        "transferringAgency": "",
        "receivingUserId": "officer-456",
        "receivingAgency": "",
        "newLocation": "Evidence Room A-12",
        "actionType": "CUSTODY_ACTION_TYPE_COLLECTED",
        "notes": "Initial collection",
        "caseNumber": "CASE-2024-001",
        "evidenceNumber": "EVID-001"
      }
    ],
    "createTime": "2025-01-14T11:00:00Z",
    "updateTime": "2025-01-14T12:00:00Z",
    "createdBy": "user-123",
    "updatedBy": "user-456",
    "version": 1,
    "status": 1,
    "resourceType": "PROPERTY"
  }
}
```

---

### 3. ListProperties

**Method:** `ListProperties`  
**Route:** `POST /hero.property.v1.PropertyService/ListProperties`

#### Message Fields

**ListPropertiesRequest:**

| Field | Type | Description |
|-------|------|-------------|
| pageSize | int32 | Number of properties to return (default: 50, max: 1000). |
| pageToken | string | Token for pagination. |
| nibrsPropertyType | NIBRSPropertyType | Filter by NIBRS type. |
| propertyStatus | PropertyStatus | Filter by property status. |
| orderBy | string | Ordering field (default: "create_time DESC"). |

**ListPropertiesResponse:**

| Field | Type | Description |
|-------|------|-------------|
| properties | repeated Property | List of properties. |
| nextPageToken | string | Token for next page of results. |

#### Sample Request and Response

**Request (JSON):**
```json
{
  "pageSize": 10,
  "nibrsPropertyType": "PROPERTY_TYPE_SEIZED",
  "propertyStatus": "PROPERTY_STATUS_CHECKED_IN"
}
```

**Response (JSON):**
```json
{
  "properties": [
    {
      "id": "prop-123",
      "orgId": 1,
      "nibrsPropertyType": "PROPERTY_TYPE_SEIZED",
      "propertyStatus": "PROPERTY_STATUS_CHECKED_IN",
      "isEvidence": true,
      "retentionPeriod": "30 days",
      "disposalType": "PROPERTY_DISPOSAL_TYPE_UNSPECIFIED",
      "notes": "Seized during traffic stop",
      "schemaId": "",
      "schemaVersion": 0,
      "details": {},
      "currentCustodian": "officer-456",
      "currentLocation": "Evidence Room A-12",
      "custodyChain": [],
      "createTime": "2025-01-14T11:00:00Z",
      "updateTime": "2025-01-14T12:00:00Z",
      "createdBy": "user-123",
      "updatedBy": "user-456",
      "version": 1,
      "resourceType": "PROPERTY"
    }
  ],
  "nextPageToken": "next-page-token"
}
```

---

### 4. UpdateProperty

**Method:** `UpdateProperty`  
**Route:** `POST /hero.property.v1.PropertyService/UpdateProperty`

#### Message Fields

**UpdatePropertyRequest:**

| Field | Type | Description |
|-------|------|-------------|
| property | Property | Property object with updated fields. The `id` field is required. |

**UpdatePropertyResponse:**

| Field | Type | Description |
|-------|------|-------------|
| property | Property | The updated property object. |

#### Sample Request and Response

**Request (JSON):**
```json
{
  "property": {
    "id": "prop-123",
    "notes": "Updated notes about the property",
    "currentLocation": "Evidence Room B-15"
  }
}
```

**Response (JSON):**
```json
{
  "property": {
    "id": "prop-123",
    "orgId": 1,
    "nibrsPropertyType": "PROPERTY_TYPE_SEIZED",
    "propertyStatus": "PROPERTY_STATUS_CHECKED_IN",
    "isEvidence": true,
    "retentionPeriod": "30 days",
    "disposalType": "PROPERTY_DISPOSAL_TYPE_UNSPECIFIED",
    "notes": "Updated notes about the property",
    "schemaId": "",
    "schemaVersion": 0,
    "details": {},
    "currentCustodian": "officer-456",
    "currentLocation": "Evidence Room B-15",
    "custodyChain": [],
    "createTime": "2025-01-14T11:00:00Z",
    "updateTime": "2025-01-14T13:00:00Z",
    "createdBy": "user-123",
    "updatedBy": "user-789",
    "version": 2,
    "resourceType": "PROPERTY"
  }
}
```

---

### 5. DeleteProperty

**Method:** `DeleteProperty`  
**Route:** `POST /hero.property.v1.PropertyService/DeleteProperty`

#### Message Fields

**DeletePropertyRequest:**

| Field | Type | Description |
|-------|------|-------------|
| id | string | Unique identifier for the property to delete. |

**DeletePropertyResponse:**

| Field | Type | Description |
|-------|------|-------------|
| (empty) | | No response body. |

#### Sample Request and Response

**Request (JSON):**
```json
{
  "id": "prop-123"
}
```

**Response (JSON):**
```json
{}
```

---

### 6. SearchProperties

**Method:** `SearchProperties`  
**Route:** `POST /hero.property.v1.PropertyService/SearchProperties`

#### Message Fields

**SearchPropertiesRequest:**

| Field | Type | Description |
|-------|------|-------------|
| query | string | General search query. |
| fieldQueries | repeated FieldQuery | Field-specific search queries. |
| dateRange | DateRange | Date range filter. |
| nibrsPropertyType | NIBRSPropertyType | Filter by NIBRS type. |
| propertyStatus | PropertyStatus | Filter by property status. |
| orderBy | SearchOrderBy | Ordering option. |
| pageSize | int32 | Number of results to return. |
| pageToken | string | Token for pagination. |

**SearchPropertiesResponse:**

| Field | Type | Description |
|-------|------|-------------|
| properties | repeated Property | List of matching properties. |
| highlights | repeated HighlightResult | Search result highlights. |
| nextPageToken | string | Token for next page of results. |
| totalCount | int32 | Total number of matching results. |

#### Sample Request and Response

**Request (JSON):**
```json
{
  "query": "evidence",
  "fieldQueries": [
    {
      "field": "notes",
      "query": "investigation"
    }
  ],
  "nibrsPropertyType": "PROPERTY_TYPE_SEIZED",
  "orderBy": "SEARCH_ORDER_BY_RELEVANCE",
  "pageSize": 10
}
```

**Response (JSON):**
```json
{
  "properties": [
    {
      "id": "prop-123",
      "orgId": 1,
      "nibrsPropertyType": "PROPERTY_TYPE_SEIZED",
      "propertyStatus": "PROPERTY_STATUS_CHECKED_IN",
      "isEvidence": true,
      "retentionPeriod": "30 days",
      "disposalType": "PROPERTY_DISPOSAL_TYPE_UNSPECIFIED",
      "notes": "Evidence collected during investigation",
      "schemaId": "",
      "schemaVersion": 0,
      "details": {},
      "currentCustodian": "officer-456",
      "currentLocation": "Evidence Room A-12",
      "custodyChain": [],
      "createTime": "2025-01-14T11:00:00Z",
      "updateTime": "2025-01-14T12:00:00Z",
      "createdBy": "user-123",
      "updatedBy": "user-456",
      "version": 1,
      "resourceType": "PROPERTY"
    }
  ],
  "highlights": [
    {
      "field": "notes",
      "fragments": ["<em>Evidence</em> collected during investigation"]
    }
  ],
  "nextPageToken": "next-page-token",
  "totalCount": 25
}
```

---

### 7. BatchGetProperties

**Method:** `BatchGetProperties`  
**Route:** `POST /hero.property.v1.PropertyService/BatchGetProperties`

#### Message Fields

**BatchGetPropertiesRequest:**

| Field | Type | Description |
|-------|------|-------------|
| ids | repeated string | List of property IDs to retrieve. |

**BatchGetPropertiesResponse:**

| Field | Type | Description |
|-------|------|-------------|
| properties | repeated Property | List of retrieved properties. |

#### Sample Request and Response

**Request (JSON):**
```json
{
  "ids": ["prop-123", "prop-456", "prop-789"]
}
```

**Response (JSON):**
```json
{
  "properties": [
    {
      "id": "prop-123",
      "orgId": 1,
      "nibrsPropertyType": "PROPERTY_TYPE_SEIZED",
      "propertyStatus": "PROPERTY_STATUS_CHECKED_IN",
      "isEvidence": true,
      "retentionPeriod": "30 days",
      "disposalType": "PROPERTY_DISPOSAL_TYPE_UNSPECIFIED",
      "notes": "Seized during traffic stop",
      "schemaId": "",
      "schemaVersion": 0,
      "details": {},
      "currentCustodian": "officer-456",
      "currentLocation": "Evidence Room A-12",
      "custodyChain": [],
      "createTime": "2025-01-14T11:00:00Z",
      "updateTime": "2025-01-14T12:00:00Z",
      "createdBy": "user-123",
      "updatedBy": "user-456",
      "version": 1,
      "resourceType": "PROPERTY"
    },
    {
      "id": "prop-456",
      "orgId": 1,
      "nibrsPropertyType": "PROPERTY_TYPE_FOUND",
      "propertyStatus": "PROPERTY_STATUS_COLLECTED",
      "isEvidence": false,
      "retentionPeriod": "",
      "disposalType": "PROPERTY_DISPOSAL_TYPE_UNSPECIFIED",
      "notes": "Found property turned in by citizen",
      "schemaId": "",
      "schemaVersion": 0,
      "details": {},
      "currentCustodian": "officer-123",
      "currentLocation": "Lost and Found",
      "custodyChain": [],
      "createTime": "2025-01-14T14:00:00Z",
      "updateTime": "2025-01-14T14:00:00Z",
      "createdBy": "user-456",
      "updatedBy": "user-456",
      "version": 1,
      "resourceType": "PROPERTY"
    }
  ]
}
```

---

### 8. AddCustodyEvent

**Method:** `AddCustodyEvent`  
**Route:** `POST /hero.property.v1.PropertyService/AddCustodyEvent`

#### Message Fields

**AddCustodyEventRequest:**

| Field | Type | Description |
|-------|------|-------------|
| propertyId | string | ID of the property to add custody event to. |
| custodyEvent | CustodyEvent | The custody event to add. |

**AddCustodyEventResponse:**

| Field | Type | Description |
|-------|------|-------------|
| (empty) | | No response body. |

#### Sample Request and Response

**Request (JSON):**
```json
{
  "propertyId": "prop-123",
  "custodyEvent": {
    "timestamp": "2025-01-14T15:00:00Z",
    "transferringUserId": "officer-456",
    "transferringAgency": "",
    "receivingUserId": "officer-789",
    "receivingAgency": "",
    "newLocation": "Evidence Room B-15",
    "actionType": "CUSTODY_ACTION_TYPE_TRANSFERRED",
    "notes": "Transferred to different evidence room",
    "caseNumber": "CASE-2024-001",
    "evidenceNumber": "EVID-001"
  }
}
```

**Response (JSON):**
```json
{}
```

---

### 9. GetCustodyChain

**Method:** `GetCustodyChain`  
**Route:** `POST /hero.property.v1.PropertyService/GetCustodyChain`

#### Message Fields

**GetCustodyChainRequest:**

| Field | Type | Description |
|-------|------|-------------|
| propertyId | string | ID of the property to get custody chain for. |

**GetCustodyChainResponse:**

| Field | Type | Description |
|-------|------|-------------|
| custodyChain | repeated CustodyEvent | Complete chain of custody history. |

#### Sample Request and Response

**Request (JSON):**
```json
{
  "propertyId": "prop-123"
}
```

**Response (JSON):**
```json
{
  "custodyChain": [
    {
      "timestamp": "2025-01-14T11:00:00Z",
      "transferringUserId": "officer-789",
      "transferringAgency": "",
      "receivingUserId": "officer-456",
      "receivingAgency": "",
      "newLocation": "Evidence Room A-12",
      "actionType": "CUSTODY_ACTION_TYPE_COLLECTED",
      "notes": "Initial collection",
      "schemaId": "",
      "schemaVersion": 0,
      "details": {},
      "evidenceNumber": "EVID-001"
    },
    {
      "timestamp": "2025-01-14T15:00:00Z",
      "transferringUserId": "officer-456",
      "transferringAgency": "",
      "receivingUserId": "officer-789",
      "receivingAgency": "",
      "newLocation": "Evidence Room B-15",
      "actionType": "CUSTODY_ACTION_TYPE_TRANSFERRED",
      "notes": "Transferred to different evidence room",
      "caseNumber": "CASE-2024-001",
      "evidenceNumber": "EVID-001"
    }
  ]
}
```

---

### 10. ListPropertyFileAttachments

**Method:** `ListPropertyFileAttachments`  
**Route:** `POST /hero.property.v1.PropertyService/ListPropertyFileAttachments`

**ListPropertyFileAttachmentsRequest:**

| Field | Type | Description |
|-------|------|-------------|
| propertyId | string | Property ID. |
| fileCategory | string | Optional category filter. |
| pageSize | int32 | Page size. |
| pageToken | string | Pagination token. |

**ListPropertyFileAttachmentsResponse:**

| Field | Type | Description |
|-------|------|-------------|
| fileAttachments | repeated PropertyFileReference | Attachments. |
| nextPageToken | string | Next page token. |

**Sample:**
```json
{
  "propertyId": "prop-123",
  "pageSize": 10
}
```

```json
{
  "fileAttachments": [
    {
      "id": "att-1",
      "propertyId": "prop-123",
      "fileId": "file-abc",
      "caption": "Evidence photo",
      "displayName": "photo.jpg",
      "displayOrder": 1,
      "fileCategory": "PHOTO",
      "metadata": {}
    }
  ],
  "nextPageToken": ""
}
```

---

### 11. AddPropertyFileAttachment

**Method:** `AddPropertyFileAttachment`  
**Route:** `POST /hero.property.v1.PropertyService/AddPropertyFileAttachment`

**AddPropertyFileAttachmentRequest:**

| Field | Type | Description |
|-------|------|-------------|
| propertyId | string | Property ID. |
| fileAttachment | PropertyFileReference | Attachment to add. |

**AddPropertyFileAttachmentResponse:**

| Field | Type | Description |
|-------|------|-------------|
| fileAttachment | PropertyFileReference | Created attachment. |

**Sample:**
```json
{
  "propertyId": "prop-123",
  "fileAttachment": {
    "fileId": "file-abc",
    "caption": "Evidence photo",
    "displayName": "photo.jpg",
    "displayOrder": 1,
    "fileCategory": "PHOTO",
    "metadata": {}
  }
}
```

---

### 12. RemovePropertyFileAttachment

**Method:** `RemovePropertyFileAttachment`  
**Route:** `POST /hero.property.v1.PropertyService/RemovePropertyFileAttachment`

**RemovePropertyFileAttachmentRequest:**

| Field | Type | Description |
|-------|------|-------------|
| propertyId | string | Property ID. |
| attachmentId | string | Attachment ID to remove. |

**RemovePropertyFileAttachmentResponse:**

| Field | Type | Description |
|-------|------|-------------|
| (empty) |  | No response body. |
