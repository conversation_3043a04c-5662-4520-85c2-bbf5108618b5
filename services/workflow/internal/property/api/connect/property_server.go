package connect

import (
	"context"
	"fmt"
	"log/slog"

	connecthelper "common/connect"
	"common/herosentry"
	property "proto/hero/property/v1"
	propertyConnect "proto/hero/property/v1/propertyconnect"
	"workflow/internal/property/usecase"

	"connectrpc.com/connect"
)

// PropertyServer implements the property service.
type PropertyServer struct {
	propertyConnect.UnimplementedPropertyServiceHandler
	propertyUseCase *usecase.PropertyUseCase
	logger          *slog.Logger // Logger instance
}

// NewPropertyServer creates a new PropertyServer.
func NewPropertyServer(propertyUseCase *usecase.PropertyUseCase, logger *slog.Logger) *PropertyServer {
	if logger == nil {
		logger = slog.Default()
	}
	return &PropertyServer{
		propertyUseCase: propertyUseCase,
		logger:          logger.With("component", "PropertyServer"),
	}
}

// CreateProperty creates a new property.
func (propertyServer *PropertyServer) CreateProperty(
	ctx context.Context,
	createPropertyRequest *connect.Request[property.CreatePropertyRequest],
) (*connect.Response[property.CreatePropertyResponse], error) {
	// DEBUG: Log what we're receiving from frontend
	propertyServer.logger.Info("🔍 STEP 11 - gRPC CreateProperty received",
		"property_type_value", createPropertyRequest.Msg.Property.NibrsPropertyType,
		"property_type_string", createPropertyRequest.Msg.Property.NibrsPropertyType.String(),
		"property_status_value", createPropertyRequest.Msg.Property.PropertyStatus,
		"property_status_string", createPropertyRequest.Msg.Property.PropertyStatus.String())

	err := propertyServer.propertyUseCase.CreateProperty(ctx, createPropertyRequest.Msg.Property)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "CreateProperty")
	}

	// Fetch the created property from the database to get server-generated fields
	createdProperty, err := propertyServer.propertyUseCase.GetProperty(ctx, createPropertyRequest.Msg.Property.Id)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "CreateProperty")
	}

	response := connect.NewResponse(&property.CreatePropertyResponse{
		Property: createdProperty,
	})
	return response, nil
}

// GetProperty retrieves a property by its ID.
func (propertyServer *PropertyServer) GetProperty(
	ctx context.Context,
	getPropertyRequest *connect.Request[property.GetPropertyRequest],
) (*connect.Response[property.GetPropertyResponse], error) {
	propertyServer.logger.InfoContext(
		ctx,
		"GetProperty called",
		slog.String("property_id", getPropertyRequest.Msg.Id),
	)

	propertyRecord, err := propertyServer.propertyUseCase.GetProperty(ctx, getPropertyRequest.Msg.Id)
	if err != nil {
		propertyServer.logger.ErrorContext(
			ctx,
			"Failed to get property",
			slog.String("property_id", getPropertyRequest.Msg.Id),
			slog.Any("error", err),
		)
		return nil, connecthelper.AsConnectError(ctx, err, "GetProperty")
	}

	propertyServer.logger.InfoContext(
		ctx,
		"Successfully retrieved property",
		slog.String("property_id", getPropertyRequest.Msg.Id),
	)

	response := connect.NewResponse(&property.GetPropertyResponse{
		Property: propertyRecord,
	})
	return response, nil
}

// DeleteProperty deletes a property.
func (propertyServer *PropertyServer) DeleteProperty(
	ctx context.Context,
	deletePropertyRequest *connect.Request[property.DeletePropertyRequest],
) (*connect.Response[property.DeletePropertyResponse], error) {
	err := propertyServer.propertyUseCase.DeleteProperty(ctx, deletePropertyRequest.Msg.Id)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "DeleteProperty")
	}
	response := connect.NewResponse(&property.DeletePropertyResponse{})
	return response, nil
}

// ListProperties lists properties with pagination and filtering.
func (propertyServer *PropertyServer) ListProperties(
	ctx context.Context,
	listPropertiesRequest *connect.Request[property.ListPropertiesRequest],
) (*connect.Response[property.ListPropertiesResponse], error) {
	properties, nextPageToken, err := propertyServer.propertyUseCase.ListProperties(
		ctx,
		int(listPropertiesRequest.Msg.PageSize),
		listPropertiesRequest.Msg.PageToken,
		listPropertiesRequest.Msg.NibrsPropertyType,
		listPropertiesRequest.Msg.PropertyStatus,
		listPropertiesRequest.Msg.OrderBy,
	)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "ListProperties")
	}

	response := connect.NewResponse(&property.ListPropertiesResponse{
		Properties:    properties,
		NextPageToken: nextPageToken,
	})
	return response, nil
}

// UpdateProperty updates an existing property.
func (propertyServer *PropertyServer) UpdateProperty(
	ctx context.Context,
	updatePropertyRequest *connect.Request[property.UpdatePropertyRequest],
) (*connect.Response[property.UpdatePropertyResponse], error) {
	updatedProperty, err := propertyServer.propertyUseCase.UpdateProperty(ctx, updatePropertyRequest.Msg.Property)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "UpdateProperty")
	}
	response := connect.NewResponse(&property.UpdatePropertyResponse{
		Property: updatedProperty,
	})
	return response, nil
}

// SearchProperties performs advanced search on properties.
func (propertyServer *PropertyServer) SearchProperties(
	ctx context.Context,
	searchPropertiesRequest *connect.Request[property.SearchPropertiesRequest],
) (*connect.Response[property.SearchPropertiesResponse], error) {
	propertyServer.logger.InfoContext(
		ctx,
		"SearchProperties called",
		slog.String("query", searchPropertiesRequest.Msg.GetQuery()),
		slog.Int("page_size", int(searchPropertiesRequest.Msg.GetPageSize())),
		slog.String("page_token", searchPropertiesRequest.Msg.GetPageToken()),
		slog.String("order_by", searchPropertiesRequest.Msg.GetOrderBy().String()),
	)
	if searchPropertiesRequest.Msg == nil {
		return nil, connecthelper.AsConnectError(ctx, fmt.Errorf("request message is nil"), "SearchProperties", herosentry.ErrorTypeValidation)
	}

	searchResponse, operationError := propertyServer.propertyUseCase.SearchProperties(ctx, searchPropertiesRequest.Msg)
	if operationError != nil {
		propertyServer.logger.ErrorContext(ctx, "Failed to search properties", slog.Any("error", operationError))
		return nil, connecthelper.AsConnectError(ctx, operationError, "SearchProperties")
	}

	response := connect.NewResponse(searchResponse)
	return response, nil
}

// BatchGetProperties retrieves multiple properties by their IDs.
func (propertyServer *PropertyServer) BatchGetProperties(
	ctx context.Context,
	batchGetPropertiesRequest *connect.Request[property.BatchGetPropertiesRequest],
) (*connect.Response[property.BatchGetPropertiesResponse], error) {
	properties, err := propertyServer.propertyUseCase.BatchGetProperties(ctx, batchGetPropertiesRequest.Msg.Ids)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "BatchGetProperties")
	}
	response := connect.NewResponse(&property.BatchGetPropertiesResponse{
		Properties: properties,
	})
	return response, nil
}

// AddCustodyEvent adds a new custody event to a property's chain of custody.
func (propertyServer *PropertyServer) AddCustodyEvent(
	ctx context.Context,
	addCustodyEventRequest *connect.Request[property.AddCustodyEventRequest],
) (*connect.Response[property.AddCustodyEventResponse], error) {
	err := propertyServer.propertyUseCase.AddCustodyEvent(
		ctx,
		addCustodyEventRequest.Msg.PropertyId,
		addCustodyEventRequest.Msg.CustodyEvent,
	)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "AddCustodyEvent")
	}
	response := connect.NewResponse(&property.AddCustodyEventResponse{})
	return response, nil
}

// GetCustodyChain returns the complete chain of custody for a property.
func (propertyServer *PropertyServer) GetCustodyChain(
	ctx context.Context,
	getCustodyChainRequest *connect.Request[property.GetCustodyChainRequest],
) (*connect.Response[property.GetCustodyChainResponse], error) {
	propertyServer.logger.InfoContext(
		ctx,
		"GetCustodyChain called",
		slog.String("property_id", getCustodyChainRequest.Msg.PropertyId),
	)

	custodyChain, err := propertyServer.propertyUseCase.GetCustodyChain(ctx, getCustodyChainRequest.Msg.PropertyId)
	if err != nil {
		propertyServer.logger.ErrorContext(
			ctx,
			"Failed to get custody chain",
			slog.String("property_id", getCustodyChainRequest.Msg.PropertyId),
			slog.Any("error", err),
		)
		return nil, connecthelper.AsConnectError(ctx, err, "GetCustodyChain")
	}

	propertyServer.logger.InfoContext(
		ctx,
		"Successfully retrieved custody chain",
		slog.String("property_id", getCustodyChainRequest.Msg.PropertyId),
		slog.Int("events_count", len(custodyChain)),
	)

	response := connect.NewResponse(&property.GetCustodyChainResponse{
		CustodyChain: custodyChain,
	})
	return response, nil
}

// ListPropertyFileAttachments lists all file attachments for a property.
func (propertyServer *PropertyServer) ListPropertyFileAttachments(
	ctx context.Context,
	listPropertyFileAttachmentsRequest *connect.Request[property.ListPropertyFileAttachmentsRequest],
) (*connect.Response[property.ListPropertyFileAttachmentsResponse], error) {
	propertyServer.logger.InfoContext(
		ctx,
		"ListPropertyFileAttachments called",
		slog.String("property_id", listPropertyFileAttachmentsRequest.Msg.PropertyId),
		slog.Int("page_size", int(listPropertyFileAttachmentsRequest.Msg.PageSize)),
		slog.String("page_token", listPropertyFileAttachmentsRequest.Msg.PageToken),
	)

	response, err := propertyServer.propertyUseCase.ListPropertyFileAttachments(ctx, listPropertyFileAttachmentsRequest.Msg)
	if err != nil {
		propertyServer.logger.ErrorContext(
			ctx,
			"Failed to list property file attachments",
			slog.String("property_id", listPropertyFileAttachmentsRequest.Msg.PropertyId),
			slog.Any("error", err),
		)
		return nil, connecthelper.AsConnectError(ctx, err, "ListPropertyFileAttachments")
	}

	propertyServer.logger.InfoContext(
		ctx,
		"Successfully listed property file attachments",
		slog.String("property_id", listPropertyFileAttachmentsRequest.Msg.PropertyId),
		slog.Int("attachments_count", len(response.FileAttachments)),
	)

	return connect.NewResponse(response), nil
}

// AddPropertyFileAttachment adds a file attachment to a property.
func (propertyServer *PropertyServer) AddPropertyFileAttachment(
	ctx context.Context,
	addPropertyFileAttachmentRequest *connect.Request[property.AddPropertyFileAttachmentRequest],
) (*connect.Response[property.AddPropertyFileAttachmentResponse], error) {
	propertyServer.logger.InfoContext(
		ctx,
		"AddPropertyFileAttachment called",
		slog.String("property_id", addPropertyFileAttachmentRequest.Msg.PropertyId),
		slog.String("file_id", addPropertyFileAttachmentRequest.Msg.FileAttachment.FileId),
	)

	response, err := propertyServer.propertyUseCase.AddPropertyFileAttachment(ctx, addPropertyFileAttachmentRequest.Msg)
	if err != nil {
		propertyServer.logger.ErrorContext(
			ctx,
			"Failed to add property file attachment",
			slog.String("property_id", addPropertyFileAttachmentRequest.Msg.PropertyId),
			slog.String("file_id", addPropertyFileAttachmentRequest.Msg.FileAttachment.FileId),
			slog.Any("error", err),
		)
		return nil, connecthelper.AsConnectError(ctx, err, "AddPropertyFileAttachment")
	}

	propertyServer.logger.InfoContext(
		ctx,
		"Successfully added property file attachment",
		slog.String("property_id", addPropertyFileAttachmentRequest.Msg.PropertyId),
		slog.String("attachment_id", response.FileAttachment.Id),
	)

	return connect.NewResponse(response), nil
}

// RemovePropertyFileAttachment removes a file attachment from a property.
func (propertyServer *PropertyServer) RemovePropertyFileAttachment(
	ctx context.Context,
	removePropertyFileAttachmentRequest *connect.Request[property.RemovePropertyFileAttachmentRequest],
) (*connect.Response[property.RemovePropertyFileAttachmentResponse], error) {
	propertyServer.logger.InfoContext(
		ctx,
		"RemovePropertyFileAttachment called",
		slog.String("property_id", removePropertyFileAttachmentRequest.Msg.PropertyId),
		slog.String("attachment_id", removePropertyFileAttachmentRequest.Msg.AttachmentId),
	)

	response, err := propertyServer.propertyUseCase.RemovePropertyFileAttachment(ctx, removePropertyFileAttachmentRequest.Msg)
	if err != nil {
		propertyServer.logger.ErrorContext(
			ctx,
			"Failed to remove property file attachment",
			slog.String("property_id", removePropertyFileAttachmentRequest.Msg.PropertyId),
			slog.String("attachment_id", removePropertyFileAttachmentRequest.Msg.AttachmentId),
			slog.Any("error", err),
		)
		return nil, connecthelper.AsConnectError(ctx, err, "RemovePropertyFileAttachment")
	}

	propertyServer.logger.InfoContext(
		ctx,
		"Successfully removed property file attachment",
		slog.String("property_id", removePropertyFileAttachmentRequest.Msg.PropertyId),
		slog.String("attachment_id", removePropertyFileAttachmentRequest.Msg.AttachmentId),
	)

	return connect.NewResponse(response), nil
}
