package repository

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"

	cmncontext "common/context"
	"common/database"
	"common/herosentry"
	commonUtils "common/utils"
	situations "proto/hero/situations/v2"

	"github.com/google/uuid"
	"github.com/lib/pq"
)

// PostgresSituationRepository implements SituationRepository using PostgreSQL.
type PostgresSituationRepository struct {
	database *sql.DB
}

// NewPostgresSituationRepository creates a new repository backed by PostgreSQL.
func NewPostgresSituationRepository(database *sql.DB) *PostgresSituationRepository {
	return &PostgresSituationRepository{database: database}
}

// deferConstraints sets all deferrable constraints to be checked at transaction commit.
func deferConstraints(ctx context.Context, tx *sql.Tx) error {
	if tx != nil {
		_, err := tx.ExecContext(ctx, "SET CONSTRAINTS ALL DEFERRED")
		return err
	}
	return nil
}

// enumSliceToInt32 converts any slice whose underlying type is int32
// (e.g., SituationStatus, SituationType, TriggerSource) into a plain []int32.
func enumSliceToInt32[EnumType ~int32](enumSlice []EnumType) []int32 {
	out := make([]int32, len(enumSlice))
	for idx, enumVal := range enumSlice {
		out[idx] = int32(enumVal)
	}
	return out
}

// scanSituationRows converts the primary SELECT rows (core columns only)
// into three parallel data-structures:
//
//   - orderedSlice  —— []*situations.Situation   (keeps DB order → response order)
//   - idSlice       —— []string                  (for IN/ANY() hydration queries)
//   - situationMap  —— map[string]*Situation     (fast lookup during hydration)
//
// The helper leaves repeated fields (Updates, Tags, …) nil/empty; those are
// populated later by hydrateRepeatedFields.
func scanSituationRows(sqlRows *sql.Rows) (
	orderedSlice []*situations.Situation,
	idSlice []string,
	situationMap map[string]*situations.Situation,
	scanErr error,
) {
	situationMap = make(map[string]*situations.Situation)

	for sqlRows.Next() {
		current := &situations.Situation{}

		// Declare locals that capture DB ints / times before conversion.
		var (
			typeInt32, statusInt32, triggerInt32 int32
			createTimeDB, updateTimeDB           time.Time
			dueTimeDB, resolvedTimeDB            sql.NullTime
			incidentTimeDB, addrUpdateTimeDB     sql.NullTime
			reporterIDNullable                   sql.NullString
			additionalInfoNullable               sql.NullString
			autoEnabledBool                      bool
		)

		// Scan core columns in exactly the same order as SELECT list.
		if err := sqlRows.Scan(
			&current.Id, &current.Priority, &current.Title, &typeInt32,
			&current.ResourceType, &current.Description, &statusInt32,
			&reporterIDNullable, &createTimeDB, &updateTimeDB, &dueTimeDB,
			&resolvedTimeDB, &incidentTimeDB, &triggerInt32,
			&current.ReporterName, &current.ContactNo, &current.ContactEmail,
			&current.Address, &current.Latitude, &current.Longitude,
			&addrUpdateTimeDB, &autoEnabledBool, &additionalInfoNullable,
		); err != nil {
			scanErr = err
			return
		}

		// Convert DB fields → proto enum / string formats.
		if reporterIDNullable.Valid {
			current.ReporterId = reporterIDNullable.String
		}
		current.Type = situations.SituationType(typeInt32)
		current.Status = situations.SituationStatus(statusInt32)
		current.TriggerSource = situations.TriggerSource(triggerInt32)
		current.CreateTime = commonUtils.TimeToISO8601String(createTimeDB)
		current.UpdateTime = commonUtils.TimeToISO8601String(updateTimeDB)

		// Handle nullable timestamps
		if dueTimeDB.Valid {
			current.DueTime = commonUtils.TimeToISO8601String(dueTimeDB.Time)
		} else {
			current.DueTime = ""
		}

		if resolvedTimeDB.Valid {
			current.ResolvedTime = commonUtils.TimeToISO8601String(resolvedTimeDB.Time)
		} else {
			current.ResolvedTime = ""
		}

		if incidentTimeDB.Valid {
			current.IncidentTime = commonUtils.TimeToISO8601String(incidentTimeDB.Time)
		} else {
			current.IncidentTime = ""
		}

		if addrUpdateTimeDB.Valid {
			current.AddressUpdateTime = commonUtils.TimeToISO8601String(addrUpdateTimeDB.Time)
		} else {
			current.AddressUpdateTime = ""
		}

		current.AutomationEnabled = autoEnabledBool
		if additionalInfoNullable.Valid {
			current.AdditionalInfoJson = additionalInfoNullable.String
		} else {
			current.AdditionalInfoJson = "{}"
		}

		// Store in the three return values
		orderedSlice = append(orderedSlice, current)
		idSlice = append(idSlice, current.Id)
		situationMap[current.Id] = current
	}

	if err := sqlRows.Err(); err != nil {
		scanErr = err
	}
	return
}

// hydrateRepeatedFields populates Updates, Tags, RelatedSituationsIds,
// MediaAttachments, and StatusUpdates for every Situation referenced in
// situationMap.  All queries use `WHERE situation_id = ANY($1)` so the DB
// does one index scan per table, not per row.
//
// Call AFTER scanSituationRows so every Situation pointer already exists.
//
// Parameters
// ----------
// ctx            —— request-scoped context
// tx             —— active *sql.Tx  (may be nil; falls back to repository.db)
// situationMap   —— map[id]*Situation with core columns already filled
// idSlice        —— []string of IDs (same order as ordered slice, but order
//
//	does not matter for hydration)
func hydrateRepeatedFields(
	ctx context.Context,
	tx *sql.Tx,
	db *sql.DB,
	situationMap map[string]*situations.Situation,
	idSlice []string,
) error {

	// Decide which *sql.DB / *sql.Tx to use for queries
	execQuerier := func(query string, args ...interface{}) (*sql.Rows, error) {
		if tx != nil {
			return tx.QueryContext(ctx, query, args...)
		}
		// fall back to global DB handle (assumes you have repository.database in scope)
		return db.QueryContext(ctx, query, args...)
	}

	/* Situation updates ------------------------------------------------- */
	updateSQL := `
		SELECT situation_id, message, timestamp, update_source,
		       display_name, event_type, updater_id
		  FROM situation_updates
		 WHERE situation_id = ANY($1)
		 ORDER BY timestamp ASC`
	updateRows, err := execQuerier(updateSQL, pq.Array(idSlice))
	if err != nil {
		return err
	}
	for updateRows.Next() {
		var (
			rowID                        string
			msg                          string
			tsDB                         time.Time
			sourceInt32                  int32
			dispName, evtType, updaterID sql.NullString
		)
		if err := updateRows.Scan(&rowID, &msg, &tsDB, &sourceInt32,
			&dispName, &evtType, &updaterID); err != nil {
			updateRows.Close()
			return err
		}
		newEntry := &situations.UpdateEntry{
			Message:      msg,
			Timestamp:    commonUtils.TimeToISO8601String(tsDB),
			UpdateSource: situations.UpdateSource(sourceInt32),
			DisplayName:  dispName.String,
			EventType:    evtType.String,
			UpdaterId:    updaterID.String,
		}
		situationMap[rowID].Updates = append(situationMap[rowID].Updates, newEntry)
	}
	updateRows.Close()

	/* Tags -------------------------------------------------------------- */
	tagsSQL := `SELECT situation_id, tag FROM situation_tags WHERE situation_id = ANY($1)`
	tagRows, err := execQuerier(tagsSQL, pq.Array(idSlice))
	if err != nil {
		return err
	}
	for tagRows.Next() {
		var rowID, tagVal string
		if err := tagRows.Scan(&rowID, &tagVal); err != nil {
			tagRows.Close()
			return err
		}
		situationMap[rowID].Tags = append(situationMap[rowID].Tags, tagVal)
	}
	tagRows.Close()

	/*  Related situation IDs ------------------------------------------- */
	relatedSQL := `SELECT situation_id, related_situation_id FROM situation_related WHERE situation_id = ANY($1)`
	relRows, err := execQuerier(relatedSQL, pq.Array(idSlice))
	if err != nil {
		return err
	}
	for relRows.Next() {
		var rowID, relID string
		if err := relRows.Scan(&rowID, &relID); err != nil {
			relRows.Close()
			return err
		}
		situationMap[rowID].RelatedSituationsIds =
			append(situationMap[rowID].RelatedSituationsIds, relID)
	}
	relRows.Close()

	/* Media attachments ------------------------------------------------ */
	mediaSQL := `SELECT situation_id, attachment_id, url, content_type
	               FROM situation_media_attachments
	              WHERE situation_id = ANY($1)`
	mediaRows, err := execQuerier(mediaSQL, pq.Array(idSlice))
	if err != nil {
		return err
	}
	for mediaRows.Next() {
		var rowID, attID, urlStr, contentType string
		if err := mediaRows.Scan(&rowID, &attID, &urlStr, &contentType); err != nil {
			mediaRows.Close()
			return err
		}
		attach := &situations.MediaAttachment{
			AttachmentId: attID,
			Url:          urlStr,
			ContentType:  contentType,
		}
		situationMap[rowID].MediaAttachments =
			append(situationMap[rowID].MediaAttachments, attach)
	}
	mediaRows.Close()

	/* Status update entries ------------------------------------------- */
	statusSQL := `
		SELECT situation_id, timestamp, new_status, previous_status,
		       note, updater_id, update_source
		  FROM situation_status_updates
		 WHERE situation_id = ANY($1)
		 ORDER BY timestamp ASC`
	statusRows, err := execQuerier(statusSQL, pq.Array(idSlice))
	if err != nil {
		return err
	}
	for statusRows.Next() {
		var (
			rowID                   string
			tsDB                    time.Time
			newStatInt, prevStatInt int32
			srcInt32                int32
			noteStr, updaterID      sql.NullString
		)
		if err := statusRows.Scan(&rowID, &tsDB, &newStatInt, &prevStatInt,
			&noteStr, &updaterID, &srcInt32); err != nil {
			statusRows.Close()
			return err
		}
		statEntry := &situations.StatusUpdateEntry{
			Timestamp:      commonUtils.TimeToISO8601String(tsDB),
			NewStatus:      situations.SituationStatus(newStatInt),
			PreviousStatus: situations.SituationStatus(prevStatInt),
			Note:           noteStr.String,
			UpdaterId:      updaterID.String,
			UpdateSource:   situations.UpdateSource(srcInt32),
		}
		situationMap[rowID].StatusUpdates =
			append(situationMap[rowID].StatusUpdates, statEntry)
	}
	statusRows.Close()

	return nil
}

// CreateSituation inserts a new situation and its repeated fields.
func (repository *PostgresSituationRepository) CreateSituation(requestContext context.Context, transaction *sql.Tx, situation *situations.Situation) (*situations.Situation, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(requestContext, "SituationRepository.CreateSituation")
	defer finishSpan()

	span.SetTag("situation.type", situation.Type.String())
	span.SetTag("situation.priority", fmt.Sprintf("%d", situation.Priority))
	span.SetTag("situation.status", situation.Status.String())

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) (*situations.Situation, error) {
		// Retrieve the org_id from ctx
		orgID := cmncontext.GetOrgId(spanContext)
		span.SetTag("org.id", fmt.Sprintf("%d", orgID))

		// If using a transaction, defer constraint checks until commit.
		if err := deferConstraints(spanContext, sessionTx); err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to defer constraints")
			return nil, err
		}

		if situation.Id == "" {
			situation.Id = uuid.New().String()
		}
		currentTime := time.Now()
		situation.CreateTime = commonUtils.TimeToISO8601String(currentTime)
		situation.UpdateTime = commonUtils.TimeToISO8601String(currentTime)
		// Always enforce resource_type as "SITUATION".
		situation.ResourceType = FixedResourceTypeSituation

		// Convert empty reporter_id to SQL NULL.
		var reporterID interface{}
		if situation.ReporterId == "" {
			reporterID = nil
		} else {
			reporterID = situation.ReporterId
		}

		if situation.AdditionalInfoJson == "" {
			situation.AdditionalInfoJson = "{}"
		}

		// Convert string timestamps to time.Time for database
		var dueTime, resolvedTime, incidentTime, addressUpdateTime sql.NullTime

		if situation.DueTime != "" {
			t, err := commonUtils.ISO8601StringToTime(situation.DueTime)
			if err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, "Invalid due_time format")
				return nil, fmt.Errorf("invalid due_time format: %w", err)
			}
			dueTime = sql.NullTime{Time: t, Valid: true}
		}

		if situation.ResolvedTime != "" {
			t, err := commonUtils.ISO8601StringToTime(situation.ResolvedTime)
			if err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, "Invalid resolved_time format")
				return nil, fmt.Errorf("invalid resolved_time format: %w", err)
			}
			resolvedTime = sql.NullTime{Time: t, Valid: true}
		}

		if situation.IncidentTime != "" {
			t, err := commonUtils.ISO8601StringToTime(situation.IncidentTime)
			if err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, "Invalid incident_time format")
				return nil, fmt.Errorf("invalid incident_time format: %w", err)
			}
			incidentTime = sql.NullTime{Time: t, Valid: true}
		}

		if situation.AddressUpdateTime != "" {
			t, err := commonUtils.ISO8601StringToTime(situation.AddressUpdateTime)
			if err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, "Invalid address_update_time format")
				return nil, fmt.Errorf("invalid address_update_time format: %w", err)
			}
			addressUpdateTime = sql.NullTime{Time: t, Valid: true}
		}

		insertSituationQuery := `
			INSERT INTO situations (
				id, org_id, priority, title, type, resource_type, description, status, reporter_id,
				create_time, update_time, due_time, resolved_time, incident_time, trigger_source,
				reporter_name, contact_no, contact_email, address, latitude, longitude, address_update_time, automation_enabled, additional_info_json
			)
			VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12,$13,$14,$15,$16,$17,$18,$19,$20,$21,$22,$23,$24)
		`
		_, err := sessionTx.ExecContext(spanContext, insertSituationQuery,
			situation.Id,
			orgID,
			situation.Priority,
			situation.Title,
			int32(situation.Type),
			situation.ResourceType,
			situation.Description,
			int32(situation.Status),
			reporterID,
			currentTime,
			currentTime,
			dueTime,
			resolvedTime,
			incidentTime,
			int32(situation.TriggerSource),
			situation.ReporterName,
			situation.ContactNo,
			situation.ContactEmail,
			situation.Address,
			situation.Latitude,
			situation.Longitude,
			addressUpdateTime,
			situation.AutomationEnabled,
			situation.AdditionalInfoJson,
		)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to insert situation")
			return nil, err
		}

		span.SetTag("situation.id", situation.Id)

		// Deduplicate tags before insertion
		uniqueTags := make(map[string]bool)
		var deduplicatedTags []string

		for _, tag := range situation.Tags {
			if !uniqueTags[tag] {
				uniqueTags[tag] = true
				deduplicatedTags = append(deduplicatedTags, tag)
			}
		}

		// Use the deduplicated tags for insertion
		for _, tag := range deduplicatedTags {
			insertTagQuery := `INSERT INTO situation_tags (situation_id, org_id, tag) VALUES ($1, $2, $3)`
			_, err := sessionTx.ExecContext(spanContext, insertTagQuery, situation.Id, orgID, tag)
			if err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to insert situation tag")
				return nil, err
			}
		}

		// Insert any repeated fields (updates, tags, etc.) as before.
		for _, updateEntry := range situation.Updates {
			updateTime, _ := commonUtils.ISO8601StringToTime(updateEntry.Timestamp)
			insertUpdateQuery := `
				INSERT INTO situation_updates 
					(situation_id, org_id, message, timestamp, update_source, display_name, event_type, updater_id)
				VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
			`
			_, err := sessionTx.ExecContext(spanContext, insertUpdateQuery,
				situation.Id, orgID, updateEntry.Message,
				updateTime,
				int32(updateEntry.UpdateSource),
				updateEntry.DisplayName, updateEntry.EventType, updateEntry.UpdaterId,
			)
			if err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to insert situation update")
				return nil, err
			}
		}
		for _, relatedSituationID := range situation.RelatedSituationsIds {
			insertRelatedQuery := `INSERT INTO situation_related (situation_id, org_id, related_situation_id) VALUES ($1, $2, $3)`
			_, err := sessionTx.ExecContext(spanContext, insertRelatedQuery, situation.Id, orgID, relatedSituationID)
			if err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to insert related situation")
				return nil, err
			}
		}
		for _, mediaAttachment := range situation.MediaAttachments {
			insertMediaAttachmentQuery := `INSERT INTO situation_media_attachments (situation_id, org_id, attachment_id, url, content_type) VALUES ($1, $2, $3, $4, $5)`
			_, err := sessionTx.ExecContext(spanContext, insertMediaAttachmentQuery, situation.Id, orgID, mediaAttachment.AttachmentId, mediaAttachment.Url, mediaAttachment.ContentType)
			if err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to insert media attachment")
				return nil, err
			}
		}

		insertStatusUpdateQuery := `
			INSERT INTO situation_status_updates
				(situation_id, org_id, timestamp, new_status, previous_status, note, updater_id, update_source)
			VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
		`
		_, err = sessionTx.ExecContext(spanContext, insertStatusUpdateQuery,
			situation.Id,
			orgID,
			currentTime,
			int32(situation.Status),
			int32(situations.SituationStatus_SITUATION_STATUS_UNSPECIFIED),
			// These fields are not used in the current implementation.
			"",
			"",
			int32(situations.UpdateSource_UPDATE_SOURCE_UNKNOWN),
		)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to insert status update")
			return nil, err
		}
		return repository.GetSituation(spanContext, sessionTx, situation.Id)
	})
}

// GetSituation retrieves a situation and its repeated fields.
func (repository *PostgresSituationRepository) GetSituation(requestContext context.Context, transaction *sql.Tx, situationID string) (*situations.Situation, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(requestContext, "SituationRepository.GetSituation")
	defer finishSpan()

	span.SetTag("situation.id", situationID)

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) (*situations.Situation, error) {
		// Retrieve the main situation record using optimized query
		fetchSituationQuery := `
			SELECT id, priority, title, type, resource_type, description, status, reporter_id,
				   create_time, update_time, due_time, resolved_time, incident_time, trigger_source,
				   reporter_name, contact_no, contact_email, address, latitude, longitude, address_update_time, automation_enabled, additional_info_json
			FROM situations
			WHERE id = $1
		`
		rows, err := sessionTx.QueryContext(spanContext, fetchSituationQuery, situationID)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to query situation")
			return nil, err
		}
		defer rows.Close()

		// Use optimized helper functions for scanning and relationship loading
		situationsList, situationIDs, situationsMap, scanErr := scanSituationRows(rows)
		if scanErr != nil {
			herosentry.CaptureException(spanContext, scanErr, herosentry.ErrorTypeDatabase, "Failed to scan situation rows")
			return nil, scanErr
		}

		// Check if situation was found
		if len(situationsList) == 0 {
			herosentry.CaptureException(spanContext, ErrSituationNotFound, herosentry.ErrorTypeNotFound, "Situation not found")
			return nil, ErrSituationNotFound
		}

		// Use optimized batch loading for all relationships
		if len(situationIDs) > 0 {
			if err := hydrateRepeatedFields(spanContext, sessionTx, repository.database, situationsMap, situationIDs); err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to hydrate repeated fields")
				return nil, err
			}
		}

		return situationsList[0], nil
	})
}

// ListSituations returns a paginated list of situations with optional filtering and ordering.
func (repository *PostgresSituationRepository) ListSituations(
	requestContext context.Context,
	transaction *sql.Tx,
	pageSize int,
	paginationToken string,
	status situations.SituationStatus,
	triggerSource situations.TriggerSource,
	situationType situations.SituationType,
	orderBy string,
) (*situations.ListSituationsResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(requestContext, "SituationRepository.ListSituations")
	defer finishSpan()

	span.SetTag("page_size", fmt.Sprintf("%d", pageSize))
	span.SetTag("filter.status", status.String())
	span.SetTag("filter.trigger_source", triggerSource.String())
	span.SetTag("filter.type", situationType.String())

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) (*situations.ListSituationsResponse, error) {
		// Calculate offset from paginationToken.
		offsetValue := 0
		if paginationToken != "" {
			if parsedOffset, err := strconv.Atoi(paginationToken); err == nil {
				offsetValue = parsedOffset
			}
		}

		// Start building the base query.
		baseQuery := `
			SELECT id, priority, title, type, resource_type, description, status, reporter_id,
				   create_time, update_time, due_time, resolved_time, incident_time, trigger_source,
				   reporter_name, contact_no, contact_email, address, latitude, longitude, address_update_time, automation_enabled, additional_info_json
			FROM situations
		`
		// Build filtering conditions.
		conditions := []string{}
		args := []interface{}{}

		// Filter by situation status if provided.
		if status != situations.SituationStatus_SITUATION_STATUS_UNSPECIFIED {
			conditions = append(conditions, "status = $"+strconv.Itoa(len(args)+1))
			args = append(args, int32(status))
		}

		// Filter by trigger source if provided.
		if triggerSource != situations.TriggerSource_TRIGGER_SOURCE_UNKNOWN {
			conditions = append(conditions, "trigger_source = $"+strconv.Itoa(len(args)+1))
			args = append(args, int32(triggerSource))
		}

		// Filter by situation type if provided.
		if situationType != situations.SituationType_SITUATION_TYPE_UNSPECIFIED {
			conditions = append(conditions, "type = $"+strconv.Itoa(len(args)+1))
			args = append(args, int32(situationType))
		}

		// Append WHERE clause if any conditions exist.
		whereClause := ""
		if len(conditions) > 0 {
			whereClause = " WHERE " + strings.Join(conditions, " AND ")
		}

		// Get total count first
		countQuery := "SELECT COUNT(*) FROM situations" + whereClause
		var totalCount int
		if err := sessionTx.QueryRowContext(spanContext, countQuery, args...).Scan(&totalCount); err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to get total count")
			return nil, err
		}

		// Append ORDER BY clause.
		orderByClause := ""
		if orderBy != "" {
			orderByClause = " ORDER BY " + orderBy
		} else {
			orderByClause = " ORDER BY create_time DESC, id DESC"
		}

		// Append LIMIT and OFFSET; their parameter indices follow the previous args.
		args = append(args, pageSize, offsetValue)
		baseQuery += whereClause + orderByClause + " LIMIT $" + strconv.Itoa(len(args)-1) + " OFFSET $" + strconv.Itoa(len(args))

		// Execute the query.
		rows, err := sessionTx.QueryContext(spanContext, baseQuery, args...)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to execute list query")
			return nil, err
		}
		defer rows.Close()

		// Use optimized helper functions instead of manual processing
		situationsList, situationIDs, situationsMap, scanErr := scanSituationRows(rows)
		if scanErr != nil {
			herosentry.CaptureException(spanContext, scanErr, herosentry.ErrorTypeDatabase, "Failed to scan situation rows")
			return nil, scanErr
		}

		// Use optimized batch loading for all relationships
		if len(situationIDs) > 0 {
			if err := hydrateRepeatedFields(spanContext, sessionTx, repository.database, situationsMap, situationIDs); err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to hydrate repeated fields")
				return nil, err
			}
		}

		// Determine the next page token.
		nextPageToken := ""
		if len(situationsList) == pageSize {
			nextPageToken = strconv.Itoa(offsetValue + pageSize)
		}

		totalResults, err := safeInt32Conversion(totalCount)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, "Total count too large")
			return nil, fmt.Errorf("total count too large: %w", err)
		}
		return &situations.ListSituationsResponse{
			Situations:    situationsList,
			NextPageToken: nextPageToken,
			TotalResults:  totalResults,
		}, nil
	})
}

// ListSituationsForAsset returns situations "touched" by an asset, with rich filtering and pagination.
func (repository *PostgresSituationRepository) ListSituationsForAsset(
	ctx context.Context,
	tx *sql.Tx,
	assetID string,
	statusFilter situations.SituationStatus,
	typeFilter situations.SituationType,
	triggerSourceFilter situations.TriggerSource,
	priorityFilter int32,
	reporterFilter string,
	createdAfter time.Time,
	createdBefore time.Time,
	minimumLatitude, maximumLatitude, minimumLongitude, maximumLongitude float64,
	tagsFilter []string,
	pageSize int,
	pageToken string,
) (*situations.ListSituationsForAssetResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "SituationRepository.ListSituationsForAsset")
	defer finishSpan()

	span.SetTag("asset.id", assetID)
	span.SetTag("page_size", fmt.Sprintf("%d", pageSize))
	span.SetTag("filter.status", statusFilter.String())
	span.SetTag("filter.type", typeFilter.String())
	span.SetTag("filter.trigger_source", triggerSourceFilter.String())
	span.SetTag("filter.priority", fmt.Sprintf("%d", priorityFilter))

	return database.WithSession(repository.database, spanContext, tx, func(activeTx *sql.Tx) (*situations.ListSituationsForAssetResponse, error) {
		// Parse pagination offset
		offsetValue := 0
		if pageToken != "" {
			if parsedOffset, parseError := strconv.Atoi(pageToken); parseError == nil {
				offsetValue = parsedOffset
			}
		}

		// Build base query with JOIN to orders
		baseQuery := `
            SELECT DISTINCT
                s.id, s.priority, s.title, s.type, s.resource_type, s.description,
                s.status, s.reporter_id, s.create_time, s.update_time,
                s.due_time, s.resolved_time, s.incident_time, s.trigger_source,
                s.reporter_name, s.contact_no, s.contact_email,
                s.address, s.latitude, s.longitude, s.address_update_time,
                s.automation_enabled, s.additional_info_json::text
            FROM situations s
            JOIN orders o
              ON o.situation_id = s.id
             AND o.org_id      = s.org_id
             AND o.asset_id    = $1
        `
		queryArguments := []interface{}{assetID}
		filterConditions := []string{}

		// Status filter
		if statusFilter != situations.SituationStatus_SITUATION_STATUS_UNSPECIFIED {
			queryArguments = append(queryArguments, int32(statusFilter))
			filterConditions = append(filterConditions,
				fmt.Sprintf("s.status = $%d", len(queryArguments)),
			)
		}
		// Type filter
		if typeFilter != situations.SituationType_SITUATION_TYPE_UNSPECIFIED {
			queryArguments = append(queryArguments, int32(typeFilter))
			filterConditions = append(filterConditions,
				fmt.Sprintf("s.type = $%d", len(queryArguments)),
			)
		}
		// Trigger source filter
		if triggerSourceFilter != situations.TriggerSource_TRIGGER_SOURCE_UNKNOWN {
			queryArguments = append(queryArguments, int32(triggerSourceFilter))
			filterConditions = append(filterConditions,
				fmt.Sprintf("s.trigger_source = $%d", len(queryArguments)),
			)
		}
		// Priority filter
		if priorityFilter != 0 {
			queryArguments = append(queryArguments, priorityFilter)
			filterConditions = append(filterConditions,
				fmt.Sprintf("s.priority = $%d", len(queryArguments)),
			)
		}
		// Reporter filter
		if reporterFilter != "" {
			queryArguments = append(queryArguments, reporterFilter)
			filterConditions = append(filterConditions,
				fmt.Sprintf("s.reporter_id = $%d", len(queryArguments)),
			)
		}
		// Creation time filters
		if !createdAfter.IsZero() {
			queryArguments = append(queryArguments, createdAfter)
			filterConditions = append(filterConditions,
				fmt.Sprintf("s.create_time >= $%d", len(queryArguments)),
			)
		}
		if !createdBefore.IsZero() {
			queryArguments = append(queryArguments, createdBefore)
			filterConditions = append(filterConditions,
				fmt.Sprintf("s.create_time <= $%d", len(queryArguments)),
			)
		}
		// Geographic bounding box
		if minimumLatitude != 0 {
			queryArguments = append(queryArguments, minimumLatitude)
			filterConditions = append(filterConditions,
				fmt.Sprintf("s.latitude >= $%d", len(queryArguments)),
			)
		}
		if maximumLatitude != 0 {
			queryArguments = append(queryArguments, maximumLatitude)
			filterConditions = append(filterConditions,
				fmt.Sprintf("s.latitude <= $%d", len(queryArguments)),
			)
		}
		if minimumLongitude != 0 {
			queryArguments = append(queryArguments, minimumLongitude)
			filterConditions = append(filterConditions,
				fmt.Sprintf("s.longitude >= $%d", len(queryArguments)),
			)
		}
		if maximumLongitude != 0 {
			queryArguments = append(queryArguments, maximumLongitude)
			filterConditions = append(filterConditions,
				fmt.Sprintf("s.longitude <= $%d", len(queryArguments)),
			)
		}
		// Tags filter
		if len(tagsFilter) > 0 {
			queryArguments = append(queryArguments, pq.Array(tagsFilter))
			filterConditions = append(filterConditions, fmt.Sprintf(`
                EXISTS (
                    SELECT 1 FROM situation_tags st
                     WHERE st.situation_id = s.id
                       AND st.tag = ANY($%d)
                )`, len(queryArguments)),
			)
		}

		// Assemble WHERE clause
		whereClause := ""
		if len(filterConditions) > 0 {
			whereClause = " WHERE " + strings.Join(filterConditions, " AND ")
		}

		// Get total count first
		countQuery := `
			SELECT COUNT(DISTINCT s.id)
			FROM situations s
			JOIN orders o
			  ON o.situation_id = s.id
			 AND o.org_id      = s.org_id
			 AND o.asset_id    = $1` + whereClause
		var totalCount int
		if err := activeTx.QueryRowContext(spanContext, countQuery, queryArguments...).Scan(&totalCount); err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to get total count for asset")
			return nil, err
		}

		// Append ORDER BY and pagination
		baseQuery += whereClause + " ORDER BY s.create_time DESC, s.id DESC"
		queryArguments = append(queryArguments, pageSize, offsetValue)
		baseQuery += fmt.Sprintf(" LIMIT $%d OFFSET $%d", len(queryArguments)-1, len(queryArguments))

		// Execute the main query
		situationRows, queryError := activeTx.QueryContext(spanContext, baseQuery, queryArguments...)
		if queryError != nil {
			herosentry.CaptureException(spanContext, queryError, herosentry.ErrorTypeDatabase, "Failed to execute list query for asset")
			return nil, queryError
		}
		// Close the primary rows immediately after scanning
		defer situationRows.Close()

		// Use optimized helper functions instead of manual processing
		situationList, situationIDs, situationByID, scanErr := scanSituationRows(situationRows)
		if scanErr != nil {
			herosentry.CaptureException(spanContext, scanErr, herosentry.ErrorTypeDatabase, "Failed to scan situation rows")
			return nil, scanErr
		}

		// Use optimized batch loading for all relationships
		if len(situationIDs) > 0 {
			if err := hydrateRepeatedFields(spanContext, activeTx, repository.database, situationByID, situationIDs); err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to hydrate repeated fields")
				return nil, err
			}
		}

		// Compute next page token
		nextPageToken := ""
		if len(situationList) == pageSize {
			nextPageToken = strconv.Itoa(offsetValue + pageSize)
		}

		totalResults, err := safeInt32Conversion(totalCount)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, "Total count too large")
			return nil, fmt.Errorf("total count too large: %w", err)
		}
		return &situations.ListSituationsForAssetResponse{
			Situations:    situationList,
			NextPageToken: nextPageToken,
			TotalResults:  totalResults,
		}, nil
	})
}

// UpdateSituation updates the main situation record and replaces repeated fields.
func (repository *PostgresSituationRepository) UpdateSituation(requestContext context.Context, transaction *sql.Tx, updatedSituation *situations.Situation) (*situations.Situation, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(requestContext, "SituationRepository.UpdateSituation")
	defer finishSpan()

	span.SetTag("situation.id", updatedSituation.Id)
	span.SetTag("situation.type", updatedSituation.Type.String())
	span.SetTag("situation.status", updatedSituation.Status.String())
	span.SetTag("situation.priority", fmt.Sprintf("%d", updatedSituation.Priority))

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) (*situations.Situation, error) {
		// Retrieve the org_id from ctx
		orgID := cmncontext.GetOrgId(spanContext)
		span.SetTag("org.id", fmt.Sprintf("%d", orgID))

		currentTime := time.Now()
		updatedSituation.UpdateTime = commonUtils.TimeToISO8601String(currentTime)
		// Enforce resource_type to always be "SITUATION".
		updatedSituation.ResourceType = FixedResourceTypeSituation
		if updatedSituation.AdditionalInfoJson == "" {
			updatedSituation.AdditionalInfoJson = "{}"
		}

		// Convert empty reporter_id to SQL NULL.
		var reporterID interface{}
		if updatedSituation.ReporterId == "" {
			reporterID = nil
		} else {
			reporterID = updatedSituation.ReporterId
		}

		// *** Fetch the current (old) status before updating ***
		var oldStatus int32
		selectStatusQuery := `SELECT status FROM situations WHERE id = $1`
		if err := sessionTx.QueryRowContext(spanContext, selectStatusQuery, updatedSituation.Id).Scan(&oldStatus); err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to fetch current status")
			return nil, err
		}

		// If the new status is "resolved" and the old status was different,
		// update the ResolvedTime to the current time.
		if int32(updatedSituation.Status) == int32(situations.SituationStatus_SITUATION_STATUS_RESOLVED) &&
			oldStatus != int32(situations.SituationStatus_SITUATION_STATUS_RESOLVED) {
			updatedSituation.ResolvedTime = commonUtils.TimeToISO8601String(currentTime)
		}

		// Convert string timestamps to sql.NullTime for database
		var dueTime, resolvedTime, incidentTime, addressUpdateTime sql.NullTime
		var err error

		if updatedSituation.DueTime != "" {
			t, err := commonUtils.ISO8601StringToTime(updatedSituation.DueTime)
			if err != nil {
				return nil, fmt.Errorf("invalid due_time format: %w", err)
			}
			dueTime = sql.NullTime{Time: t, Valid: true}
		} else {
			dueTime = sql.NullTime{Valid: false}
		}

		if updatedSituation.ResolvedTime != "" {
			t, err := commonUtils.ISO8601StringToTime(updatedSituation.ResolvedTime)
			if err != nil {
				return nil, fmt.Errorf("invalid resolved_time format: %w", err)
			}
			resolvedTime = sql.NullTime{Time: t, Valid: true}
		} else {
			resolvedTime = sql.NullTime{Valid: false}
		}

		if updatedSituation.IncidentTime != "" {
			t, err := commonUtils.ISO8601StringToTime(updatedSituation.IncidentTime)
			if err != nil {
				return nil, fmt.Errorf("invalid incident_time format: %w", err)
			}
			incidentTime = sql.NullTime{Time: t, Valid: true}
		} else {
			incidentTime = sql.NullTime{Valid: false}
		}

		if updatedSituation.AddressUpdateTime != "" {
			t, err := commonUtils.ISO8601StringToTime(updatedSituation.AddressUpdateTime)
			if err != nil {
				return nil, fmt.Errorf("invalid address_update_time format: %w", err)
			}
			addressUpdateTime = sql.NullTime{Time: t, Valid: true}
		} else {
			addressUpdateTime = sql.NullTime{Valid: false}
		}

		updateSituationQuery := `
			UPDATE situations
			SET priority=$1, title=$2, type=$3, resource_type=$4, description=$5, status=$6,
				reporter_id=$7, update_time=$8, due_time=$9, resolved_time=$10, incident_time=$11,
				trigger_source=$12, reporter_name=$13, contact_no=$14, contact_email=$15, address=$16,
				latitude=$17, longitude=$18, address_update_time=$19, automation_enabled=$20, additional_info_json=$21
			WHERE id=$22
		`
		_, err = sessionTx.ExecContext(spanContext, updateSituationQuery,
			updatedSituation.Priority,
			updatedSituation.Title,
			int32(updatedSituation.Type),
			updatedSituation.ResourceType,
			updatedSituation.Description,
			int32(updatedSituation.Status),
			reporterID,
			updatedSituation.UpdateTime,
			dueTime,
			resolvedTime,
			incidentTime,
			int32(updatedSituation.TriggerSource),
			updatedSituation.ReporterName,
			updatedSituation.ContactNo,
			updatedSituation.ContactEmail,
			updatedSituation.Address,
			updatedSituation.Latitude,
			updatedSituation.Longitude,
			addressUpdateTime,
			updatedSituation.AutomationEnabled,
			updatedSituation.AdditionalInfoJson,
			updatedSituation.Id,
		)
		if err != nil {
			return nil, err
		}

		// *** If status has changed, automatically insert a new status update entry ***
		if oldStatus != int32(updatedSituation.Status) {
			insertStatusUpdateQuery := `
				INSERT INTO situation_status_updates
					(situation_id, org_id, timestamp, new_status, previous_status, note, updater_id, update_source)
				VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
			`
			_, err = sessionTx.ExecContext(spanContext, insertStatusUpdateQuery,
				updatedSituation.Id,
				orgID,
				updatedSituation.UpdateTime,
				int32(updatedSituation.Status),
				oldStatus,
				// These fields are not used in the current implementation.
				"",
				"",
				int32(situations.UpdateSource_UPDATE_SOURCE_UNKNOWN),
			)
			if err != nil {
				return nil, err
			}
		}

		_, err = sessionTx.ExecContext(spanContext, `DELETE FROM situation_updates WHERE situation_id = $1`, updatedSituation.Id)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to delete existing situation updates")
			return nil, err
		}
		for _, updateEntry := range updatedSituation.Updates {
			insertUpdateQuery := `
				INSERT INTO situation_updates 
					(situation_id, org_id, message, timestamp, update_source, display_name, event_type, updater_id)
				VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
			`
			_, err := sessionTx.ExecContext(spanContext, insertUpdateQuery,
				updatedSituation.Id, orgID, updateEntry.Message,
				updateEntry.Timestamp,
				int32(updateEntry.UpdateSource),
				updateEntry.DisplayName, updateEntry.EventType, updateEntry.UpdaterId,
			)
			if err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to insert situation update")
				return nil, err
			}
		}
		_, err = sessionTx.ExecContext(spanContext, `DELETE FROM situation_tags WHERE situation_id = $1`, updatedSituation.Id)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to delete existing situation tags")
			return nil, err
		}
		for _, tag := range updatedSituation.Tags {
			insertTagQuery := `INSERT INTO situation_tags (situation_id, org_id, tag) VALUES ($1, $2, $3)`
			_, err := sessionTx.ExecContext(spanContext, insertTagQuery, updatedSituation.Id, orgID, tag)
			if err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to insert situation tag")
				return nil, err
			}
		}
		_, err = sessionTx.ExecContext(spanContext, `DELETE FROM situation_related WHERE situation_id = $1`, updatedSituation.Id)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to delete existing related situations")
			return nil, err
		}
		for _, relatedSituationID := range updatedSituation.RelatedSituationsIds {
			insertRelatedQuery := `INSERT INTO situation_related (situation_id, org_id, related_situation_id) VALUES ($1, $2, $3)`
			_, err := sessionTx.ExecContext(spanContext, insertRelatedQuery, updatedSituation.Id, orgID, relatedSituationID)
			if err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to insert related situation")
				return nil, err
			}
		}
		_, err = sessionTx.ExecContext(spanContext, `DELETE FROM situation_media_attachments WHERE situation_id = $1`, updatedSituation.Id)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to delete existing media attachments")
			return nil, err
		}
		for _, mediaAttachment := range updatedSituation.MediaAttachments {
			insertMediaAttachmentQuery := `INSERT INTO situation_media_attachments (situation_id, org_id, attachment_id, url, content_type) VALUES ($1, $2, $3, $4, $5)`
			_, err := sessionTx.ExecContext(spanContext, insertMediaAttachmentQuery, updatedSituation.Id, orgID, mediaAttachment.AttachmentId, mediaAttachment.Url, mediaAttachment.ContentType)
			if err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to insert media attachment")
				return nil, err
			}
		}
		return repository.GetSituation(spanContext, sessionTx, updatedSituation.Id)
	})
}

// DeleteSituation physically deletes a situation.
func (repository *PostgresSituationRepository) DeleteSituation(requestContext context.Context, transaction *sql.Tx, situationID string) error {
	spanContext, span, finishSpan := herosentry.StartSpan(requestContext, "SituationRepository.DeleteSituation")
	defer finishSpan()

	span.SetTag("situation.id", situationID)

	return database.WithSessionErr(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) error {
		deleteSituationQuery := `DELETE FROM situations WHERE id = $1`
		result, err := sessionTx.ExecContext(spanContext, deleteSituationQuery, situationID)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to delete situation")
			return err
		}
		rowsAffected, err := result.RowsAffected()
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to get rows affected")
			return err
		}
		if rowsAffected == 0 {
			herosentry.CaptureException(spanContext, ErrSituationNotFound, herosentry.ErrorTypeNotFound, "Situation not found for deletion")
			return ErrSituationNotFound
		}
		return nil
	})
}

// Fine-grained operations:

func (repository *PostgresSituationRepository) AddSituationUpdate(requestContext context.Context, transaction *sql.Tx, situationID string, updateEntry *situations.UpdateEntry) (*situations.Situation, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(requestContext, "SituationRepository.AddSituationUpdate")
	defer finishSpan()

	span.SetTag("situation.id", situationID)
	span.SetTag("update.source", updateEntry.UpdateSource.String())

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) (*situations.Situation, error) {
		// Retrieve the org_id from ctx
		orgID := cmncontext.GetOrgId(spanContext)
		span.SetTag("org.id", fmt.Sprintf("%d", orgID))

		insertUpdateQuery := `
			INSERT INTO situation_updates 
				(situation_id, org_id, message, timestamp, update_source, display_name, event_type, updater_id)
			VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
		`
		var updateTime sql.NullTime

		if updateEntry.Timestamp != "" {
			t, err := commonUtils.ISO8601StringToTime(updateEntry.Timestamp)
			if err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, "Invalid timestamp format")
				return nil, fmt.Errorf("invalid timestamp format: %w", err)
			}
			updateTime = sql.NullTime{Time: t, Valid: true}
		} else {
			// If no timestamp provided, use current time
			updateTime = sql.NullTime{Time: time.Now(), Valid: true}
		}

		_, err := sessionTx.ExecContext(spanContext, insertUpdateQuery,
			situationID, orgID, updateEntry.Message,
			updateTime,
			int32(updateEntry.UpdateSource),
			updateEntry.DisplayName, updateEntry.EventType, updateEntry.UpdaterId,
		)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to insert situation update")
			return nil, err
		}
		return repository.GetSituation(spanContext, sessionTx, situationID)
	})
}

func (repository *PostgresSituationRepository) RemoveSituationUpdate(requestContext context.Context, transaction *sql.Tx, situationID string, updateEntry *situations.UpdateEntry) (*situations.Situation, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(requestContext, "SituationRepository.RemoveSituationUpdate")
	defer finishSpan()

	span.SetTag("situation.id", situationID)

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) (*situations.Situation, error) {
		deleteUpdateQuery := `
			DELETE FROM situation_updates
			WHERE situation_id = $1 AND message = $2 AND timestamp = $3 AND update_source = $4
			      AND display_name = $5 AND event_type = $6 AND updater_id = $7
		`
		var updateTime sql.NullTime

		if updateEntry.Timestamp != "" {
			t, err := commonUtils.ISO8601StringToTime(updateEntry.Timestamp)
			if err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, "Invalid timestamp format")
				return nil, fmt.Errorf("invalid timestamp format: %w", err)
			}
			updateTime = sql.NullTime{Time: t, Valid: true}
		} else {
			// If no timestamp provided, use an empty time (will likely not match anything)
			updateTime = sql.NullTime{Valid: false}
		}

		result, err := sessionTx.ExecContext(spanContext, deleteUpdateQuery,
			situationID, updateEntry.Message,
			updateTime,
			int32(updateEntry.UpdateSource),
			updateEntry.DisplayName, updateEntry.EventType, updateEntry.UpdaterId,
		)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to delete situation update")
			return nil, err
		}
		rowsAffected, err := result.RowsAffected()
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to get rows affected")
			return nil, err
		}
		if rowsAffected == 0 {
			err := errors.New("update entry not found")
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeNotFound, "Update entry not found")
			return nil, err
		}
		return repository.GetSituation(spanContext, sessionTx, situationID)
	})
}

func (repository *PostgresSituationRepository) AddSituationTag(requestContext context.Context, transaction *sql.Tx, situationID string, tag string) (*situations.Situation, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(requestContext, "SituationRepository.AddSituationTag")
	defer finishSpan()

	span.SetTag("situation.id", situationID)
	span.SetTag("tag", tag)

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) (*situations.Situation, error) {
		// Retrieve the org_id from ctx
		orgID := cmncontext.GetOrgId(spanContext)
		span.SetTag("org.id", fmt.Sprintf("%d", orgID))

		insertTagQuery := `INSERT INTO situation_tags (situation_id, org_id, tag) VALUES ($1, $2, $3)`
		_, err := sessionTx.ExecContext(spanContext, insertTagQuery, situationID, orgID, tag)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to insert situation tag")
			return nil, err
		}
		return repository.GetSituation(spanContext, sessionTx, situationID)
	})
}

func (repository *PostgresSituationRepository) RemoveSituationTag(requestContext context.Context, transaction *sql.Tx, situationID string, tag string) (*situations.Situation, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(requestContext, "SituationRepository.RemoveSituationTag")
	defer finishSpan()

	span.SetTag("situation.id", situationID)
	span.SetTag("tag", tag)

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) (*situations.Situation, error) {
		deleteTagQuery := `DELETE FROM situation_tags WHERE situation_id = $1 AND tag = $2`
		result, err := sessionTx.ExecContext(spanContext, deleteTagQuery, situationID, tag)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to delete situation tag")
			return nil, err
		}
		rowsAffected, err := result.RowsAffected()
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to get rows affected")
			return nil, err
		}
		if rowsAffected == 0 {
			err := errors.New("tag not found")
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeNotFound, "Tag not found")
			return nil, err
		}
		return repository.GetSituation(spanContext, sessionTx, situationID)
	})
}

func (repository *PostgresSituationRepository) AddRelatedSituation(requestContext context.Context, transaction *sql.Tx, situationID string, relatedSituationID string) (*situations.Situation, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(requestContext, "SituationRepository.AddRelatedSituation")
	defer finishSpan()

	span.SetTag("situation.id", situationID)
	span.SetTag("related_situation.id", relatedSituationID)

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) (*situations.Situation, error) {
		// Retrieve the org_id from ctx
		orgID := cmncontext.GetOrgId(spanContext)
		span.SetTag("org.id", fmt.Sprintf("%d", orgID))

		insertRelatedQuery := `INSERT INTO situation_related (situation_id, org_id, related_situation_id) VALUES ($1, $2, $3)`
		_, err := sessionTx.ExecContext(spanContext, insertRelatedQuery, situationID, orgID, relatedSituationID)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to insert related situation")
			return nil, err
		}
		return repository.GetSituation(spanContext, sessionTx, situationID)
	})
}

func (repository *PostgresSituationRepository) RemoveRelatedSituation(requestContext context.Context, transaction *sql.Tx, situationID string, relatedSituationID string) (*situations.Situation, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(requestContext, "SituationRepository.RemoveRelatedSituation")
	defer finishSpan()

	span.SetTag("situation.id", situationID)
	span.SetTag("related_situation.id", relatedSituationID)

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) (*situations.Situation, error) {
		deleteRelatedQuery := `DELETE FROM situation_related WHERE situation_id = $1 AND related_situation_id = $2`
		result, err := sessionTx.ExecContext(spanContext, deleteRelatedQuery, situationID, relatedSituationID)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to delete related situation")
			return nil, err
		}
		rowsAffected, err := result.RowsAffected()
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to get rows affected")
			return nil, err
		}
		if rowsAffected == 0 {
			err := errors.New("related situation not found")
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeNotFound, "Related situation not found")
			return nil, err
		}
		return repository.GetSituation(spanContext, sessionTx, situationID)
	})
}

func (repository *PostgresSituationRepository) AddMediaAttachmentForSituation(requestContext context.Context, transaction *sql.Tx, situationID string, mediaAttachment *situations.MediaAttachment) (*situations.Situation, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(requestContext, "SituationRepository.AddMediaAttachmentForSituation")
	defer finishSpan()

	span.SetTag("situation.id", situationID)
	span.SetTag("attachment.id", mediaAttachment.AttachmentId)
	span.SetTag("attachment.content_type", mediaAttachment.ContentType)

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) (*situations.Situation, error) {
		// Retrieve the org_id from ctx
		orgID := cmncontext.GetOrgId(spanContext)
		span.SetTag("org.id", fmt.Sprintf("%d", orgID))

		insertMediaAttachmentQuery := `INSERT INTO situation_media_attachments (situation_id, org_id, attachment_id, url, content_type) VALUES ($1, $2, $3, $4, $5)`
		_, err := sessionTx.ExecContext(spanContext, insertMediaAttachmentQuery, situationID, orgID, mediaAttachment.AttachmentId, mediaAttachment.Url, mediaAttachment.ContentType)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to insert media attachment")
			return nil, err
		}
		return repository.GetSituation(spanContext, sessionTx, situationID)
	})
}

func (repository *PostgresSituationRepository) RemoveMediaAttachmentForSituation(requestContext context.Context, transaction *sql.Tx, situationID string, mediaAttachment *situations.MediaAttachment) (*situations.Situation, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(requestContext, "SituationRepository.RemoveMediaAttachmentForSituation")
	defer finishSpan()

	span.SetTag("situation.id", situationID)
	span.SetTag("attachment.id", mediaAttachment.AttachmentId)

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) (*situations.Situation, error) {
		deleteMediaAttachmentQuery := `DELETE FROM situation_media_attachments WHERE situation_id = $1 AND attachment_id = $2`
		result, err := sessionTx.ExecContext(spanContext, deleteMediaAttachmentQuery, situationID, mediaAttachment.AttachmentId)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to delete media attachment")
			return nil, err
		}
		rowsAffected, err := result.RowsAffected()
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to get rows affected")
			return nil, err
		}
		if rowsAffected == 0 {
			err := errors.New("media attachment not found")
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeNotFound, "Media attachment not found")
			return nil, err
		}
		return repository.GetSituation(spanContext, sessionTx, situationID)
	})
}

// UpdateAdditionalInfoJSON updates the AdditionalInfoJson field of the situation in PostgreSQL.
func (repository *PostgresSituationRepository) UpdateAdditionalInfoJSON(ctx context.Context, transaction *sql.Tx, situationID string, additionalInfoJSON string) (string, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "SituationRepository.UpdateAdditionalInfoJSON")
	defer finishSpan()

	span.SetTag("situation.id", situationID)

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) (string, error) {
		currentTime := time.Now()

		updateQuery := `
		UPDATE situations
		SET additional_info_json = $1, update_time = $2
		WHERE id = $3
	`
		result, err := sessionTx.ExecContext(spanContext, updateQuery, additionalInfoJSON, currentTime, situationID)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to update additional info JSON")
			return "", err
		}
		rowsAffected, err := result.RowsAffected()
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to get rows affected")
			return "", err
		}
		if rowsAffected == 0 {
			herosentry.CaptureException(spanContext, ErrSituationNotFound, herosentry.ErrorTypeNotFound, "Situation not found for additional info update")
			return "", ErrSituationNotFound
		}
		return situationID, nil
	})
}

// SearchSituations turns a `SearchSituationsRequest` into an SQL query that
// relies on the indexes TRGM, BTREE, BRIN and returns
// a paginated `ListSituationsResult`.
//
// Features supported:
//
//   - **Text search** — performs ILIKE pattern matching across all searchable fields
//     by default; when specific `searchFields` are provided, only searches those fields.
//
//   - **Field-specific queries** — allows targeting specific fields with different search terms
//     via the `field_queries` parameter.
//
//   - **Partial text match** — title / description / reporter / e-mail / phone / address / tags
//     use the TRGM GIN indexes for efficient ILIKE searches.
//
//   - **Exact-value filters** — status, type, trigger_source, priority, tags.
//
//   - **Date-range filters** — five timestamp fields, inclusive.
//
//   - **Geo bounding-box** — simple latitude/longitude comparisons.
//
//   - **Pagination & safe ordering** — OFFSET/LIMIT + enum whitelist
//     (`SearchOrderBy`) so no raw "ORDER BY "+userInput`.
//
//   - **Result highlighting** — generates highlighted fragments showing matched terms
//     in the search results.
//
// Execution flow:
//
//  0. Helper lambdas: placeholder builder, enum→int32, ASC/DESC text.
//  1. Build `SELECT … FROM situations` plus a dynamic `WHERE` section.
//     1A.  Pattern matching search across all searchable fields or field-scoped search.
//     1B.  Field-specific queries for targeted searching.
//     1C.  Exact-value filters for status / type / trigger source / priority / tags.
//     1D.  Date-range filters for five timestamp columns.
//     1E.  Latitude / longitude bounding box.
//  2. Append a *whitelisted* ORDER BY clause based on `SearchOrderBy`.
//  3. Append LIMIT / OFFSET using request's pageSize + pageToken.
//  4. Execute the query and scan "core" columns into Situation objects.
//  5. Bulk-hydrate Updates, Tags, Related IDs, Media, StatusUpdates.
//  6. Generate highlight fragments for matched query terms.
//  7. Return slice + next-page token + highlights.
//
// Example Request and Generated SQL:
//
//	Sample Request:
//	  {
//	    "query": "emergency fire",
//	    "field_queries": [{"field": "title", "query": "urgent"}, {"field": "description", "query": "smoke"}],
//	    "status": [SITUATION_STATUS_ACTIVE, SITUATION_STATUS_IN_PROGRESS],
//	    "type": [SITUATION_TYPE_EMERGENCY],
//	    "priority": [1, 2],
//	    "createTime": {"from": "2023-01-01T00:00:00Z", "to": "2023-12-31T23:59:59Z"},
//	    "minLatitude": 37.0, "maxLatitude": 38.0,
//	    "minLongitude": -123.0, "maxLongitude": -122.0,
//	    "tags": ["urgent", "fire"],
//	    "orderBy": SEARCH_ORDER_BY_RELEVANCE,
//	    "ascending": false,
//	    "pageSize": 20,
//	    "pageToken": "0"
//	  }
//
//	Generated SQL:
//	  SELECT id, priority, title, type, resource_type, description, status,
//	         reporter_id, create_time, update_time, due_time, resolved_time,
//	         incident_time, trigger_source, reporter_name, contact_no,
//	         contact_email, address, latitude, longitude, address_update_time,
//	         automation_enabled, additional_info_json::text
//	    FROM situations
//	   WHERE (lower(title) ILIKE '%emergency fire%'
//	      OR lower(description) ILIKE '%emergency fire%'
//	      OR lower(reporter_name) ILIKE '%emergency fire%'
//	      OR lower(contact_email) ILIKE '%emergency fire%'
//	      OR lower(contact_no) ILIKE '%emergency fire%'
//	      OR lower(address) ILIKE '%emergency fire%'
//	      OR EXISTS (SELECT 1 FROM situation_tags st
//	                 WHERE st.situation_id = situations.id
//	                   AND lower(st.tag) ILIKE '%emergency fire%'))
//	     AND lower(title) ILIKE '%urgent%'
//	     AND lower(description) ILIKE '%smoke%'
//	     AND status = ANY($1)
//	     AND type = ANY($2)
//	     AND priority = ANY($3)
//	     AND create_time >= $4 AND create_time <= $5
//	     AND latitude >= $6 AND latitude <= $7
//	     AND longitude >= $8 AND longitude <= $9
//	     AND EXISTS (SELECT 1 FROM situation_tags st
//	                 WHERE st.situation_id = situations.id
//	                   AND st.tag = ANY($10))
//	   ORDER BY create_time DESC, id DESC
//	   LIMIT $11 OFFSET $12
//
//	With bound values:
//	  $1: [1, 2] (ACTIVE, IN_PROGRESS enum values)
//	  $2: [1] (EMERGENCY enum value)
//	  $3: [1, 2] (priority values)
//	  $4: 2023-01-01T00:00:00Z (create_time from)
//	  $5: 2023-12-31T23:59:59Z (create_time to)
//	  $6: 37.0 (min latitude)
//	  $7: 38.0 (max latitude)
//	  $8: -123.0 (min longitude)
//	  $9: -122.0 (max longitude)
//	  $10: ["urgent", "fire"] (tags)
//	  $11: 20 (limit)
//	  $12: 0 (offset)
func (repository *PostgresSituationRepository) SearchSituations(
	requestCtx context.Context,
	dbTx *sql.Tx,
	searchReq *situations.SearchSituationsRequest,
) (*situations.SearchSituationsResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(requestCtx, "SituationRepository.SearchSituations")
	defer finishSpan()

	span.SetTag("search.query", searchReq.Query)
	span.SetTag("page_size", fmt.Sprintf("%d", searchReq.PageSize))
	span.SetTag("order_by", searchReq.OrderBy.String())

	return database.WithSession(repository.database, spanContext, dbTx, func(activeTx *sql.Tx) (*situations.SearchSituationsResponse, error) {
		/* ─────────────────────────────────────────────────────────────
		   0. Helpers
		   ───────────────────────────────────────────────────────────── */

		// Valid field names whitelist for search fields and field queries
		validSearchFields := map[string]bool{
			"id":            true,
			"title":         true,
			"description":   true,
			"contact_email": true,
			"reporter_name": true,
			"address":       true,
			"contact_no":    true,
			"tags":          true,
		}

		// Validate field name against whitelist
		isValidField := func(fieldName string) bool {
			return validSearchFields[fieldName]
		}

		// placeholder() appends a value to `boundValues` and returns "$N".
		placeholder := func(boundValues *[]interface{}, value interface{}) string {
			*boundValues = append(*boundValues, value)
			return "$" + strconv.Itoa(len(*boundValues))
		}

		// sqlDirectionText returns "ASC" or "DESC".
		sqlDirectionText := func(isAscending bool) string {
			if isAscending {
				return "ASC"
			}
			return "DESC"
		}

		/* ─────────────────────────────────────────────────────────────
		   1. SELECT clause and dynamic WHERE fragments
		   ───────────────────────────────────────────────────────────── */

		const selectClause = `
			SELECT id, priority, title, type, resource_type, description, status,
				   reporter_id, create_time, update_time, due_time, resolved_time,
				   incident_time, trigger_source, reporter_name, contact_no,
				   contact_email, address, latitude, longitude, address_update_time,
				   automation_enabled, additional_info_json::text
			  FROM situations
		`

		var (
			whereFragments []string      // collected predicates
			boundValues    []interface{} // positional parameters
		)

		// Track search terms for highlighting later
		searchTerms := make(map[string][]string) // field -> list of search terms

		// 1A. Pattern matching search across all searchable fields or field-scoped search
		if searchText := strings.TrimSpace(searchReq.Query); searchText != "" {
			ilikePattern := "%" + searchText + "%"
			lowerIlikePattern := strings.ToLower(ilikePattern)

			// Define all searchable fields
			searchableFields := []string{"title", "description", "contact_email", "reporter_name", "address", "contact_no", "tags", "id"}

			// Use all fields if SearchFields is empty, otherwise use specified fields
			fieldsToSearch := searchableFields
			if len(searchReq.SearchFields) > 0 {
				// Filter out invalid fields from search fields
				fieldsToSearch = []string{}
				for _, fieldName := range searchReq.SearchFields {
					if isValidField(fieldName) {
						fieldsToSearch = append(fieldsToSearch, fieldName)
					}
				}
				// If all fields were invalid, fall back to default searchable fields
				if len(fieldsToSearch) == 0 {
					fieldsToSearch = searchableFields
				}
			}

			// Create a slice to hold the individual field search conditions
			fieldConditions := []string{}

			for _, columnName := range fieldsToSearch {
				// Add this search term to highlight tracking
				searchTerms[columnName] = append(searchTerms[columnName], searchText)

				switch columnName {
				case "id":
					// ID field can be an exact match or a LIKE pattern
					fieldConditions = append(fieldConditions,
						fmt.Sprintf(`id = %s OR id LIKE %s`,
							placeholder(&boundValues, searchText),
							placeholder(&boundValues, lowerIlikePattern)))
				case "title", "contact_email":
					// These fields have trigram indexes on lower(column_name),
					// so lower() is used here to match the index definition.
					fieldConditions = append(fieldConditions,
						fmt.Sprintf(`lower(%s) ILIKE %s`,
							pq.QuoteIdentifier(columnName),
							placeholder(&boundValues, lowerIlikePattern)))
				case "description", "reporter_name", "address", "contact_no":
					// reporter_name, address, contact_no have trigram indexes directly on the column.
					// description currently does not have a dedicated trigram index.
					// Using lower() on both column and pattern ensures true case-insensitivity
					// and consistency, and prepares for potential future index changes (e.g., on lower(description)).
					fieldConditions = append(fieldConditions,
						fmt.Sprintf(`lower(%s) ILIKE %s`,
							pq.QuoteIdentifier(columnName),
							placeholder(&boundValues, lowerIlikePattern)))
				case "tags":
					// The situation_tags.tag column has a trigram index on lower(tag).
					fieldConditions = append(fieldConditions,
						fmt.Sprintf(`EXISTS (SELECT 1 FROM situation_tags st
					                 WHERE st.situation_id = situations.id
					                   AND lower(st.tag) ILIKE %s)`,
							placeholder(&boundValues, lowerIlikePattern)))
				}
			}

			// Combine field conditions with OR between them and add to where fragments
			if len(fieldConditions) > 0 {
				whereFragments = append(whereFragments,
					fmt.Sprintf(`(%s)`, strings.Join(fieldConditions, " OR ")))
			}
		}

		// 1B. Field-specific queries for targeted searching
		for _, fieldQuery := range searchReq.FieldQueries {
			if queryText := strings.TrimSpace(fieldQuery.Query); queryText != "" {
				columnName := fieldQuery.Field

				// Skip invalid field names
				if !isValidField(columnName) {
					continue
				}

				ilikePattern := "%" + queryText + "%"
				lowerIlikePattern := strings.ToLower(ilikePattern)

				// Add this search term to highlight tracking
				searchTerms[columnName] = append(searchTerms[columnName], queryText)

				switch columnName {
				case "id":
					whereFragments = append(whereFragments,
						fmt.Sprintf(`(id = %s OR id LIKE %s)`,
							placeholder(&boundValues, queryText),
							placeholder(&boundValues, lowerIlikePattern)))
				case "title", "contact_email":
					whereFragments = append(whereFragments,
						fmt.Sprintf(`lower(%s) ILIKE %s`,
							pq.QuoteIdentifier(columnName),
							placeholder(&boundValues, lowerIlikePattern)))
				case "description", "reporter_name", "address", "contact_no":
					whereFragments = append(whereFragments,
						fmt.Sprintf(`lower(%s) ILIKE %s`,
							pq.QuoteIdentifier(columnName),
							placeholder(&boundValues, lowerIlikePattern)))
				case "tags":
					whereFragments = append(whereFragments,
						fmt.Sprintf(`EXISTS (SELECT 1 FROM situation_tags st
									 WHERE st.situation_id = situations.id
									   AND lower(st.tag) ILIKE %s)`,
							placeholder(&boundValues, lowerIlikePattern)))
				}
			}
		}

		// 1C. Exact-value filters (status / type / trigger source / priority / tags)
		addEnumArrayFilter := func(columnName string, enumValues []int32) {
			if len(enumValues) > 0 {
				whereFragments = append(whereFragments,
					fmt.Sprintf("%s = ANY(%s)", columnName,
						placeholder(&boundValues, pq.Array(enumValues))))
			}
		}
		addEnumArrayFilter("status", enumSliceToInt32(searchReq.Status))
		addEnumArrayFilter("type", enumSliceToInt32(searchReq.Type))
		addEnumArrayFilter("trigger_source", enumSliceToInt32(searchReq.TriggerSource))

		if len(searchReq.Priority) > 0 {
			whereFragments = append(whereFragments,
				fmt.Sprintf("priority = ANY(%s)",
					placeholder(&boundValues, pq.Array(searchReq.Priority))))
		}
		if len(searchReq.Tags) > 0 {
			whereFragments = append(whereFragments,
				fmt.Sprintf(`EXISTS (SELECT 1 FROM situation_tags st
			                 WHERE st.situation_id = situations.id
			                   AND st.tag = ANY(%s))`,
					placeholder(&boundValues, pq.Array(searchReq.Tags))))
		}

		// 1D. Date-range filters (inclusive)
		addDateRangeFilter := func(columnName string, dateRange *situations.DateRange) {
			if dateRange == nil {
				return
			}
			if dateRange.From != "" {
				fromTime, err := commonUtils.ISO8601StringToTime(dateRange.From)
				if err != nil {
					// Log the error and skip this part of the filter.
					// Consider using a proper logger if available in your project.
					fmt.Printf("Warning: Error parsing 'From' date for %s: %v. Skipping filter.\n", columnName, err)
				} else {
					whereFragments = append(whereFragments,
						fmt.Sprintf("%s >= %s",
							columnName, placeholder(&boundValues, fromTime)))
				}
			}
			if dateRange.To != "" {
				toTime, err := commonUtils.ISO8601StringToTime(dateRange.To)
				if err != nil {
					// Log the error and skip this part of the filter.
					fmt.Printf("Warning: Error parsing 'To' date for %s: %v. Skipping filter.\n", columnName, err)
				} else {
					whereFragments = append(whereFragments,
						fmt.Sprintf("%s <= %s",
							columnName, placeholder(&boundValues, toTime)))
				}
			}
		}
		addDateRangeFilter("create_time", searchReq.CreateTime)
		addDateRangeFilter("update_time", searchReq.UpdateTime)
		addDateRangeFilter("incident_time", searchReq.IncidentTime)
		addDateRangeFilter("resolved_time", searchReq.ResolvedTime)
		addDateRangeFilter("due_time", searchReq.DueTime)

		// 1E. Geo bounding-box comparisons
		// A geo-filter is considered active if any of the four bounding box parameters are non-zero.
		// This allows 0.0 to be a valid coordinate for any specific bound if a geo-filter is active.
		geoFilterActive := searchReq.MinLatitude != 0 ||
			searchReq.MaxLatitude != 0 ||
			searchReq.MinLongitude != 0 ||
			searchReq.MaxLongitude != 0

		if geoFilterActive {
			// If a geo-filter is active, all four boundary conditions are added to the query,
			// using the values directly from the request. This allows 0.0 to be used as a
			// valid coordinate as part of the bounding box.
			whereFragments = append(whereFragments,
				fmt.Sprintf("latitude >= %s",
					placeholder(&boundValues, searchReq.MinLatitude)))
			whereFragments = append(whereFragments,
				fmt.Sprintf("latitude <= %s",
					placeholder(&boundValues, searchReq.MaxLatitude)))
			whereFragments = append(whereFragments,
				fmt.Sprintf("longitude >= %s",
					placeholder(&boundValues, searchReq.MinLongitude)))
			whereFragments = append(whereFragments,
				fmt.Sprintf("longitude <= %s",
					placeholder(&boundValues, searchReq.MaxLongitude)))
		}

		// Combine SELECT + WHERE into one SQL builder
		var sqlBuilder strings.Builder
		sqlBuilder.WriteString(selectClause)
		if len(whereFragments) > 0 {
			sqlBuilder.WriteString(" WHERE ")
			sqlBuilder.WriteString(strings.Join(whereFragments, " AND "))
		}

		// Get total count before pagination
		countSQL := "SELECT COUNT(*) FROM situations"
		if len(whereFragments) > 0 {
			countSQL += " WHERE " + strings.Join(whereFragments, " AND ")
		}
		var totalResults int
		if err := activeTx.QueryRowContext(spanContext, countSQL, boundValues...).Scan(&totalResults); err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to get total count for search")
			return nil, err
		}

		/* ─────────────────────────────────────────────────────────────
		   2. Safe ORDER BY assembly
		   ───────────────────────────────────────────────────────────── */
		switch searchReq.OrderBy {
		case situations.SearchOrderBy_SEARCH_ORDER_BY_RELEVANCE,
			situations.SearchOrderBy_SEARCH_ORDER_BY_UNSPECIFIED:

			// Always use create_time and id for ordering since search_doc is no longer available
			sqlBuilder.WriteString(` ORDER BY create_time ` +
				sqlDirectionText(searchReq.Ascending) + `, id ` + sqlDirectionText(searchReq.Ascending))

		case situations.SearchOrderBy_SEARCH_ORDER_BY_CREATE_TIME:
			sqlBuilder.WriteString(` ORDER BY create_time ` +
				sqlDirectionText(searchReq.Ascending) + `, id ` + sqlDirectionText(searchReq.Ascending))

		case situations.SearchOrderBy_SEARCH_ORDER_BY_UPDATE_TIME:
			sqlBuilder.WriteString(` ORDER BY update_time ` +
				sqlDirectionText(searchReq.Ascending) + `, id ` + sqlDirectionText(searchReq.Ascending))

		case situations.SearchOrderBy_SEARCH_ORDER_BY_PRIORITY:
			sqlBuilder.WriteString(` ORDER BY priority ` +
				sqlDirectionText(searchReq.Ascending) + `, id ` + sqlDirectionText(searchReq.Ascending))
		}

		/* ─────────────────────────────────────────────────────────────
		   3. LIMIT / OFFSET pagination
		   ───────────────────────────────────────────────────────────── */
		pageLimit := int(searchReq.PageSize)
		if pageLimit <= 0 || pageLimit > 500 {
			pageLimit = 50
		}
		pageOffset := 0
		if searchReq.PageToken != "" {
			if parsedOffset, parseErr := strconv.Atoi(searchReq.PageToken); parseErr == nil {
				pageOffset = parsedOffset
			}
		}
		sqlBuilder.WriteString(
			fmt.Sprintf(" LIMIT %s OFFSET %s",
				placeholder(&boundValues, pageLimit),
				placeholder(&boundValues, pageOffset)),
		)

		finalSQL := sqlBuilder.String()

		/* ─────────────────────────────────────────────────────────────
		   4. Execute SQL & scan "core" columns
		   ───────────────────────────────────────────────────────────── */
		sqlRows, sqlError := activeTx.QueryContext(spanContext, finalSQL, boundValues...)
		if sqlError != nil {
			herosentry.CaptureException(spanContext, sqlError, herosentry.ErrorTypeDatabase, "Failed to execute search query")
			return nil, sqlError
		}
		defer sqlRows.Close()

		// scanSituationRows returns:
		//   • orderedSituations  – []*Situation for response order
		//   • situationIDs       – []string for bulk hydration
		//   • situationByIDMap   – map[id]*Situation for O(1) lookup
		orderedSituations, situationIDs, situationByIDMap, scanErr :=
			scanSituationRows(sqlRows)
		if scanErr != nil {
			herosentry.CaptureException(spanContext, scanErr, herosentry.ErrorTypeDatabase, "Failed to scan search results")
			return nil, scanErr
		}

		/* ─────────────────────────────────────────────────────────────
		   5. Hydrate repeated fields in bulk (updates, tags, media, …)
		   ───────────────────────────────────────────────────────────── */
		if len(situationIDs) > 0 {
			if hydrateErr := hydrateRepeatedFields(spanContext, activeTx, repository.database, situationByIDMap, situationIDs); hydrateErr != nil {
				herosentry.CaptureException(spanContext, hydrateErr, herosentry.ErrorTypeDatabase, "Failed to hydrate repeated fields")
				return nil, hydrateErr
			}
		}

		/* ─────────────────────────────────────────────────────────────
		   6. Generate highlights for matched terms in results
		   ───────────────────────────────────────────────────────────── */
		highlights := make(map[string]*situations.HighlightResult)

		// Only generate highlights if there are search terms
		if len(searchTerms) > 0 {
			// For each situation in the results
			for _, situation := range orderedSituations {
				// For each field that was searched
				for field, terms := range searchTerms {
					// Skip fields that don't exist in the situation
					var fieldValue string
					switch field {
					case "title":
						fieldValue = situation.Title
					case "description":
						fieldValue = situation.Description
					case "contact_email":
						fieldValue = situation.ContactEmail
					case "reporter_name":
						fieldValue = situation.ReporterName
					case "address":
						fieldValue = situation.Address
					case "contact_no":
						fieldValue = situation.ContactNo
					case "id":
						fieldValue = situation.Id
					case "tags":
						fieldValue = strings.Join(situation.Tags, ", ")
					default:
						continue
					}

					// Skip empty fields
					if fieldValue == "" {
						continue
					}

					// Check if any term matches this field
					for _, term := range terms {
						if strings.Contains(strings.ToLower(fieldValue), strings.ToLower(term)) {
							// Create highlight result if it doesn't exist for this situation
							if _, exists := highlights[situation.Id]; !exists {
								highlights[situation.Id] = &situations.HighlightResult{
									Field:     field,
									Fragments: []string{},
								}
							}

							// Only add a fragment if the field matches the highlight field
							if highlights[situation.Id].Field == field {
								// Generate a fragment for this match
								// Extract a context window of up to ~100 chars around the match
								termIndex := strings.Index(strings.ToLower(fieldValue), strings.ToLower(term))
								if termIndex >= 0 {
									startPos := max(0, termIndex-40)
									endPos := min(len(fieldValue), termIndex+len(term)+40)

									// Add ellipsis if we truncated
									prefix := ""
									if startPos > 0 {
										prefix = "..."
									}

									suffix := ""
									if endPos < len(fieldValue) {
										suffix = "..."
									}

									// Extract the fragment with the term
									fragment := prefix + fieldValue[startPos:endPos] + suffix

									// Add the fragment if it's not already present
									fragmentExists := false
									for _, existingFragment := range highlights[situation.Id].Fragments {
										if existingFragment == fragment {
											fragmentExists = true
											break
										}
									}

									if !fragmentExists {
										highlights[situation.Id].Fragments = append(
											highlights[situation.Id].Fragments, fragment)
									}
								}
							}

							// Only process the first term that matches - we don't need multiple
							// highlights for the same field in a situation
							break
						}
					}
				}
			}
		}

		// Compute next-page token
		nextToken := ""
		if len(orderedSituations) == pageLimit {
			nextToken = strconv.Itoa(pageOffset + pageLimit)
		}

		totalResultsInt32, err := safeInt32Conversion(totalResults)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, "Total count too large")
			return nil, fmt.Errorf("total count too large: %w", err)
		}
		return &situations.SearchSituationsResponse{
			Situations:    orderedSituations,
			NextPageToken: nextToken,
			Highlights:    highlights,
			TotalResults:  totalResultsInt32,
		}, nil
	})
}

// Helper function for min that works with Go 1.17+
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// Helper function for max that works with Go 1.17+
func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

// safeInt32Conversion safely converts an int to int32, returning an error if overflow would occur
func safeInt32Conversion(n int) (int32, error) {
	if n > math.MaxInt32 || n < math.MinInt32 {
		return 0, fmt.Errorf("integer overflow: %d cannot be represented as int32", n)
	}
	return int32(n), nil
}
