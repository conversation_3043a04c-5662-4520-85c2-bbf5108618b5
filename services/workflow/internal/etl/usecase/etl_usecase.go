package usecase

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"reflect"
	"time"

	"common/herosentry"
	etl "proto/hero/etl/v1"

	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/structpb"

	assetsRepository "workflow/internal/assets/data"
	entityRepository "workflow/internal/entity/data"
	etlRepository "workflow/internal/etl/data"
	extractionV2 "workflow/internal/etl/extraction/reports/v2"
	"workflow/internal/etl/extraction/types"
	"workflow/internal/etl/transformation/core"
	"workflow/internal/etl/transformation/formatters"
	propertyRepository "workflow/internal/property/data"
	reportsRepository "workflow/internal/reports/data"
	situationsRepository "workflow/internal/situations/data"
)

const defaultPageSize = 100

// ETLUseCase groups business-level operations for the ETL domain and
// delegates data persistence to repository implementations.
type ETLUseCase struct {
	databaseConnection   *sql.DB
	etlRepository        etlRepository.ETLRepository
	reportsRepository    reportsRepository.ReportRepository
	entityRepository     entityRepository.EntityRepository
	situationsRepository situationsRepository.SituationRepository
	assetsRepository     assetsRepository.AssetRepository
	extractor            *extractionV2.ReportDataExtractor
	mappingEngine        *core.MappingEngine
}

// NewETLUseCase constructs a fully-initialised ETLUseCase instance.
func NewETLUseCase(
	databaseConnection *sql.DB,
	etlRepository etlRepository.ETLRepository,
	reportsRepository reportsRepository.ReportRepository,
	entityRepository entityRepository.EntityRepository,
	situationsRepository situationsRepository.SituationRepository,
	assetsRepository assetsRepository.AssetRepository,
	propertyRepository propertyRepository.PropertyRepository,
) (*ETLUseCase, error) {
	ctx := context.Background()
	if databaseConnection == nil {
		err := fmt.Errorf("database connection is nil")
		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeValidation)
		return nil, err
	}
	if etlRepository == nil {
		err := fmt.Errorf("ETL repository must not be nil")
		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeValidation)
		return nil, err
	}
	if reportsRepository == nil {
		err := fmt.Errorf("reports repository must not be nil")
		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeValidation)
		return nil, err
	}
	if entityRepository == nil {
		err := fmt.Errorf("entity repository must not be nil")
		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeValidation)
		return nil, err
	}
	if situationsRepository == nil {
		err := fmt.Errorf("situations repository must not be nil")
		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeValidation)
		return nil, err
	}
	if assetsRepository == nil {
		err := fmt.Errorf("assets repository must not be nil")
		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeValidation)
		return nil, err
	}

	// Initialize typed extractor with ALL repositories for comprehensive data extraction
	extractor, err := extractionV2.NewReportDataExtractor(
		databaseConnection, reportsRepository, situationsRepository, assetsRepository, entityRepository, propertyRepository,
	)
	if err != nil {
		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeInternal)
		return nil, fmt.Errorf("failed to create typed extractor: %w", err)
	}

	// Initialize new mapping engine for configuration-driven transformations
	mappingEngine := core.NewMappingEngine()

	return &ETLUseCase{
		databaseConnection:   databaseConnection,
		etlRepository:        etlRepository,
		reportsRepository:    reportsRepository,
		entityRepository:     entityRepository,
		situationsRepository: situationsRepository,
		assetsRepository:     assetsRepository,
		extractor:            extractor,
		mappingEngine:        mappingEngine,
	}, nil
}

// executeInTx wraps a series of repository calls in a SQL transaction, ensuring
// commit on success and rollback on error or panic.
func (uc *ETLUseCase) executeInTx(ctx context.Context, transactionalWork func(transaction *sql.Tx) error) error {
	transaction, transactionErr := uc.databaseConnection.BeginTx(ctx, nil)
	if transactionErr != nil {
		herosentry.CaptureException(ctx, transactionErr, herosentry.ErrorTypeDatabase)
		return fmt.Errorf("failed to begin transaction: %w", transactionErr)
	}

	defer func() {
		if recoverErr := recover(); recoverErr != nil {
			_ = transaction.Rollback()
			panic(recoverErr)
		}
	}()

	if err := transactionalWork(transaction); err != nil {
		_ = transaction.Rollback()
		return err
	}

	if commitErr := transaction.Commit(); commitErr != nil {
		herosentry.CaptureException(ctx, commitErr, herosentry.ErrorTypeDatabase)
		return fmt.Errorf("failed to commit transaction: %w", commitErr)
	}

	return nil
}

// ProcessReports creates and starts an ETL job to process reports
func (uc *ETLUseCase) ProcessReports(ctx context.Context, request *etl.ProcessReportsRequest) (*etl.ProcessReportsResponse, error) {
	spanContext, _, finishSpan := herosentry.StartSpan(ctx, "ETLUseCase.ProcessReports")
	defer finishSpan()

	var createdJob *etl.ETLJob
	var estimatedCount int32

	err := uc.executeInTx(spanContext, func(tx *sql.Tx) error {
		// Validate request
		if err := uc.validateProcessReportsRequest(request); err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation)
			return fmt.Errorf("invalid request: %w", err)
		}

		// Create ETL job
		job := &etl.ETLJob{
			AgencyId:         request.AgencyId,
			OutputFormat:     request.OutputFormat,
			ReportIds:        request.ReportIds,
			DateFrom:         request.DateFrom,
			DateTo:           request.DateTo,
			CaseTypes:        request.CaseTypes,
			PreviewOnly:      request.PreviewOnly,
			JobName:          request.JobName,
			MaxRetries:       request.MaxRetries,
			AutoRetryEnabled: request.AutoRetryEnabled,
		}

		// Set defaults
		if job.MaxRetries == 0 {
			job.MaxRetries = 3
		}

		// Estimate report count if using date range
		if len(request.ReportIds) == 0 {
			count, err := uc.estimateReportCount(spanContext, tx, request)
			if err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
				return fmt.Errorf("failed to estimate report count: %w", err)
			}
			estimatedCount = count
		} else {
			reportCount := len(request.ReportIds)
			if reportCount > 2147483647 { // int32 max value
				estimatedCount = 2147483647
			} else {
				estimatedCount = int32(reportCount)
			}
		}

		// Create job in database
		var err error
		createdJob, err = uc.etlRepository.CreateJob(spanContext, tx, job)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return fmt.Errorf("failed to create ETL job: %w", err)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	// TODO: Start async processing of the job
	// This would be handled by a job processor/orchestrator

	return &etl.ProcessReportsResponse{
		Job:                  createdJob,
		EstimatedReportCount: estimatedCount,
	}, nil
}

// GetJob retrieves an ETL job by ID
func (uc *ETLUseCase) GetJob(ctx context.Context, request *etl.GetJobRequest) (*etl.GetJobResponse, error) {
	spanContext, _, finishSpan := herosentry.StartSpan(ctx, "ETLUseCase.GetJob")
	defer finishSpan()

	if request.JobId == "" {
		err := fmt.Errorf("job ID is required")
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation)
		return nil, err
	}

	job, err := uc.etlRepository.GetJob(spanContext, nil, request.JobId)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
		return nil, fmt.Errorf("failed to get ETL job: %w", err)
	}

	return &etl.GetJobResponse{
		Job: job,
	}, nil
}

// ListJobs lists ETL jobs with filtering and pagination
func (uc *ETLUseCase) ListJobs(ctx context.Context, request *etl.ListJobsRequest) (*etl.ListJobsResponse, error) {
	spanContext, _, finishSpan := herosentry.StartSpan(ctx, "ETLUseCase.ListJobs")
	defer finishSpan()

	pageSize := int(request.PageSize)
	if pageSize <= 0 {
		pageSize = defaultPageSize
	}

	var createdFrom, createdTo time.Time
	var err error

	if request.CreatedFrom != "" {
		createdFrom, err = time.Parse(time.RFC3339, request.CreatedFrom)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation)
			return nil, fmt.Errorf("invalid created_from date: %w", err)
		}
	}

	if request.CreatedTo != "" {
		createdTo, err = time.Parse(time.RFC3339, request.CreatedTo)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation)
			return nil, fmt.Errorf("invalid created_to date: %w", err)
		}
	}

	response, err := uc.etlRepository.ListJobs(
		spanContext, nil, pageSize, request.PageToken,
		request.AgencyId, request.Status,
		createdFrom, createdTo,
	)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
		return nil, fmt.Errorf("failed to list ETL jobs: %w", err)
	}

	return response, nil
}

// CancelJob cancels a running ETL job
func (uc *ETLUseCase) CancelJob(ctx context.Context, request *etl.CancelJobRequest) (*etl.CancelJobResponse, error) {
	spanContext, _, finishSpan := herosentry.StartSpan(ctx, "ETLUseCase.CancelJob")
	defer finishSpan()

	if request.JobId == "" {
		err := fmt.Errorf("job ID is required")
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation)
		return nil, err
	}

	var cancelledJob *etl.ETLJob

	err := uc.executeInTx(spanContext, func(tx *sql.Tx) error {
		// Get current job to check status
		job, err := uc.etlRepository.GetJob(spanContext, tx, request.JobId)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return fmt.Errorf("failed to get job: %w", err)
		}

		// Check if job can be cancelled
		if job.Status == etl.JobStatus_JOB_STATUS_COMPLETED ||
			job.Status == etl.JobStatus_JOB_STATUS_FAILED ||
			job.Status == etl.JobStatus_JOB_STATUS_CANCELLED {
			err := fmt.Errorf("job cannot be cancelled in status: %s", job.Status)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation)
			return err
		}

		// Cancel the job
		cancelledJob, err = uc.etlRepository.CancelJob(spanContext, tx, request.JobId)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return fmt.Errorf("failed to cancel job: %w", err)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return &etl.CancelJobResponse{
		Job: cancelledJob,
	}, nil
}

// RetryJob retries a failed ETL job
func (uc *ETLUseCase) RetryJob(ctx context.Context, request *etl.RetryJobRequest) (*etl.RetryJobResponse, error) {
	spanContext, _, finishSpan := herosentry.StartSpan(ctx, "ETLUseCase.RetryJob")
	defer finishSpan()

	if request.JobId == "" {
		err := fmt.Errorf("job ID is required")
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation)
		return nil, err
	}

	var retriedJob *etl.ETLJob
	var retryScheduled bool
	var retryReason string

	err := uc.executeInTx(spanContext, func(tx *sql.Tx) error {
		// Get current job
		job, err := uc.etlRepository.GetJob(spanContext, tx, request.JobId)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return fmt.Errorf("failed to get job: %w", err)
		}

		// Check if job can be retried
		if job.Status != etl.JobStatus_JOB_STATUS_FAILED {
			retryReason = fmt.Sprintf("job is not in FAILED status: %s", job.Status)
			retriedJob = job
			return nil
		}

		// Check retry limits unless forced
		if !request.ForceRetry && job.RetryCount >= job.MaxRetries {
			retryReason = fmt.Sprintf("job exceeded max retries (%d/%d)", job.RetryCount, job.MaxRetries)
			retriedJob = job
			return nil
		}

		// Update max retries if overridden
		if request.OverrideMaxRetries > 0 {
			job.MaxRetries = request.OverrideMaxRetries
		}

		// Reset job for retry
		job.Status = etl.JobStatus_JOB_STATUS_PENDING
		job.ErrorMessage = ""
		job.CompletedAt = ""

		// Increment retry count
		err = uc.etlRepository.IncrementRetryCount(spanContext, tx, request.JobId)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return fmt.Errorf("failed to increment retry count: %w", err)
		}

		// Update job
		retriedJob, err = uc.etlRepository.UpdateJob(spanContext, tx, job)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return fmt.Errorf("failed to update job for retry: %w", err)
		}

		retryScheduled = true
		return nil
	})

	if err != nil {
		return nil, err
	}

	// TODO: Trigger async retry processing

	return &etl.RetryJobResponse{
		Job:            retriedJob,
		RetryScheduled: retryScheduled,
		RetryReason:    retryReason,
	}, nil
}

// DownloadJobContent returns the generated content from a completed job
func (uc *ETLUseCase) DownloadJobContent(ctx context.Context, request *etl.DownloadJobContentRequest) (*etl.DownloadJobContentResponse, error) {
	spanContext, _, finishSpan := herosentry.StartSpan(ctx, "ETLUseCase.DownloadJobContent")
	defer finishSpan()

	if request.JobId == "" {
		err := fmt.Errorf("job ID is required")
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation)
		return nil, err
	}

	job, err := uc.etlRepository.GetJob(spanContext, nil, request.JobId)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
		return nil, fmt.Errorf("failed to get job: %w", err)
	}

	if len(job.GeneratedContent) == 0 {
		err := fmt.Errorf("job has no generated content")
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeNotFound)
		return nil, err
	}

	// Generate filename based on job details
	filename := uc.generateFilename(job)

	return &etl.DownloadJobContentResponse{
		Content:     job.GeneratedContent,
		ContentType: job.ContentType,
		Filename:    filename,
		SizeBytes:   job.ContentSizeBytes,
	}, nil
}

// ListAgencies returns the list of available agencies
func (uc *ETLUseCase) ListAgencies(ctx context.Context, request *etl.ListAgenciesRequest) (*etl.ListAgenciesResponse, error) {
	spanContext, _, finishSpan := herosentry.StartSpan(ctx, "ETLUseCase.ListAgencies")
	defer finishSpan()

	agencies, err := uc.etlRepository.ListAgencies(spanContext, nil)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
		return nil, fmt.Errorf("failed to list agencies: %w", err)
	}

	return &etl.ListAgenciesResponse{
		Agencies: agencies,
	}, nil
}

// GetJobStats returns job statistics
func (uc *ETLUseCase) GetJobStats(ctx context.Context, request *etl.GetJobStatsRequest) (*etl.GetJobStatsResponse, error) {
	spanContext, _, finishSpan := herosentry.StartSpan(ctx, "ETLUseCase.GetJobStats")
	defer finishSpan()

	var dateFrom, dateTo time.Time
	var err error

	if request.DateFrom != "" {
		dateFrom, err = time.Parse(time.RFC3339, request.DateFrom)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation)
			return nil, fmt.Errorf("invalid date_from: %w", err)
		}
	}

	if request.DateTo != "" {
		dateTo, err = time.Parse(time.RFC3339, request.DateTo)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation)
			return nil, fmt.Errorf("invalid date_to: %w", err)
		}
	}

	stats, err := uc.etlRepository.GetJobStats(spanContext, nil, request.AgencyId, request.OutputFormat, dateFrom, dateTo)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
		return nil, fmt.Errorf("failed to get job stats: %w", err)
	}

	return stats, nil
}

// ExtractReportData extracts raw report data without transformation
func (uc *ETLUseCase) ExtractReportData(ctx context.Context, request *etl.ExtractReportDataRequest) (*etl.ExtractReportDataResponse, error) {
	spanContext, _, finishSpan := herosentry.StartSpan(ctx, "ETLUseCase.ExtractReportData")
	defer finishSpan()

	if request.ReportId == "" {
		err := fmt.Errorf("report ID is required")
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation)
		return nil, err
	}

	var extractionResult *types.ExtractionResult
	var warnings []string

	err := uc.executeInTx(spanContext, func(tx *sql.Tx) error {
		// Extract report data using the typed extractor
		var err error
		extractionResult, err = uc.extractor.Extract(spanContext, request.ReportId)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal)
			return fmt.Errorf("failed to extract report data: %w", err)
		}

		return nil
	})

	if err != nil {
		return &etl.ExtractReportDataResponse{
			ExtractedData:  nil,
			ExtractionTime: time.Now().Format(time.RFC3339),
			Warnings:       []string{fmt.Sprintf("Extraction failed: %v", err)},
			Success:        false,
			ErrorMessage:   err.Error(),
			DataSetsCount:  0,
			DataSetNames:   []string{},
		}, nil
	}

	// Convert extracted data to JSON with enum names instead of numbers
	jsonData, err := marshalWithEnumNames(extractionResult.Data)
	if err != nil {
		return &etl.ExtractReportDataResponse{
			ExtractedData:  nil,
			ExtractionTime: time.Now().Format(time.RFC3339),
			Warnings:       []string{"Failed to serialize extracted data to JSON"},
			Success:        false,
			ErrorMessage:   fmt.Sprintf("JSON serialization failed: %v", err),
			DataSetsCount:  0,
			DataSetNames:   []string{},
		}, nil
	}

	// Convert JSON back to map[string]interface{} for protobuf Struct (this converts time.Time to strings)
	var extractedMap map[string]interface{}
	if err := json.Unmarshal(jsonData, &extractedMap); err != nil {
		return &etl.ExtractReportDataResponse{
			ExtractedData:  nil,
			ExtractionTime: time.Now().Format(time.RFC3339),
			Warnings:       []string{"Failed to convert JSON to map"},
			Success:        false,
			ErrorMessage:   fmt.Sprintf("JSON to map conversion failed: %v", err),
			DataSetsCount:  0,
			DataSetNames:   []string{},
		}, nil
	}

	// Convert to protobuf Struct
	extractedStruct, err := structpb.NewStruct(extractedMap)
	if err != nil {
		return &etl.ExtractReportDataResponse{
			ExtractedData:  nil,
			ExtractionTime: time.Now().Format(time.RFC3339),
			Warnings:       []string{"Failed to convert extracted data to protobuf struct"},
			Success:        false,
			ErrorMessage:   fmt.Sprintf("Struct conversion failed: %v", err),
			DataSetsCount:  0,
			DataSetNames:   []string{},
		}, nil
	}

	// Collect data set names from the typed extraction result
	dataSetInfo := extractionResult.Data.GetDataSetInfo()
	var dataSetNames []string
	for _, info := range dataSetInfo {
		dataSetNames = append(dataSetNames, info.Name)
	}

	// Convert warnings from extraction result
	for _, warning := range extractionResult.Warnings {
		warnings = append(warnings, fmt.Sprintf("%s: %s", warning.Code, warning.Message))
	}

	return &etl.ExtractReportDataResponse{
		ExtractedData:  extractedStruct,
		ExtractionTime: extractionResult.Data.ExtractedAt.Format(time.RFC3339),
		Warnings:       warnings,
		Success:        extractionResult.Success,
		ErrorMessage:   "",
		DataSetsCount: func() int32 {
			count := len(dataSetInfo)
			if count > 2147483647 { // int32 max value
				return 2147483647
			}
			return int32(count)
		}(),
		DataSetNames: dataSetNames,
	}, nil
}

// TestReportMapping tests mapping configuration transformation without template formatting
func (uc *ETLUseCase) TestReportMapping(ctx context.Context, request *etl.TestReportMappingRequest) (*etl.TestReportMappingResponse, error) {
	spanContext, _, finishSpan := herosentry.StartSpan(ctx, "ETLUseCase.TestReportMapping")
	defer finishSpan()

	if request.ReportId == "" {
		err := fmt.Errorf("report ID is required")
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation)
		return nil, err
	}
	if request.OutputFormat == etl.OutputFormat_OUTPUT_FORMAT_UNSPECIFIED {
		err := fmt.Errorf("output format is required")
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation)
		return nil, err
	}

	var mappedData *structpb.Struct
	var warnings []string
	var configPath string
	success := true

	err := uc.executeInTx(spanContext, func(tx *sql.Tx) error {
		// Extract report data using typed extractor
		extractionResult, err := uc.extractor.Extract(spanContext, request.ReportId)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal)
			return fmt.Errorf("failed to extract report data: %w", err)
		}

		// Convert typed extraction data to transformation input format
		transformationData, err := convertTypedToTransformationData(spanContext, extractionResult.Data)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal)
			return fmt.Errorf("failed to convert typed data to transformation format: %w", err)
		}

		// Check if custom mapping config is provided
		var result *core.TransformationResult
		if request.MappingConfigJson != "" {
			// Use custom mapping configuration
			configPath = "custom-config"
			result, err = uc.mappingEngine.TransformWithConfigAndWarnings(transformationData, request.MappingConfigJson)
			if err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal)
				return fmt.Errorf("failed to transform report with custom mapping configuration: %w", err)
			}
		} else {
			// Apply mapping configuration based on output format/standard
			switch request.OutputFormat {
			case etl.OutputFormat_OUTPUT_FORMAT_NIBRS_XML:
				configPath = "internal/etl/templates/nibrs/reports/v2/nibrs_mapping_config.json"
			default:
				err := fmt.Errorf("unsupported output format: %s", request.OutputFormat)
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation)
				return err
			}

			// Execute mapping transformation with warning collection
			result, err = uc.mappingEngine.TransformWithConfigFileAndWarnings(transformationData, configPath)
			if err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal)
				return fmt.Errorf("failed to transform report with %s mapping configuration: %w", request.OutputFormat.String(), err)
			}
		}

		// Collect any warnings from transformation
		if result != nil {
			warnings = append(warnings, result.Warnings...)

			// Convert result to structpb.Struct for proto response
			mappedStruct, err := structpb.NewStruct(result.Data)
			if err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal)
				return fmt.Errorf("failed to convert mapped data to struct: %w", err)
			}
			mappedData = mappedStruct

			// Set success based on transformation result
			success = result.Success
		}

		return nil
	})

	if err != nil {
		success = false
		warnings = append(warnings, fmt.Sprintf("Mapping transformation failed: %v", err))
		// Return error response with details
		emptyStruct, _ := structpb.NewStruct(map[string]interface{}{})
		return &etl.TestReportMappingResponse{
			MappedData:         emptyStruct,
			ContentType:        "application/json",
			MappingConfigUsed:  configPath,
			TransformationTime: time.Now().Format(time.RFC3339),
			Success:            false,
			Warnings:           warnings,
			ErrorMessage:       err.Error(),
		}, nil
	}

	return &etl.TestReportMappingResponse{
		MappedData:         mappedData,
		ContentType:        "application/json",
		MappingConfigUsed:  configPath,
		TransformationTime: time.Now().Format(time.RFC3339),
		Success:            success,
		Warnings:           warnings,
	}, nil
}

// TestReportTransformation performs complete transformation pipeline - extract, map, and format
func (uc *ETLUseCase) TestReportTransformation(ctx context.Context, request *etl.TestReportTransformationRequest) (*etl.TestReportTransformationResponse, error) {
	spanContext, _, finishSpan := herosentry.StartSpan(ctx, "ETLUseCase.TestReportTransformation")
	defer finishSpan()

	if request.ReportId == "" {
		err := fmt.Errorf("report ID is required")
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation)
		return nil, err
	}
	if request.OutputFormat == etl.OutputFormat_OUTPUT_FORMAT_UNSPECIFIED {
		err := fmt.Errorf("output format is required")
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation)
		return nil, err
	}

	// Step 1: Call TestReportMapping to get transformed data
	mappingRequest := &etl.TestReportMappingRequest{
		ReportId:          request.ReportId,
		OutputFormat:      request.OutputFormat,
		MappingConfigJson: request.MappingConfigJson, // Pass through custom mapping config
	}

	mappingResponse, err := uc.TestReportMapping(spanContext, mappingRequest)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal)
		return nil, fmt.Errorf("mapping transformation failed: %w", err)
	}

	// Check if mapping was successful
	if !mappingResponse.Success {
		return &etl.TestReportTransformationResponse{
			TransformedContent: nil,
			ContentType:        "application/xml", // Default for errors
			TransformationTime: time.Now().Format(time.RFC3339),
			Success:            false,
			Warnings:           mappingResponse.Warnings,
			ErrorMessage:       mappingResponse.ErrorMessage,
			ReadableContent:    "",                                // Empty for errors
			MappingConfigUsed:  mappingResponse.MappingConfigUsed, // Pass through even on error
			TemplateUsed:       "",                                // No template used if mapping failed
		}, nil
	}

	// Step 2: Format the mapped data using templates
	var formatter formatters.Formatter
	var templatePath string

	if request.TemplateContent != "" {
		// Use custom template content
		contentType := getContentTypeForFormat(request.OutputFormat)
		formatter, err = formatters.NewTemplateFormatterFromString(spanContext, request.TemplateContent, contentType)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal)
			return nil, fmt.Errorf("failed to create formatter from custom template: %w", err)
		}
		templatePath = "custom-template"
	} else {
		// Use default template based on output format
		factory := formatters.NewFormatterFactory("internal/etl/templates")
		formatter, err = factory.CreateFormatter(spanContext, request.OutputFormat)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal)
			return nil, fmt.Errorf("failed to create formatter: %w", err)
		}
		templatePath = formatter.TemplatePath()
	}

	// Convert protobuf Struct to map for template execution
	dataMap := mappingResponse.MappedData.AsMap()

	// Execute template formatting - all logic is in the template
	formattedContent, err := formatter.Format(spanContext, dataMap)
	if err != nil {
		// Return error response with details
		return &etl.TestReportTransformationResponse{
			TransformedContent: nil,
			ContentType:        formatter.ContentType(),
			TransformationTime: time.Now().Format(time.RFC3339),
			Success:            false,
			Warnings:           mappingResponse.Warnings,
			ErrorMessage:       fmt.Sprintf("Template formatting failed: %v", err),
			ReadableContent:    "",                                // Empty for errors
			MappingConfigUsed:  mappingResponse.MappingConfigUsed, // Pass through from mapping
			TemplateUsed:       templatePath,                      // Show which template failed
		}, nil
	}

	// Combine warnings from mapping and formatting
	warnings := mappingResponse.Warnings

	// TODO: Add validation if requested
	if request.Validate {
		warnings = append(warnings, "Validation not yet implemented")
	}

	return &etl.TestReportTransformationResponse{
		TransformedContent: formattedContent,
		ContentType:        formatter.ContentType(),
		TransformationTime: time.Now().Format(time.RFC3339),
		Success:            true,
		Warnings:           warnings,
		ReadableContent:    string(formattedContent),          // Human-readable version
		MappingConfigUsed:  mappingResponse.MappingConfigUsed, // Pass through from mapping response
		TemplateUsed:       templatePath,                      // Use the templatePath variable
	}, nil
}

// getContentTypeForFormat returns the appropriate content type for an output format
func getContentTypeForFormat(format etl.OutputFormat) string {
	switch format {
	case etl.OutputFormat_OUTPUT_FORMAT_NIBRS_XML:
		return "application/xml"
	default:
		return "application/octet-stream"
	}
}

// ValidateReports validates reports against a data standard
func (uc *ETLUseCase) ValidateReports(ctx context.Context, request *etl.ValidateReportsRequest) (*etl.ValidateReportsResponse, error) {
	// TODO: Implement actual validation logic
	return nil, nil
}

// UpdateJobProgress updates job progress counters
func (uc *ETLUseCase) UpdateJobProgress(ctx context.Context, request *etl.UpdateJobProgressRequest) (*etl.UpdateJobProgressResponse, error) {
	spanContext, _, finishSpan := herosentry.StartSpan(ctx, "ETLUseCase.UpdateJobProgress")
	defer finishSpan()

	if request.JobId == "" {
		err := fmt.Errorf("job ID is required")
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation)
		return nil, err
	}

	var updatedJob *etl.ETLJob
	var progressUpdated bool

	err := uc.executeInTx(spanContext, func(tx *sql.Tx) error {
		// Get current job to verify it exists and can be updated
		job, err := uc.etlRepository.GetJob(spanContext, tx, request.JobId)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return fmt.Errorf("failed to get job: %w", err)
		}

		// Check if job is in a state that allows progress updates
		if job.Status == etl.JobStatus_JOB_STATUS_COMPLETED ||
			job.Status == etl.JobStatus_JOB_STATUS_FAILED ||
			job.Status == etl.JobStatus_JOB_STATUS_CANCELLED {
			err := fmt.Errorf("cannot update progress for job in status: %s", job.Status)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation)
			return err
		}

		// Update job progress
		err = uc.etlRepository.UpdateJobProgress(spanContext, tx, request.JobId,
			request.TotalReports, request.ReportsProcessed,
			request.ReportsFailed, request.ReportsSkipped)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return fmt.Errorf("failed to update job progress: %w", err)
		}

		// Get updated job
		updatedJob, err = uc.etlRepository.GetJob(spanContext, tx, request.JobId)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return fmt.Errorf("failed to get updated job: %w", err)
		}

		progressUpdated = true
		return nil
	})

	if err != nil {
		return nil, err
	}

	return &etl.UpdateJobProgressResponse{
		Job:             updatedJob,
		ProgressUpdated: progressUpdated,
	}, nil
}

// GetAgencyStats returns detailed statistics for a specific agency
func (uc *ETLUseCase) GetAgencyStats(ctx context.Context, request *etl.GetAgencyStatsRequest) (*etl.GetAgencyStatsResponse, error) {
	spanContext, _, finishSpan := herosentry.StartSpan(ctx, "ETLUseCase.GetAgencyStats")
	defer finishSpan()

	if request.AgencyId == "" {
		err := fmt.Errorf("agency ID is required")
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation)
		return nil, err
	}

	var dateFrom, dateTo time.Time
	var err error

	if request.DateFrom != "" {
		dateFrom, err = time.Parse(time.RFC3339, request.DateFrom)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation)
			return nil, fmt.Errorf("invalid date_from: %w", err)
		}
	}

	if request.DateTo != "" {
		dateTo, err = time.Parse(time.RFC3339, request.DateTo)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation)
			return nil, fmt.Errorf("invalid date_to: %w", err)
		}
	}

	response, err := uc.etlRepository.GetAgencyStats(spanContext, nil, request.AgencyId, dateFrom, dateTo, request.IncludeJobDetails)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
		return nil, fmt.Errorf("failed to get agency stats: %w", err)
	}

	return response, nil
}

// Helper methods

// validateProcessReportsRequest validates the process reports request
func (uc *ETLUseCase) validateProcessReportsRequest(request *etl.ProcessReportsRequest) error {
	if request.AgencyId == "" {
		return fmt.Errorf("agency ID is required")
	}

	if request.OutputFormat == etl.OutputFormat_OUTPUT_FORMAT_UNSPECIFIED {
		return fmt.Errorf("output format is required")
	}

	// Must have either specific report IDs or date range
	hasReportIds := len(request.ReportIds) > 0
	hasDateRange := request.DateFrom != "" || request.DateTo != ""

	if !hasReportIds && !hasDateRange {
		return fmt.Errorf("either report IDs or date range must be specified")
	}

	if hasReportIds && hasDateRange {
		return fmt.Errorf("cannot specify both report IDs and date range")
	}

	// Validate date formats if provided
	if request.DateFrom != "" {
		if _, err := time.Parse(time.RFC3339, request.DateFrom); err != nil {
			return fmt.Errorf("invalid date_from format: %w", err)
		}
	}

	if request.DateTo != "" {
		if _, err := time.Parse(time.RFC3339, request.DateTo); err != nil {
			return fmt.Errorf("invalid date_to format: %w", err)
		}
	}

	return nil
}

// estimateReportCount estimates the number of reports for date range queries
func (uc *ETLUseCase) estimateReportCount(ctx context.Context, tx *sql.Tx, request *etl.ProcessReportsRequest) (int32, error) {
	// TODO: Implement actual report counting logic
	// For now, return a placeholder estimate
	return 100, nil
}

// generateFilename generates a filename for downloaded content
func (uc *ETLUseCase) generateFilename(job *etl.ETLJob) string {
	extension := ".xml"

	timestamp := time.Now().Format("20060102_150405")
	return fmt.Sprintf("etl_%s_%s_%s%s", job.AgencyId, job.OutputFormat.String(), timestamp, extension)
}

// marshalWithEnumNames marshals a struct containing protobuf messages with enum names instead of numbers
func marshalWithEnumNames(data interface{}) ([]byte, error) {
	// Create a copy of the data structure and convert protobuf messages to enum-friendly maps
	converted, err := convertProtobufEnums(data)
	if err != nil {
		return nil, fmt.Errorf("failed to convert protobuf enums: %w", err)
	}

	// Marshal the converted data using standard JSON
	return json.Marshal(converted)
}

// convertProtobufEnums recursively converts protobuf messages to maps with enum names
func convertProtobufEnums(data interface{}) (interface{}, error) {
	if data == nil {
		return nil, nil
	}

	value := reflect.ValueOf(data)

	// Handle pointers
	if value.Kind() == reflect.Ptr {
		if value.IsNil() {
			return nil, nil
		}
		value = value.Elem()
	}

	// Check if this is a protobuf message
	if protoMsg, ok := data.(proto.Message); ok {
		// Use protojson to marshal protobuf messages with enum names
		marshaler := protojson.MarshalOptions{
			UseProtoNames:   true,
			EmitUnpopulated: false,
			UseEnumNumbers:  false,
		}
		jsonData, err := marshaler.Marshal(protoMsg)
		if err != nil {
			return nil, err
		}

		// Unmarshal back to map[string]interface{} for consistent structure
		var result map[string]interface{}
		if err := json.Unmarshal(jsonData, &result); err != nil {
			return nil, err
		}
		return result, nil
	}

	switch value.Kind() {
	case reflect.Struct:
		// Handle regular structs by processing each field
		result := make(map[string]interface{})
		valueType := value.Type()

		for i := 0; i < value.NumField(); i++ {
			field := valueType.Field(i)
			fieldValue := value.Field(i)

			// Skip unexported fields
			if !fieldValue.CanInterface() {
				continue
			}

			// Get JSON field name
			jsonTag := field.Tag.Get("json")
			fieldName := field.Name
			if jsonTag != "" && jsonTag != "-" {
				if commaIndex := len(jsonTag); commaIndex > 0 {
					if idx := findComma(jsonTag); idx != -1 {
						fieldName = jsonTag[:idx]
					} else {
						fieldName = jsonTag
					}
				}
			}

			// Recursively convert the field value
			convertedValue, err := convertProtobufEnums(fieldValue.Interface())
			if err != nil {
				return nil, err
			}

			result[fieldName] = convertedValue
		}
		return result, nil

	case reflect.Slice:
		// Handle slices
		result := make([]interface{}, value.Len())
		for i := 0; i < value.Len(); i++ {
			convertedItem, err := convertProtobufEnums(value.Index(i).Interface())
			if err != nil {
				return nil, err
			}
			result[i] = convertedItem
		}
		return result, nil

	case reflect.Map:
		// Handle maps
		result := make(map[string]interface{})
		for _, key := range value.MapKeys() {
			keyStr := fmt.Sprintf("%v", key.Interface())
			mapValue := value.MapIndex(key)
			convertedValue, err := convertProtobufEnums(mapValue.Interface())
			if err != nil {
				return nil, err
			}
			result[keyStr] = convertedValue
		}
		return result, nil
	}

	// For basic types, return as-is
	return data, nil
}

// findComma finds the first comma in a string, returns -1 if not found
func findComma(s string) int {
	for i, r := range s {
		if r == ',' {
			return i
		}
	}
	return -1
}

// convertTypedToTransformationData converts typed extraction data to the format expected by transformation engine.
//
// This function bridges the gap between the typed extraction output and the transformation input format.
// It converts the structured ExtractedData to a map format that can be processed by path resolution
// and field mapping operations.
//
// Parameters:
//   - data: Typed extracted data from the parallel extractor
//
// Returns:
//   - map[string]interface{}: Data structure compatible with transformation engine
//   - error: Any conversion errors
func convertTypedToTransformationData(ctx context.Context, data *types.ExtractedData) (map[string]interface{}, error) {
	if data == nil {
		err := fmt.Errorf("extraction data is nil")
		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeInternal)
		return nil, err
	}

	// Convert the typed extraction result to JSON and back to map[string]interface{}
	// This ensures proper enum serialization and consistent data structure
	jsonData, err := marshalWithEnumNames(data)
	if err != nil {
		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeInternal)
		return nil, fmt.Errorf("failed to marshal extraction data: %w", err)
	}

	var result map[string]interface{}
	if err := json.Unmarshal(jsonData, &result); err != nil {
		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeInternal)
		return nil, fmt.Errorf("failed to unmarshal to transformation format: %w", err)
	}

	return result, nil
}
