package connect

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"log/slog"

	connecthelper "common/connect"
	"common/herosentry"

	"connectrpc.com/connect"

	etl "proto/hero/etl/v1"
	etlconnect "proto/hero/etl/v1/etlconnect"

	etlErrors "workflow/internal/etl/internal"
	"workflow/internal/etl/usecase"
)

// ETLServer implements all RPCs defined in ETLService.
// Each handler validates the request, invokes the corresponding
// business-logic method on ETLUseCase, logs the call, and converts
// domain errors into Connect error codes.
type ETLServer struct {
	etlconnect.UnimplementedETLServiceHandler

	etlUseCase *usecase.ETLUseCase
	logger     *slog.Logger
}

// NewETLServer constructs an ETLServer. If logger is nil, slog.Default()
// is used so that a non-nil logger is always available.
func NewETLServer(etlUseCase *usecase.ETLUseCase, logger *slog.Logger) *ETLServer {
	if logger == nil {
		logger = slog.Default()
	}
	return &ETLServer{
		etlUseCase: etlUseCase,
		logger:     logger.With("component", "ETLServer"),
	}
}

// translate domain errors → Connect errors with proper herosentry error types.
func (server *ETLServer) asConnectError(ctx context.Context, domainErr error, msg string, operation string) error {
	if domainErr == nil {
		return nil
	}

	// Handle specific ETL errors and map to appropriate herosentry error types
	var errorType herosentry.ErrorType
	var wrappedErr error

	switch {
	case errors.Is(domainErr, etlErrors.ErrJobNotFound):
		errorType = herosentry.ErrorTypeNotFound
		wrappedErr = domainErr
	case errors.Is(domainErr, etlErrors.ErrJobAlreadyCompleted):
		errorType = herosentry.ErrorTypeConflict
		wrappedErr = domainErr
	case errors.Is(domainErr, etlErrors.ErrJobCancelled):
		errorType = herosentry.ErrorTypeInternal
		wrappedErr = domainErr
	case errors.Is(domainErr, etlErrors.ErrJobMaxRetriesExceeded):
		errorType = herosentry.ErrorTypeInternal
		wrappedErr = domainErr
	case errors.Is(domainErr, etlErrors.ErrReportNotFound):
		errorType = herosentry.ErrorTypeNotFound
		wrappedErr = domainErr
	case errors.Is(domainErr, etlErrors.ErrAgencyNotFound):
		errorType = herosentry.ErrorTypeNotFound
		wrappedErr = domainErr
	case errors.Is(domainErr, etlErrors.ErrInvalidRequest):
		errorType = herosentry.ErrorTypeValidation
		wrappedErr = domainErr
	case errors.Is(domainErr, etlErrors.ErrValidationFailed):
		errorType = herosentry.ErrorTypeValidation
		wrappedErr = domainErr
	case errors.Is(domainErr, etlErrors.ErrNetworkError):
		errorType = herosentry.ErrorTypeExternal
		wrappedErr = domainErr
	case errors.Is(domainErr, etlErrors.ErrAgencyUnavailable):
		errorType = herosentry.ErrorTypeExternal
		wrappedErr = domainErr
	case errors.Is(domainErr, etlErrors.ErrResourceUnavailable):
		errorType = herosentry.ErrorTypeInternal
		wrappedErr = domainErr
	case errors.Is(domainErr, etlErrors.ErrConcurrencyLimit):
		errorType = herosentry.ErrorTypeInternal
		wrappedErr = domainErr
	case errors.Is(domainErr, sql.ErrNoRows):
		errorType = herosentry.ErrorTypeNotFound
		wrappedErr = domainErr
	default:
		errorType = herosentry.ErrorTypeInternal
		wrappedErr = fmt.Errorf("%s: %w", msg, domainErr)
	}

	return connecthelper.AsConnectError(ctx, wrappedErr, operation, errorType)
}

// ProcessReports creates and starts an ETL job to process reports
func (server *ETLServer) ProcessReports(
	ctx context.Context,
	req *connect.Request[etl.ProcessReportsRequest],
) (*connect.Response[etl.ProcessReportsResponse], error) {
	server.logger.InfoContext(ctx, "ProcessReports called",
		"agency_id", req.Msg.AgencyId,
		"output_format", req.Msg.OutputFormat,
		"report_count", len(req.Msg.ReportIds),
		"preview_only", req.Msg.PreviewOnly,
	)

	response, err := server.etlUseCase.ProcessReports(ctx, req.Msg)
	if err != nil {
		server.logger.ErrorContext(ctx, "ProcessReports failed", "error", err)
		return nil, server.asConnectError(ctx, err, "failed to process reports", "ProcessReports")
	}

	server.logger.InfoContext(ctx, "ProcessReports completed",
		"job_id", response.Job.Id,
		"estimated_report_count", response.EstimatedReportCount,
	)

	return connect.NewResponse(response), nil
}

// GetJob retrieves an ETL job by ID
func (server *ETLServer) GetJob(
	ctx context.Context,
	req *connect.Request[etl.GetJobRequest],
) (*connect.Response[etl.GetJobResponse], error) {
	server.logger.InfoContext(ctx, "GetJob called", "job_id", req.Msg.JobId)

	response, err := server.etlUseCase.GetJob(ctx, req.Msg)
	if err != nil {
		server.logger.ErrorContext(ctx, "GetJob failed", "job_id", req.Msg.JobId, "error", err)
		return nil, server.asConnectError(ctx, err, "failed to get job", "GetJob")
	}

	server.logger.InfoContext(ctx, "GetJob completed",
		"job_id", response.Job.Id,
		"status", response.Job.Status,
	)

	return connect.NewResponse(response), nil
}

// ListJobs lists ETL jobs with filtering and pagination
func (server *ETLServer) ListJobs(
	ctx context.Context,
	req *connect.Request[etl.ListJobsRequest],
) (*connect.Response[etl.ListJobsResponse], error) {
	server.logger.InfoContext(ctx, "ListJobs called",
		"agency_id", req.Msg.AgencyId,
		"status", req.Msg.Status,
		"page_size", req.Msg.PageSize,
	)

	response, err := server.etlUseCase.ListJobs(ctx, req.Msg)
	if err != nil {
		server.logger.ErrorContext(ctx, "ListJobs failed", "error", err)
		return nil, server.asConnectError(ctx, err, "failed to list jobs", "ListJobs")
	}

	server.logger.InfoContext(ctx, "ListJobs completed",
		"job_count", len(response.Jobs),
		"has_next_page", response.NextPageToken != "",
	)

	return connect.NewResponse(response), nil
}

// CancelJob cancels a running ETL job
func (server *ETLServer) CancelJob(
	ctx context.Context,
	req *connect.Request[etl.CancelJobRequest],
) (*connect.Response[etl.CancelJobResponse], error) {
	server.logger.InfoContext(ctx, "CancelJob called", "job_id", req.Msg.JobId)

	response, err := server.etlUseCase.CancelJob(ctx, req.Msg)
	if err != nil {
		server.logger.ErrorContext(ctx, "CancelJob failed", "job_id", req.Msg.JobId, "error", err)
		return nil, server.asConnectError(ctx, err, "failed to cancel job", "CancelJob")
	}

	server.logger.InfoContext(ctx, "CancelJob completed",
		"job_id", response.Job.Id,
		"status", response.Job.Status,
	)

	return connect.NewResponse(response), nil
}

// RetryJob retries a failed ETL job
func (server *ETLServer) RetryJob(
	ctx context.Context,
	req *connect.Request[etl.RetryJobRequest],
) (*connect.Response[etl.RetryJobResponse], error) {
	server.logger.InfoContext(ctx, "RetryJob called",
		"job_id", req.Msg.JobId,
		"force_retry", req.Msg.ForceRetry,
		"override_max_retries", req.Msg.OverrideMaxRetries,
	)

	response, err := server.etlUseCase.RetryJob(ctx, req.Msg)
	if err != nil {
		server.logger.ErrorContext(ctx, "RetryJob failed", "job_id", req.Msg.JobId, "error", err)
		return nil, server.asConnectError(ctx, err, "failed to retry job", "RetryJob")
	}

	server.logger.InfoContext(ctx, "RetryJob completed",
		"job_id", response.Job.Id,
		"retry_scheduled", response.RetryScheduled,
		"retry_reason", response.RetryReason,
	)

	return connect.NewResponse(response), nil
}

// DownloadJobContent returns the generated content from a completed job
func (server *ETLServer) DownloadJobContent(
	ctx context.Context,
	req *connect.Request[etl.DownloadJobContentRequest],
) (*connect.Response[etl.DownloadJobContentResponse], error) {
	server.logger.InfoContext(ctx, "DownloadJobContent called", "job_id", req.Msg.JobId)

	response, err := server.etlUseCase.DownloadJobContent(ctx, req.Msg)
	if err != nil {
		server.logger.ErrorContext(ctx, "DownloadJobContent failed", "job_id", req.Msg.JobId, "error", err)
		return nil, server.asConnectError(ctx, err, "failed to download job content", "DownloadJobContent")
	}

	server.logger.InfoContext(ctx, "DownloadJobContent completed",
		"job_id", req.Msg.JobId,
		"content_type", response.ContentType,
		"size_bytes", response.SizeBytes,
		"filename", response.Filename,
	)

	return connect.NewResponse(response), nil
}

// ListAgencies returns the list of available agencies
func (server *ETLServer) ListAgencies(
	ctx context.Context,
	req *connect.Request[etl.ListAgenciesRequest],
) (*connect.Response[etl.ListAgenciesResponse], error) {
	server.logger.InfoContext(ctx, "ListAgencies called")

	response, err := server.etlUseCase.ListAgencies(ctx, req.Msg)
	if err != nil {
		server.logger.ErrorContext(ctx, "ListAgencies failed", "error", err)
		return nil, server.asConnectError(ctx, err, "failed to list agencies", "ListAgencies")
	}

	server.logger.InfoContext(ctx, "ListAgencies completed",
		"agency_count", len(response.Agencies),
	)

	return connect.NewResponse(response), nil
}

// GetJobStats returns job statistics
func (server *ETLServer) GetJobStats(
	ctx context.Context,
	req *connect.Request[etl.GetJobStatsRequest],
) (*connect.Response[etl.GetJobStatsResponse], error) {
	server.logger.InfoContext(ctx, "GetJobStats called",
		"agency_id", req.Msg.AgencyId,
		"output_format", req.Msg.OutputFormat,
		"date_from", req.Msg.DateFrom,
		"date_to", req.Msg.DateTo,
	)

	response, err := server.etlUseCase.GetJobStats(ctx, req.Msg)
	if err != nil {
		server.logger.ErrorContext(ctx, "GetJobStats failed", "error", err)
		return nil, server.asConnectError(ctx, err, "failed to get job stats", "GetJobStats")
	}

	server.logger.InfoContext(ctx, "GetJobStats completed",
		"total_jobs", response.TotalJobs,
		"completed_jobs", response.CompletedJobs,
		"failed_jobs", response.FailedJobs,
	)

	return connect.NewResponse(response), nil
}

// ExtractReportData extracts raw report data without transformation
func (server *ETLServer) ExtractReportData(
	ctx context.Context,
	req *connect.Request[etl.ExtractReportDataRequest],
) (*connect.Response[etl.ExtractReportDataResponse], error) {
	server.logger.InfoContext(ctx, "ExtractReportData called",
		"report_id", req.Msg.ReportId,
	)

	response, err := server.etlUseCase.ExtractReportData(ctx, req.Msg)
	if err != nil {
		server.logger.ErrorContext(ctx, "ExtractReportData failed",
			"report_id", req.Msg.ReportId,
			"error", err,
		)
		return nil, server.asConnectError(ctx, err, "failed to extract report data", "ExtractReportData")
	}

	server.logger.InfoContext(ctx, "ExtractReportData completed",
		"report_id", req.Msg.ReportId,
		"success", response.Success,
		"data_sets_count", response.DataSetsCount,
	)

	return connect.NewResponse(response), nil
}

// TestReportMapping tests mapping configuration transformation without template formatting
func (server *ETLServer) TestReportMapping(
	ctx context.Context,
	req *connect.Request[etl.TestReportMappingRequest],
) (*connect.Response[etl.TestReportMappingResponse], error) {
	server.logger.InfoContext(ctx, "TestReportMapping called",
		"report_id", req.Msg.ReportId,
		"output_format", req.Msg.OutputFormat,
	)

	response, err := server.etlUseCase.TestReportMapping(ctx, req.Msg)
	if err != nil {
		server.logger.ErrorContext(ctx, "TestReportMapping failed",
			"report_id", req.Msg.ReportId,
			"error", err,
		)
		return nil, server.asConnectError(ctx, err, "failed to test report mapping", "TestReportMapping")
	}

	server.logger.InfoContext(ctx, "TestReportMapping completed",
		"report_id", req.Msg.ReportId,
		"success", response.Success,
		"config_used", response.MappingConfigUsed,
		"mapped_fields", len(response.MappedData.GetFields()),
		"warning_count", len(response.Warnings),
	)

	return connect.NewResponse(response), nil
}

// TestReportTransformation tests transformation of a single report without creating a job
func (server *ETLServer) TestReportTransformation(
	ctx context.Context,
	req *connect.Request[etl.TestReportTransformationRequest],
) (*connect.Response[etl.TestReportTransformationResponse], error) {
	server.logger.InfoContext(ctx, "TestReportTransformation called",
		"report_id", req.Msg.ReportId,
		"output_format", req.Msg.OutputFormat,
	)

	response, err := server.etlUseCase.TestReportTransformation(ctx, req.Msg)
	if err != nil {
		server.logger.ErrorContext(ctx, "TestReportTransformation failed",
			"report_id", req.Msg.ReportId,
			"error", err,
		)
		return nil, server.asConnectError(ctx, err, "failed to test report transformation", "TestReportTransformation")
	}

	server.logger.InfoContext(ctx, "TestReportTransformation completed",
		"report_id", req.Msg.ReportId,
		"success", response.Success,
		"content_size", len(response.TransformedContent),
		"warning_count", len(response.Warnings),
	)

	return connect.NewResponse(response), nil
}

// ValidateReports validates reports against a data standard
func (server *ETLServer) ValidateReports(
	ctx context.Context,
	req *connect.Request[etl.ValidateReportsRequest],
) (*connect.Response[etl.ValidateReportsResponse], error) {
	server.logger.InfoContext(ctx, "ValidateReports called",
		"report_count", len(req.Msg.ReportIds),
		"output_format", req.Msg.OutputFormat,
		"agency_id", req.Msg.AgencyId,
	)

	response, err := server.etlUseCase.ValidateReports(ctx, req.Msg)
	if err != nil {
		server.logger.ErrorContext(ctx, "ValidateReports failed", "error", err)
		return nil, server.asConnectError(ctx, err, "failed to validate reports", "ValidateReports")
	}

	server.logger.InfoContext(ctx, "ValidateReports completed",
		"valid_reports", response.ValidReports,
		"invalid_reports", response.InvalidReports,
		"all_valid", response.AllValid,
	)

	return connect.NewResponse(response), nil
}

// UpdateJobProgress updates job progress counters
func (server *ETLServer) UpdateJobProgress(
	ctx context.Context,
	req *connect.Request[etl.UpdateJobProgressRequest],
) (*connect.Response[etl.UpdateJobProgressResponse], error) {
	server.logger.InfoContext(ctx, "UpdateJobProgress called",
		"job_id", req.Msg.JobId,
		"total_reports", req.Msg.TotalReports,
		"reports_processed", req.Msg.ReportsProcessed,
		"reports_failed", req.Msg.ReportsFailed,
		"reports_skipped", req.Msg.ReportsSkipped,
	)

	response, err := server.etlUseCase.UpdateJobProgress(ctx, req.Msg)
	if err != nil {
		server.logger.ErrorContext(ctx, "UpdateJobProgress failed",
			"job_id", req.Msg.JobId,
			"error", err,
		)
		return nil, server.asConnectError(ctx, err, "failed to update job progress", "UpdateJobProgress")
	}

	server.logger.InfoContext(ctx, "UpdateJobProgress completed",
		"job_id", response.Job.Id,
		"progress_updated", response.ProgressUpdated,
		"total_reports", response.Job.TotalReports,
		"reports_processed", response.Job.ReportsProcessed,
	)

	return connect.NewResponse(response), nil
}

// GetAgencyStats returns detailed statistics for a specific agency
func (server *ETLServer) GetAgencyStats(
	ctx context.Context,
	req *connect.Request[etl.GetAgencyStatsRequest],
) (*connect.Response[etl.GetAgencyStatsResponse], error) {
	server.logger.InfoContext(ctx, "GetAgencyStats called",
		"agency_id", req.Msg.AgencyId,
		"date_from", req.Msg.DateFrom,
		"date_to", req.Msg.DateTo,
		"include_job_details", req.Msg.IncludeJobDetails,
	)

	response, err := server.etlUseCase.GetAgencyStats(ctx, req.Msg)
	if err != nil {
		server.logger.ErrorContext(ctx, "GetAgencyStats failed",
			"agency_id", req.Msg.AgencyId,
			"error", err,
		)
		return nil, server.asConnectError(ctx, err, "failed to get agency stats", "GetAgencyStats")
	}

	server.logger.InfoContext(ctx, "GetAgencyStats completed",
		"agency_id", response.AgencyStats.AgencyId,
		"job_count", response.AgencyStats.JobCount,
		"report_count", response.AgencyStats.ReportCount,
		"recent_jobs_count", len(response.RecentJobs),
	)

	return connect.NewResponse(response), nil
}
