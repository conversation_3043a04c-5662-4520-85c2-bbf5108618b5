package tests

import (
	"context"
	"strings"
	"testing"
	"time"

	"workflow/internal/etl/transformation/formatters"
)

// TestTemplateFormatter_Creation tests the creation of template formatters.
func TestTemplateFormatter_Creation(t *testing.T) {
	tests := []struct {
		name        string
		template    string
		contentType string
		wantErr     bool
		errContains string
	}{
		{
			name: "Valid template creation",
			template: `<?xml version="1.0" encoding="UTF-8"?>
<Report>{{.ID}}</Report>`,
			contentType: "application/xml",
			wantErr:     false,
		},
		{
			name: "Invalid template syntax",
			template: `<?xml version="1.0" encoding="UTF-8"?>
<Report>{{.ID</Report>`,
			contentType: "application/xml",
			wantErr:     true,
			errContains: "bad character",
		},
		{
			name:        "Empty template",
			template:    "",
			contentType: "application/xml",
			wantErr:     false, // Empty templates are valid
		},
		{
			name: "Template with complex expressions",
			template: `{{- range .Items}}
{{.Name | upper}} - {{.Value | default "N/A"}}
{{- end}}`,
			contentType: "text/plain",
			wantErr:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create test template
			templatePath := createTestTemplate(t, tt.template)

			// Create formatter
			formatter, err := formatters.NewTemplateFormatter(context.Background(), templatePath, tt.contentType)

			if tt.wantErr {
				if err == nil {
					t.Errorf("Expected error but got none")
				} else if tt.errContains != "" && !strings.Contains(err.Error(), tt.errContains) {
					t.Errorf("Error should contain '%s', got: %v", tt.errContains, err)
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error: %v", err)
				}
				if formatter == nil {
					t.Error("Expected formatter to be created")
				} else if formatter.ContentType() != tt.contentType {
					t.Errorf("Expected content type %s, got %s", tt.contentType, formatter.ContentType())
				}
			}
		})
	}
}

// TestTemplateFormatter_Format tests the formatting functionality.
func TestTemplateFormatter_Format(t *testing.T) {
	tests := []struct {
		name        string
		template    string
		data        interface{}
		want        string
		wantErr     bool
		errContains string
	}{
		{
			name:     "Simple field replacement",
			template: `<Report><ID>{{.ID}}</ID></Report>`,
			data:     map[string]interface{}{"ID": "123"},
			want:     `<Report><ID>123</ID></Report>`,
			wantErr:  false,
		},
		{
			name: "Conditional rendering",
			template: `<Report>
{{- if .ID}}
<ID>{{.ID}}</ID>
{{- end}}
</Report>`,
			data:    map[string]interface{}{"ID": "123"},
			want:    "<Report>\n<ID>123</ID>\n</Report>",
			wantErr: false,
		},
		{
			name: "Conditional with missing field",
			template: `<Report>
{{- if .ID}}
<ID>{{.ID}}</ID>
{{- end}}
</Report>`,
			data:    map[string]interface{}{},
			want:    "<Report>\n</Report>",
			wantErr: false,
		},
		{
			name: "Loop rendering",
			template: `<Items>
{{- range .Items}}
<Item>{{.Name}}</Item>
{{- end}}
</Items>`,
			data: map[string]interface{}{
				"Items": []interface{}{
					map[string]interface{}{"Name": "Item1"},
					map[string]interface{}{"Name": "Item2"},
				},
			},
			want:    "<Items>\n<Item>Item1</Item>\n<Item>Item2</Item>\n</Items>",
			wantErr: false,
		},
		{
			name:     "Default function",
			template: `<Value>{{.Value | default "N/A"}}</Value>`,
			data:     map[string]interface{}{},
			want:     `<Value>N/A</Value>`,
			wantErr:  false,
		},
		{
			name:     "Upper function",
			template: `<Name>{{.Name | upper}}</Name>`,
			data:     map[string]interface{}{"Name": "test"},
			want:     `<Name>TEST</Name>`,
			wantErr:  false,
		},
		{
			name:     "Lower function",
			template: `<Name>{{.Name | lower}}</Name>`,
			data:     map[string]interface{}{"Name": "TEST"},
			want:     `<Name>test</Name>`,
			wantErr:  false,
		},
		{
			name:     "Trim function",
			template: `<Name>{{.Name | trim}}</Name>`,
			data:     map[string]interface{}{"Name": "  test  "},
			want:     `<Name>test</Name>`,
			wantErr:  false,
		},
		{
			name:     "Date formatting",
			template: `<Date>{{.Date | formatDate "2006-01-02"}}</Date>`,
			data:     map[string]interface{}{"Date": "2024-01-15T10:30:00Z"},
			want:     `<Date>2024-01-15</Date>`,
			wantErr:  false,
		},
		{
			name:     "Missing required field",
			template: `<ID>{{.ID}}</ID>`,
			data:     map[string]interface{}{},
			want:     `<ID><no value></ID>`,
			wantErr:  false,
		},
		{
			name:        "Invalid template execution",
			template:    `{{.Field.Invalid.Access}}`,
			data:        map[string]interface{}{"Field": "string"},
			wantErr:     true,
			errContains: "template execution failed",
		},
		{
			name:     "Nil data",
			template: `<Report>Empty</Report>`,
			data:     nil,
			want:     `<Report>Empty</Report>`,
			wantErr:  false,
		},
		{
			name: "Complex nested template",
			template: `<?xml version="1.0" encoding="UTF-8"?>
<Report>
  <Header>
    <Date>{{.ReportDate}}</Date>
    {{- if .ORI}}
    <ORI>{{.ORI}}</ORI>
    {{- end}}
  </Header>
  {{- if .Victims}}
  <Victims>
    {{- range .Victims}}
    <Victim>
      <ID>{{.SequenceNumber}}</ID>
      <Age>{{.Age | default "00"}}</Age>
      <Sex>{{.Sex | upper}}</Sex>
    </Victim>
    {{- end}}
  </Victims>
  {{- end}}
</Report>`,
			data: map[string]interface{}{
				"ReportDate": "2024-01-15",
				"ORI":        "TX0000000",
				"Victims": []interface{}{
					map[string]interface{}{
						"SequenceNumber": "01",
						"Age":            "25",
						"Sex":            "m",
					},
					map[string]interface{}{
						"SequenceNumber": "02",
						"Age":            nil,
						"Sex":            "f",
					},
				},
			},
			want: `<?xml version="1.0" encoding="UTF-8"?>
<Report>
  <Header>
    <Date>2024-01-15</Date>
    <ORI>TX0000000</ORI>
  </Header>
  <Victims>
    <Victim>
      <ID>01</ID>
      <Age>25</Age>
      <Sex>M</Sex>
    </Victim>
    <Victim>
      <ID>02</ID>
      <Age>00</Age>
      <Sex>F</Sex>
    </Victim>
  </Victims>
</Report>`,
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create test template
			templatePath := createTestTemplate(t, tt.template)

			// Create formatter
			formatter, err := formatters.NewTemplateFormatter(context.Background(), templatePath, "application/xml")
			if err != nil {
				t.Fatalf("Failed to create formatter: %v", err)
			}

			// Format data
			result, err := formatter.Format(context.Background(), tt.data)

			if tt.wantErr {
				if err == nil {
					t.Errorf("Expected error but got none")
				} else if tt.errContains != "" && !strings.Contains(err.Error(), tt.errContains) {
					t.Errorf("Error should contain '%s', got: %v", tt.errContains, err)
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error: %v", err)
				}

				resultStr := string(result)
				if diff := compareOutput(resultStr, tt.want); diff != "" {
					t.Errorf("Output mismatch:\n%s\nActual:\n%s\nExpected:\n%s", diff, resultStr, tt.want)
				}
			}
		})
	}
}

// TestTemplateFormatter_TemplateFunctions tests the custom template functions.
func TestTemplateFormatter_TemplateFunctions(t *testing.T) {
	tests := []struct {
		name     string
		template string
		data     interface{}
		want     string
	}{
		{
			name:     "Default with nil value",
			template: `{{.Missing | default "DEFAULT"}}`,
			data:     map[string]interface{}{},
			want:     "DEFAULT",
		},
		{
			name:     "Default with empty string",
			template: `{{.Empty | default "DEFAULT"}}`,
			data:     map[string]interface{}{"Empty": ""},
			want:     "DEFAULT",
		},
		{
			name:     "Default with value present",
			template: `{{.Value | default "DEFAULT"}}`,
			data:     map[string]interface{}{"Value": "ACTUAL"},
			want:     "ACTUAL",
		},
		{
			name:     "Upper case conversion",
			template: `{{.Text | upper}}`,
			data:     map[string]interface{}{"Text": "hello world"},
			want:     "HELLO WORLD",
		},
		{
			name:     "Lower case conversion",
			template: `{{.Text | lower}}`,
			data:     map[string]interface{}{"Text": "HELLO WORLD"},
			want:     "hello world",
		},
		{
			name:     "Trim whitespace",
			template: `{{.Text | trim}}`,
			data:     map[string]interface{}{"Text": "  hello world  "},
			want:     "hello world",
		},
		{
			name:     "FormatDate with time.Time",
			template: `{{.Date | formatDate "2006-01-02"}}`,
			data:     map[string]interface{}{"Date": time.Date(2024, 1, 15, 10, 30, 0, 0, time.UTC)},
			want:     "2024-01-15",
		},
		{
			name:     "FormatDate with RFC3339 string",
			template: `{{.Date | formatDate "01/02/2006"}}`,
			data:     map[string]interface{}{"Date": "2024-01-15T10:30:00Z"},
			want:     "01/15/2024",
		},
		{
			name:     "FormatDate with simple date string",
			template: `{{.Date | formatDate "Jan 2, 2006"}}`,
			data:     map[string]interface{}{"Date": "2024-01-15"},
			want:     "Jan 15, 2024",
		},
		{
			name:     "FormatDate with nil",
			template: `{{.Date | formatDate "2006-01-02"}}`,
			data:     map[string]interface{}{"Date": nil},
			want:     "",
		},
		{
			name:     "FormatDate with unparseable string",
			template: `{{.Date | formatDate "2006-01-02"}}`,
			data:     map[string]interface{}{"Date": "not a date"},
			want:     "not a date",
		},
		{
			name:     "Chained functions",
			template: `{{.Text | trim | upper}}`,
			data:     map[string]interface{}{"Text": "  hello  "},
			want:     "HELLO",
		},
		{
			name:     "Function with missing field",
			template: `{{.Missing | upper | default "NONE"}}`,
			data:     map[string]interface{}{},
			want:     "NONE",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create test template
			templatePath := createTestTemplate(t, tt.template)

			// Create formatter
			formatter, err := formatters.NewTemplateFormatter(context.Background(), templatePath, "text/plain")
			if err != nil {
				t.Fatalf("Failed to create formatter: %v", err)
			}

			// Format data
			result, err := formatter.Format(context.Background(), tt.data)
			if err != nil {
				t.Errorf("Unexpected error: %v", err)
			}

			resultStr := string(result)
			if resultStr != tt.want {
				t.Errorf("Expected '%s', got '%s'", tt.want, resultStr)
			}
		})
	}
}

// TestTemplateFormatter_ErrorHandling tests error handling scenarios.
func TestTemplateFormatter_ErrorHandling(t *testing.T) {
	tests := []struct {
		name        string
		setupError  string // "file", "template"
		template    string
		data        interface{}
		errContains string
	}{
		{
			name:        "Non-existent template file",
			setupError:  "file",
			errContains: "failed to read template file",
		},
		{
			name:        "Invalid template syntax",
			setupError:  "template",
			template:    `{{.Field | unknownFunc}}`,
			errContains: "failed to parse template",
		},
		{
			name:        "Template execution error",
			template:    `{{.Map.Invalid.Deep.Access}}`,
			data:        map[string]interface{}{"Map": "not a map"},
			errContains: "template execution failed",
		},
		{
			name:        "Nil pointer in template",
			template:    `{{.Ptr.Field}}`,
			data:        map[string]interface{}{"Ptr": (*string)(nil)},
			errContains: "template execution failed",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var formatter *formatters.TemplateFormatter
			var err error

			switch tt.setupError {
			case "file":
				// Try to create formatter with non-existent file
				_, err = formatters.NewTemplateFormatter(context.Background(), "/non/existent/file.tmpl", "text/plain")
				if err == nil {
					t.Fatal("Expected error for non-existent file")
				}
				if !strings.Contains(err.Error(), tt.errContains) {
					t.Errorf("Error should contain '%s', got: %v", tt.errContains, err)
				}
				return // Test complete for file error

			case "template":
				// Create formatter with invalid template
				templatePath := createTestTemplate(t, tt.template)
				_, err = formatters.NewTemplateFormatter(context.Background(), templatePath, "text/plain")
				if err == nil {
					t.Fatal("Expected error for invalid template")
				}
				if !strings.Contains(err.Error(), tt.errContains) {
					t.Errorf("Error should contain '%s', got: %v", tt.errContains, err)
				}
				return // Test complete for template error

			default:
				// Normal template creation for execution errors
				templatePath := createTestTemplate(t, tt.template)
				formatter, err = formatters.NewTemplateFormatter(context.Background(), templatePath, "text/plain")
				if err != nil {
					t.Fatalf("Failed to create formatter: %v", err)
				}
			}

			// Test execution error
			_, err = formatter.Format(context.Background(), tt.data)
			if err == nil {
				t.Error("Expected error during formatting")
			} else if !strings.Contains(err.Error(), tt.errContains) {
				t.Errorf("Error should contain '%s', got: %v", tt.errContains, err)
			}
		})
	}
}

// TestTemplateFormatter_ContentType tests content type handling.
func TestTemplateFormatter_ContentType(t *testing.T) {
	tests := []struct {
		contentType string
	}{
		{"application/xml"},
		{"application/json"},
		{"text/plain"},
		{"text/csv"},
		{"text/html"},
		{"custom/type"},
	}

	for _, tt := range tests {
		t.Run(tt.contentType, func(t *testing.T) {
			templatePath := createTestTemplate(t, "test")
			formatter, err := formatters.NewTemplateFormatter(context.Background(), templatePath, tt.contentType)
			if err != nil {
				t.Fatalf("Failed to create formatter: %v", err)
			}

			if formatter.ContentType() != tt.contentType {
				t.Errorf("Expected content type %s, got %s", tt.contentType, formatter.ContentType())
			}
		})
	}
}

// TestTemplateFormatter_ThreadSafety tests concurrent formatting.
func TestTemplateFormatter_ThreadSafety(t *testing.T) {
	template := `<Item>
  <ID>{{.ID}}</ID>
  <Name>{{.Name | upper}}</Name>
  <Value>{{.Value | default "0"}}</Value>
</Item>`

	templatePath := createTestTemplate(t, template)
	formatter, err := formatters.NewTemplateFormatter(context.Background(), templatePath, "application/xml")
	if err != nil {
		t.Fatalf("Failed to create formatter: %v", err)
	}

	// Run concurrent formatting operations
	const goroutines = 10
	const iterations = 100

	errChan := make(chan error, goroutines)

	for i := 0; i < goroutines; i++ {
		go func(id int) {
			for j := 0; j < iterations; j++ {
				data := map[string]interface{}{
					"ID":    id*1000 + j,
					"Name":  "test",
					"Value": j,
				}

				result, err := formatter.Format(context.Background(), data)
				if err != nil {
					errChan <- err
					return
				}

				// Verify result contains expected ID
				expectedID := strings.Contains(string(result),
					"<ID>"+string(rune(id*1000+j))+"</ID>")
				if !expectedID {
					errChan <- err
					return
				}
			}
			errChan <- nil
		}(i)
	}

	// Wait for all goroutines
	for i := 0; i < goroutines; i++ {
		if err := <-errChan; err != nil {
			t.Errorf("Concurrent formatting error: %v", err)
		}
	}
}
