package tests

import (
	"context"
	"os"
	"path/filepath"
	"strings"
	"testing"

	etl "proto/hero/etl/v1"
	"workflow/internal/etl/transformation/formatters"
)

// TestFormatterFactory_Creation tests factory creation.
func TestFormatterFactory_Creation(t *testing.T) {
	tests := []struct {
		name              string
		templatesBasePath string
	}{
		{
			name:              "Factory with valid base path",
			templatesBasePath: "templates",
		},
		{
			name:              "Factory with nested base path",
			templatesBasePath: "internal/etl/templates",
		},
		{
			name:              "Factory with absolute path",
			templatesBasePath: "/absolute/path/templates",
		},
		{
			name:              "Factory with empty base path",
			templatesBasePath: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			factory := formatters.NewFormatterFactory(tt.templatesBasePath)
			if factory == nil {
				t.Error("Expected factory to be created")
			}
		})
	}
}

// TestFormatterFactory_CreateFormatter tests formatter creation for different formats.
func TestFormatterFactory_CreateFormatter(t *testing.T) {
	// Create test template directory structure
	tempDir := t.TempDir()

	// Create NIBRS template
	nibrsDir := filepath.Join(tempDir, "nibrs", "reports", "v2")
	err := os.MkdirAll(nibrsDir, 0755)
	if err != nil {
		t.Fatalf("Failed to create nibrs directory: %v", err)
	}

	nibrsTemplate := `<?xml version="1.0" encoding="UTF-8"?>
<nibrs:Submission xmlns:nibrs="http://fbi.gov/cjis/nibrs/4.1">
  <nibrs:ReportDate>{{.ReportDate}}</nibrs:ReportDate>
</nibrs:Submission>`

	err = os.WriteFile(filepath.Join(nibrsDir, "nibrs.xml.tmpl"), []byte(nibrsTemplate), 0600)
	if err != nil {
		t.Fatalf("Failed to create NIBRS template: %v", err)
	}

	factory := formatters.NewFormatterFactory(tempDir)

	tests := []struct {
		name         string
		outputFormat etl.OutputFormat
		wantMimeType string
		wantErr      bool
		errContains  string
	}{
		{
			name:         "NIBRS XML format",
			outputFormat: etl.OutputFormat_OUTPUT_FORMAT_NIBRS_XML,
			wantMimeType: "application/xml",
			wantErr:      false,
		},
		{
			name:         "Unsupported format",
			outputFormat: etl.OutputFormat(999),
			wantErr:      true,
			errContains:  "unsupported output format",
		},
		{
			name:         "Unspecified format",
			outputFormat: etl.OutputFormat_OUTPUT_FORMAT_UNSPECIFIED,
			wantErr:      true,
			errContains:  "unsupported output format",
		},
		{
			name:         "Invalid format value",
			outputFormat: etl.OutputFormat(999),
			wantErr:      true,
			errContains:  "unsupported output format",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			formatter, err := factory.CreateFormatter(context.Background(), tt.outputFormat)

			if tt.wantErr {
				if err == nil {
					t.Error("Expected error but got none")
				} else if tt.errContains != "" && !strings.Contains(err.Error(), tt.errContains) {
					t.Errorf("Error should contain '%s', got: %v", tt.errContains, err)
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error: %v", err)
				}
				if formatter == nil {
					t.Error("Expected formatter to be created")
				} else if formatter.ContentType() != tt.wantMimeType {
					t.Errorf("Expected MIME type %s, got %s", tt.wantMimeType, formatter.ContentType())
				}
			}
		})
	}
}

// TestFormatterFactory_GetTemplatePath tests template path resolution.
func TestFormatterFactory_GetTemplatePath(t *testing.T) {
	basePath := "internal/etl/templates"
	factory := formatters.NewFormatterFactory(basePath)

	tests := []struct {
		name         string
		outputFormat etl.OutputFormat
		wantPath     string
		wantErr      bool
	}{
		{
			name:         "NIBRS XML template path",
			outputFormat: etl.OutputFormat_OUTPUT_FORMAT_NIBRS_XML,
			wantPath:     filepath.Join(basePath, "nibrs", "reports", "v2", "nibrs.xml.tmpl"),
			wantErr:      false,
		},
		{
			name:         "Unsupported format",
			outputFormat: etl.OutputFormat(999),
			wantErr:      true,
		},
		{
			name:         "Unspecified format",
			outputFormat: etl.OutputFormat_OUTPUT_FORMAT_UNSPECIFIED,
			wantErr:      true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			path, err := factory.GetTemplatePath(tt.outputFormat)

			if tt.wantErr {
				if err == nil {
					t.Error("Expected error but got none")
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error: %v", err)
				}
				if path != tt.wantPath {
					t.Errorf("Expected path %s, got %s", tt.wantPath, path)
				}
			}
		})
	}
}

// TestFormatterFactory_MissingTemplate tests behavior when template file is missing.
func TestFormatterFactory_MissingTemplate(t *testing.T) {
	// Create factory with directory that has no templates
	tempDir := t.TempDir()
	factory := formatters.NewFormatterFactory(tempDir)

	// Log the expected template path for debugging
	expectedPath := filepath.Join(tempDir, "nibrs", "reports", "v2", "nibrs.xml.tmpl")
	t.Logf("Expected template path: %s", expectedPath)

	// Try to create formatter for NIBRS (template doesn't exist)
	formatter, err := factory.CreateFormatter(context.Background(), etl.OutputFormat_OUTPUT_FORMAT_NIBRS_XML)

	// Debug output
	t.Logf("Formatter: %v, Error: %v", formatter, err)

	if err == nil {
		t.Error("Expected error for missing template")
	}
	if formatter != nil {
		t.Error("Expected no formatter for missing template")
	}
	if err != nil && !strings.Contains(err.Error(), "failed to read template file") {
		t.Errorf("Expected 'failed to read template file' error, got: %v", err)
	}
}

// TestFormatterFactory_Integration tests factory with actual formatting.
func TestFormatterFactory_Integration(t *testing.T) {
	// Create test template directory
	tempDir := t.TempDir()
	nibrsDir := filepath.Join(tempDir, "nibrs", "reports", "v2")
	err := os.MkdirAll(nibrsDir, 0755)
	if err != nil {
		t.Fatalf("Failed to create nibrs directory: %v", err)
	}

	// Create a simple NIBRS template
	nibrsTemplate := `<?xml version="1.0" encoding="UTF-8"?>
<nibrs:Submission xmlns:nibrs="http://fbi.gov/cjis/nibrs/4.1">
  <nibrs:ReportHeader>
    <nibrs:ReportDate>{{.ReportDate}}</nibrs:ReportDate>
    <nibrs:ORI>{{.ORI}}</nibrs:ORI>
    {{- if .AgencyName}}
    <nibrs:ReportingAgencyName>{{.AgencyName}}</nibrs:ReportingAgencyName>
    {{- end}}
  </nibrs:ReportHeader>
  {{- if .Victims}}
  <nibrs:Victims>
    {{- range .Victims}}
    <nibrs:Victim>
      <nibrs:VictimSequenceNumber>{{.SequenceNumber}}</nibrs:VictimSequenceNumber>
      <nibrs:AgeOfVictim>{{.Age | default "00"}}</nibrs:AgeOfVictim>
      <nibrs:SexOfVictim>{{.Sex | upper}}</nibrs:SexOfVictim>
    </nibrs:Victim>
    {{- end}}
  </nibrs:Victims>
  {{- end}}
</nibrs:Submission>`

	err = os.WriteFile(filepath.Join(nibrsDir, "nibrs.xml.tmpl"), []byte(nibrsTemplate), 0600)
	if err != nil {
		t.Fatalf("Failed to create NIBRS template: %v", err)
	}

	// Create factory
	factory := formatters.NewFormatterFactory(tempDir)

	// Create formatter
	formatter, err := factory.CreateFormatter(context.Background(), etl.OutputFormat_OUTPUT_FORMAT_NIBRS_XML)
	if err != nil {
		t.Fatalf("Failed to create formatter: %v", err)
	}

	// Test data
	data := map[string]interface{}{
		"ReportDate": "2024-01-15",
		"ORI":        "TX0000000",
		"AgencyName": "Test Police Department",
		"Victims": []interface{}{
			map[string]interface{}{
				"SequenceNumber": "01",
				"Age":            "25",
				"Sex":            "m",
			},
			map[string]interface{}{
				"SequenceNumber": "02",
				"Age":            nil,
				"Sex":            "f",
			},
		},
	}

	// Format data
	result, err := formatter.Format(context.Background(), data)
	if err != nil {
		t.Fatalf("Failed to format data: %v", err)
	}

	// Validate result
	resultStr := string(result)

	// Check for key elements
	expectedElements := []string{
		`<?xml version="1.0" encoding="UTF-8"?>`,
		`<nibrs:Submission xmlns:nibrs="http://fbi.gov/cjis/nibrs/4.1">`,
		`<nibrs:ReportDate>2024-01-15</nibrs:ReportDate>`,
		`<nibrs:ORI>TX0000000</nibrs:ORI>`,
		`<nibrs:ReportingAgencyName>Test Police Department</nibrs:ReportingAgencyName>`,
		`<nibrs:VictimSequenceNumber>01</nibrs:VictimSequenceNumber>`,
		`<nibrs:AgeOfVictim>25</nibrs:AgeOfVictim>`,
		`<nibrs:SexOfVictim>M</nibrs:SexOfVictim>`,
		`<nibrs:VictimSequenceNumber>02</nibrs:VictimSequenceNumber>`,
		`<nibrs:AgeOfVictim>00</nibrs:AgeOfVictim>`,
		`<nibrs:SexOfVictim>F</nibrs:SexOfVictim>`,
	}

	for _, expected := range expectedElements {
		if !strings.Contains(resultStr, expected) {
			t.Errorf("Expected result to contain: %s", expected)
		}
	}

	// Check MIME type
	if formatter.ContentType() != "application/xml" {
		t.Errorf("Expected MIME type 'application/xml', got '%s'", formatter.ContentType())
	}
}

// TestFormatterFactory_PathHandling tests various path handling scenarios.
func TestFormatterFactory_PathHandling(t *testing.T) {
	tests := []struct {
		name              string
		basePath          string
		outputFormat      etl.OutputFormat
		expectedPathParts []string
	}{
		{
			name:              "Simple base path",
			basePath:          "templates",
			outputFormat:      etl.OutputFormat_OUTPUT_FORMAT_NIBRS_XML,
			expectedPathParts: []string{"templates", "nibrs", "reports", "v2", "nibrs.xml.tmpl"},
		},
		{
			name:              "Nested base path",
			basePath:          "internal/etl/templates",
			outputFormat:      etl.OutputFormat_OUTPUT_FORMAT_NIBRS_XML,
			expectedPathParts: []string{"internal", "etl", "templates", "nibrs", "reports", "v2", "nibrs.xml.tmpl"},
		},
		{
			name:              "Base path with trailing slash",
			basePath:          "templates/",
			outputFormat:      etl.OutputFormat_OUTPUT_FORMAT_NIBRS_XML,
			expectedPathParts: []string{"templates", "nibrs", "reports", "v2", "nibrs.xml.tmpl"},
		},
		{
			name:              "Empty base path",
			basePath:          "",
			outputFormat:      etl.OutputFormat_OUTPUT_FORMAT_NIBRS_XML,
			expectedPathParts: []string{"nibrs", "reports", "v2", "nibrs.xml.tmpl"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			factory := formatters.NewFormatterFactory(tt.basePath)

			path, err := factory.GetTemplatePath(tt.outputFormat)
			if err != nil {
				t.Fatalf("Unexpected error: %v", err)
			}

			// Verify path contains expected parts
			for _, part := range tt.expectedPathParts {
				if !strings.Contains(path, part) {
					t.Errorf("Expected path to contain '%s', got: %s", part, path)
				}
			}
		})
	}
}
