package formatters

import (
	"context"
	"fmt"
	"path/filepath"

	"common/herosentry"
	etl "proto/hero/etl/v1"
)

// FormatterFactory creates formatters based on output format.
//
// This factory is intentionally simple - it only maps output formats to
// template files and creates the appropriate formatter. All format-specific
// logic resides in the templates themselves, not in this factory.
//
// Adding New Formats:
// To support a new output format:
// 1. Create a template file in the templates directory
// 2. Add a case to the factory switch statement
// 3. The template controls everything else
//
// No business logic should be added to this factory. It's purely a
// mapping mechanism from output format to template file.
type FormatterFactory struct {
	templatesBasePath string // Base path where templates are stored
}

// NewFormatterFactory creates a new formatter factory.
//
// Parameters:
//   - templatesBasePath: Base directory containing all template files
//
// Returns:
//   - *FormatterFactory: Factory instance for creating formatters
//
// Example:
//
//	factory := NewFormatterFactory("internal/etl/templates")
func NewFormatterFactory(templatesBasePath string) *FormatterFactory {
	return &FormatterFactory{
		templatesBasePath: templatesBasePath,
	}
}

// C<PERSON><PERSON><PERSON><PERSON><PERSON> creates a formatter for the specified output format.
//
// This method maps output formats to their corresponding template files
// and creates a template formatter. The mapping is the only logic here -
// all format-specific behavior is controlled by the templates.
//
// Parameters:
//   - ctx: Context for distributed tracing
//   - outputFormat: The desired output format (NIBRS_XML, etc.)
//
// Returns:
//   - Formatter: A formatter configured for the specified format
//   - error: If the format is unsupported or template loading fails
//
// Currently Supported Formats:
//   - OUTPUT_FORMAT_NIBRS_XML: NIBRS XML format
//
// Example:
//
//	formatter, err := factory.CreateFormatter(ctx, etl.OutputFormat_OUTPUT_FORMAT_NIBRS_XML)
func (f *FormatterFactory) CreateFormatter(ctx context.Context, outputFormat etl.OutputFormat) (Formatter, error) {
	spanContext, _, finishSpan := herosentry.StartSpan(ctx, "FormatterFactory.CreateFormatter")
	defer finishSpan()
	switch outputFormat {
	case etl.OutputFormat_OUTPUT_FORMAT_NIBRS_XML:
		// NIBRS XML format - template controls all NIBRS-specific logic
		templatePath := filepath.Join(f.templatesBasePath, "nibrs", "reports", "v2", "nibrs.xml.tmpl")
		formatter, err := NewTemplateFormatter(spanContext, templatePath, "application/xml")
		if err != nil {
			return nil, err
		}
		return formatter, nil

	default:
		return nil, fmt.Errorf("unsupported output format: %v", outputFormat)
	}
}

// GetTemplatePath returns the template path for a given output format.
//
// This is a utility method that returns the template file path without
// creating a formatter. Useful for debugging or documentation.
//
// Parameters:
//   - outputFormat: The output format to get template path for
//
// Returns:
//   - string: Path to the template file
//   - error: If the format is unsupported
func (f *FormatterFactory) GetTemplatePath(outputFormat etl.OutputFormat) (string, error) {
	switch outputFormat {
	case etl.OutputFormat_OUTPUT_FORMAT_NIBRS_XML:
		return filepath.Join(f.templatesBasePath, "nibrs", "reports", "v2", "nibrs.xml.tmpl"), nil
	default:
		return "", fmt.Errorf("unsupported output format: %v", outputFormat)
	}
}
