// Package core provides the foundational transformation infrastructure for converting
// extracted report data into various output formats (NIBRS XML, UCR, custom formats).
//
// The transformation engine implements the core principle of zero business logic in Go code.
// All transformation behavior is defined in JSON configuration files, making the system
// completely data-driven and allowing new transformation rules to be added without code
// changes, deployments, or developer intervention.
//
// Transformation Philosophy:
// The transformation engine embodies the principle that business logic should live in
// configuration, not code. This approach provides several critical benefits:
//   - Compliance teams can modify field mappings without developer involvement
//   - New output formats can be added by creating configuration files
//   - Transformation logic is transparent and auditable by non-technical stakeholders
//   - Debugging transformation issues involves examining configuration, not code
//   - Changes can be made and tested without system downtime
//
// Supported Transformation Types:
// The engine provides 8 fundamental transformation types that can be combined to
// handle any data transformation requirement:
//
//  1. Direct Transformation:
//     Purpose: Pass data through unchanged (field copying)
//     Use Case: Moving fields from input to output without modification
//     Configuration: No additional configuration required
//     Example: Copy report ID directly to output
//
//  2. Lookup Transformation:
//     Purpose: Map values using lookup tables (enum conversion, code mapping)
//     Use Case: Convert Hero enums to standard reporting codes
//     Configuration: Requires lookup_table reference and optional default_value
//     Example: Convert "SEX_MALE" to "M" for NIBRS reporting
//
//  3. Filter Transformation:
//     Purpose: Filter arrays based on specified criteria
//     Use Case: Extract only victims from entities, or only active cases
//     Configuration: Requires field, operator, and value for filtering
//     Example: Filter entities where role equals "VICTIM"
//
//  4. Format Transformation:
//     Purpose: Apply printf-style formatting to values
//     Use Case: Pad numbers, format IDs, apply consistent formatting
//     Configuration: Requires format_pattern using printf syntax
//     Example: Format incident number as "INC-%06d"
//
//  5. Date Format Transformation:
//     Purpose: Convert date strings between different formats
//     Use Case: Convert ISO timestamps to NIBRS date format
//     Configuration: Requires date_format pattern using Go time format
//     Example: Convert "2024-01-15T10:30:00Z" to "2024-01-15"
//
//  6. Add Sequence Transformation:
//     Purpose: Add sequence numbers to array items
//     Use Case: Number victims, offenders, or other sequential items
//     Configuration: Requires sequence_field, optional start and format
//     Example: Add "victim_sequence": "01", "02", "03" to victim objects
//
//  7. Group By Transformation:
//     Purpose: Group array items by field values
//     Use Case: Group entities by role, events by type
//     Configuration: Requires group_field for grouping criteria
//     Example: Group entities into victims and offenders
//
//  8. Sort Transformation:
//     Purpose: Sort arrays by field values
//     Use Case: Order items alphabetically, numerically, or by custom criteria
//     Configuration: Requires sort_field, optional sort_order (asc/desc)
//     Example: Sort entities by age or name
//
// Configuration-Driven Architecture:
// Each transformation is completely configured through JSON, not Go code:
//
//	{
//	  "transformation": "lookup",
//	  "config": {
//	    "lookup_table": "sex_codes",
//	    "default_value": "U"
//	  }
//	}
//
// Performance Characteristics:
// The engine is optimized for high-throughput transformation operations:
//   - Efficient reflection-based value access
//   - Minimal memory allocation during transformations
//   - Fast path resolution for nested data access
//   - Optimized comparison and sorting algorithms
//
// Error Handling Strategy:
// Transformations provide detailed error information for debugging:
//   - Configuration validation errors with specific field feedback
//   - Data type mismatch errors with expected vs actual types
//   - Missing field errors with path context
//   - Transformation-specific error messages with context
package core

import (
	"fmt"
	"reflect"
	"sort"
	"strconv"
	"strings"
	"time"
)

// TransformationEngine executes data transformations using configuration-driven rules.
//
// This component implements the core transformation capabilities that convert extracted
// report data into various output formats. It maintains strict separation between
// transformation logic (defined in configuration) and transformation execution
// (implemented in generic Go code).
//
// Core Responsibilities:
//   - Execute 8 different transformation types based on configuration
//   - Apply transformations to any data type (primitives, arrays, objects)
//   - Provide consistent error handling and reporting across all transformation types
//   - Maintain high performance for large-scale data transformation operations
//   - Ensure zero business logic exists in Go code (all logic in configuration)
//
// Business Context:
// The transformation engine is the heart of the ETL system's Transform phase.
// It converts typed extraction data into format-specific output structures
// according to compliance requirements, agency specifications, or custom formats.
// All transformation behavior is controlled by JSON configuration files.
//
// Transformation Execution Model:
// 1. Receive input data and transformation type from mapping engine
// 2. Parse transformation-specific configuration parameters
// 3. Apply generic transformation algorithm using configuration rules
// 4. Return transformed data ready for output generation
//
// Thread Safety:
// TransformationEngine instances are thread-safe and can be used concurrently.
// Each transformation operation is independent and stateless.
type TransformationEngine struct {
	pathResolver *PathResolver // Generic path navigation for complex data structures
}

// NewTransformationEngine creates a new transformation engine instance ready for operation.
//
// The returned engine is fully configured with path resolution capabilities and
// is ready to execute any of the 8 supported transformation types. The engine
// is thread-safe and can be used concurrently across multiple transformation operations.
//
// Returns:
//   - *TransformationEngine: A new transformation engine ready for immediate use
//
// Example Usage:
//
//	engine := NewTransformationEngine()
//	result, err := engine.ApplyTransformation(data, "lookup", config)
func NewTransformationEngine() *TransformationEngine {
	return &TransformationEngine{
		pathResolver: NewPathResolver(),
	}
}

// ApplyTransformation executes a specific transformation on input data using configuration rules.
//
// This method serves as the central dispatcher for all transformation operations,
// routing transformation requests to the appropriate implementation based on the
// transformation type. It ensures consistent error handling and behavior across
// all transformation types while maintaining the configuration-driven approach.
//
// Transformation Routing:
// The method supports 8 transformation types, each with specific configuration requirements:
//   - "direct": Pass-through transformation (no configuration needed)
//   - "lookup": Value mapping using lookup tables
//   - "filter": Array filtering with criteria-based selection
//   - "format": String formatting using printf-style patterns
//   - "date_format": Date string conversion between formats
//   - "add_sequence": Sequential numbering for array elements
//   - "group_by": Array grouping by field values
//   - "sort": Array sorting by field values with custom ordering
//
// Configuration Processing:
// Each transformation type has specific configuration requirements that are
// validated and processed by the corresponding transformation implementation.
// Invalid configurations result in detailed error messages for debugging.
//
// Error Handling:
// The method provides comprehensive error reporting including:
//   - Unknown transformation type errors
//   - Configuration validation errors
//   - Data type mismatch errors
//   - Transformation-specific processing errors
//
// Parameters:
//   - inputData: The data to transform (can be any type)
//   - transformation: String identifying the transformation type to apply
//   - config: Configuration map containing transformation-specific parameters
//
// Returns:
//   - interface{}: The transformed data (type depends on transformation)
//   - error: Detailed error information if transformation fails
//
// Common Use Cases:
//   - Convert enums: ApplyTransformation("SEX_MALE", "lookup", lookupConfig)
//   - Filter arrays: ApplyTransformation(entities, "filter", filterConfig)
//   - Format values: ApplyTransformation(123, "format", formatConfig)
//   - Transform dates: ApplyTransformation("2024-01-15T10:30:00Z", "date_format", dateConfig)
//
// Example Usage:
//
//	// Apply lookup transformation
//	result, err := engine.ApplyTransformation(
//	    "SEX_MALE",
//	    "lookup",
//	    map[string]interface{}{
//	        "lookup_table_data": map[string]string{"SEX_MALE": "M"},
//	    },
//	)
//	if err != nil {
//	    return fmt.Errorf("transformation failed: %w", err)
//	}
//	// result contains "M"
func (engine *TransformationEngine) ApplyTransformation(
	inputData interface{},
	transformation string,
	config map[string]interface{},
) (interface{}, error) {
	switch transformation {
	case "direct":
		return engine.directTransformation(inputData, config)
	case "lookup":
		return engine.lookupTransformation(inputData, config)
	case "filter":
		return engine.filterTransformation(inputData, config)
	case "format":
		return engine.formatTransformation(inputData, config)
	case "date_format":
		return engine.dateFormatTransformation(inputData, config)
	case "add_sequence":
		return engine.addSequenceTransformation(inputData, config)
	case "group_by":
		return engine.groupByTransformation(inputData, config)
	case "sort":
		return engine.sortTransformation(inputData, config)
	default:
		err := fmt.Errorf("unknown transformation type: %s", transformation)
		return nil, err
	}
}

// directTransformation passes the input data through without modification.
//
// This is the simplest transformation type that performs no data manipulation.
// It's useful for copying fields from input to output locations without any
// changes to the values.
//
// Use Cases:
//   - Copying ID fields that should remain unchanged
//   - Moving reference data between structures
//   - Preserving original values alongside transformed ones
//
// Parameters:
//   - inputData: Any value to pass through
//   - config: Not used for direct transformation (included for interface consistency)
//
// Returns:
//   - interface{}: The exact input data without modification
//   - error: Always nil for direct transformation
//
// Example:
//
//	result, _ := engine.directTransformation("RPT-123", nil)
//	// Returns: "RPT-123"
func (engine *TransformationEngine) directTransformation(

	inputData interface{},
	config map[string]interface{},
) (interface{}, error) {
	return inputData, nil
}

// lookupTransformation applies value mapping using a lookup table.
//
// This transformation maps input values to output values using a predefined
// lookup table. It's essential for converting internal enums to external codes,
// standardizing values, and handling data format conversions required by
// different reporting standards.
//
// Configuration Requirements:
//   - lookup_table_data: map[string]string containing value mappings
//   - default_value: Optional string to use when no match is found
//
// Supported Input Types:
//   - nil: Returns default_value or nil
//   - string: Single value lookup
//   - []interface{}: Array of values (from wildcard paths)
//
// Special Handling:
//   - Nil values: Use default_value if provided
//   - Empty strings: Use default_value if provided
//   - Missing mappings: Use default_value or return error
//   - Arrays: Process each element independently
//
// Parameters:
//   - inputData: Value(s) to look up
//   - config: Must contain lookup_table_data and optional default_value
//
// Returns:
//   - interface{}: Mapped value(s) based on lookup table
//   - error: If required config missing or no match without default
//
// Example:
//
//	config := map[string]interface{}{
//	    "lookup_table_data": map[string]string{"SEX_MALE": "M"},
//	    "default_value": "U",
//	}
//	result, _ := engine.lookupTransformation("SEX_MALE", config)
//	// Returns: "M"
func (engine *TransformationEngine) lookupTransformation(

	inputData interface{},
	config map[string]interface{},
) (interface{}, error) {
	lookupTableData, ok := config["lookup_table_data"].(map[string]string)
	if !ok {
		err := fmt.Errorf("lookup_table_data is required and must be a map[string]string")
		return nil, err
	}

	defaultValue, _ := config["default_value"].(string)

	// Handle both single strings and arrays of strings (from wildcard paths)
	switch typedInput := inputData.(type) {
	case nil:
		// Return default value for nil input, or nil if no default
		if defaultValue != "" {
			return defaultValue, nil
		}
		return nil, nil
	case string:
		// Single string lookup
		if mappedValue, exists := lookupTableData[typedInput]; exists {
			return mappedValue, nil
		}
		// Return default value if no match found
		if defaultValue != "" {
			return defaultValue, nil
		}
		err := fmt.Errorf("no lookup match for value '%s' and no default value provided", typedInput)
		return nil, err

	case []interface{}:
		// Array of values from wildcard path - apply lookup to each element
		var result []interface{}
		for _, item := range typedInput {
			// Handle nil elements in array
			if item == nil {
				if defaultValue != "" {
					result = append(result, defaultValue)
				}
				// Skip nil items if no default value
				continue
			}

			// Handle string elements
			if itemStr, ok := item.(string); ok {
				// Handle empty strings
				if itemStr == "" {
					if defaultValue != "" {
						result = append(result, defaultValue)
					}
					continue
				}

				// Normal lookup
				if mappedValue, exists := lookupTableData[itemStr]; exists {
					result = append(result, mappedValue)
				} else if defaultValue != "" {
					result = append(result, defaultValue)
				} else {
					err := fmt.Errorf("no lookup match for value '%s' and no default value provided", itemStr)
					return nil, err
				}
			} else {
				// Handle non-string, non-nil elements
				if defaultValue != "" {
					result = append(result, defaultValue)
				} else {
					err := fmt.Errorf("lookup transformation requires string elements in array, got %T", item)
					return nil, err
				}
			}
		}
		return result, nil

	default:
		err := fmt.Errorf("lookup transformation requires string or []interface{} input, got %T", inputData)
		return nil, err
	}
}

// filterTransformation filters array data based on specified criteria.
//
// This transformation extracts a subset of array elements that match specified
// conditions. It's crucial for separating different types of entities (victims,
// offenders), filtering by status, or extracting records meeting specific criteria.
//
// Configuration Requirements:
//   - field: Path to the field to evaluate
//   - operator: Comparison operator (equals, contains, greater_than, etc.)
//   - value: Value to compare against
//
// Filtering Process:
// 1. Parse and validate filter configuration
// 2. Convert input to array format
// 3. Evaluate each array element against filter criteria
// 4. Return array containing only matching elements
//
// Parameters:
//   - inputData: Array of items to filter
//   - config: Filter configuration with field, operator, and value
//
// Returns:
//   - interface{}: Filtered array containing only matching elements
//   - error: If input not array or configuration invalid
//
// Example:
//
//	config := map[string]interface{}{
//	    "field": "role",
//	    "operator": "equals",
//	    "value": "VICTIM",
//	}
//	result, _ := engine.filterTransformation(entities, config)
//	// Returns: Array of entities where role == "VICTIM"
func (engine *TransformationEngine) filterTransformation(

	inputData interface{},
	config map[string]interface{},
) (interface{}, error) {
	filterConfig, err := engine.parseFilterConfig(config)
	if err != nil {
		return nil, err
	}

	// Convert input to array
	inputArray, err := engine.toArray(inputData)
	if err != nil {
		err := fmt.Errorf("filter transformation requires array input: %w", err)
		return nil, err
	}

	var filtered []interface{}
	for _, item := range inputArray {
		match, err := engine.evaluateFilterCondition(item, filterConfig)
		if err != nil {
			err := fmt.Errorf("filter evaluation failed: %w", err)
			return nil, err
		}
		if match {
			filtered = append(filtered, item)
		}
	}

	return filtered, nil
}

// formatTransformation applies string formatting to the input value.
//
// This transformation uses printf-style formatting to create standardized string
// representations of data. It handles padding, prefixes, suffixes, and various
// format specifications for consistent output formatting.
//
// Configuration Requirements:
//   - format_pattern: Printf-style format string or static value
//
// Format Pattern Features:
//   - %s: String formatting
//   - %d: Integer formatting
//   - %06d: Zero-padded integers
//   - %f, %.2f: Float formatting with precision
//   - Static strings: No % placeholder returns pattern as-is
//
// Parameters:
//   - inputData: Value to format (int, float64, string, or any type)
//   - config: Must contain format_pattern string
//
// Returns:
//   - interface{}: Formatted string based on pattern
//   - error: If format_pattern missing or invalid
//
// Example:
//
//	config := map[string]interface{}{
//	    "format_pattern": "RPT-%06d",
//	}
//	result, _ := engine.formatTransformation(123, config)
//	// Returns: "RPT-000123"
func (engine *TransformationEngine) formatTransformation(

	inputData interface{},
	config map[string]interface{},
) (interface{}, error) {
	formatConfig, err := engine.parseFormatConfig(config)
	if err != nil {
		return nil, err
	}

	// Check if the format pattern contains placeholders
	// If not, return the pattern as-is (for static values)
	if !strings.Contains(formatConfig.FormatPattern, "%") {
		return formatConfig.FormatPattern, nil
	}

	// Apply formatting based on input type
	switch typedValue := inputData.(type) {
	case int:
		return fmt.Sprintf(formatConfig.FormatPattern, typedValue), nil
	case float64:
		return fmt.Sprintf(formatConfig.FormatPattern, typedValue), nil
	case string:
		return fmt.Sprintf(formatConfig.FormatPattern, typedValue), nil
	default:
		return fmt.Sprintf(formatConfig.FormatPattern, typedValue), nil
	}
}

// dateFormatTransformation formats date strings according to specified pattern.
//
// This transformation converts date strings from RFC3339 format to any desired
// format using Go's time formatting syntax. It's essential for meeting different
// reporting standards' date format requirements.
//
// Configuration Requirements:
//   - date_format: Go time format string for output
//
// Input Requirements:
//   - Input must be RFC3339 formatted date string
//   - Example: "2024-01-15T10:30:00Z"
//
// Common Output Formats:
//   - "2006-01-02": YYYY-MM-DD
//   - "01/02/2006": MM/DD/YYYY
//   - "Jan 2, 2006": Month Day, Year
//   - "2006-01-02 15:04": Date and time
//
// Parameters:
//   - inputData: RFC3339 date string or nil
//   - config: Must contain date_format string
//
// Returns:
//   - interface{}: Formatted date string or nil
//   - error: If parsing fails or config invalid
//
// Example:
//
//	config := map[string]interface{}{
//	    "date_format": "2006-01-02",
//	}
//	result, _ := engine.dateFormatTransformation("2024-01-15T10:30:00Z", config)
//	// Returns: "2024-01-15"
func (engine *TransformationEngine) dateFormatTransformation(

	inputData interface{},
	config map[string]interface{},
) (interface{}, error) {
	dateConfig, err := engine.parseDateFormatConfig(config)
	if err != nil {
		return nil, err
	}

	// Handle nil input
	if inputData == nil {
		return nil, nil
	}

	// Convert input to string
	inputStr, ok := inputData.(string)
	if !ok {
		err := fmt.Errorf("date_format transformation requires string input, got %T", inputData)
		return nil, err
	}

	// Parse the input date (assuming RFC3339 format)
	parsedTime, err := time.Parse(time.RFC3339, inputStr)
	if err != nil {
		err := fmt.Errorf("failed to parse date '%s': %w", inputStr, err)
		return nil, err
	}

	// Format according to the specified pattern
	return parsedTime.Format(dateConfig.DateFormat), nil
}

// addSequenceTransformation adds sequence numbers to array items.
//
// This transformation adds sequential numbering to array elements, which is
// crucial for compliance reporting that requires numbered lists (victims,
// offenders, etc.). It modifies array elements in-place by adding a new field.
//
// Configuration Requirements:
//   - sequence_field: Name of field to add for sequence number
//   - start: Starting number (optional, defaults to 1)
//   - format: Printf format for numbers (optional, defaults to "%d")
//
// Processing:
// 1. Parse configuration with defaults
// 2. Convert input to array
// 3. Add sequence field to each element (must be map)
// 4. Format sequence numbers according to pattern
//
// In-Place Modification:
// This transformation modifies the input array elements directly by adding
// the sequence field to each map element.
//
// Parameters:
//   - inputData: Array of maps to add sequences to
//   - config: Sequence configuration options
//
// Returns:
//   - interface{}: Array with sequence numbers added
//   - error: If input not array or elements not maps
//
// Example:
//
//	config := map[string]interface{}{
//	    "sequence_field": "victim_number",
//	    "start": 1,
//	    "format": "%02d",
//	}
//	result, _ := engine.addSequenceTransformation(victims, config)
//	// Adds: victim_number: "01", "02", "03" to each victim
func (engine *TransformationEngine) addSequenceTransformation(

	inputData interface{},
	config map[string]interface{},
) (interface{}, error) {
	sequenceConfig, err := engine.parseSequenceConfig(config)
	if err != nil {
		return nil, err
	}

	// Convert input to array
	inputArray, err := engine.toArray(inputData)
	if err != nil {
		err := fmt.Errorf("add_sequence transformation requires array input: %w", err)
		return nil, err
	}

	// Add sequence numbers to each item
	for itemIndex, item := range inputArray {
		sequenceNum := sequenceConfig.Start + itemIndex
		formattedSeq := fmt.Sprintf(sequenceConfig.Format, sequenceNum)

		// Add sequence to the item (assuming it's a map)
		if itemMap, ok := item.(map[string]interface{}); ok {
			itemMap[sequenceConfig.SequenceField] = formattedSeq
		}
	}

	return inputArray, nil
}

// groupByTransformation groups array items by a specified field.
//
// This transformation organizes array elements into groups based on a common
// field value. It's useful for categorizing entities by role, grouping events
// by type, or organizing data for hierarchical reporting structures.
//
// Configuration Requirements:
//   - group_field: Path to field to group by
//
// Output Structure:
// Returns array of group objects, each containing:
//   - group_key: The shared field value for this group
//   - items: Array of items with this group_key value
//
// Processing:
// 1. Parse group field from configuration
// 2. Convert input to array
// 3. Extract group field value from each item
// 4. Organize items into groups by field value
// 5. Return array of group objects
//
// Parameters:
//   - inputData: Array of items to group
//   - config: Must contain group_field path
//
// Returns:
//   - interface{}: Array of group objects
//   - error: If input not array or config invalid
//
// Example:
//
//	config := map[string]interface{}{
//	    "group_field": "role",
//	}
//	result, _ := engine.groupByTransformation(entities, config)
//	// Returns: [
//	//   {"group_key": "VICTIM", "items": [...]},
//	//   {"group_key": "WITNESS", "items": [...]}
//	// ]
func (engine *TransformationEngine) groupByTransformation(

	inputData interface{},
	config map[string]interface{},
) (interface{}, error) {
	groupConfig, err := engine.parseGroupByConfig(config)
	if err != nil {
		return nil, err
	}

	// Convert input to array
	inputArray, err := engine.toArray(inputData)
	if err != nil {
		err := fmt.Errorf("group_by transformation requires array input: %w", err)
		return nil, err
	}

	// Group items by the specified field
	groups := make(map[string][]interface{})
	for _, item := range inputArray {
		groupValue, err := engine.pathResolver.GetValue(item, groupConfig.GroupField)
		if err != nil {
			continue // Skip items that don't have the group field
		}

		groupKey := fmt.Sprintf("%v", groupValue)
		groups[groupKey] = append(groups[groupKey], item)
	}

	// Convert to array of group objects
	var result []interface{}
	for groupKey, groupItems := range groups {
		result = append(result, map[string]interface{}{
			"group_key": groupKey,
			"items":     groupItems,
		})
	}

	return result, nil
}

// sortTransformation sorts array items by a specified field.
//
// This transformation orders array elements based on a field value, supporting
// both ascending and descending order. It intelligently handles different data
// types with numeric values sorted numerically and strings lexicographically.
//
// Configuration Requirements:
//   - sort_field: Path to field to sort by
//   - sort_order: "asc" or "desc" (optional, defaults to "asc")
//
// Sort Behavior:
//   - Numeric values: Sorted numerically (10 > 2)
//   - String values: Sorted lexicographically ("abc" < "def")
//   - Mixed types: Converted to strings then compared
//   - Nil values: Sorted to beginning (asc) or end (desc)
//   - Missing fields: Treated as nil values
//
// Parameters:
//   - inputData: Array of items to sort
//   - config: Sort configuration with field and order
//
// Returns:
//   - interface{}: Sorted array
//   - error: If input not array or config invalid
//
// Example:
//
//	config := map[string]interface{}{
//	    "sort_field": "age",
//	    "sort_order": "desc",
//	}
//	result, _ := engine.sortTransformation(entities, config)
//	// Returns: Entities sorted by age in descending order
func (engine *TransformationEngine) sortTransformation(

	inputData interface{},
	config map[string]interface{},
) (interface{}, error) {
	sortConfig, err := engine.parseSortConfig(config)
	if err != nil {
		return nil, err
	}

	// Convert input to array
	inputArray, err := engine.toArray(inputData)
	if err != nil {
		err := fmt.Errorf("sort transformation requires array input: %w", err)
		return nil, err
	}

	// Sort the array
	sort.Slice(inputArray, func(leftIndex, rightIndex int) bool {
		leftValue, leftError := engine.pathResolver.GetValue(inputArray[leftIndex], sortConfig.SortField)
		if leftError != nil {
			return false
		}
		rightValue, rightError := engine.pathResolver.GetValue(inputArray[rightIndex], sortConfig.SortField)
		if rightError != nil {
			return false
		}

		// Compare values based on type
		compareResult := engine.compareValues(leftValue, rightValue)
		if sortConfig.SortOrder == "desc" {
			return compareResult > 0
		}
		return compareResult < 0
	})

	return inputArray, nil
}

// Helper methods for parsing configuration structs

// parseFilterConfig extracts and validates filter transformation configuration parameters.
//
// This method ensures all required filter configuration fields are present and properly
// typed. Filter transformations require field path, comparison operator, and value to
// compare against for array filtering operations.
//
// Required Configuration Fields:
//   - field: String path to the field to filter on (e.g., "role", "demographics.age")
//   - operator: String operator for comparison (equals, contains, greater_than, etc.)
//   - value: The value to compare against (type depends on operator)
//
// Supported Operators:
//   - equals, not_equals: Exact match comparison
//   - contains, starts_with, ends_with: String operations
//   - in, not_in: Array membership tests
//   - greater_than, less_than, greater_than_or_equal, less_than_or_equal: Numeric comparisons
//
// Parameters:
//   - config: Raw configuration map from the transformation specification
//
// Returns:
//   - *FilterConfig: Validated configuration ready for filter transformation
//   - error: Detailed error if required fields are missing
//
// Example Configuration:
//
//	{
//	  "field": "role",
//	  "operator": "equals",
//	  "value": "VICTIM"
//	}
func (engine *TransformationEngine) parseFilterConfig(config map[string]interface{}) (*FilterConfig, error) {
	field, ok := config["field"].(string)
	if !ok {
		err := fmt.Errorf("field is required and must be a string")
		return nil, err
	}

	operator, ok := config["operator"].(string)
	if !ok {
		err := fmt.Errorf("operator is required and must be a string")
		return nil, err
	}

	value, exists := config["value"]
	if !exists {
		err := fmt.Errorf("value is required")
		return nil, err
	}

	return &FilterConfig{
		Field:    field,
		Operator: operator,
		Value:    value,
	}, nil
}

// parseFormatConfig extracts and validates format transformation configuration parameters.
//
// This method parses printf-style format patterns used for string formatting operations.
// The format pattern can contain placeholders (%s, %d, %06d, etc.) or be a static string.
//
// Required Configuration Fields:
//   - format_pattern: Printf-style format string or static value
//
// Format Pattern Examples:
//   - "RPT-%06d": Formats number with leading zeros (123 → "RPT-000123")
//   - "%s-ACTIVE": Appends suffix to string value
//   - "STATIC-001": No placeholders, returns as-is
//
// Parameters:
//   - config: Raw configuration map from the transformation specification
//
// Returns:
//   - *FormatConfig: Validated configuration ready for format transformation
//   - error: Detailed error if format_pattern is missing or not a string
//
// Example Configuration:
//
//	{
//	  "format_pattern": "INC-%06d"
//	}
func (engine *TransformationEngine) parseFormatConfig(config map[string]interface{}) (*FormatConfig, error) {
	formatPattern, ok := config["format_pattern"].(string)
	if !ok {
		err := fmt.Errorf("format_pattern is required and must be a string")
		return nil, err
	}

	return &FormatConfig{
		FormatPattern: formatPattern,
	}, nil
}

// parseDateFormatConfig extracts and validates date format transformation configuration.
//
// This method parses date format patterns using Go's time formatting syntax. The input
// dates are expected to be in RFC3339 format and will be converted to the specified
// output format.
//
// Required Configuration Fields:
//   - date_format: Go time format string for output formatting
//
// Common Date Format Patterns:
//   - "2006-01-02": YYYY-MM-DD format
//   - "01/02/2006": MM/DD/YYYY format
//   - "Jan 2, 2006": Month Day, Year format
//   - "2006-01-02 15:04": Date and time format
//
// Parameters:
//   - config: Raw configuration map from the transformation specification
//
// Returns:
//   - *DateFormatConfig: Validated configuration ready for date transformation
//   - error: Detailed error if date_format is missing or not a string
//
// Example Configuration:
//
//	{
//	  "date_format": "2006-01-02"
//	}
func (engine *TransformationEngine) parseDateFormatConfig(config map[string]interface{}) (*DateFormatConfig, error) {
	dateFormat, ok := config["date_format"].(string)
	if !ok {
		err := fmt.Errorf("date_format is required and must be a string")
		return nil, err
	}

	return &DateFormatConfig{
		DateFormat: dateFormat,
	}, nil
}

// parseSequenceConfig extracts and validates sequence transformation configuration.
//
// This method parses configuration for adding sequential numbers to array elements.
// It supports customizable starting numbers and format patterns for the sequence values.
//
// Required Configuration Fields:
//   - sequence_field: String name of the field to add containing sequence number
//
// Optional Configuration Fields:
//   - start: Integer starting number for sequence (defaults to 1)
//   - format: Printf format string for sequence values (defaults to "%d")
//
// Common Format Patterns:
//   - "%d": Simple numbers (1, 2, 3)
//   - "%02d": Two-digit with leading zeros (01, 02, 03)
//   - "%03d": Three-digit with leading zeros (001, 002, 003)
//   - "V-%02d": Prefixed format (V-01, V-02, V-03)
//
// Parameters:
//   - config: Raw configuration map from the transformation specification
//
// Returns:
//   - *SequenceConfig: Validated configuration with defaults applied
//   - error: Detailed error if sequence_field is missing or invalid
//
// Example Configuration:
//
//	{
//	  "sequence_field": "victim_number",
//	  "start": 1,
//	  "format": "%02d"
//	}
func (engine *TransformationEngine) parseSequenceConfig(config map[string]interface{}) (*SequenceConfig, error) {
	sequenceField, ok := config["sequence_field"].(string)
	if !ok {
		err := fmt.Errorf("sequence_field is required and must be a string")
		return nil, err
	}

	start, ok := config["start"].(int)
	if !ok {
		start = 1 // Default to 1 if not specified
	}

	format, ok := config["format"].(string)
	if !ok {
		format = "%d" // Default format if not specified
	}

	return &SequenceConfig{
		SequenceField: sequenceField,
		Start:         start,
		Format:        format,
	}, nil
}

// parseGroupByConfig extracts and validates group by transformation configuration.
//
// This method parses configuration for grouping array elements by a specified field value.
// The transformation creates a new structure with items organized by their group key values.
//
// Required Configuration Fields:
//   - group_field: String path to the field to group by (e.g., "role", "type", "status")
//
// Output Structure:
// The transformation produces an array of group objects, each containing:
//   - group_key: The value that items in this group share
//   - items: Array of items that have this group_key value
//
// Parameters:
//   - config: Raw configuration map from the transformation specification
//
// Returns:
//   - *GroupByConfig: Validated configuration ready for grouping transformation
//   - error: Detailed error if group_field is missing or not a string
//
// Example Configuration:
//
//	{
//	  "group_field": "role"
//	}
//
// Example Output:
//
//	[
//	  {"group_key": "VICTIM", "items": [...]},
//	  {"group_key": "WITNESS", "items": [...]}
//	]
func (engine *TransformationEngine) parseGroupByConfig(config map[string]interface{}) (*GroupByConfig, error) {
	groupField, ok := config["group_field"].(string)
	if !ok {
		err := fmt.Errorf("group_field is required and must be a string")
		return nil, err
	}

	return &GroupByConfig{
		GroupField: groupField,
	}, nil
}

// parseSortConfig extracts and validates sort transformation configuration.
//
// This method parses configuration for sorting array elements by a specified field.
// It supports both ascending and descending sort orders with intelligent type handling
// for numeric and string comparisons.
//
// Required Configuration Fields:
//   - sort_field: String path to the field to sort by (e.g., "age", "name", "created_at")
//
// Optional Configuration Fields:
//   - sort_order: String "asc" or "desc" (defaults to "asc")
//
// Sort Behavior:
//   - Numeric values are sorted numerically
//   - String values are sorted lexicographically
//   - Nil values are sorted to the beginning (asc) or end (desc)
//   - Mixed types fall back to string comparison
//
// Parameters:
//   - config: Raw configuration map from the transformation specification
//
// Returns:
//   - *SortConfig: Validated configuration with defaults applied
//   - error: Detailed error if sort_field is missing or invalid
//
// Example Configuration:
//
//	{
//	  "sort_field": "demographics.age",
//	  "sort_order": "desc"
//	}
func (engine *TransformationEngine) parseSortConfig(config map[string]interface{}) (*SortConfig, error) {
	sortField, ok := config["sort_field"].(string)
	if !ok {
		err := fmt.Errorf("sort_field is required and must be a string")
		return nil, err
	}

	sortOrder, ok := config["sort_order"].(string)
	if !ok {
		sortOrder = "asc" // Default to ascending if not specified
	}

	return &SortConfig{
		SortField: sortField,
		SortOrder: sortOrder,
	}, nil
}

// Helper methods for data processing

// toArray converts various input types to a standardized []interface{} array format.
//
// This utility method handles the conversion of different Go types (slices, arrays)
// to a uniform array format that can be processed by transformation operations.
// It uses reflection to handle any array-like type generically.
//
// Supported Input Types:
//   - []interface{}: Already in correct format, converted element by element
//   - []string, []int, []map[string]interface{}, etc.: Any typed slice
//   - [N]T: Fixed-size arrays of any type
//
// Parameters:
//   - data: Input data that should be array-like
//
// Returns:
//   - []interface{}: Standardized array with each element as interface{}
//   - error: Type error if input is not array-like
//
// Example Usage:
//
//	arr, err := engine.toArray([]string{"a", "b", "c"})
//	// Returns: []interface{}{"a", "b", "c"}
func (engine *TransformationEngine) toArray(data interface{}) ([]interface{}, error) {
	value := reflect.ValueOf(data)
	if value.Kind() != reflect.Slice && value.Kind() != reflect.Array {
		err := fmt.Errorf("expected array or slice, got %T", data)
		return nil, err
	}

	result := make([]interface{}, value.Len())
	for arrayIndex := 0; arrayIndex < value.Len(); arrayIndex++ {
		result[arrayIndex] = value.Index(arrayIndex).Interface()
	}

	return result, nil
}

// evaluateFilterCondition checks if an item matches the specified filter criteria.
//
// This method implements the core filtering logic for array transformations, evaluating
// various comparison operators against field values. It handles different data types
// intelligently and provides comprehensive operator support for complex filtering needs.
//
// Supported Operators:
//   - equals: Exact match using compareValues for type-aware comparison
//   - not_equals: Inverse of equals
//   - contains: Substring search (strings only)
//   - starts_with: Prefix matching (strings only)
//   - ends_with: Suffix matching (strings only)
//   - in: Value exists in provided array
//   - not_in: Value doesn't exist in provided array
//   - greater_than, less_than: Numeric comparisons with string fallback
//   - greater_than_or_equal, less_than_or_equal: Inclusive numeric comparisons
//
// Special Handling:
//   - Nil field values: Always return false (don't match any filter)
//   - Missing fields: Error is propagated from path resolution
//   - Type mismatches: Appropriate errors for operator-specific requirements
//
// Parameters:
//   - item: The array element to evaluate
//   - filterConfig: Parsed filter configuration with field, operator, and value
//
// Returns:
//   - bool: True if item matches filter criteria, false otherwise
//   - error: Detailed error for invalid operations or type mismatches
//
// Example:
//
//	matches, err := engine.evaluateFilterCondition(
//	    map[string]interface{}{"role": "VICTIM", "age": 25},
//	    &FilterConfig{Field: "role", Operator: "equals", Value: "VICTIM"},
//	)
//	// Returns: true, nil
func (engine *TransformationEngine) evaluateFilterCondition(

	item interface{},
	filterConfig *FilterConfig,
) (bool, error) {
	// Get the field value from the item
	fieldValue, err := engine.pathResolver.GetValue(item, filterConfig.Field)
	if err != nil {
		return false, err
	}

	// Handle nil field values gracefully - they don't match any filters
	if fieldValue == nil {
		return false, nil
	}

	// Evaluate the condition based on the operator
	switch filterConfig.Operator {
	case "equals":
		return engine.compareValues(fieldValue, filterConfig.Value) == 0, nil
	case "not_equals":
		return engine.compareValues(fieldValue, filterConfig.Value) != 0, nil
	case "contains":
		if fieldStr, ok := fieldValue.(string); ok {
			if valueStr, ok := filterConfig.Value.(string); ok {
				return strings.Contains(fieldStr, valueStr), nil
			}
		}
		err := fmt.Errorf("contains operator requires string values")
		return false, err
	case "starts_with":
		if fieldStr, ok := fieldValue.(string); ok {
			if valueStr, ok := filterConfig.Value.(string); ok {
				return strings.HasPrefix(fieldStr, valueStr), nil
			}
		}
		err := fmt.Errorf("starts_with operator requires string values")
		return false, err
	case "ends_with":
		if fieldStr, ok := fieldValue.(string); ok {
			if valueStr, ok := filterConfig.Value.(string); ok {
				return strings.HasSuffix(fieldStr, valueStr), nil
			}
		}
		err := fmt.Errorf("ends_with operator requires string values")
		return false, err
	case "in":
		if valueArray, ok := filterConfig.Value.([]interface{}); ok {
			for _, arrayValue := range valueArray {
				if engine.compareValues(fieldValue, arrayValue) == 0 {
					return true, nil
				}
			}
			return false, nil
		}
		err := fmt.Errorf("in operator requires array value")
		return false, err
	case "not_in":
		if valueArray, ok := filterConfig.Value.([]interface{}); ok {
			for _, arrayValue := range valueArray {
				if engine.compareValues(fieldValue, arrayValue) == 0 {
					return false, nil
				}
			}
			return true, nil
		}
		err := fmt.Errorf("not_in operator requires array value")
		return false, err
	case "greater_than":
		return engine.compareValues(fieldValue, filterConfig.Value) > 0, nil
	case "less_than":
		return engine.compareValues(fieldValue, filterConfig.Value) < 0, nil
	case "greater_than_or_equal":
		return engine.compareValues(fieldValue, filterConfig.Value) >= 0, nil
	case "less_than_or_equal":
		return engine.compareValues(fieldValue, filterConfig.Value) <= 0, nil
	default:
		err := fmt.Errorf("unknown filter operator: %s", filterConfig.Operator)
		return false, err
	}
}

// compareValues performs intelligent comparison between two values of potentially different types.
//
// This method implements a sophisticated comparison algorithm that handles various
// Go types appropriately. It attempts numeric comparison first for string representations
// of numbers, then falls back to lexicographic string comparison. This ensures
// consistent ordering across different data types.
//
// Comparison Rules:
//  1. Nil handling: nil < any non-nil value; nil == nil
//  2. Numeric comparison: If both values parse as float64, compare numerically
//  3. String comparison: Fall back to lexicographic comparison of string representations
//
// Return Values:
//   - -1: leftValue < rightValue
//   - 0: leftValue == rightValue
//   - 1: leftValue > rightValue
//
// Type Handling Examples:
//   - compareValues(10, 2) returns 1 (numeric: 10 > 2)
//   - compareValues("10", "2") returns 1 (numeric: 10 > 2, not string "10" < "2")
//   - compareValues("abc", "def") returns -1 (string: "abc" < "def")
//   - compareValues(nil, "any") returns -1 (nil < non-nil)
//
// Parameters:
//   - leftValue: First value to compare
//   - rightValue: Second value to compare
//
// Returns:
//   - int: Comparison result (-1, 0, or 1)
//
// This method is used by sort and filter transformations to ensure consistent
// ordering and comparison behavior across the transformation system.
func (engine *TransformationEngine) compareValues(leftValue, rightValue interface{}) int {
	// Handle nil values
	if leftValue == nil && rightValue == nil {
		return 0
	}
	if leftValue == nil {
		return -1
	}
	if rightValue == nil {
		return 1
	}

	// Convert to strings for comparison
	leftString := fmt.Sprintf("%v", leftValue)
	rightString := fmt.Sprintf("%v", rightValue)

	// Try numeric comparison first
	if leftNumber, leftParseError := strconv.ParseFloat(leftString, 64); leftParseError == nil {
		if rightNumber, rightParseError := strconv.ParseFloat(rightString, 64); rightParseError == nil {
			if leftNumber < rightNumber {
				return -1
			}
			if leftNumber > rightNumber {
				return 1
			}
			return 0
		}
	}

	// Fall back to string comparison
	return strings.Compare(leftString, rightString)
}
