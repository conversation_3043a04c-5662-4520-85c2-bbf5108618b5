package extractors

import (
	"context"
	"database/sql"

	reports "proto/hero/reports/v2"
	assetsRepository "workflow/internal/assets/data"
	entityRepository "workflow/internal/entity/data"
	"workflow/internal/etl/extraction/types"
	propertyRepository "workflow/internal/property/data"
	reportsRepository "workflow/internal/reports/data"
)

// ExtractHydratedSections extracts sections with full hydration for a report.
// This function retrieves all sections from a report and provides comprehensive
// hydration of referenced entities and assets within each section.
//
// This provides type-safe access to section data while maintaining all
// hydration context about entities and assets referenced within sections.
//
// Parameters:
//   - ctx: Context for database operations
//   - databaseTransaction: Database transaction for consistency
//   - reportProtobuf: The source report protobuf containing section references
//   - reportsRepo: Repository for accessing report sections
//   - assetsRepo: Repository for accessing asset data
//   - entitiesRepo: Repository for accessing entity data
//
// Returns:
//   - []*types.HydratedSection: Sections with full hydration context
//   - error: Any errors encountered during section retrieval
func ExtractHydratedSections(
	ctx context.Context,
	databaseTransaction *sql.Tx,
	reportProtobuf *reports.Report,
	reportsRepo reportsRepository.ReportRepository,
	assetsRepo assetsRepository.AssetRepository,
	entitiesRepo entityRepository.EntityRepository,
	propertyRepo propertyRepository.PropertyRepository,
) ([]*types.HydratedSection, error) {
	if reportsRepo == nil {
		return []*types.HydratedSection{}, nil // Return empty slice but no error for optional repository
	}

	var hydratedSections []*types.HydratedSection

	// First, check if sections are embedded in the protobuf message
	if len(reportProtobuf.Sections) > 0 {
		// Use embedded sections from protobuf
		for _, section := range reportProtobuf.Sections {
			extractHydratedSection(ctx, databaseTransaction, section, reportProtobuf.Id, assetsRepo, entitiesRepo, propertyRepo, &hydratedSections)
		}
	} else {
		// Fall back to database sections if none are embedded
		sections, err := reportsRepo.ListReportSections(ctx, databaseTransaction, reportProtobuf.Id)
		if err != nil {
			return nil, err
		}

		for _, section := range sections {
			extractHydratedSection(ctx, databaseTransaction, section, reportProtobuf.Id, assetsRepo, entitiesRepo, propertyRepo, &hydratedSections)
		}
	}

	return hydratedSections, nil
}

// extractHydratedSection processes a single section and adds it to the hydrated sections list
func extractHydratedSection(
	ctx context.Context,
	databaseTransaction *sql.Tx,
	section *reports.ReportSection,
	reportId string,
	assetsRepo assetsRepository.AssetRepository,
	entitiesRepo entityRepository.EntityRepository,
	propertyRepo propertyRepository.PropertyRepository,
	hydratedSections *[]*types.HydratedSection,
) {
	hydratedSection := &types.HydratedSection{
		Section: section,
		ExtractionContext: map[string]interface{}{
			"extractedFrom": "report_sections",
			"reportId":      reportId,
			"sectionId":     section.Id,
			"sectionType":   section.Type.String(),
		},
	}

	// Extract entities and assets referenced in this section based on section type
	switch section.Type {
	case reports.SectionType_SECTION_TYPE_ENTITY_LIST_PEOPLE,
		reports.SectionType_SECTION_TYPE_ENTITY_LIST_VEHICLE,
		reports.SectionType_SECTION_TYPE_ENTITY_LIST_PROPERTIES,
		reports.SectionType_SECTION_TYPE_ENTITY_LIST_ORGANIZATIONS:
		// Extract entities from entity list sections
		if entitiesRepo != nil {
			hydratedSection.ReferencedEntities = extractEntitiesFromEntityListSection(
				ctx, databaseTransaction, section, entitiesRepo)
		}

	case reports.SectionType_SECTION_TYPE_INCIDENT_DETAILS:
		// Extract assets from incident details (reporting person, responders)
		if assetsRepo != nil {
			hydratedSection.ReferencedAssets = extractAssetsFromIncidentDetailsSection(
				ctx, databaseTransaction, section, assetsRepo)
		}

	case reports.SectionType_SECTION_TYPE_ARREST:
		// Extract entities from arrest sections (arrestees)
		if entitiesRepo != nil {
			hydratedSection.ReferencedEntities = extractEntitiesFromArrestSection(
				ctx, databaseTransaction, section, entitiesRepo)
		}

	case reports.SectionType_SECTION_TYPE_OFFENSE:
		// Extract entities from offense sections (victims, suspects)
		if entitiesRepo != nil {
			hydratedSection.ReferencedEntities = extractEntitiesFromOffenseSection(
				ctx, databaseTransaction, section, entitiesRepo)
		}

	case reports.SectionType_SECTION_TYPE_PROPERTY:
		// Extract properties from property list sections
		if propertyRepo != nil {
			hydratedSection.ReferencedProperties = extractPropertiesFromPropertyListSection(
				ctx, databaseTransaction, section, propertyRepo)
		}

	default:
		// No specific extraction for other section types
	}

	*hydratedSections = append(*hydratedSections, hydratedSection)
}

// extractPropertiesFromPropertyListSection extracts properties from property list sections.
func extractPropertiesFromPropertyListSection(
	ctx context.Context,
	databaseTransaction *sql.Tx,
	section *reports.ReportSection,
	propertyRepo propertyRepository.PropertyRepository,
) []*types.HydratedProperty {
	var hydratedProperties []*types.HydratedProperty

	// Extract properties from property list content
	if section.GetPropertyList() != nil {
		propertyList := section.GetPropertyList()
		for _, propertyRef := range propertyList.PropertyRefs {
			if propertyRef.Id != "" {
				property, err := propertyRepo.GetProperty(ctx, databaseTransaction, propertyRef.Id)
				if err == nil {
					hydratedProperties = append(hydratedProperties, &types.HydratedProperty{
						Property:                property,
						ReferencedInSectionId:   section.Id,
						ReferencedInSectionType: section.Type.String(),
						ExtractionMetadata: map[string]interface{}{
							"extractedFrom": "property_list_section",
							"sectionId":     section.Id,
							"sectionType":   section.Type.String(),
							"propertyRefId": propertyRef.Id,
							"displayName":   propertyRef.DisplayName,
							"relationType":  propertyRef.RelationType,
							"version":       propertyRef.Version,
						},
					})
				}
			}
		}
	}

	return hydratedProperties
}

// extractEntitiesFromEntityListSection extracts entities from entity list sections.
func extractEntitiesFromEntityListSection(
	ctx context.Context,
	databaseTransaction *sql.Tx,
	section *reports.ReportSection,
	entitiesRepo entityRepository.EntityRepository,
) []*types.HydratedEntity {
	var hydratedEntities []*types.HydratedEntity

	// Extract entities from entity list content
	if section.GetEntityList() != nil {
		entityList := section.GetEntityList()
		for _, entityRef := range entityList.EntityRefs {
			if entityRef.Id != "" {
				entity, err := entitiesRepo.GetLatestEntity(ctx, databaseTransaction, entityRef.Id)
				if err == nil {
					hydratedEntities = append(hydratedEntities, &types.HydratedEntity{
						Entity:                  entity,
						ReferencedInSectionId:   section.Id,
						ReferencedInSectionType: section.Type.String(),
						ExtractionMetadata: map[string]interface{}{
							"extractedFrom": "entity_list_section",
							"sectionId":     section.Id,
							"sectionType":   section.Type.String(),
							"entityRefId":   entityRef.Id,
							"displayName":   entityRef.DisplayName,
							"relationType":  entityRef.RelationType,
							"version":       entityRef.Version,
						},
					})
				}
			}
		}
	}

	return hydratedEntities
}

// extractAssetsFromIncidentDetailsSection extracts assets from incident details sections.
func extractAssetsFromIncidentDetailsSection(
	ctx context.Context,
	databaseTransaction *sql.Tx,
	section *reports.ReportSection,
	assetsRepo assetsRepository.AssetRepository,
) []*types.HydratedAsset {
	var hydratedAssets []*types.HydratedAsset

	// Extract assets from incident details content
	if section.GetIncidentDetails() != nil {
		incidentDetails := section.GetIncidentDetails()

		// Extract reporting person asset
		if incidentDetails.ReportingPerson != nil && incidentDetails.ReportingPerson.AssetId != "" {
			asset, err := assetsRepo.GetAsset(ctx, databaseTransaction, incidentDetails.ReportingPerson.AssetId)
			if err == nil {
				hydratedAssets = append(hydratedAssets, &types.HydratedAsset{
					Asset: asset,
					Role:  "reporting_person",
					ExtractionContext: map[string]interface{}{
						"extractedFrom": "incident_details_reporting_person",
						"sectionId":     section.Id,
						"sectionType":   section.Type.String(),
						"assetId":       asset.Id,
						"context":       "reporting_person",
					},
				})
			}
		}

		// Extract responder assets
		for i, responder := range incidentDetails.Responders {
			if responder.AssetId != "" {
				asset, err := assetsRepo.GetAsset(ctx, databaseTransaction, responder.AssetId)
				if err == nil {
					hydratedAssets = append(hydratedAssets, &types.HydratedAsset{
						Asset: asset,
						Role:  "responder",
						ExtractionContext: map[string]interface{}{
							"extractedFrom":  "incident_details_responder",
							"sectionId":      section.Id,
							"sectionType":    section.Type.String(),
							"assetId":        asset.Id,
							"context":        "responder",
							"responderIndex": i,
							"responderRole":  responder.Role,
							"responderName":  responder.DisplayName,
						},
					})
				}
			}
		}
	}

	return hydratedAssets
}

// extractEntitiesFromArrestSection extracts entities from arrest sections.
func extractEntitiesFromArrestSection(
	ctx context.Context,
	databaseTransaction *sql.Tx,
	section *reports.ReportSection,
	entitiesRepo entityRepository.EntityRepository,
) []*types.HydratedEntity {
	var hydratedEntities []*types.HydratedEntity

	// Extract entities from arrest content
	if section.GetArrestList() != nil {
		arrestList := section.GetArrestList()
		for _, arrest := range arrestList.Arrests {
			if arrest.Data != nil {
				// Check if arrestee is referenced as an entity
				if arresteeId := getStringFromProtobufStruct(arrest.Data, "arresteeId"); arresteeId != "" {
					entity, err := entitiesRepo.GetLatestEntity(ctx, databaseTransaction, arresteeId)
					if err == nil {
						hydratedEntities = append(hydratedEntities, &types.HydratedEntity{
							Entity:                  entity,
							ReferencedInSectionId:   section.Id,
							ReferencedInSectionType: section.Type.String(),
							ExtractionMetadata: map[string]interface{}{
								"extractedFrom": "arrest_section_arrestee",
								"sectionId":     section.Id,
								"sectionType":   section.Type.String(),
								"arrestId":      arrest.Id,
								"context":       "arrestee",
							},
						})
					}
				}
			}
		}
	}

	return hydratedEntities
}

// extractEntitiesFromOffenseSection extracts entities from offense sections.
func extractEntitiesFromOffenseSection(
	ctx context.Context,
	databaseTransaction *sql.Tx,
	section *reports.ReportSection,
	entitiesRepo entityRepository.EntityRepository,
) []*types.HydratedEntity {
	var hydratedEntities []*types.HydratedEntity

	// Extract entities from offense content
	if section.GetOffenseList() != nil {
		offenseList := section.GetOffenseList()
		for _, offense := range offenseList.Offenses {
			if offense.Data != nil {
				// Check for various entity references in offense data
				entityFields := []string{"victimId", "suspectId", "witnessId", "reportingPersonId"}

				for _, field := range entityFields {
					if entityId := getStringFromProtobufStruct(offense.Data, field); entityId != "" {
						entity, err := entitiesRepo.GetLatestEntity(ctx, databaseTransaction, entityId)
						if err == nil {
							hydratedEntities = append(hydratedEntities, &types.HydratedEntity{
								Entity:                  entity,
								ReferencedInSectionId:   section.Id,
								ReferencedInSectionType: section.Type.String(),
								ExtractionMetadata: map[string]interface{}{
									"extractedFrom": "offense_section_entity",
									"sectionId":     section.Id,
									"sectionType":   section.Type.String(),
									"offenseId":     offense.Id,
									"context":       field,
								},
							})
						}
					}
				}
			}
		}
	}

	return hydratedEntities
}

// GetSectionsByType filters hydrated sections by section type.
func GetSectionsByType(hydratedSections []*types.HydratedSection, sectionType reports.SectionType) []*types.HydratedSection {
	var filtered []*types.HydratedSection
	for _, hydratedSection := range hydratedSections {
		if hydratedSection.Section.Type == sectionType {
			filtered = append(filtered, hydratedSection)
		}
	}
	return filtered
}

// GetSectionById finds a section by its ID.
func GetSectionById(hydratedSections []*types.HydratedSection, sectionId string) *types.HydratedSection {
	for _, hydratedSection := range hydratedSections {
		if hydratedSection.Section.Id == sectionId {
			return hydratedSection
		}
	}
	return nil
}

// GetAllReferencedEntities returns all entities referenced across all sections.
func GetAllReferencedEntities(hydratedSections []*types.HydratedSection) []*types.HydratedEntity {
	var allEntities []*types.HydratedEntity
	for _, section := range hydratedSections {
		allEntities = append(allEntities, section.ReferencedEntities...)
	}
	return allEntities
}

// GetAllReferencedAssets returns all assets referenced across all sections.
func GetAllReferencedAssets(hydratedSections []*types.HydratedSection) []*types.HydratedAsset {
	var allAssets []*types.HydratedAsset
	for _, section := range hydratedSections {
		allAssets = append(allAssets, section.ReferencedAssets...)
	}
	return allAssets
}
