// Package types defines the core data structures for the ETL extraction layer.
// These types provide a type-safe interface between the extraction and transformation layers,
// replacing the previous generic map[string]interface{} approach with strongly typed structs.
//
// The extraction layer is responsible for:
//   - Retrieving data from multiple repositories (reports, assets, entities, situations)
//   - Hydrating relationships between objects (e.g., embedding full asset data in reports)
//   - Maintaining referential context (e.g., which section an entity is referenced in)
//   - Providing parallel extraction capabilities for performance
//
// Key design principles:
//   - Type safety: All data is represented using protobuf-generated types
//   - Hydration: Related objects are fully embedded, not just referenced by ID
//   - Context preservation: Extraction metadata maintains relationship context
//   - Performance: Supports parallel extraction without sacrificing data integrity
//   - Independence: Extraction layer is completely independent of transformation layer
package types

import (
	"time"

	assetsv2 "proto/hero/assets/v2"
	entityv1 "proto/hero/entity/v1"
	propertyv1 "proto/hero/property/v1"
	reportsv2 "proto/hero/reports/v2"
	situationsv2 "proto/hero/situations/v2"
)

// HydratedReport represents a fully hydrated report with all related data embedded.
//
// In the Hero system, reports are the primary data structure that connects various
// resources (assets, entities, situations) together. This struct provides a complete
// view of a report with all its relationships resolved and embedded.
//
// Example usage:
//
//	hydratedReport := &HydratedReport{
//	    Report: reportProtobuf,
//	    AuthorAsset: authorAssetData,
//	    CreatorAsset: creatorAssetData,
//	    ConnectedSituation: situationData,
//	    WatcherAssets: []*assetsv2.Asset{watcher1, watcher2},
//	}
type HydratedReport struct {
	// Report is the base protobuf report containing all core report data including
	// sections, relations, and metadata. This is never nil in a valid hydrated report.
	Report *reportsv2.Report `json:"report"`

	// AuthorAsset is the fully hydrated asset data for the report author.
	// This replaces the author_asset_id reference with the complete asset object.
	// May be nil if the author asset cannot be retrieved or doesn't exist.
	AuthorAsset *assetsv2.Asset `json:"authorAsset,omitempty"`

	// CreatorAsset is the fully hydrated asset data for who created the report.
	// This replaces the created_by_asset_id reference with the complete asset object.
	// May be nil if the creator asset cannot be retrieved or doesn't exist.
	CreatorAsset *assetsv2.Asset `json:"creatorAsset,omitempty"`

	// ConnectedSituation is the primary situation this report is associated with.
	// This replaces the situation_id reference with the complete situation object.
	// May be nil if the report is not connected to a situation.
	ConnectedSituation *situationsv2.Situation `json:"connectedSituation,omitempty"`

	// WatcherAssets contains the fully hydrated assets for all watchers of this report.
	// This expands the watcher_asset_ids array into complete asset objects.
	// The array preserves the original order from watcher_asset_ids.
	WatcherAssets []*assetsv2.Asset `json:"watcherAssets,omitempty"`

	// HydrationMetadata contains additional context about the hydration process,
	// such as extraction timestamps, methods used, and any custom metadata.
	// Common keys include: "extractedAt", "extractionMethod", "reportId"
	HydrationMetadata map[string]interface{} `json:"hydrationMetadata,omitempty"`
}

// HydratedAsset represents an asset with its role context within a report.
//
// Assets in the Hero system represent users, officers, or other personnel. When
// extracted in the context of a report, each asset has a specific role that
// defines their relationship to the report.
//
// Supported roles:
//   - "creator": The asset that created the report
//   - "author": The asset that authored/wrote the report content
//   - "watcher": Assets that are watching/monitoring the report
//
// Example usage:
//
//	hydratedAsset := &HydratedAsset{
//	    Asset: officerAsset,
//	    Role: "author",
//	    ExtractionContext: map[string]interface{}{
//	        "extracted_from": "report_author_link",
//	        "report_id": "123",
//	    },
//	}
type HydratedAsset struct {
	// Asset is the complete protobuf asset data including all fields like
	// name, contact info, location, status, and additional metadata.
	// This is never nil in a valid hydrated asset.
	Asset *assetsv2.Asset `json:"asset"`

	// Role defines the asset's relationship to the report.
	// Valid values: "creator", "author", "watcher"
	// This field is always populated for report-related assets.
	Role string `json:"role"`

	// ExtractionContext contains metadata about how and where this asset
	// was extracted from. Common keys include:
	//   - "extracted_from": Source of extraction (e.g., "report_author_link")
	//   - "report_id": The report this asset is associated with
	//   - "assetId": The asset's unique identifier
	//   - "watcherIndex": For watcher assets, their position in the watcher array
	ExtractionContext map[string]interface{} `json:"extractionContext,omitempty"`
}

// HydratedEntity represents an entity with its section reference context.
//
// Entities in the Hero system represent people, vehicles, organizations, or other
// objects involved in incidents. When extracted from a report, each entity maintains
// context about which section(s) it was referenced in.
//
// Entity types include:
//   - ENTITY_TYPE_PERSON: People involved (victims, suspects, witnesses, officers)
//   - ENTITY_TYPE_VEHICLE: Vehicles involved in incidents
//   - ENTITY_TYPE_ORGANIZATION: Banks, businesses, agencies
//   - ENTITY_TYPE_PROPERTY: Physical property or evidence
//
// Example usage:
//
//	hydratedEntity := &HydratedEntity{
//	    Entity: suspectEntity,
//	    ReferencedInSectionId: "arrest-section-123",
//	    ReferencedInSectionType: "SECTION_TYPE_ARREST",
//	    ExtractionMetadata: map[string]interface{}{
//	        "extractedFrom": "arrest_arrestee_ref",
//	        "context": "arrestee",
//	    },
//	}
type HydratedEntity struct {
	// Entity is the complete protobuf entity data including all fields like
	// entity type, data payload, tags, and version information.
	// This is never nil in a valid hydrated entity.
	Entity *entityv1.Entity `json:"entity"`

	// ReferencedInSectionId identifies which report section this entity was
	// referenced in. This helps maintain context about where in the report
	// structure this entity appears. May be empty for entities not tied to sections.
	ReferencedInSectionId string `json:"referencedInSectionId,omitempty"`

	// ReferencedInSectionType specifies the type of section this entity was
	// referenced in (e.g., "SECTION_TYPE_ENTITY_LIST_PEOPLE", "SECTION_TYPE_ARREST").
	// This provides additional context about the entity's role in the report.
	ReferencedInSectionType string `json:"referencedInSectionType,omitempty"`

	// ExtractionMetadata contains detailed information about how this entity
	// was extracted. Common keys include:
	//   - "extractedFrom": Source type (e.g., "section_entity_refs", "arrest_arrestee_ref")
	//   - "sectionId": The section containing this entity reference
	//   - "sectionType": The type of section
	//   - "relationType": The entity's role (e.g., "victim", "suspect", "witness")
	//   - "context": Additional context (e.g., "arrestee")
	//   - "sectionReferences": Array of all sections this entity appears in
	ExtractionMetadata map[string]interface{} `json:"extractionMetadata,omitempty"`
}

// HydratedProperty represents a property with full hydration context.
//
// This type provides complete property data along with metadata about how
// the property was extracted and referenced within a report section.
//
// Example usage:
//
//	hydratedProperty := &HydratedProperty{
//	    Property: propertyRecord,
//	    ReferencedInSectionId: "section-123",
//	    ReferencedInSectionType: "SECTION_TYPE_PROPERTY",
//	    ExtractionMetadata: map[string]interface{}{
//	        "extractedFrom": "property_list_section",
//	        "propertyRefId": "property-456",
//	    },
//	}
type HydratedProperty struct {
	// Property is the complete protobuf property data including all fields like
	// property type, status, custody chain, and metadata.
	// This is never nil in a valid hydrated property.
	Property *propertyv1.Property `json:"property"`

	// ReferencedInSectionId identifies which report section this property was
	// referenced in. This helps maintain context about where in the report
	// structure this property appears. May be empty for properties not tied to sections.
	ReferencedInSectionId string `json:"referencedInSectionId,omitempty"`

	// ReferencedInSectionType specifies the type of section this property was
	// referenced in (e.g., "SECTION_TYPE_PROPERTY").
	// This provides additional context about the property's role in the report.
	ReferencedInSectionType string `json:"referencedInSectionType,omitempty"`

	// ExtractionMetadata contains detailed information about how this property
	// was extracted. Common keys include:
	//   - "extractedFrom": Source type (e.g., "property_list_section")
	//   - "sectionId": The section containing this property reference
	//   - "sectionType": The type of section
	//   - "propertyRefId": The property reference ID
	//   - "displayName": The property's display name
	//   - "relationType": The property's relation type
	//   - "version": The property's version
	ExtractionMetadata map[string]interface{} `json:"extractionMetadata,omitempty"`
}

// HydratedRelationship represents a relationship between objects with resolved names.
//
// Relationships in the Hero system define connections between different objects
// (entities, assets, situations). This struct enhances the base relation with
// human-readable names and types for easier consumption.
//
// Common relationship types:
//   - "RELATION_TYPE_VICTIM_OFFENDER_ST": Victim-offender relationship
//   - "worked_with": Professional relationship
//   - "used_in_crime": Object used in criminal activity
//   - "employs": Employment relationship
//   - "assigned_to": Assignment relationship (often asset to situation)
//
// Example usage:
//
//	hydratedRelation := &HydratedRelationship{
//	    Relation: relationProtobuf,
//	    FromObjectName: "John Suspect",
//	    FromObjectType: "entity",
//	    ToObjectName: "2020 Honda Civic",
//	    ToObjectType: "entity",
//	}
type HydratedRelationship struct {
	// Relation is the complete protobuf relation data including both objects,
	// relationship type, description, and metadata.
	// This is never nil in a valid hydrated relationship.
	Relation *reportsv2.Relation `json:"relation"`

	// FromObjectName is the human-readable name of the first object in the
	// relationship. This is resolved from the object's display name or title.
	// Examples: "Jane Victim", "Detective Smith", "First National Bank"
	FromObjectName string `json:"fromObjectName,omitempty"`

	// ToObjectName is the human-readable name of the second object in the
	// relationship. This is resolved from the object's display name or title.
	// Examples: "John Suspect", "Armed Robbery Investigation", "2020 Honda Civic"
	ToObjectName string `json:"toObjectName,omitempty"`

	// FromObjectType identifies the type of the first object.
	// Valid values: "entity", "asset", "situation", "report"
	FromObjectType string `json:"fromObjectType,omitempty"`

	// ToObjectType identifies the type of the second object.
	// Valid values: "entity", "asset", "situation", "report"
	ToObjectType string `json:"toObjectType,omitempty"`

	// RelationshipContext contains additional metadata about the relationship
	// extraction. Common keys include:
	//   - "extractedFrom": Source of extraction (e.g., "report_relations")
	//   - "relationId": The relationship's unique identifier
	//   - "reportId": The report containing this relationship
	RelationshipContext map[string]interface{} `json:"relationshipContext,omitempty"`
}

// HydratedSection represents a report section with all referenced data hydrated.
//
// Sections are the building blocks of reports in the Hero system. Each section
// contains specific types of information (narrative, incident details, arrests, etc.)
// and may reference entities and assets. This struct provides a complete view of
// a section with all its references resolved.
//
// Common section types:
//   - SECTION_TYPE_NARRATIVE: Free-text narrative description
//   - SECTION_TYPE_INCIDENT_DETAILS: Structured incident information
//   - SECTION_TYPE_ENTITY_LIST_PEOPLE: List of people involved
//   - SECTION_TYPE_ENTITY_LIST_VEHICLE: List of vehicles involved
//   - SECTION_TYPE_ENTITY_LIST_ORGANIZATIONS: List of organizations
//   - SECTION_TYPE_ARREST: Arrest information
//   - SECTION_TYPE_OFFENSE: Offense details
//   - SECTION_TYPE_MEDIA: Media attachments and evidence
//
// Example usage:
//
//	hydratedSection := &HydratedSection{
//	    Section: arrestSection,
//	    ReferencedEntities: []*HydratedEntity{arresteeEntity},
//	    ExtractionContext: map[string]interface{}{
//	        "sectionType": "SECTION_TYPE_ARREST",
//	    },
//	}
type HydratedSection struct {
	// Section is the complete protobuf section data including content,
	// metadata, and timestamps. The content varies by section type.
	// This is never nil in a valid hydrated section.
	Section *reportsv2.ReportSection `json:"section"`

	// ReferencedEntities contains all entities that are referenced within
	// this section. For example, an arrest section would include the arrestee,
	// while a people list section would include all listed people.
	// The entities maintain their reference context.
	ReferencedEntities []*HydratedEntity `json:"referencedEntities,omitempty"`

	// ReferencedAssets contains all assets that are referenced within
	// this section. For example, incident details might reference responding
	// officers or the reporting person's asset information.
	ReferencedAssets []*HydratedAsset `json:"referencedAssets,omitempty"`

	// ReferencedProperties contains all properties that are referenced within
	// this section. For example, property list sections would include all
	// listed properties with their reference context.
	ReferencedProperties []*HydratedProperty `json:"referencedProperties,omitempty"`

	// ExtractionContext contains metadata about the section extraction.
	// Common keys include:
	//   - "extractedFrom": Source of extraction (e.g., "report_sections")
	//   - "reportId": The report containing this section
	//   - "sectionId": The section's unique identifier
	//   - "sectionType": The type of section
	ExtractionContext map[string]interface{} `json:"extractionContext,omitempty"`
}

// ExtractedData represents the complete output of the extraction layer.
//
// This is the primary data structure that serves as the interface between the
// extraction and transformation layers in the ETL pipeline. It contains all
// extracted and hydrated data from a report, ready for transformation into
// the desired output format.
//
// The extraction layer ensures that:
//   - All data is fully hydrated (no unresolved references)
//   - All relationships are bi-directionally connected
//   - All context is preserved (section references, roles, etc.)
//   - Data integrity is maintained across parallel extraction
//
// Example usage:
//
//	extractedData := &ExtractedData{
//	    Reports: []*HydratedReport{hydratedReport},
//	    Assets: []*HydratedAsset{author, creator, watcher},
//	    Entities: []*HydratedEntity{suspect, victim, witness},
//	    Situations: []*situationsv2.Situation{investigation},
//	    ExtractedAt: time.Now(),
//	}
type ExtractedData struct {
	// Reports contains fully hydrated reports with all related data embedded.
	// Typically contains a single report, but the array structure supports
	// future multi-report extraction scenarios.
	Reports []*HydratedReport `json:"reports"`

	// Assets contains all assets extracted from the report with their role
	// context. Includes creators, authors, watchers, and any assets referenced
	// in sections (e.g., responding officers).
	Assets []*HydratedAsset `json:"assets"`

	// Entities contains all entities extracted from the report with their
	// section reference context. Includes people, vehicles, organizations,
	// and other objects referenced in the report.
	Entities []*HydratedEntity `json:"entities"`

	// Situations contains all situations connected to the report.
	// These are full protobuf objects representing incidents or events.
	Situations []*situationsv2.Situation `json:"situations"`

	// Sections contains all report sections with their referenced entities
	// and assets fully hydrated. Preserves the report's structure.
	Sections []*HydratedSection `json:"sections"`

	// Relationships contains all relationships defined in the report with
	// object names resolved for human readability.
	Relationships []*HydratedRelationship `json:"relationships"`

	// Metadata contains global extraction metadata. Common keys include:
	//   - "extractionMethod": Method used (e.g., "parallel", "sequential")
	//   - "reportId": The primary report ID being extracted
	//   - "aggregatedPhaseCount": Number of extraction phases completed
	Metadata map[string]interface{} `json:"metadata"`

	// ExtractedAt records when the extraction process completed.
	// This timestamp is set when all extraction phases are done.
	ExtractedAt time.Time `json:"extractedAt"`
}

// ExtractionWarning represents a non-fatal issue during extraction.
type ExtractionWarning struct {
	Code    string                 `json:"code"`
	Message string                 `json:"message"`
	Context map[string]interface{} `json:"context,omitempty"`
}

// ExtractionMetrics contains performance data about the extraction process.
type ExtractionMetrics struct {
	// Total extraction duration
	TotalDuration time.Duration `json:"totalDuration"`

	// Duration for different phases
	PhaseDurations map[string]time.Duration `json:"phaseDurations"`

	// Database query metrics
	QueryCount int           `json:"queryCount"`
	QueryTime  time.Duration `json:"queryTime"`

	// Memory usage metrics
	PeakMemoryMB int `json:"peakMemoryMB,omitempty"`

	// Items processed
	ItemsProcessed map[string]int `json:"itemsProcessed"`
}

// ExtractionResult represents the complete result of an extraction operation.
type ExtractionResult struct {
	// The extracted and hydrated data
	Data *ExtractedData `json:"data"`

	// Any warnings encountered during extraction
	Warnings []ExtractionWarning `json:"warnings,omitempty"`

	// Performance metrics for the extraction
	Metrics *ExtractionMetrics `json:"metrics,omitempty"`

	// Whether the extraction was successful
	Success bool `json:"success"`
}

// DataSetInfo provides metadata about extracted data sets.
type DataSetInfo struct {
	Name        string    `json:"name"`
	Type        string    `json:"type"`
	ItemCount   int       `json:"itemCount"`
	ExtractedAt time.Time `json:"extractedAt"`
}

// GetDataSetInfo returns metadata about each populated data set in the extraction.
//
// This method provides a summary view of what data was extracted, useful for
// logging, debugging, and understanding the extraction results at a glance.
//
// Returns a slice of DataSetInfo structs, one for each non-empty data set.
// Empty data sets are excluded from the results.
//
// Example output:
//
//	[
//	  {Name: "reports", Type: "report", ItemCount: 1, ExtractedAt: ...},
//	  {Name: "entities", Type: "entity", ItemCount: 6, ExtractedAt: ...},
//	  {Name: "assets", Type: "asset", ItemCount: 3, ExtractedAt: ...},
//	]
func (extractedData *ExtractedData) GetDataSetInfo() []DataSetInfo {
	var dataSetInfoList []DataSetInfo

	// Check each data set and add info if non-empty
	if len(extractedData.Reports) > 0 {
		dataSetInfoList = append(dataSetInfoList, DataSetInfo{
			Name:        "reports",
			Type:        "report",
			ItemCount:   len(extractedData.Reports),
			ExtractedAt: extractedData.ExtractedAt,
		})
	}

	if len(extractedData.Assets) > 0 {
		dataSetInfoList = append(dataSetInfoList, DataSetInfo{
			Name:        "assets",
			Type:        "asset",
			ItemCount:   len(extractedData.Assets),
			ExtractedAt: extractedData.ExtractedAt,
		})
	}

	if len(extractedData.Entities) > 0 {
		dataSetInfoList = append(dataSetInfoList, DataSetInfo{
			Name:        "entities",
			Type:        "entity",
			ItemCount:   len(extractedData.Entities),
			ExtractedAt: extractedData.ExtractedAt,
		})
	}

	if len(extractedData.Situations) > 0 {
		dataSetInfoList = append(dataSetInfoList, DataSetInfo{
			Name:        "situations",
			Type:        "situation",
			ItemCount:   len(extractedData.Situations),
			ExtractedAt: extractedData.ExtractedAt,
		})
	}

	if len(extractedData.Sections) > 0 {
		dataSetInfoList = append(dataSetInfoList, DataSetInfo{
			Name:        "sections",
			Type:        "section",
			ItemCount:   len(extractedData.Sections),
			ExtractedAt: extractedData.ExtractedAt,
		})
	}

	if len(extractedData.Relationships) > 0 {
		dataSetInfoList = append(dataSetInfoList, DataSetInfo{
			Name:        "relationships",
			Type:        "relationship",
			ItemCount:   len(extractedData.Relationships),
			ExtractedAt: extractedData.ExtractedAt,
		})
	}

	return dataSetInfoList
}

// GetTotalItemCount returns the total number of items across all data sets.
//
// This method provides a quick count of all extracted items, useful for
// metrics, logging, and validation purposes.
//
// The count includes:
//   - Reports (typically 1)
//   - Assets (creators, authors, watchers, responders)
//   - Entities (people, vehicles, organizations)
//   - Situations (connected incidents)
//   - Sections (report structure elements)
//   - Relationships (connections between objects)
//
// Example:
//
//	total := extractedData.GetTotalItemCount()
//	fmt.Printf("Extracted %d total items\n", total)
func (extractedData *ExtractedData) GetTotalItemCount() int {
	return len(extractedData.Reports) +
		len(extractedData.Assets) +
		len(extractedData.Entities) +
		len(extractedData.Situations) +
		len(extractedData.Sections) +
		len(extractedData.Relationships)
}
