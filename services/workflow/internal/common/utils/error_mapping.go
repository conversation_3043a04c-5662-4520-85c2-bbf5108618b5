package utils

import (
	"context"

	"common/herosentry"

	"connectrpc.com/connect"
)

// ToConnectError converts an error to a connect.Error with appropriate code based on ErrorType.
// This ensures consistent error code mapping across all services.
func ToConnectError(err error, errorType herosentry.ErrorType) error {
	if err == nil {
		return nil
	}

	// Map ErrorType to appropriate connect code
	var code connect.Code
	switch errorType {
	case herosentry.ErrorTypeValidation:
		code = connect.CodeInvalidArgument
	case herosentry.ErrorTypeNotFound:
		code = connect.CodeNotFound
	case herosentry.ErrorTypeUnauthorized:
		code = connect.CodeUnauthenticated
	case herosentry.ErrorTypeForbidden:
		code = connect.CodePermissionDenied
	case herosentry.ErrorTypeConflict:
		code = connect.CodeAlreadyExists
	case herosentry.ErrorTypeDatabase:
		// Database errors are internal server errors
		code = connect.CodeInternal
	case herosentry.ErrorTypeExternal:
		// External service failures are unavailable
		code = connect.CodeUnavailable
	case herosentry.ErrorTypeInternal:
		code = connect.CodeInternal
	default:
		// Unknown errors default to internal
		code = connect.CodeInternal
	}

	return connect.NewError(code, err)
}

// CaptureAndReturnError is a helper that captures an exception and returns a connect error.
// This reduces boilerplate in handlers.
func CaptureAndReturnError(ctx context.Context, err error, errorType herosentry.ErrorType, message string) error {
	if err == nil {
		return nil
	}

	// Capture to Sentry with the error type
	herosentry.CaptureException(ctx, err, errorType, message)

	// Return appropriate connect error
	return ToConnectError(err, errorType)
}
