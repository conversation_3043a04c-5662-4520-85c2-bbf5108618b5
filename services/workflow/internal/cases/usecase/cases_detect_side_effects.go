// cases_detect_side_effects.go
package usecase

import (
	casespb "proto/hero/cases/v1"
)

// CaseSideEffectType indicates kinds of side effects triggered by case changes.
// All side effects are "Try" operations - they attempt the action but only execute if conditions are met.
type CaseSideEffectType int

const (
	// No side effect to process.
	CaseSideEffect_None CaseSideEffectType = iota

	// TryCreateManageCaseOrder attempts to create a MANAGE_CASE order for the current primary investigator.
	// Suggested when: Primary investigator is set (new assignment or reassignment) OR case is reactivated from terminal state with an investigator.
	// Execution: Cancels any existing MANAGE_CASE orders and creates a new order for the current investigator.
	CaseSideEffect_TryCreateManageCaseOrder

	// TryCompleteAllCaseOrders attempts to complete all non-terminal orders for the case.
	// Suggested when: Investigator is removed OR case moves to terminal state (ON_HOLD, RESOLVED, CLOSED, ARCHIVED).
	// Execution: Completes all non-terminal orders for the case.
	CaseSideEffect_TryCompleteAllCaseOrders
)

// CaseSideEffectChecker evaluates a Case update and returns side-effects that should be triggered.
type CaseSideEffectChecker struct{}

// isTerminalStatus returns true if the case status is terminal (orders should be completed).
// Terminal states are: ON_HOLD, RESOLVED, CLOSED, ARCHIVED
func isTerminalStatus(status casespb.CaseStatus) bool {
	switch status {
	case casespb.CaseStatus_CASE_STATUS_ON_HOLD,
		casespb.CaseStatus_CASE_STATUS_RESOLVED,
		casespb.CaseStatus_CASE_STATUS_CLOSED,
		casespb.CaseStatus_CASE_STATUS_ARCHIVED:
		return true
	default:
		return false
	}
}

// DetectSideEffects is the main entry point that compares previous and current case states
// and returns all side effects that should be triggered.
func (checker *CaseSideEffectChecker) DetectSideEffects(
	previousCase *casespb.Case,
	currentCase *casespb.Case,
) []CaseSideEffectType {
	effectsMap := make(map[CaseSideEffectType]bool)

	// Detect investigator assignment changes
	previousInvestigator := checker.findPrimaryInvestigator(previousCase)
	currentInvestigator := checker.findPrimaryInvestigator(currentCase)

	// Check for investigator changes - all side effects are "Try" operations
	if currentInvestigator != nil {
		// We have a primary investigator - check if it's new or changed
		if previousInvestigator == nil || previousInvestigator.AssetId != currentInvestigator.AssetId {
			// New investigator or investigator changed - create order (will cancel existing if needed)
			effectsMap[CaseSideEffect_TryCreateManageCaseOrder] = true
		}
	} else if previousInvestigator != nil {
		// Investigator removed - complete orders
		effectsMap[CaseSideEffect_TryCompleteAllCaseOrders] = true
	}

	// Detect status changes
	var previousStatus casespb.CaseStatus
	if previousCase != nil {
		previousStatus = previousCase.Status
	}

	if previousStatus != currentCase.Status {
		statusEffects := checker.detectStatusChangeSideEffects(previousStatus, currentCase)
		for _, effect := range statusEffects {
			effectsMap[effect] = true
		}
	}

	// Convert map to slice to ensure uniqueness
	if len(effectsMap) == 0 {
		return []CaseSideEffectType{CaseSideEffect_None}
	}

	effects := []CaseSideEffectType{}
	for effect := range effectsMap {
		effects = append(effects, effect)
	}
	return effects
}

// Helper function to find the primary investigator in a case
func (checker *CaseSideEffectChecker) findPrimaryInvestigator(caseObj *casespb.Case) *casespb.CaseAssetAssociation {
	if caseObj == nil {
		return nil
	}
	for _, association := range caseObj.AssetAssociations {
		if association.AssociationType == casespb.CaseAssetAssociationType_ASSET_ASSOCIATION_TYPE_PRIMARY_INVESTIGATOR {
			return association
		}
	}
	return nil
}

// detectStatusChangeSideEffects detects side effects based on status transitions
func (checker *CaseSideEffectChecker) detectStatusChangeSideEffects(
	previousStatus casespb.CaseStatus,
	currentCase *casespb.Case,
) []CaseSideEffectType {
	effects := []CaseSideEffectType{}

	// Check if case is moving to a terminal state where orders should be completed
	switch currentCase.Status {
	case casespb.CaseStatus_CASE_STATUS_ON_HOLD,
		casespb.CaseStatus_CASE_STATUS_RESOLVED,
		casespb.CaseStatus_CASE_STATUS_CLOSED,
		casespb.CaseStatus_CASE_STATUS_ARCHIVED:
		// Case is moving to a terminal state - complete all orders if not already in terminal state
		if !isTerminalStatus(previousStatus) {
			effects = append(effects, CaseSideEffect_TryCompleteAllCaseOrders)
		}

	case casespb.CaseStatus_CASE_STATUS_INVESTIGATING,
		casespb.CaseStatus_CASE_STATUS_OPEN,
		casespb.CaseStatus_CASE_STATUS_ASSIGNED_FOR_INVESTIGATION,
		casespb.CaseStatus_CASE_STATUS_PENDING_INFORMATION,
		casespb.CaseStatus_CASE_STATUS_UNDER_REVIEW:
		// Case is being reactivated from a terminal state
		if isTerminalStatus(previousStatus) {
			// Case is being reactivated - create new order if investigator exists
			if checker.findPrimaryInvestigator(currentCase) != nil {
				effects = append(effects, CaseSideEffect_TryCreateManageCaseOrder)
			}
		}
	}

	return effects
}
