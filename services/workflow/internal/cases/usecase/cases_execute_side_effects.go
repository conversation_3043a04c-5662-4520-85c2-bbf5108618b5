// cases_execute_side_effects.go
package usecase

import (
	"context"
	"database/sql"
	"fmt"

	"common/herosentry"
	casespb "proto/hero/cases/v1"
	orderpb "proto/hero/orders/v2"
	orderRepository "workflow/internal/orders/data"
)

// CaseSideEffectExecutor orchestrates order side-effects for case lifecycle transitions.
type CaseSideEffectExecutor struct{}

// NewCaseSideEffectExecutor creates an executor instance.
func NewCaseSideEffectExecutor() *CaseSideEffectExecutor {
	return &CaseSideEffectExecutor{}
}

// findPrimaryInvestigator finds the primary investigator in a case
func (executor *CaseSideEffectExecutor) findPrimaryInvestigator(caseObj *casespb.Case) *casespb.CaseAssetAssociation {
	if caseObj == nil {
		return nil
	}
	for _, association := range caseObj.AssetAssociations {
		if association.AssociationType == casespb.CaseAssetAssociationType_ASSET_ASSOCIATION_TYPE_PRIMARY_INVESTIGATOR {
			return association
		}
	}
	return nil
}

// ExecuteSideEffect dispatches the side-effect based on the type.
// Must run within a transaction for atomicity.
func (executor *CaseSideEffectExecutor) ExecuteSideEffect(
	ctx context.Context,
	transaction *sql.Tx,
	effect CaseSideEffectType,
	updatedCase *casespb.Case,
	previousCase *casespb.Case,
	caseUseCase *CaseUseCase,
) error {
	// Find the primary investigator from the updated case using helper method
	currentInvestigator := executor.findPrimaryInvestigator(updatedCase)

	// Add side effect details to current span
	if span := herosentry.CurrentSpan(ctx); span != nil {
		span.SetTag("side_effect.type", fmt.Sprintf("%v", effect))
		if updatedCase != nil {
			span.SetTag("side_effect.case_id", updatedCase.Id)
			span.SetData("side_effect.case_status", updatedCase.Status.String())
		}
		if currentInvestigator != nil {
			span.SetTag("side_effect.primary_investigator.asset_id", currentInvestigator.AssetId)
		}
	}

	switch effect {
	case CaseSideEffect_None:
		return nil
	case CaseSideEffect_TryCreateManageCaseOrder:
		return executor.tryCreateManageCaseOrder(ctx, transaction, updatedCase, previousCase, caseUseCase)
	case CaseSideEffect_TryCompleteAllCaseOrders:
		return executor.tryCompleteAllCaseOrders(ctx, transaction, updatedCase, caseUseCase)
	default:
		return nil
	}
}

// terminalOrder returns true if the order status is final.
func terminalOrder(status orderpb.OrderStatus) bool {
	switch status {
	case orderpb.OrderStatus_ORDER_STATUS_COMPLETED,
		orderpb.OrderStatus_ORDER_STATUS_CANCELLED,
		orderpb.OrderStatus_ORDER_STATUS_REJECTED:
		return true
	default:
		return false
	}
}

// listAllOrdersForCase handles pagination for ListOrdersForCase.
func listAllOrdersForCase(
	ctx context.Context,
	repository orderRepository.OrderRepository,
	transaction *sql.Tx,
	caseID string,
	status orderpb.OrderStatus,
) ([]*orderpb.Order, error) {
	var allOrders []*orderpb.Order
	pageToken := ""
	for {
		response, err := repository.ListOrdersForCase(ctx, transaction, caseID, defaultPageSize, pageToken, status)
		if err != nil {
			return nil, err
		}
		allOrders = append(allOrders, response.Orders...)
		if response.PageToken == "" {
			break
		}
		pageToken = response.PageToken
	}
	return allOrders, nil
}

// createManageCaseOrderInternal is a shared function to create MANAGE_CASE orders.
func (executor *CaseSideEffectExecutor) createManageCaseOrderInternal(
	ctx context.Context,
	transaction *sql.Tx,
	updatedCase *casespb.Case,
	investigator *casespb.CaseAssetAssociation,
	instructions string,
	caseUseCase *CaseUseCase,
) error {
	// Create span for this operation
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "CaseSideEffectExecutor.createManageCaseOrderInternal")
	defer finishSpan()

	span.SetTag("case.id", updatedCase.Id)
	span.SetTag("case.title", updatedCase.Title)
	span.SetTag("investigator.asset_id", investigator.AssetId)
	span.SetData("instructions", instructions)

	// Create the order for the investigator
	order := &orderpb.Order{
		AssetId:      investigator.AssetId,
		CaseId:       updatedCase.Id,
		Type:         orderpb.OrderType_ORDER_TYPE_MANAGE_CASE,
		Status:       orderpb.OrderStatus_ORDER_STATUS_CREATED,
		Instructions: instructions,
		Priority:     int32(updatedCase.Priority),
	}

	_, err := caseUseCase.orderRepository.CreateOrder(spanContext, transaction, order)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase,
			fmt.Sprintf("Failed to create manage case order for case %s", updatedCase.Id))
	}
	return err
}

// tryCreateManageCaseOrder creates a MANAGE_CASE order for the primary investigator.
// This handles both new assignments and reassignments:
// - If investigator changed: cancels existing orders and creates new
// - If same investigator: only creates if all existing MANAGE_CASE orders are terminal
func (executor *CaseSideEffectExecutor) tryCreateManageCaseOrder(
	ctx context.Context,
	transaction *sql.Tx,
	updatedCase *casespb.Case,
	previousCase *casespb.Case,
	caseUseCase *CaseUseCase,
) error {
	// Create span for this operation
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "CaseSideEffectExecutor.tryCreateManageCaseOrder")
	defer finishSpan()

	// Find investigators in both cases
	previousInvestigator := executor.findPrimaryInvestigator(previousCase)
	currentInvestigator := executor.findPrimaryInvestigator(updatedCase)

	// If no current investigator, nothing to do
	if currentInvestigator == nil {
		return nil
	}

	span.SetTag("case.id", updatedCase.Id)
	span.SetTag("investigator.asset_id", currentInvestigator.AssetId)

	// Check if investigator changed
	investigatorChanged := previousInvestigator == nil || previousInvestigator.AssetId != currentInvestigator.AssetId

	// List existing orders for this case
	existingOrders, err := listAllOrdersForCase(spanContext, caseUseCase.orderRepository, transaction, updatedCase.Id, orderpb.OrderStatus_ORDER_STATUS_UNSPECIFIED)
	if err != nil {
		err := fmt.Errorf("failed to list existing orders for case %s: %w", updatedCase.Id, err)
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
		return err
	}

	// Check if we have any non-terminal MANAGE_CASE orders
	hasActiveManageCaseOrder := false
	for _, order := range existingOrders {
		if order.Type == orderpb.OrderType_ORDER_TYPE_MANAGE_CASE && !terminalOrder(order.Status) {
			hasActiveManageCaseOrder = true

			// If investigator changed, cancel the existing order
			if investigatorChanged {
				if _, cancelError := caseUseCase.orderRepository.CancelOrder(spanContext, transaction, order.Id, "Case assigned to different investigator"); cancelError != nil {
					herosentry.CaptureException(spanContext, cancelError, herosentry.ErrorTypeDatabase,
						fmt.Sprintf("Failed to cancel order %s", order.Id))
					// Continue even if cancellation fails
				}
			}
		}
	}

	// Decide whether to create a new order
	shouldCreateOrder := false
	if investigatorChanged {
		// Investigator changed - always create new order
		shouldCreateOrder = true
	} else if !hasActiveManageCaseOrder {
		// Same investigator but no active orders - create new order
		shouldCreateOrder = true
	}
	// If same investigator and has active orders - don't create new order

	if shouldCreateOrder {
		instructions := fmt.Sprintf("Investigate case: %s", updatedCase.Title)
		return executor.createManageCaseOrderInternal(spanContext, transaction, updatedCase, currentInvestigator, instructions, caseUseCase)
	}

	return nil
}

// completeAllCaseOrdersInternal is a shared function to complete all non-terminal orders for a case.
func (executor *CaseSideEffectExecutor) completeAllCaseOrdersInternal(
	ctx context.Context,
	transaction *sql.Tx,
	updatedCase *casespb.Case,
	reason string,
	caseUseCase *CaseUseCase,
) error {
	// Create span for this operation
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "CaseSideEffectExecutor.completeAllCaseOrdersInternal")
	defer finishSpan()

	span.SetTag("case.id", updatedCase.Id)
	span.SetTag("case.status", updatedCase.Status.String())
	span.SetData("reason", reason)

	orders, err := listAllOrdersForCase(spanContext, caseUseCase.orderRepository, transaction, updatedCase.Id, orderpb.OrderStatus_ORDER_STATUS_UNSPECIFIED)
	if err != nil {
		err := fmt.Errorf("failed to list orders for %s: %w", reason, err)
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
		return err
	}

	for _, order := range orders {
		if !terminalOrder(order.Status) {
			if _, completeError := caseUseCase.orderRepository.CompleteOrder(spanContext, transaction, order.Id); completeError != nil {
				herosentry.CaptureException(spanContext, completeError, herosentry.ErrorTypeDatabase,
					fmt.Sprintf("Failed to complete order %s for %s", order.Id, reason))
				return completeError
			}
		}
	}
	return nil
}

// tryCompleteAllCaseOrders attempts to complete all non-terminal orders for the case.
// This is a "Try" operation - used when case moves to terminal state or investigator removed.
// Terminal states: ON_HOLD, RESOLVED, CLOSED, ARCHIVED
func (executor *CaseSideEffectExecutor) tryCompleteAllCaseOrders(
	ctx context.Context,
	transaction *sql.Tx,
	updatedCase *casespb.Case,
	caseUseCase *CaseUseCase,
) error {
	reason := "case status change"
	switch updatedCase.Status {
	case casespb.CaseStatus_CASE_STATUS_CLOSED:
		reason = "case closed"
	case casespb.CaseStatus_CASE_STATUS_ON_HOLD:
		reason = "case on hold"
	case casespb.CaseStatus_CASE_STATUS_RESOLVED:
		reason = "case resolved"
	case casespb.CaseStatus_CASE_STATUS_ARCHIVED:
		reason = "case archived"
	}
	return executor.completeAllCaseOrdersInternal(ctx, transaction, updatedCase, reason, caseUseCase)
}
